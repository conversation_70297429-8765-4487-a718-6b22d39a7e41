package com.healthconnect.service;

import com.healthconnect.dto.*;
import com.healthconnect.entity.*;
import com.healthconnect.repository.ChatRepository;
import com.healthconnect.repository.MessageRepository;
import com.healthconnect.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class ChatService {
    
    private final ChatRepository chatRepository;
    private final MessageRepository messageRepository;
    private final UserRepository userRepository;
    
    public ChatResponse createOrGetChat(Long currentUserId, Long participantId) {
        User currentUser = userRepository.findById(currentUserId)
                .orElseThrow(() -> new RuntimeException("Current user not found"));
        User participant = userRepository.findById(participantId)
                .orElseThrow(() -> new RuntimeException("Participant not found"));
        
        // Ensure one is patient and one is doctor
        User patient = currentUser.getRole() == UserRole.PATIENT ? currentUser : participant;
        User doctor = currentUser.getRole() == UserRole.DOCTOR ? currentUser : participant;
        
        if (patient.getRole() == doctor.getRole()) {
            throw new RuntimeException("Chat can only be created between patient and doctor");
        }
        
        Chat chat = chatRepository.findByParticipants(patient, doctor)
                .orElseGet(() -> {
                    Chat newChat = new Chat();
                    newChat.setPatient(patient);
                    newChat.setDoctor(doctor);
                    return chatRepository.save(newChat);
                });
        
        return convertToChatResponse(chat, currentUser);
    }
    
    public List<ChatResponse> getUserChats(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        List<Chat> chats = chatRepository.findByParticipant(user);
        return chats.stream()
                .map(chat -> convertToChatResponse(chat, user))
                .collect(Collectors.toList());
    }
    
    public MessageResponse sendMessage(Long chatId, String content, Long senderId) {
        Chat chat = chatRepository.findById(chatId)
                .orElseThrow(() -> new RuntimeException("Chat not found"));
        User sender = userRepository.findById(senderId)
                .orElseThrow(() -> new RuntimeException("Sender not found"));
        
        // Verify sender is participant in chat
        if (!chat.getPatient().getId().equals(senderId) && !chat.getDoctor().getId().equals(senderId)) {
            throw new RuntimeException("User is not a participant in this chat");
        }
        
        Message message = new Message();
        message.setChat(chat);
        message.setSender(sender);
        message.setContent(content);
        message.setStatus(MessageStatus.SENT);
        
        message = messageRepository.save(message);
        
        // Update chat timestamp
        chat.setUpdatedAt(message.getCreatedAt());
        chatRepository.save(chat);
        
        return convertToMessageResponse(message);
    }
    
    public List<MessageResponse> getChatMessages(Long chatId, Long userId, int page, int size) {
        Chat chat = chatRepository.findById(chatId)
                .orElseThrow(() -> new RuntimeException("Chat not found"));
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        // Verify user is participant in chat
        if (!chat.getPatient().getId().equals(userId) && !chat.getDoctor().getId().equals(userId)) {
            throw new RuntimeException("User is not a participant in this chat");
        }
        
        Pageable pageable = PageRequest.of(page, size);
        Page<Message> messages = messageRepository.findByChatOrderByCreatedAtDesc(chat, pageable);
        
        return messages.getContent().stream()
                .map(this::convertToMessageResponse)
                .collect(Collectors.toList());
    }
    
    public void markMessagesAsRead(Long chatId, Long userId) {
        Chat chat = chatRepository.findById(chatId)
                .orElseThrow(() -> new RuntimeException("Chat not found"));
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        messageRepository.markMessagesAsRead(chat, user, MessageStatus.READ);
    }
    
    private ChatResponse convertToChatResponse(Chat chat, User currentUser) {
        ChatResponse response = new ChatResponse();
        response.setId(chat.getId());
        response.setPatient(convertToUserResponse(chat.getPatient()));
        response.setDoctor(convertToUserResponse(chat.getDoctor()));
        response.setCreatedAt(chat.getCreatedAt());
        response.setUpdatedAt(chat.getUpdatedAt());
        
        // Get last message
        Message lastMessage = messageRepository.findLastMessageByChat(chat);
        if (lastMessage != null) {
            response.setLastMessage(convertToMessageResponse(lastMessage));
        }
        
        // Get unread count for current user
        Integer unreadCount = messageRepository.countUnreadMessages(chat, currentUser, MessageStatus.READ);
        response.setUnreadCount(unreadCount);
        
        return response;
    }
    
    private MessageResponse convertToMessageResponse(Message message) {
        MessageResponse response = new MessageResponse();
        response.setId(message.getId());
        response.setChatId(message.getChat().getId());
        response.setSender(convertToUserResponse(message.getSender()));
        response.setContent(message.getContent());
        response.setStatus(message.getStatus());
        response.setCreatedAt(message.getCreatedAt());
        response.setReadAt(message.getReadAt());
        return response;
    }
    
    private UserResponse convertToUserResponse(User user) {
        UserResponse response = new UserResponse();
        response.setId(user.getId());
        response.setFullName(user.getFullName());
        response.setEmail(user.getEmail());
        response.setRole(user.getRole());
        response.setAvatar(user.getAvatar());
        response.setSpecialization(user.getSpecialization());
        response.setAffiliation(user.getAffiliation());
        return response;
    }
}
