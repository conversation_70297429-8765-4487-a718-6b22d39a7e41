package com.healthconnect.dto;

import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class UpdateProfileRequest {
    
    @Size(min = 2, max = 100, message = "Full name must be between 2 and 100 characters")
    private String fullName;
    
    // Doctor-specific fields
    private String specialization;
    private String affiliation;
    private Integer yearsOfExperience;
    
    // Common fields
    private String phoneNumber;
    private String address;
    private String avatar;
}
