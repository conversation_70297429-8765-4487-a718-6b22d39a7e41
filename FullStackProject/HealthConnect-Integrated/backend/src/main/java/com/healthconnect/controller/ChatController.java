package com.healthconnect.controller;

import com.healthconnect.dto.*;
import com.healthconnect.entity.User;
import com.healthconnect.service.ChatService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/chats")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class ChatController {
    
    private final ChatService chatService;
    
    @PostMapping
    public ResponseEntity<ChatResponse> createOrGetChat(
            @Valid @RequestBody ChatRequest request,
            Authentication authentication) {
        User currentUser = (User) authentication.getPrincipal();
        ChatResponse chat = chatService.createOrGetChat(currentUser.getId(), request.getParticipantId());
        return ResponseEntity.ok(chat);
    }
    
    @GetMapping
    public ResponseEntity<List<ChatResponse>> getUserChats(Authentication authentication) {
        User currentUser = (User) authentication.getPrincipal();
        List<ChatResponse> chats = chatService.getUserChats(currentUser.getId());
        return ResponseEntity.ok(chats);
    }
    
    @GetMapping("/{chatId}/messages")
    public ResponseEntity<List<MessageResponse>> getChatMessages(
            @PathVariable Long chatId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "50") int size,
            Authentication authentication) {
        User currentUser = (User) authentication.getPrincipal();
        List<MessageResponse> messages = chatService.getChatMessages(chatId, currentUser.getId(), page, size);
        return ResponseEntity.ok(messages);
    }
    
    @PostMapping("/{chatId}/messages")
    public ResponseEntity<MessageResponse> sendMessage(
            @PathVariable Long chatId,
            @Valid @RequestBody MessageRequest request,
            Authentication authentication) {
        User currentUser = (User) authentication.getPrincipal();
        MessageResponse message = chatService.sendMessage(chatId, request.getContent(), currentUser.getId());
        return ResponseEntity.ok(message);
    }
    
    @PutMapping("/{chatId}/read")
    public ResponseEntity<Void> markMessagesAsRead(
            @PathVariable Long chatId,
            Authentication authentication) {
        User currentUser = (User) authentication.getPrincipal();
        chatService.markMessagesAsRead(chatId, currentUser.getId());
        return ResponseEntity.ok().build();
    }
}
