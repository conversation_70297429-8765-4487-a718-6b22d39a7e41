package com.healthconnect.controller;

import com.healthconnect.dto.AppointmentRequest;
import com.healthconnect.dto.AppointmentUpdateRequest;
import com.healthconnect.dto.AppointmentResponse;
import com.healthconnect.entity.Appointment;
import com.healthconnect.entity.AppointmentStatus;
import com.healthconnect.entity.AppointmentType;
import com.healthconnect.entity.User;
import com.healthconnect.service.AppointmentService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/appointments")
@RequiredArgsConstructor
@CrossOrigin(origins = "http://localhost:4200")
public class AppointmentController {
    
    private final AppointmentService appointmentService;
    
    // Get appointments with optional filters
    @GetMapping
    public ResponseEntity<List<AppointmentResponse>> getAppointments(
            @AuthenticationPrincipal User user,
            @RequestParam(required = false) AppointmentStatus status,
            @RequestParam(required = false) AppointmentType type,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        List<Appointment> appointments = appointmentService.getAppointments(user, status, type, startDate, endDate);
        List<AppointmentResponse> response = appointments.stream()
                .map(AppointmentResponse::new)
                .toList();
        return ResponseEntity.ok(response);
    }
    
    // Get appointment by ID
    @GetMapping("/{id}")
    public ResponseEntity<AppointmentResponse> getAppointment(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {

        return appointmentService.getAppointmentById(id, user)
                .map(appointment -> ResponseEntity.ok(new AppointmentResponse(appointment)))
                .orElse(ResponseEntity.notFound().build());
    }
    
    // Create new appointment
    @PostMapping
    public ResponseEntity<AppointmentResponse> createAppointment(
            @Valid @RequestBody AppointmentRequest request,
            @AuthenticationPrincipal User user) {

        try {
            Appointment appointment = appointmentService.createAppointment(request, user);
            return ResponseEntity.status(HttpStatus.CREATED).body(new AppointmentResponse(appointment));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    // Update appointment
    @PutMapping("/{id}")
    public ResponseEntity<AppointmentResponse> updateAppointment(
            @PathVariable Long id,
            @RequestBody AppointmentUpdateRequest request,
            @AuthenticationPrincipal User user) {

        try {
            Appointment appointment = appointmentService.updateAppointment(id, request, user);
            return ResponseEntity.ok(new AppointmentResponse(appointment));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    // Cancel appointment
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> cancelAppointment(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {
        
        try {
            appointmentService.cancelAppointment(id, user);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    // Get today's appointments
    @GetMapping("/today")
    public ResponseEntity<List<AppointmentResponse>> getTodayAppointments(
            @AuthenticationPrincipal User user) {

        List<Appointment> appointments = appointmentService.getTodayAppointments(user);
        List<AppointmentResponse> response = appointments.stream()
                .map(AppointmentResponse::new)
                .toList();
        return ResponseEntity.ok(response);
    }
}
