package com.healthconnect.controller;

import com.healthconnect.dto.MessageRequest;
import com.healthconnect.dto.MessageResponse;
import com.healthconnect.service.ChatService;
import com.healthconnect.service.JwtService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;

@Controller
@RequiredArgsConstructor
@Slf4j
public class WebSocketController {
    
    private final ChatService chatService;
    private final SimpMessagingTemplate messagingTemplate;
    private final JwtService jwtService;
    
    @MessageMapping("/chat/{chatId}/send")
    public void sendMessage(
            @DestinationVariable Long chatId,
            @Payload MessageRequest request,
            @Header("Authorization") String authHeader) {
        try {
            // Extract user ID from JWT token
            String token = authHeader.substring(7); // Remove "Bearer " prefix
            String userEmail = jwtService.extractUsername(token);
            Long senderId = jwtService.extractUserId(token);
            
            log.info("Sending message from user {} to chat {}", senderId, chatId);
            
            // Send message through chat service
            MessageResponse message = chatService.sendMessage(chatId, request.getContent(), senderId);
            
            // Broadcast message to all users in the chat
            messagingTemplate.convertAndSend(
                "/topic/chat/" + chatId,
                message
            );
            
            log.info("Message sent successfully: {}", message.getId());
            
        } catch (Exception e) {
            log.error("Failed to send message: {}", e.getMessage());
            // Send error message back to sender
            messagingTemplate.convertAndSendToUser(
                jwtService.extractUsername(authHeader.substring(7)),
                "/queue/errors",
                "Failed to send message: " + e.getMessage()
            );
        }
    }
    
    @MessageMapping("/chat/{chatId}/typing")
    public void handleTyping(
            @DestinationVariable Long chatId,
            @Payload String typingStatus,
            @Header("Authorization") String authHeader) {
        try {
            String token = authHeader.substring(7);
            String userEmail = jwtService.extractUsername(token);
            Long userId = jwtService.extractUserId(token);
            
            // Broadcast typing status to other participants
            messagingTemplate.convertAndSend(
                "/topic/chat/" + chatId + "/typing",
                new TypingNotification(userId, userEmail, typingStatus)
            );
            
        } catch (Exception e) {
            log.error("Failed to handle typing notification: {}", e.getMessage());
        }
    }
    
    // Inner class for typing notifications
    public static class TypingNotification {
        private Long userId;
        private String userEmail;
        private String status; // "typing" or "stopped"
        
        public TypingNotification(Long userId, String userEmail, String status) {
            this.userId = userId;
            this.userEmail = userEmail;
            this.status = status;
        }
        
        // Getters
        public Long getUserId() { return userId; }
        public String getUserEmail() { return userEmail; }
        public String getStatus() { return status; }
    }
}
