package com.healthconnect.dto;

import com.healthconnect.entity.MessageStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageResponse {
    
    private Long id;
    private Long chatId;
    private UserResponse sender;
    private String content;
    private MessageStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime readAt;
}
