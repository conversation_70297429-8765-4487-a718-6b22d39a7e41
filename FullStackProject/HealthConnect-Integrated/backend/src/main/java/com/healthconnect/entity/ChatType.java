package com.healthconnect.entity;

public enum ChatType {
    GENERAL,                    // General conversation
    PRE_APPOINTMENT,           // Before appointment discussion
    POST_APPOINTMENT,          // After appointment follow-up
    URGENT,                    // Urgent medical concerns
    PRESCRIPTION_INQUIRY,      // Questions about medications
    FOLLOW_UP,                 // Follow-up care discussions
    APPOINTMENT_SCHEDULING     // Appointment-related communication
}
