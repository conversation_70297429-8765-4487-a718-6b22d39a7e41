<!-- Navigation Bar -->
<nav *ngIf="showNavigation && currentUser" class="navbar navbar-expand-lg navbar-dark bg-primary">
  <div class="container-fluid">
    <!-- Brand -->
    <a class="navbar-brand fw-bold" (click)="navigateToDashboard()" style="cursor: pointer;">
      <i class="bi bi-heart-pulse me-2"></i>HealthConnect
    </a>

    <!-- Mobile toggle button -->
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
      <span class="navbar-toggler-icon"></span>
    </button>

    <!-- Navigation items -->
    <div class="collapse navbar-collapse" id="navbarNav">
      <ul class="navbar-nav me-auto">
        <li class="nav-item">
          <a class="nav-link" (click)="navigateToDashboard()" style="cursor: pointer;">
            <i class="bi bi-house me-1"></i>Dashboard
          </a>
        </li>
        
        <!-- Patient-specific navigation -->
        <ng-container *ngIf="currentUser.role === 'PATIENT'">
          <li class="nav-item">
            <a class="nav-link" routerLink="/appointments" routerLinkActive="active">
              <i class="bi bi-calendar me-1"></i>Appointments
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" routerLink="/doctors" routerLinkActive="active">
              <i class="bi bi-search me-1"></i>Find Doctors
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" routerLink="/health-bot" routerLinkActive="active">
              <i class="bi bi-robot me-1"></i>Health Assistant
            </a>
          </li>
        </ng-container>

        <!-- Doctor-specific navigation -->
        <ng-container *ngIf="currentUser.role === 'DOCTOR'">
          <li class="nav-item">
            <a class="nav-link" routerLink="/patients" routerLinkActive="active">
              <i class="bi bi-people me-1"></i>Patients
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" routerLink="/appointments" routerLinkActive="active">
              <i class="bi bi-calendar me-1"></i>Schedule
            </a>
          </li>
        </ng-container>

        <!-- Common navigation -->
        <li class="nav-item">
          <a class="nav-link" routerLink="/chat" routerLinkActive="active">
            <i class="bi bi-chat-dots me-1"></i>Messages
          </a>
        </li>
      </ul>

      <!-- User menu -->
      <ul class="navbar-nav">
        <!-- Notification Bell -->
        <li class="nav-item">
          <app-notification-bell></app-notification-bell>
        </li>

        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" 
             role="button" data-bs-toggle="dropdown">
            <div class="rounded-circle bg-light text-primary d-flex align-items-center justify-content-center me-2" 
                 style="width: 32px; height: 32px;">
              <i class="bi bi-person"></i>
            </div>
            <span class="d-none d-md-inline">{{ currentUser.fullName }}</span>
          </a>
          <ul class="dropdown-menu dropdown-menu-end">
            <li>
              <h6 class="dropdown-header">
                {{ currentUser.fullName }}
                <br>
                <small class="text-muted">{{ currentUser.role | titlecase }}</small>
              </h6>
            </li>
            <li><hr class="dropdown-divider"></li>
            <li>
              <a class="dropdown-item" (click)="navigateToProfile()" style="cursor: pointer;">
                <i class="bi bi-person-gear me-2"></i>Profile Settings
              </a>
            </li>
            <li>
              <a class="dropdown-item" href="#" style="cursor: pointer;">
                <i class="bi bi-bell me-2"></i>Notifications
              </a>
            </li>
            <li>
              <a class="dropdown-item" href="#" style="cursor: pointer;">
                <i class="bi bi-question-circle me-2"></i>Help & Support
              </a>
            </li>
            <li><hr class="dropdown-divider"></li>
            <li>
              <a class="dropdown-item text-danger" (click)="logout()" style="cursor: pointer;">
                <i class="bi bi-box-arrow-right me-2"></i>Sign Out
              </a>
            </li>
          </ul>
        </li>
      </ul>
    </div>
  </div>
</nav>

<!-- Main Content -->
<main class="main-content" [class.with-navbar]="showNavigation && currentUser">
  <router-outlet></router-outlet>
</main>

<!-- Footer (only show when not in auth pages) -->
<footer *ngIf="showNavigation" class="bg-light text-center py-3 mt-auto">
  <div class="container">
    <small class="text-muted">
      &copy; 2024 HealthConnect. All rights reserved. | 
      <a href="#" class="text-decoration-none">Privacy Policy</a> | 
      <a href="#" class="text-decoration-none">Terms of Service</a>
    </small>
  </div>
</footer>
