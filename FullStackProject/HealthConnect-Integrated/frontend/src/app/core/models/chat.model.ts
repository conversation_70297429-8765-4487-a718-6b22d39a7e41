export interface User {
  id: number;
  fullName: string;
  email: string;
  role: 'PATIENT' | 'DOCTOR';
  avatar?: string;
  specialization?: string;
  affiliation?: string;
}

export interface Message {
  id: number;
  chatId: number;
  sender: User;
  content: string;
  status: 'SENT' | 'DELIVERED' | 'READ';
  createdAt: string;
  readAt?: string;
}

export interface Chat {
  id: number;
  patient: User;
  doctor: User;
  lastMessage?: Message;
  unreadCount: number;
  relatedAppointment?: any;
  type?: string;
  status?: string;
  subject?: string;
  createdAt: string;
  updatedAt: string;
  lastMessageAt?: string;
}

export interface ChatRequest {
  participantId: number;
}

export interface MessageRequest {
  chatId: number;
  content: string;
}

export interface TypingNotification {
  userId: number;
  userEmail: string;
  status: 'typing' | 'stopped';
}
