import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { Client } from '@stomp/stompjs';
import SockJS from 'sockjs-client';
import { environment } from '../../../environments/environment';
import { Chat, Message, ChatRequest, MessageRequest, TypingNotification } from '../models/chat.model';
import { AuthService } from './auth.service';
import { NotificationService } from './notification.service';

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  private apiUrl = `${environment.apiUrl}/chats`;
  private wsUrl = `${environment.apiUrl}/ws`;
  
  private stompClient: Client | null = null;
  private connectionStatusSubject = new BehaviorSubject<boolean>(false);
  private messageSubject = new Subject<Message>();
  private typingSubject = new Subject<TypingNotification>();
  private chatsSubject = new BehaviorSubject<Chat[]>([]);
  
  public connectionStatus$ = this.connectionStatusSubject.asObservable();
  public messages$ = this.messageSubject.asObservable();
  public typing$ = this.typingSubject.asObservable();
  public chats$ = this.chatsSubject.asObservable();

  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private notificationService: NotificationService
  ) {
    this.initializeWebSocketConnection();
  }

  private initializeWebSocketConnection(): void {
    if (this.authService.isAuthenticated()) {
      this.connect();
    }
    
    // Listen for authentication changes
    this.authService.currentUser$.subscribe(user => {
      if (user) {
        this.connect();
      } else {
        this.disconnect();
      }
    });
  }

  private connect(): void {
    if (this.stompClient?.connected) {
      return;
    }

    const token = this.authService.getToken();
    if (!token) {
      return;
    }

    this.stompClient = new Client({
      webSocketFactory: () => new SockJS(this.wsUrl),
      connectHeaders: {
        Authorization: `Bearer ${token}`
      },
      debug: (str) => {
        console.log('STOMP Debug:', str);
      },
      onConnect: () => {
        this.connectionStatusSubject.next(true);
        console.log('WebSocket connected successfully');
        this.subscribeToUserChannels();
      },
      onWebSocketClose: () => {
        this.connectionStatusSubject.next(false);
        console.log('WebSocket connection closed');
        // Try to reconnect after 5 seconds
        setTimeout(() => {
          if (this.authService.isAuthenticated()) {
            this.connect();
          }
        }, 5000);
      },
      onStompError: (frame) => {
        console.error('STOMP error:', frame);
        this.connectionStatusSubject.next(false);
      }
    });

    this.stompClient.activate();
  }

  private subscribeToUserChannels(): void {
    if (!this.stompClient?.connected) {
      return;
    }

    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) {
      return;
    }

    // Subscribe to error messages
    this.stompClient.subscribe('/user/queue/errors', (message) => {
      console.error('WebSocket error:', message.body);
    });
  }

  public subscribeToChatMessages(chatId: number): void {
    if (!this.stompClient?.connected) {
      return;
    }

    // Subscribe to chat messages
    this.stompClient.subscribe(`/topic/chat/${chatId}`, (message) => {
      const newMessage: Message = JSON.parse(message.body);
      this.messageSubject.next(newMessage);

      // Add notification for new messages from other users
      const currentUser = this.authService.getCurrentUser();
      if (newMessage.sender.id !== currentUser?.id) {
        this.notificationService.addMessageNotification(
          newMessage.sender,
          newMessage.content,
          chatId
        );
      }
    });

    // Subscribe to typing notifications
    this.stompClient.subscribe(`/topic/chat/${chatId}/typing`, (message) => {
      const typingNotification: TypingNotification = JSON.parse(message.body);
      this.typingSubject.next(typingNotification);
    });
  }

  public sendMessage(chatId: number, content: string): void {
    if (!this.stompClient?.connected) {
      throw new Error('WebSocket not connected');
    }

    const token = this.authService.getToken();
    if (!token) {
      throw new Error('No authentication token');
    }

    const messageRequest: MessageRequest = {
      chatId,
      content
    };

    this.stompClient.publish({
      destination: `/app/chat/${chatId}/send`,
      body: JSON.stringify(messageRequest),
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
  }

  public sendTypingNotification(chatId: number, isTyping: boolean): void {
    if (!this.stompClient?.connected) {
      return;
    }

    const token = this.authService.getToken();
    if (!token) {
      return;
    }

    this.stompClient.publish({
      destination: `/app/chat/${chatId}/typing`,
      body: isTyping ? 'typing' : 'stopped',
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
  }

  public disconnect(): void {
    if (this.stompClient) {
      this.stompClient.deactivate();
      this.connectionStatusSubject.next(false);
    }
  }

  // HTTP API methods
  public createOrGetChat(participantId: number): Observable<Chat> {
    const request: ChatRequest = { participantId };
    return this.http.post<Chat>(this.apiUrl, request, this.getHttpOptions());
  }

  public getUserChats(): Observable<Chat[]> {
    return this.http.get<Chat[]>(this.apiUrl, this.getHttpOptions());
  }

  public getChatMessages(chatId: number, page: number = 0, size: number = 50): Observable<Message[]> {
    const params = { page: page.toString(), size: size.toString() };
    return this.http.get<Message[]>(`${this.apiUrl}/${chatId}/messages`, {
      ...this.getHttpOptions(),
      params
    });
  }

  public markMessagesAsRead(chatId: number): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${chatId}/read`, {}, this.getHttpOptions());
  }

  public loadUserChats(): void {
    this.getUserChats().subscribe({
      next: (chats) => {
        this.chatsSubject.next(chats);
      },
      error: (error) => {
        console.error('Failed to load chats:', error);
      }
    });
  }

  private getHttpOptions() {
    const token = this.authService.getToken();
    return {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      })
    };
  }
}
