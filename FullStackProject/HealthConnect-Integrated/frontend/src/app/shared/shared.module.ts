import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { ChatAccessComponent } from './components/chat-access/chat-access.component';
import { DoctorAvailabilityComponent } from './components/doctor-availability/doctor-availability.component';

@NgModule({
  declarations: [
    ChatAccessComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule
  ],
  exports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule,
    ChatAccessComponent
  ]
})
export class SharedModule { }
