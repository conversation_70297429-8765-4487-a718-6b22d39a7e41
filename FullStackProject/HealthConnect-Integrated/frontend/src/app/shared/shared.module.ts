import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { ChatAccessComponent } from './components/chat-access/chat-access.component';
import { DoctorAvailabilityComponent } from './components/doctor-availability/doctor-availability.component';
import { NotificationBellComponent } from './components/notification-bell/notification-bell.component';
import { QuickChatWidgetComponent } from './components/quick-chat-widget/quick-chat-widget.component';

@NgModule({
  declarations: [
    ChatAccessComponent,
    DoctorAvailabilityComponent,
    NotificationBellComponent,
    QuickChatWidgetComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule
  ],
  exports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule,
    ChatAccessComponent,
    DoctorAvailabilityComponent,
    NotificationBellComponent,
    QuickChatWidgetComponent
  ]
})
export class SharedModule { }
