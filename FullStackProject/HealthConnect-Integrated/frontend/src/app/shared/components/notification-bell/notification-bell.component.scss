.notification-bell {
  position: relative;
  display: inline-block;

  .notification-trigger {
    position: relative;
    padding: 0.5rem;
    color: #6c757d;
    border: none;
    background: none;
    font-size: 1.25rem;
    transition: color 0.2s ease;

    &:hover {
      color: #495057;
    }

    &.has-notifications {
      color: #007bff;
      animation: bellShake 2s infinite;
    }

    .notification-badge {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 0.75rem;
      min-width: 18px;
      height: 18px;
      border-radius: 9px;
      display: flex;
      align-items: center;
      justify-content: center;
      transform: translate(25%, -25%);
    }
  }

  .notification-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 380px;
    max-height: 500px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1050;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;

    &.show {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .dropdown-header {
      padding: 1rem;
      border-bottom: 1px solid #dee2e6;
      background: #f8f9fa;
      border-radius: 8px 8px 0 0;

      h6 {
        color: #495057;
        font-weight: 600;
      }

      .dropdown-actions {
        .btn {
          padding: 0.25rem 0.5rem;
          font-size: 0.875rem;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }

    .notifications-list {
      max-height: 350px;
      overflow-y: auto;

      .notification-item {
        position: relative;
        padding: 1rem;
        border-bottom: 1px solid #f1f3f4;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f8f9fa;
        }

        &:last-child {
          border-bottom: none;
        }

        &.unread {
          background-color: #f0f8ff;
          border-left: 3px solid #007bff;

          .notification-title {
            font-weight: 600;
          }
        }

        &.priority-urgent {
          border-left-color: #dc3545;
          
          .notification-icon i {
            color: #dc3545;
            animation: pulse 2s infinite;
          }
        }

        &.priority-high {
          border-left-color: #fd7e14;
        }

        .notification-content {
          display: flex;
          align-items: flex-start;
          gap: 0.75rem;
        }

        .notification-icon {
          flex-shrink: 0;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: #e9ecef;
          display: flex;
          align-items: center;
          justify-content: center;

          i {
            font-size: 0.875rem;
            color: #6c757d;
          }
        }

        .notification-body {
          flex: 1;
          min-width: 0;

          .notification-title {
            font-size: 0.875rem;
            color: #212529;
            margin-bottom: 0.25rem;
            line-height: 1.4;
          }

          .notification-message {
            font-size: 0.8125rem;
            color: #6c757d;
            line-height: 1.4;
            margin-bottom: 0.5rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .notification-from {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.25rem;

            .from-avatar {
              width: 20px;
              height: 20px;
              border-radius: 50%;
              object-fit: cover;
            }

            .from-name {
              font-size: 0.75rem;
              color: #495057;
              font-weight: 500;
            }
          }

          .notification-time {
            font-size: 0.75rem;
            color: #adb5bd;
          }
        }

        .notification-actions {
          flex-shrink: 0;

          .btn {
            padding: 0.25rem;
            border: none;
            background: none;

            &:hover {
              background: #e9ecef;
              border-radius: 4px;
            }
          }
        }

        .unread-indicator {
          position: absolute;
          top: 50%;
          right: 0.5rem;
          width: 8px;
          height: 8px;
          background: #007bff;
          border-radius: 50%;
          transform: translateY(-50%);
        }
      }

      .no-notifications {
        padding: 2rem 1rem;
      }
    }

    .dropdown-footer {
      padding: 0.75rem 1rem;
      border-top: 1px solid #dee2e6;
      background: #f8f9fa;
      border-radius: 0 0 8px 8px;
    }
  }
}

// Animations
@keyframes bellShake {
  0%, 50%, 100% { transform: rotate(0deg); }
  10%, 30% { transform: rotate(-10deg); }
  20%, 40% { transform: rotate(10deg); }
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

// Responsive design
@media (max-width: 768px) {
  .notification-bell .notification-dropdown {
    width: 320px;
    right: -50px;
  }
}

@media (max-width: 576px) {
  .notification-bell .notification-dropdown {
    width: 280px;
    right: -100px;
  }
}
