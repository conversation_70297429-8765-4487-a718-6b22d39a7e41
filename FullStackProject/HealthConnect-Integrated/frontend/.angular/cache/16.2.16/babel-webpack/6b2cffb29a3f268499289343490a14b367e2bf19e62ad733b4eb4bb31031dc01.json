{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/auth.service\";\nimport * as i2 from \"../../core/services/user.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction PatientDashboardComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"span\", 6);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 7);\n    i0.ɵɵtext(5, \"Loading your dashboard...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PatientDashboardComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"i\", 9);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction PatientDashboardComponent_div_3_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 23)(2, \"div\", 35);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementStart(4, \"h6\", 36);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h4\", 37);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementStart(8, \"small\", 38);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"titlecase\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const metric_r7 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMapInterpolate2(\"bi bi-\", metric_r7.icon, \" display-6 \", metric_r7.color, \" mb-2\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(metric_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", metric_r7.value, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(metric_r7.unit);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r3.getStatusBadgeClass(metric_r7.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 10, metric_r7.status));\n  }\n}\nfunction PatientDashboardComponent_div_3_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 39);\n    i0.ɵɵlistener(\"click\", function PatientDashboardComponent_div_3_div_23_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const action_r8 = restoredCtx.$implicit;\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.navigateTo(action_r8.route));\n    });\n    i0.ɵɵelementStart(2, \"div\", 35)(3, \"div\", 40);\n    i0.ɵɵelement(4, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h6\", 36);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 41);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const action_r8 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(action_r8.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMapInterpolate1(\"bi bi-\", action_r8.icon, \" text-white fs-4\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r8.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r8.description);\n  }\n}\nfunction PatientDashboardComponent_div_3_div_32_hr_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"hr\", 49);\n  }\n}\nfunction PatientDashboardComponent_div_3_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 43)(2, \"div\", 44);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 45)(5, \"h6\", 46);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 47);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"small\", 38);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, PatientDashboardComponent_div_3_div_32_hr_11_Template, 1, 0, \"hr\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const activity_r11 = ctx.$implicit;\n    const isLast_r12 = ctx.last;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMapInterpolate2(\"bi bi-\", activity_r11.icon, \" \", activity_r11.color, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r11.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r11.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r11.time);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !isLast_r12);\n  }\n}\nfunction PatientDashboardComponent_div_3_div_40_hr_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"hr\", 49);\n  }\n}\nfunction PatientDashboardComponent_div_3_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 43)(2, \"div\", 51);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 45)(5, \"h6\", 46);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 52);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, PatientDashboardComponent_div_3_div_40_hr_9_Template, 1, 0, \"hr\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tip_r14 = ctx.$implicit;\n    const isLast_r15 = ctx.last;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMapInterpolate1(\"bi bi-\", tip_r14.icon, \" text-primary\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tip_r14.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tip_r14.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !isLast_r15);\n  }\n}\nfunction PatientDashboardComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 10)(2, \"div\", 11)(3, \"div\", 12)(4, \"div\")(5, \"h1\", 13);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 14);\n    i0.ɵɵtext(8, \"Here's your health overview for today\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function PatientDashboardComponent_div_3_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.refreshData());\n    });\n    i0.ɵɵelement(10, \"i\", 16);\n    i0.ɵɵtext(11, \"Refresh \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(12, \"div\", 10)(13, \"div\", 11)(14, \"h5\", 17);\n    i0.ɵɵelement(15, \"i\", 18);\n    i0.ɵɵtext(16, \"Health Metrics \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(17, PatientDashboardComponent_div_3_div_17_Template, 13, 12, \"div\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 10)(19, \"div\", 11)(20, \"h5\", 17);\n    i0.ɵɵelement(21, \"i\", 20);\n    i0.ɵɵtext(22, \"Quick Actions \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(23, PatientDashboardComponent_div_3_div_23_Template, 9, 7, \"div\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 21)(25, \"div\", 22)(26, \"div\", 23)(27, \"div\", 24)(28, \"h6\", 25);\n    i0.ɵɵelement(29, \"i\", 26);\n    i0.ɵɵtext(30, \"Recent Activities \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 27);\n    i0.ɵɵtemplate(32, PatientDashboardComponent_div_3_div_32_Template, 12, 8, \"div\", 28);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 22)(34, \"div\", 23)(35, \"div\", 24)(36, \"h6\", 25);\n    i0.ɵɵelement(37, \"i\", 29);\n    i0.ɵɵtext(38, \"Health Tips \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 27);\n    i0.ɵɵtemplate(40, PatientDashboardComponent_div_3_div_40_Template, 10, 6, \"div\", 30);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(41, \"div\", 21)(42, \"div\", 11)(43, \"div\", 31);\n    i0.ɵɵelement(44, \"i\", 32);\n    i0.ɵɵelementStart(45, \"div\")(46, \"h6\", 33);\n    i0.ɵɵtext(47, \"Emergency Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"p\", 25);\n    i0.ɵɵtext(49, \"For medical emergencies, call \");\n    i0.ɵɵelementStart(50, \"strong\");\n    i0.ɵɵtext(51, \"911\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(52, \" or visit your nearest emergency room.\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.getGreeting(), \", \", ctx_r2.currentUser == null ? null : ctx_r2.currentUser.fullName, \"!\");\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.healthMetrics);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.quickActions);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.recentActivities);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.healthTips);\n  }\n}\nexport class PatientDashboardComponent {\n  constructor(authService, userService, router) {\n    this.authService = authService;\n    this.userService = userService;\n    this.router = router;\n    this.currentUser = null;\n    this.isLoading = true;\n    this.error = '';\n    this.healthMetrics = [{\n      name: 'Heart Rate',\n      value: '72',\n      unit: 'bpm',\n      status: 'normal',\n      icon: 'heart-pulse',\n      color: 'text-success'\n    }, {\n      name: 'Blood Pressure',\n      value: '120/80',\n      unit: 'mmHg',\n      status: 'normal',\n      icon: 'activity',\n      color: 'text-success'\n    }, {\n      name: 'Weight',\n      value: '70',\n      unit: 'kg',\n      status: 'normal',\n      icon: 'speedometer2',\n      color: 'text-info'\n    }, {\n      name: 'Temperature',\n      value: '98.6',\n      unit: '°F',\n      status: 'normal',\n      icon: 'thermometer-half',\n      color: 'text-success'\n    }];\n    this.quickActions = [{\n      title: 'Book Appointment',\n      description: 'Schedule a consultation with a doctor',\n      icon: 'calendar-plus',\n      color: 'bg-primary',\n      route: '/appointments/book'\n    }, {\n      title: 'Find Doctors',\n      description: 'Browse available healthcare providers',\n      icon: 'search',\n      color: 'bg-info',\n      route: '/appointments/doctors'\n    }, {\n      title: 'Health Assistant',\n      description: 'Get AI-powered health guidance',\n      icon: 'robot',\n      color: 'bg-success',\n      route: '/health-bot'\n    }, {\n      title: 'Messages',\n      description: 'Chat with your healthcare providers',\n      icon: 'chat-dots',\n      color: 'bg-warning',\n      route: '/chat'\n    }];\n    this.recentActivities = [{\n      title: 'Appointment Scheduled',\n      description: 'Consultation with Dr. Smith on Dec 15, 2024',\n      time: '2 hours ago',\n      icon: 'calendar-check',\n      color: 'text-primary'\n    }, {\n      title: 'Health Metrics Updated',\n      description: 'Blood pressure and weight recorded',\n      time: '1 day ago',\n      icon: 'graph-up',\n      color: 'text-success'\n    }, {\n      title: 'Message Received',\n      description: 'New message from Dr. Johnson',\n      time: '2 days ago',\n      icon: 'envelope',\n      color: 'text-info'\n    }];\n    this.healthTips = [{\n      title: 'Stay Hydrated',\n      description: 'Drink at least 8 glasses of water daily for optimal health.',\n      icon: 'droplet'\n    }, {\n      title: 'Regular Exercise',\n      description: 'Aim for 30 minutes of moderate exercise 5 days a week.',\n      icon: 'bicycle'\n    }, {\n      title: 'Healthy Sleep',\n      description: 'Get 7-9 hours of quality sleep each night.',\n      icon: 'moon'\n    }];\n  }\n  ngOnInit() {\n    this.loadUserData();\n  }\n  loadUserData() {\n    this.authService.currentUser$.subscribe({\n      next: user => {\n        this.currentUser = user;\n        this.isLoading = false;\n      },\n      error: error => {\n        this.error = 'Failed to load user data';\n        this.isLoading = false;\n      }\n    });\n  }\n  navigateTo(route) {\n    this.router.navigate([route]);\n  }\n  getGreeting() {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Good morning';\n    if (hour < 18) return 'Good afternoon';\n    return 'Good evening';\n  }\n  getStatusBadgeClass(status) {\n    switch (status) {\n      case 'normal':\n        return 'badge bg-success';\n      case 'warning':\n        return 'badge bg-warning';\n      case 'danger':\n        return 'badge bg-danger';\n      default:\n        return 'badge bg-secondary';\n    }\n  }\n  refreshData() {\n    this.isLoading = true;\n    // Simulate data refresh\n    setTimeout(() => {\n      this.isLoading = false;\n    }, 1000);\n  }\n  static {\n    this.ɵfac = function PatientDashboardComponent_Factory(t) {\n      return new (t || PatientDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PatientDashboardComponent,\n      selectors: [[\"app-patient-dashboard\"]],\n      decls: 4,\n      vars: 3,\n      consts: [[1, \"container-fluid\", \"py-4\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-3\", \"text-muted\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"bi\", \"bi-exclamation-triangle\", \"me-2\"], [1, \"row\", \"mb-4\"], [1, \"col-12\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"h3\", \"mb-1\"], [1, \"text-muted\", \"mb-0\"], [1, \"btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\", \"me-2\"], [1, \"mb-3\"], [1, \"bi\", \"bi-heart-pulse\", \"me-2\", \"text-primary\"], [\"class\", \"col-md-3 col-sm-6 mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"bi\", \"bi-lightning\", \"me-2\", \"text-primary\"], [1, \"row\"], [1, \"col-md-6\", \"mb-4\"], [1, \"card\", \"h-100\"], [1, \"card-header\"], [1, \"mb-0\"], [1, \"bi\", \"bi-clock-history\", \"me-2\"], [1, \"card-body\"], [\"class\", \"activity-item d-flex align-items-start mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"bi\", \"bi-lightbulb\", \"me-2\"], [\"class\", \"tip-item d-flex align-items-start mb-3\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"alert\", 1, \"alert\", \"alert-info\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-info-circle\", \"me-3\", \"fs-4\"], [1, \"alert-heading\", \"mb-1\"], [1, \"col-md-3\", \"col-sm-6\", \"mb-3\"], [1, \"card-body\", \"text-center\"], [1, \"card-title\"], [1, \"mb-2\"], [1, \"text-muted\"], [1, \"card\", \"h-100\", \"action-card\", 3, \"click\"], [1, \"rounded-circle\", \"d-inline-flex\", \"align-items-center\", \"justify-content-center\", \"mb-3\", 2, \"width\", \"60px\", \"height\", \"60px\"], [1, \"card-text\", \"text-muted\", \"small\"], [1, \"activity-item\", \"d-flex\", \"align-items-start\", \"mb-3\"], [1, \"flex-shrink-0\", \"me-3\"], [1, \"rounded-circle\", \"bg-light\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"40px\", \"height\", \"40px\"], [1, \"flex-grow-1\"], [1, \"mb-1\"], [1, \"mb-1\", \"text-muted\", \"small\"], [\"class\", \"my-3\", 4, \"ngIf\"], [1, \"my-3\"], [1, \"tip-item\", \"d-flex\", \"align-items-start\", \"mb-3\"], [1, \"rounded-circle\", \"bg-primary\", \"bg-opacity-10\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"40px\", \"height\", \"40px\"], [1, \"mb-0\", \"text-muted\", \"small\"]],\n      template: function PatientDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, PatientDashboardComponent_div_1_Template, 6, 0, \"div\", 1);\n          i0.ɵɵtemplate(2, PatientDashboardComponent_div_2_Template, 3, 1, \"div\", 2);\n          i0.ɵɵtemplate(3, PatientDashboardComponent_div_3_Template, 53, 6, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i4.TitleCasePipe],\n      styles: [\".action-card[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  border: none;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.action-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  border-bottom: 1px solid #e9ecef;\\n  font-weight: 600;\\n}\\n\\n.activity-item[_ngcontent-%COMP%], .tip-item[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #f8f9fa;\\n  padding-bottom: 1rem;\\n}\\n\\n.activity-item[_ngcontent-%COMP%]:last-child, .tip-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n  padding-bottom: 0;\\n}\\n\\n.bg-primary[_ngcontent-%COMP%] {\\n  background-color: #0d6efd !important;\\n}\\n\\n.bg-info[_ngcontent-%COMP%] {\\n  background-color: #0dcaf0 !important;\\n}\\n\\n.bg-success[_ngcontent-%COMP%] {\\n  background-color: #198754 !important;\\n}\\n\\n.bg-warning[_ngcontent-%COMP%] {\\n  background-color: #ffc107 !important;\\n}\\n\\n.text-primary[_ngcontent-%COMP%] {\\n  color: #0d6efd !important;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n\\n.spinner-border[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .container-fluid[_ngcontent-%COMP%] {\\n    padding-left: 1rem;\\n    padding-right: 1rem;\\n  }\\n  .card-body[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .display-6[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ɵɵclassMapInterpolate2", "metric_r7", "icon", "color", "ɵɵtextInterpolate", "name", "value", "unit", "ɵɵclassMap", "ctx_r3", "getStatusBadgeClass", "status", "ɵɵpipeBind1", "ɵɵlistener", "PatientDashboardComponent_div_3_div_23_Template_div_click_1_listener", "restoredCtx", "ɵɵrestoreView", "_r10", "action_r8", "$implicit", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "navigateTo", "route", "ɵɵclassMapInterpolate1", "title", "description", "ɵɵtemplate", "PatientDashboardComponent_div_3_div_32_hr_11_Template", "activity_r11", "time", "ɵɵproperty", "isLast_r12", "PatientDashboardComponent_div_3_div_40_hr_9_Template", "tip_r14", "isLast_r15", "PatientDashboardComponent_div_3_Template_button_click_9_listener", "_r18", "ctx_r17", "refreshData", "PatientDashboardComponent_div_3_div_17_Template", "PatientDashboardComponent_div_3_div_23_Template", "PatientDashboardComponent_div_3_div_32_Template", "PatientDashboardComponent_div_3_div_40_Template", "ɵɵtextInterpolate2", "ctx_r2", "getGreeting", "currentUser", "fullName", "healthMetrics", "quickActions", "recentActivities", "healthTips", "PatientDashboardComponent", "constructor", "authService", "userService", "router", "isLoading", "ngOnInit", "loadUserData", "currentUser$", "subscribe", "next", "user", "navigate", "hour", "Date", "getHours", "setTimeout", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "UserService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "PatientDashboardComponent_Template", "rf", "ctx", "PatientDashboardComponent_div_1_Template", "PatientDashboardComponent_div_2_Template", "PatientDashboardComponent_div_3_Template"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/patient/dashboard/dashboard.component.ts", "/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/patient/dashboard/dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../core/services/auth.service';\nimport { UserService } from '../../core/services/user.service';\nimport { User } from '../../core/models/user.model';\n\ninterface HealthMetric {\n  name: string;\n  value: string;\n  unit: string;\n  status: 'normal' | 'warning' | 'danger';\n  icon: string;\n  color: string;\n}\n\ninterface QuickAction {\n  title: string;\n  description: string;\n  icon: string;\n  color: string;\n  route: string;\n}\n\n@Component({\n  selector: 'app-patient-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class PatientDashboardComponent implements OnInit {\n  currentUser: User | null = null;\n  isLoading = true;\n  error = '';\n\n  healthMetrics: HealthMetric[] = [\n    {\n      name: 'Heart Rate',\n      value: '72',\n      unit: 'bpm',\n      status: 'normal',\n      icon: 'heart-pulse',\n      color: 'text-success'\n    },\n    {\n      name: 'Blood Pressure',\n      value: '120/80',\n      unit: 'mmHg',\n      status: 'normal',\n      icon: 'activity',\n      color: 'text-success'\n    },\n    {\n      name: 'Weight',\n      value: '70',\n      unit: 'kg',\n      status: 'normal',\n      icon: 'speedometer2',\n      color: 'text-info'\n    },\n    {\n      name: 'Temperature',\n      value: '98.6',\n      unit: '°F',\n      status: 'normal',\n      icon: 'thermometer-half',\n      color: 'text-success'\n    }\n  ];\n\n  quickActions: QuickAction[] = [\n    {\n      title: 'Book Appointment',\n      description: 'Schedule a consultation with a doctor',\n      icon: 'calendar-plus',\n      color: 'bg-primary',\n      route: '/appointments/book'\n    },\n    {\n      title: 'Find Doctors',\n      description: 'Browse available healthcare providers',\n      icon: 'search',\n      color: 'bg-info',\n      route: '/appointments/doctors'\n    },\n    {\n      title: 'Health Assistant',\n      description: 'Get AI-powered health guidance',\n      icon: 'robot',\n      color: 'bg-success',\n      route: '/health-bot'\n    },\n    {\n      title: 'Messages',\n      description: 'Chat with your healthcare providers',\n      icon: 'chat-dots',\n      color: 'bg-warning',\n      route: '/chat'\n    }\n  ];\n\n  recentActivities = [\n    {\n      title: 'Appointment Scheduled',\n      description: 'Consultation with Dr. Smith on Dec 15, 2024',\n      time: '2 hours ago',\n      icon: 'calendar-check',\n      color: 'text-primary'\n    },\n    {\n      title: 'Health Metrics Updated',\n      description: 'Blood pressure and weight recorded',\n      time: '1 day ago',\n      icon: 'graph-up',\n      color: 'text-success'\n    },\n    {\n      title: 'Message Received',\n      description: 'New message from Dr. Johnson',\n      time: '2 days ago',\n      icon: 'envelope',\n      color: 'text-info'\n    }\n  ];\n\n  healthTips = [\n    {\n      title: 'Stay Hydrated',\n      description: 'Drink at least 8 glasses of water daily for optimal health.',\n      icon: 'droplet'\n    },\n    {\n      title: 'Regular Exercise',\n      description: 'Aim for 30 minutes of moderate exercise 5 days a week.',\n      icon: 'bicycle'\n    },\n    {\n      title: 'Healthy Sleep',\n      description: 'Get 7-9 hours of quality sleep each night.',\n      icon: 'moon'\n    }\n  ];\n\n  constructor(\n    private authService: AuthService,\n    private userService: UserService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.loadUserData();\n  }\n\n  private loadUserData(): void {\n    this.authService.currentUser$.subscribe({\n      next: (user) => {\n        this.currentUser = user;\n        this.isLoading = false;\n      },\n      error: (error) => {\n        this.error = 'Failed to load user data';\n        this.isLoading = false;\n      }\n    });\n  }\n\n  navigateTo(route: string): void {\n    this.router.navigate([route]);\n  }\n\n  getGreeting(): string {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Good morning';\n    if (hour < 18) return 'Good afternoon';\n    return 'Good evening';\n  }\n\n  getStatusBadgeClass(status: string): string {\n    switch (status) {\n      case 'normal': return 'badge bg-success';\n      case 'warning': return 'badge bg-warning';\n      case 'danger': return 'badge bg-danger';\n      default: return 'badge bg-secondary';\n    }\n  }\n\n  refreshData(): void {\n    this.isLoading = true;\n    // Simulate data refresh\n    setTimeout(() => {\n      this.isLoading = false;\n    }, 1000);\n  }\n}\n", "<div class=\"container-fluid py-4\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"text-center py-5\">\n    <div class=\"spinner-border text-primary\" role=\"status\">\n      <span class=\"visually-hidden\">Loading...</span>\n    </div>\n    <p class=\"mt-3 text-muted\">Loading your dashboard...</p>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"alert alert-danger\" role=\"alert\">\n    <i class=\"bi bi-exclamation-triangle me-2\"></i>\n    {{ error }}\n  </div>\n\n  <!-- Dashboard Content -->\n  <div *ngIf=\"!isLoading && !error\">\n    <!-- Welcome Header -->\n    <div class=\"row mb-4\">\n      <div class=\"col-12\">\n        <div class=\"d-flex justify-content-between align-items-center\">\n          <div>\n            <h1 class=\"h3 mb-1\">{{ getGreeting() }}, {{ currentUser?.fullName }}!</h1>\n            <p class=\"text-muted mb-0\">Here's your health overview for today</p>\n          </div>\n          <button class=\"btn btn-outline-primary\" (click)=\"refreshData()\">\n            <i class=\"bi bi-arrow-clockwise me-2\"></i>Refresh\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Health Metrics -->\n    <div class=\"row mb-4\">\n      <div class=\"col-12\">\n        <h5 class=\"mb-3\">\n          <i class=\"bi bi-heart-pulse me-2 text-primary\"></i>Health Metrics\n        </h5>\n      </div>\n      <div class=\"col-md-3 col-sm-6 mb-3\" *ngFor=\"let metric of healthMetrics\">\n        <div class=\"card h-100\">\n          <div class=\"card-body text-center\">\n            <i class=\"bi bi-{{ metric.icon }} display-6 {{ metric.color }} mb-2\"></i>\n            <h6 class=\"card-title\">{{ metric.name }}</h6>\n            <h4 class=\"mb-2\">{{ metric.value }} <small class=\"text-muted\">{{ metric.unit }}</small></h4>\n            <span [class]=\"getStatusBadgeClass(metric.status)\">{{ metric.status | titlecase }}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Quick Actions -->\n    <div class=\"row mb-4\">\n      <div class=\"col-12\">\n        <h5 class=\"mb-3\">\n          <i class=\"bi bi-lightning me-2 text-primary\"></i>Quick Actions\n        </h5>\n      </div>\n      <div class=\"col-md-3 col-sm-6 mb-3\" *ngFor=\"let action of quickActions\">\n        <div class=\"card h-100 action-card\" (click)=\"navigateTo(action.route)\">\n          <div class=\"card-body text-center\">\n            <div class=\"rounded-circle d-inline-flex align-items-center justify-content-center mb-3\" \n                 [class]=\"action.color\" style=\"width: 60px; height: 60px;\">\n              <i class=\"bi bi-{{ action.icon }} text-white fs-4\"></i>\n            </div>\n            <h6 class=\"card-title\">{{ action.title }}</h6>\n            <p class=\"card-text text-muted small\">{{ action.description }}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Recent Activities & Health Tips -->\n    <div class=\"row\">\n      <!-- Recent Activities -->\n      <div class=\"col-md-6 mb-4\">\n        <div class=\"card h-100\">\n          <div class=\"card-header\">\n            <h6 class=\"mb-0\">\n              <i class=\"bi bi-clock-history me-2\"></i>Recent Activities\n            </h6>\n          </div>\n          <div class=\"card-body\">\n            <div class=\"activity-item d-flex align-items-start mb-3\" *ngFor=\"let activity of recentActivities; last as isLast\">\n              <div class=\"flex-shrink-0 me-3\">\n                <div class=\"rounded-circle bg-light d-flex align-items-center justify-content-center\" \n                     style=\"width: 40px; height: 40px;\">\n                  <i class=\"bi bi-{{ activity.icon }} {{ activity.color }}\"></i>\n                </div>\n              </div>\n              <div class=\"flex-grow-1\">\n                <h6 class=\"mb-1\">{{ activity.title }}</h6>\n                <p class=\"mb-1 text-muted small\">{{ activity.description }}</p>\n                <small class=\"text-muted\">{{ activity.time }}</small>\n              </div>\n              <hr *ngIf=\"!isLast\" class=\"my-3\">\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Health Tips -->\n      <div class=\"col-md-6 mb-4\">\n        <div class=\"card h-100\">\n          <div class=\"card-header\">\n            <h6 class=\"mb-0\">\n              <i class=\"bi bi-lightbulb me-2\"></i>Health Tips\n            </h6>\n          </div>\n          <div class=\"card-body\">\n            <div class=\"tip-item d-flex align-items-start mb-3\" *ngFor=\"let tip of healthTips; last as isLast\">\n              <div class=\"flex-shrink-0 me-3\">\n                <div class=\"rounded-circle bg-primary bg-opacity-10 d-flex align-items-center justify-content-center\" \n                     style=\"width: 40px; height: 40px;\">\n                  <i class=\"bi bi-{{ tip.icon }} text-primary\"></i>\n                </div>\n              </div>\n              <div class=\"flex-grow-1\">\n                <h6 class=\"mb-1\">{{ tip.title }}</h6>\n                <p class=\"mb-0 text-muted small\">{{ tip.description }}</p>\n              </div>\n              <hr *ngIf=\"!isLast\" class=\"my-3\">\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Emergency Contact -->\n    <div class=\"row\">\n      <div class=\"col-12\">\n        <div class=\"alert alert-info d-flex align-items-center\" role=\"alert\">\n          <i class=\"bi bi-info-circle me-3 fs-4\"></i>\n          <div>\n            <h6 class=\"alert-heading mb-1\">Emergency Contact</h6>\n            <p class=\"mb-0\">For medical emergencies, call <strong>911</strong> or visit your nearest emergency room.</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;ICEEA,EAAA,CAAAC,cAAA,aAAgD;IAEdD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEjDH,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAI1DH,EAAA,CAAAC,cAAA,aAAyE;IACvED,EAAA,CAAAI,SAAA,WAA+C;IAC/CJ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IA0BIR,EAAA,CAAAC,cAAA,cAAyE;IAGnED,EAAA,CAAAI,SAAA,QAAyE;IACzEJ,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7CH,EAAA,CAAAC,cAAA,aAAiB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvFH,EAAA,CAAAC,cAAA,YAAmD;IAAAD,EAAA,CAAAE,MAAA,IAA+B;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAHtFH,EAAA,CAAAK,SAAA,GAAiE;IAAjEL,EAAA,CAAAS,sBAAA,WAAAC,SAAA,CAAAC,IAAA,iBAAAD,SAAA,CAAAE,KAAA,UAAiE;IAC7CZ,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAa,iBAAA,CAAAH,SAAA,CAAAI,IAAA,CAAiB;IACvBd,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,kBAAA,KAAAI,SAAA,CAAAK,KAAA,MAAmB;IAA0Bf,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAa,iBAAA,CAAAH,SAAA,CAAAM,IAAA,CAAiB;IACzEhB,EAAA,CAAAK,SAAA,GAA4C;IAA5CL,EAAA,CAAAiB,UAAA,CAAAC,MAAA,CAAAC,mBAAA,CAAAT,SAAA,CAAAU,MAAA,EAA4C;IAACpB,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAAqB,WAAA,SAAAX,SAAA,CAAAU,MAAA,EAA+B;;;;;;IAaxFpB,EAAA,CAAAC,cAAA,cAAwE;IAClCD,EAAA,CAAAsB,UAAA,mBAAAC,qEAAA;MAAA,MAAAC,WAAA,GAAAxB,EAAA,CAAAyB,aAAA,CAAAC,IAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAA7B,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAF,MAAA,CAAAG,UAAA,CAAAL,SAAA,CAAAM,KAAA,CAAwB;IAAA,EAAC;IACpEjC,EAAA,CAAAC,cAAA,cAAmC;IAG/BD,EAAA,CAAAI,SAAA,QAAuD;IACzDJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,YAAsC;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAJ7DH,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAiB,UAAA,CAAAU,SAAA,CAAAf,KAAA,CAAsB;IACtBZ,EAAA,CAAAK,SAAA,GAA+C;IAA/CL,EAAA,CAAAkC,sBAAA,WAAAP,SAAA,CAAAhB,IAAA,qBAA+C;IAE7BX,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAa,iBAAA,CAAAc,SAAA,CAAAQ,KAAA,CAAkB;IACHnC,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAa,iBAAA,CAAAc,SAAA,CAAAS,WAAA,CAAwB;;;;;IA6B5DpC,EAAA,CAAAI,SAAA,aAAiC;;;;;IAZnCJ,EAAA,CAAAC,cAAA,cAAmH;IAI7GD,EAAA,CAAAI,SAAA,QAA8D;IAChEJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAAyB;IACND,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/DH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEvDH,EAAA,CAAAqC,UAAA,KAAAC,qDAAA,iBAAiC;IACnCtC,EAAA,CAAAG,YAAA,EAAM;;;;;IATGH,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAAS,sBAAA,WAAA8B,YAAA,CAAA5B,IAAA,OAAA4B,YAAA,CAAA3B,KAAA,KAAsD;IAI1CZ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAa,iBAAA,CAAA0B,YAAA,CAAAJ,KAAA,CAAoB;IACJnC,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAa,iBAAA,CAAA0B,YAAA,CAAAH,WAAA,CAA0B;IACjCpC,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAa,iBAAA,CAAA0B,YAAA,CAAAC,IAAA,CAAmB;IAE1CxC,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAyC,UAAA,UAAAC,UAAA,CAAa;;;;;IA0BlB1C,EAAA,CAAAI,SAAA,aAAiC;;;;;IAXnCJ,EAAA,CAAAC,cAAA,cAAmG;IAI7FD,EAAA,CAAAI,SAAA,QAAiD;IACnDJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAAyB;IACND,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE5DH,EAAA,CAAAqC,UAAA,IAAAM,oDAAA,iBAAiC;IACnC3C,EAAA,CAAAG,YAAA,EAAM;;;;;IARGH,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAkC,sBAAA,WAAAU,OAAA,CAAAjC,IAAA,kBAAyC;IAI7BX,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAa,iBAAA,CAAA+B,OAAA,CAAAT,KAAA,CAAe;IACCnC,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAa,iBAAA,CAAA+B,OAAA,CAAAR,WAAA,CAAqB;IAEnDpC,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAyC,UAAA,UAAAI,UAAA,CAAa;;;;;;IAzG9B7C,EAAA,CAAAC,cAAA,UAAkC;IAMJD,EAAA,CAAAE,MAAA,GAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1EH,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAE,MAAA,4CAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEtEH,EAAA,CAAAC,cAAA,iBAAgE;IAAxBD,EAAA,CAAAsB,UAAA,mBAAAwB,iEAAA;MAAA9C,EAAA,CAAAyB,aAAA,CAAAsB,IAAA;MAAA,MAAAC,OAAA,GAAAhD,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAiB,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAC7DjD,EAAA,CAAAI,SAAA,aAA0C;IAAAJ,EAAA,CAAAE,MAAA,gBAC5C;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAMfH,EAAA,CAAAC,cAAA,eAAsB;IAGhBD,EAAA,CAAAI,SAAA,aAAmD;IAAAJ,EAAA,CAAAE,MAAA,uBACrD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAqC,UAAA,KAAAa,+CAAA,oBASM;IACRlD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAsB;IAGhBD,EAAA,CAAAI,SAAA,aAAiD;IAAAJ,EAAA,CAAAE,MAAA,sBACnD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAqC,UAAA,KAAAc,+CAAA,kBAWM;IACRnD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAiB;IAMPD,EAAA,CAAAI,SAAA,aAAwC;IAAAJ,EAAA,CAAAE,MAAA,0BAC1C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAqC,UAAA,KAAAe,+CAAA,mBAaM;IACRpD,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,eAA2B;IAInBD,EAAA,CAAAI,SAAA,aAAoC;IAAAJ,EAAA,CAAAE,MAAA,oBACtC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAqC,UAAA,KAAAgB,+CAAA,mBAYM;IACRrD,EAAA,CAAAG,YAAA,EAAM;IAMZH,EAAA,CAAAC,cAAA,eAAiB;IAGXD,EAAA,CAAAI,SAAA,aAA2C;IAC3CJ,EAAA,CAAAC,cAAA,WAAK;IAC4BD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrDH,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAE,MAAA,sCAA8B;IAAAF,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,8CAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAjHxFH,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAsD,kBAAA,KAAAC,MAAA,CAAAC,WAAA,UAAAD,MAAA,CAAAE,WAAA,kBAAAF,MAAA,CAAAE,WAAA,CAAAC,QAAA,MAAiD;IAiBpB1D,EAAA,CAAAK,SAAA,IAAgB;IAAhBL,EAAA,CAAAyC,UAAA,YAAAc,MAAA,CAAAI,aAAA,CAAgB;IAmBhB3D,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAyC,UAAA,YAAAc,MAAA,CAAAK,YAAA,CAAe;IAyBc5D,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAyC,UAAA,YAAAc,MAAA,CAAAM,gBAAA,CAAqB;IA2B/B7D,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAyC,UAAA,YAAAc,MAAA,CAAAO,UAAA,CAAe;;;ADlF/F,OAAM,MAAOC,yBAAyB;EAiHpCC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAnHhB,KAAAV,WAAW,GAAgB,IAAI;IAC/B,KAAAW,SAAS,GAAG,IAAI;IAChB,KAAA5D,KAAK,GAAG,EAAE;IAEV,KAAAmD,aAAa,GAAmB,CAC9B;MACE7C,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,KAAK;MACXI,MAAM,EAAE,QAAQ;MAChBT,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE;KACR,EACD;MACEE,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE,MAAM;MACZI,MAAM,EAAE,QAAQ;MAChBT,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;KACR,EACD;MACEE,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,IAAI;MACVI,MAAM,EAAE,QAAQ;MAChBT,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE;KACR,EACD;MACEE,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,IAAI;MACVI,MAAM,EAAE,QAAQ;MAChBT,IAAI,EAAE,kBAAkB;MACxBC,KAAK,EAAE;KACR,CACF;IAED,KAAAgD,YAAY,GAAkB,CAC5B;MACEzB,KAAK,EAAE,kBAAkB;MACzBC,WAAW,EAAE,uCAAuC;MACpDzB,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,YAAY;MACnBqB,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,cAAc;MACrBC,WAAW,EAAE,uCAAuC;MACpDzB,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,SAAS;MAChBqB,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,kBAAkB;MACzBC,WAAW,EAAE,gCAAgC;MAC7CzB,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,YAAY;MACnBqB,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE,qCAAqC;MAClDzB,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE,YAAY;MACnBqB,KAAK,EAAE;KACR,CACF;IAED,KAAA4B,gBAAgB,GAAG,CACjB;MACE1B,KAAK,EAAE,uBAAuB;MAC9BC,WAAW,EAAE,6CAA6C;MAC1DI,IAAI,EAAE,aAAa;MACnB7B,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;KACR,EACD;MACEuB,KAAK,EAAE,wBAAwB;MAC/BC,WAAW,EAAE,oCAAoC;MACjDI,IAAI,EAAE,WAAW;MACjB7B,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;KACR,EACD;MACEuB,KAAK,EAAE,kBAAkB;MACzBC,WAAW,EAAE,8BAA8B;MAC3CI,IAAI,EAAE,YAAY;MAClB7B,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;KACR,CACF;IAED,KAAAkD,UAAU,GAAG,CACX;MACE3B,KAAK,EAAE,eAAe;MACtBC,WAAW,EAAE,6DAA6D;MAC1EzB,IAAI,EAAE;KACP,EACD;MACEwB,KAAK,EAAE,kBAAkB;MACzBC,WAAW,EAAE,wDAAwD;MACrEzB,IAAI,EAAE;KACP,EACD;MACEwB,KAAK,EAAE,eAAe;MACtBC,WAAW,EAAE,4CAA4C;MACzDzB,IAAI,EAAE;KACP,CACF;EAME;EAEH0D,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEQA,YAAYA,CAAA;IAClB,IAAI,CAACL,WAAW,CAACM,YAAY,CAACC,SAAS,CAAC;MACtCC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACjB,WAAW,GAAGiB,IAAI;QACvB,IAAI,CAACN,SAAS,GAAG,KAAK;MACxB,CAAC;MACD5D,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,0BAA0B;QACvC,IAAI,CAAC4D,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEApC,UAAUA,CAACC,KAAa;IACtB,IAAI,CAACkC,MAAM,CAACQ,QAAQ,CAAC,CAAC1C,KAAK,CAAC,CAAC;EAC/B;EAEAuB,WAAWA,CAAA;IACT,MAAMoB,IAAI,GAAG,IAAIC,IAAI,EAAE,CAACC,QAAQ,EAAE;IAClC,IAAIF,IAAI,GAAG,EAAE,EAAE,OAAO,cAAc;IACpC,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,cAAc;EACvB;EAEAzD,mBAAmBA,CAACC,MAAc;IAChC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,kBAAkB;MACxC,KAAK,SAAS;QAAE,OAAO,kBAAkB;MACzC,KAAK,QAAQ;QAAE,OAAO,iBAAiB;MACvC;QAAS,OAAO,oBAAoB;;EAExC;EAEA6B,WAAWA,CAAA;IACT,IAAI,CAACmB,SAAS,GAAG,IAAI;IACrB;IACAW,UAAU,CAAC,MAAK;MACd,IAAI,CAACX,SAAS,GAAG,KAAK;IACxB,CAAC,EAAE,IAAI,CAAC;EACV;;;uBAlKWL,yBAAyB,EAAA/D,EAAA,CAAAgF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlF,EAAA,CAAAgF,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAApF,EAAA,CAAAgF,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAzBvB,yBAAyB;MAAAwB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5BtC7F,EAAA,CAAAC,cAAA,aAAkC;UAEhCD,EAAA,CAAAqC,UAAA,IAAA0D,wCAAA,iBAKM;UAGN/F,EAAA,CAAAqC,UAAA,IAAA2D,wCAAA,iBAGM;UAGNhG,EAAA,CAAAqC,UAAA,IAAA4D,wCAAA,kBA4HM;UACRjG,EAAA,CAAAG,YAAA,EAAM;;;UA3IEH,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAAyC,UAAA,SAAAqD,GAAA,CAAA1B,SAAA,CAAe;UAQfpE,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAyC,UAAA,SAAAqD,GAAA,CAAAtF,KAAA,KAAAsF,GAAA,CAAA1B,SAAA,CAAyB;UAMzBpE,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAAyC,UAAA,UAAAqD,GAAA,CAAA1B,SAAA,KAAA0B,GAAA,CAAAtF,KAAA,CAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}