{"ast": null, "code": "'use strict';\n\nvar EventEmitter = require('events').EventEmitter,\n  inherits = require('inherits'),\n  urlUtils = require('./utils/url'),\n  XDR = require('./transport/sender/xdr'),\n  XHRCors = require('./transport/sender/xhr-cors'),\n  XHRLocal = require('./transport/sender/xhr-local'),\n  XHRFake = require('./transport/sender/xhr-fake'),\n  InfoIframe = require('./info-iframe'),\n  InfoAjax = require('./info-ajax');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:info-receiver');\n}\nfunction InfoReceiver(baseUrl, urlInfo) {\n  debug(baseUrl);\n  var self = this;\n  EventEmitter.call(this);\n  setTimeout(function () {\n    self.doXhr(baseUrl, urlInfo);\n  }, 0);\n}\ninherits(InfoReceiver, EventEmitter);\n\n// TODO this is currently ignoring the list of available transports and the whitelist\n\nInfoReceiver._getReceiver = function (baseUrl, url, urlInfo) {\n  // determine method of CORS support (if needed)\n  if (urlInfo.sameOrigin) {\n    return new InfoAjax(url, XHRLocal);\n  }\n  if (XHRCors.enabled) {\n    return new InfoAjax(url, XHRCors);\n  }\n  if (XDR.enabled && urlInfo.sameScheme) {\n    return new InfoAjax(url, XDR);\n  }\n  if (InfoIframe.enabled()) {\n    return new InfoIframe(baseUrl, url);\n  }\n  return new InfoAjax(url, XHRFake);\n};\nInfoReceiver.prototype.doXhr = function (baseUrl, urlInfo) {\n  var self = this,\n    url = urlUtils.addPath(baseUrl, '/info');\n  debug('doXhr', url);\n  this.xo = InfoReceiver._getReceiver(baseUrl, url, urlInfo);\n  this.timeoutRef = setTimeout(function () {\n    debug('timeout');\n    self._cleanup(false);\n    self.emit('finish');\n  }, InfoReceiver.timeout);\n  this.xo.once('finish', function (info, rtt) {\n    debug('finish', info, rtt);\n    self._cleanup(true);\n    self.emit('finish', info, rtt);\n  });\n};\nInfoReceiver.prototype._cleanup = function (wasClean) {\n  debug('_cleanup');\n  clearTimeout(this.timeoutRef);\n  this.timeoutRef = null;\n  if (!wasClean && this.xo) {\n    this.xo.close();\n  }\n  this.xo = null;\n};\nInfoReceiver.prototype.close = function () {\n  debug('close');\n  this.removeAllListeners();\n  this._cleanup(false);\n};\nInfoReceiver.timeout = 8000;\nmodule.exports = InfoReceiver;", "map": {"version": 3, "names": ["EventEmitter", "require", "inherits", "urlUtils", "XDR", "XHRCors", "XHRLocal", "XHRFake", "InfoIframe", "InfoAjax", "debug", "process", "env", "NODE_ENV", "InfoReceiver", "baseUrl", "urlInfo", "self", "call", "setTimeout", "doXhr", "_getReceiver", "url", "<PERSON><PERSON><PERSON><PERSON>", "enabled", "sameScheme", "prototype", "addPath", "xo", "timeoutRef", "_cleanup", "emit", "timeout", "once", "info", "rtt", "<PERSON><PERSON><PERSON>", "clearTimeout", "close", "removeAllListeners", "module", "exports"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/sockjs-client/lib/info-receiver.js"], "sourcesContent": ["'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , urlUtils = require('./utils/url')\n  , XDR = require('./transport/sender/xdr')\n  , XHRCors = require('./transport/sender/xhr-cors')\n  , XHRLocal = require('./transport/sender/xhr-local')\n  , XHRFake = require('./transport/sender/xhr-fake')\n  , InfoIframe = require('./info-iframe')\n  , InfoAjax = require('./info-ajax')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:info-receiver');\n}\n\nfunction InfoReceiver(baseUrl, urlInfo) {\n  debug(baseUrl);\n  var self = this;\n  EventEmitter.call(this);\n\n  setTimeout(function() {\n    self.doXhr(baseUrl, urlInfo);\n  }, 0);\n}\n\ninherits(InfoReceiver, EventEmitter);\n\n// TODO this is currently ignoring the list of available transports and the whitelist\n\nInfoReceiver._getReceiver = function(baseUrl, url, urlInfo) {\n  // determine method of CORS support (if needed)\n  if (urlInfo.sameOrigin) {\n    return new InfoAjax(url, XHRLocal);\n  }\n  if (XHRCors.enabled) {\n    return new InfoAjax(url, XHRCors);\n  }\n  if (XDR.enabled && urlInfo.sameScheme) {\n    return new InfoAjax(url, XDR);\n  }\n  if (InfoIframe.enabled()) {\n    return new InfoIframe(baseUrl, url);\n  }\n  return new InfoAjax(url, XHRFake);\n};\n\nInfoReceiver.prototype.doXhr = function(baseUrl, urlInfo) {\n  var self = this\n    , url = urlUtils.addPath(baseUrl, '/info')\n    ;\n  debug('doXhr', url);\n\n  this.xo = InfoReceiver._getReceiver(baseUrl, url, urlInfo);\n\n  this.timeoutRef = setTimeout(function() {\n    debug('timeout');\n    self._cleanup(false);\n    self.emit('finish');\n  }, InfoReceiver.timeout);\n\n  this.xo.once('finish', function(info, rtt) {\n    debug('finish', info, rtt);\n    self._cleanup(true);\n    self.emit('finish', info, rtt);\n  });\n};\n\nInfoReceiver.prototype._cleanup = function(wasClean) {\n  debug('_cleanup');\n  clearTimeout(this.timeoutRef);\n  this.timeoutRef = null;\n  if (!wasClean && this.xo) {\n    this.xo.close();\n  }\n  this.xo = null;\n};\n\nInfoReceiver.prototype.close = function() {\n  debug('close');\n  this.removeAllListeners();\n  this._cleanup(false);\n};\n\nInfoReceiver.timeout = 8000;\n\nmodule.exports = InfoReceiver;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,QAAQ,CAAC,CAACD,YAAY;EAC7CE,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;EAC9BE,QAAQ,GAAGF,OAAO,CAAC,aAAa,CAAC;EACjCG,GAAG,GAAGH,OAAO,CAAC,wBAAwB,CAAC;EACvCI,OAAO,GAAGJ,OAAO,CAAC,6BAA6B,CAAC;EAChDK,QAAQ,GAAGL,OAAO,CAAC,8BAA8B,CAAC;EAClDM,OAAO,GAAGN,OAAO,CAAC,6BAA6B,CAAC;EAChDO,UAAU,GAAGP,OAAO,CAAC,eAAe,CAAC;EACrCQ,QAAQ,GAAGR,OAAO,CAAC,aAAa,CAAC;AAGrC,IAAIS,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGT,OAAO,CAAC,OAAO,CAAC,CAAC,6BAA6B,CAAC;AACzD;AAEA,SAASa,YAAYA,CAACC,OAAO,EAAEC,OAAO,EAAE;EACtCN,KAAK,CAACK,OAAO,CAAC;EACd,IAAIE,IAAI,GAAG,IAAI;EACfjB,YAAY,CAACkB,IAAI,CAAC,IAAI,CAAC;EAEvBC,UAAU,CAAC,YAAW;IACpBF,IAAI,CAACG,KAAK,CAACL,OAAO,EAAEC,OAAO,CAAC;EAC9B,CAAC,EAAE,CAAC,CAAC;AACP;AAEAd,QAAQ,CAACY,YAAY,EAAEd,YAAY,CAAC;;AAEpC;;AAEAc,YAAY,CAACO,YAAY,GAAG,UAASN,OAAO,EAAEO,GAAG,EAAEN,OAAO,EAAE;EAC1D;EACA,IAAIA,OAAO,CAACO,UAAU,EAAE;IACtB,OAAO,IAAId,QAAQ,CAACa,GAAG,EAAEhB,QAAQ,CAAC;EACpC;EACA,IAAID,OAAO,CAACmB,OAAO,EAAE;IACnB,OAAO,IAAIf,QAAQ,CAACa,GAAG,EAAEjB,OAAO,CAAC;EACnC;EACA,IAAID,GAAG,CAACoB,OAAO,IAAIR,OAAO,CAACS,UAAU,EAAE;IACrC,OAAO,IAAIhB,QAAQ,CAACa,GAAG,EAAElB,GAAG,CAAC;EAC/B;EACA,IAAII,UAAU,CAACgB,OAAO,CAAC,CAAC,EAAE;IACxB,OAAO,IAAIhB,UAAU,CAACO,OAAO,EAAEO,GAAG,CAAC;EACrC;EACA,OAAO,IAAIb,QAAQ,CAACa,GAAG,EAAEf,OAAO,CAAC;AACnC,CAAC;AAEDO,YAAY,CAACY,SAAS,CAACN,KAAK,GAAG,UAASL,OAAO,EAAEC,OAAO,EAAE;EACxD,IAAIC,IAAI,GAAG,IAAI;IACXK,GAAG,GAAGnB,QAAQ,CAACwB,OAAO,CAACZ,OAAO,EAAE,OAAO,CAAC;EAE5CL,KAAK,CAAC,OAAO,EAAEY,GAAG,CAAC;EAEnB,IAAI,CAACM,EAAE,GAAGd,YAAY,CAACO,YAAY,CAACN,OAAO,EAAEO,GAAG,EAAEN,OAAO,CAAC;EAE1D,IAAI,CAACa,UAAU,GAAGV,UAAU,CAAC,YAAW;IACtCT,KAAK,CAAC,SAAS,CAAC;IAChBO,IAAI,CAACa,QAAQ,CAAC,KAAK,CAAC;IACpBb,IAAI,CAACc,IAAI,CAAC,QAAQ,CAAC;EACrB,CAAC,EAAEjB,YAAY,CAACkB,OAAO,CAAC;EAExB,IAAI,CAACJ,EAAE,CAACK,IAAI,CAAC,QAAQ,EAAE,UAASC,IAAI,EAAEC,GAAG,EAAE;IACzCzB,KAAK,CAAC,QAAQ,EAAEwB,IAAI,EAAEC,GAAG,CAAC;IAC1BlB,IAAI,CAACa,QAAQ,CAAC,IAAI,CAAC;IACnBb,IAAI,CAACc,IAAI,CAAC,QAAQ,EAAEG,IAAI,EAAEC,GAAG,CAAC;EAChC,CAAC,CAAC;AACJ,CAAC;AAEDrB,YAAY,CAACY,SAAS,CAACI,QAAQ,GAAG,UAASM,QAAQ,EAAE;EACnD1B,KAAK,CAAC,UAAU,CAAC;EACjB2B,YAAY,CAAC,IAAI,CAACR,UAAU,CAAC;EAC7B,IAAI,CAACA,UAAU,GAAG,IAAI;EACtB,IAAI,CAACO,QAAQ,IAAI,IAAI,CAACR,EAAE,EAAE;IACxB,IAAI,CAACA,EAAE,CAACU,KAAK,CAAC,CAAC;EACjB;EACA,IAAI,CAACV,EAAE,GAAG,IAAI;AAChB,CAAC;AAEDd,YAAY,CAACY,SAAS,CAACY,KAAK,GAAG,YAAW;EACxC5B,KAAK,CAAC,OAAO,CAAC;EACd,IAAI,CAAC6B,kBAAkB,CAAC,CAAC;EACzB,IAAI,CAACT,QAAQ,CAAC,KAAK,CAAC;AACtB,CAAC;AAEDhB,YAAY,CAACkB,OAAO,GAAG,IAAI;AAE3BQ,MAAM,CAACC,OAAO,GAAG3B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}