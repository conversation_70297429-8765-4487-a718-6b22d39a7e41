{"ast": null, "code": "'use strict';\n\nvar iframeUtils = require('./utils/iframe');\nfunction FacadeJS(transport) {\n  this._transport = transport;\n  transport.on('message', this._transportMessage.bind(this));\n  transport.on('close', this._transportClose.bind(this));\n}\nFacadeJS.prototype._transportClose = function (code, reason) {\n  iframeUtils.postMessage('c', JSON.stringify([code, reason]));\n};\nFacadeJS.prototype._transportMessage = function (frame) {\n  iframeUtils.postMessage('t', frame);\n};\nFacadeJS.prototype._send = function (data) {\n  this._transport.send(data);\n};\nFacadeJS.prototype._close = function () {\n  this._transport.close();\n  this._transport.removeAllListeners();\n};\nmodule.exports = FacadeJS;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}