{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../core/services/appointment.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction DoctorSearchComponent_option_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const spec_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", spec_r5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", spec_r5, \" \");\n  }\n}\nfunction DoctorSearchComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵelement(1, \"i\", 25);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction DoctorSearchComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"span\", 28);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 29);\n    i0.ɵɵtext(5, \"Searching for doctors...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DoctorSearchComponent_div_27_div_1_p_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 40);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const doctor_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", doctor_r7.affiliation, \" \");\n  }\n}\nfunction DoctorSearchComponent_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"div\", 7)(3, \"div\", 33)(4, \"div\", 34);\n    i0.ɵɵelement(5, \"i\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\")(7, \"h5\", 36);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 37);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 38);\n    i0.ɵɵtemplate(12, DoctorSearchComponent_div_27_div_1_p_12_Template, 3, 1, \"p\", 39);\n    i0.ɵɵelementStart(13, \"p\", 40);\n    i0.ɵɵelement(14, \"i\", 41);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\", 42);\n    i0.ɵɵelement(17, \"i\", 43);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function DoctorSearchComponent_div_27_div_1_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const doctor_r7 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.bookAppointment(doctor_r7));\n    });\n    i0.ɵɵelement(20, \"i\", 45);\n    i0.ɵɵtext(21, \" Book Appointment \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const doctor_r7 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(doctor_r7.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(doctor_r7.specialization || \"General Practice\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", doctor_r7.affiliation);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getExperienceText(doctor_r7.yearsOfExperience), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", doctor_r7.email, \" \");\n  }\n}\nfunction DoctorSearchComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, DoctorSearchComponent_div_27_div_1_Template, 22, 5, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.doctors);\n  }\n}\nfunction DoctorSearchComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵelementStart(2, \"h5\", 49);\n    i0.ɵɵtext(3, \"No doctors found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 49);\n    i0.ɵɵtext(5, \"Try adjusting your search criteria or check back later.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let DoctorSearchComponent = /*#__PURE__*/(() => {\n  class DoctorSearchComponent {\n    constructor(fb, appointmentService, router) {\n      this.fb = fb;\n      this.appointmentService = appointmentService;\n      this.router = router;\n      this.doctors = [];\n      this.specializations = [];\n      this.loading = false;\n      this.error = null;\n      this.initializeForm();\n    }\n    initializeForm() {\n      this.searchForm = this.fb.group({\n        specialization: ['']\n      });\n    }\n    ngOnInit() {\n      this.loadSpecializations();\n      this.loadDoctors();\n    }\n    loadSpecializations() {\n      this.appointmentService.getSpecializations().subscribe({\n        next: specializations => {\n          this.specializations = specializations;\n        },\n        error: error => {\n          console.error('Error loading specializations:', error);\n        }\n      });\n    }\n    loadDoctors() {\n      this.loading = true;\n      this.error = null;\n      const specialization = this.searchForm.get('specialization')?.value;\n      this.appointmentService.getDoctors(specialization || undefined).subscribe({\n        next: doctors => {\n          this.doctors = doctors;\n          this.loading = false;\n        },\n        error: error => {\n          this.error = 'Failed to load doctors. Please try again.';\n          this.loading = false;\n          console.error('Error loading doctors:', error);\n        }\n      });\n    }\n    onSearch() {\n      this.loadDoctors();\n    }\n    onClearSearch() {\n      this.searchForm.reset();\n      this.loadDoctors();\n    }\n    bookAppointment(doctor) {\n      this.router.navigate(['/appointments/book'], {\n        queryParams: {\n          doctorId: doctor.id\n        }\n      });\n    }\n    getExperienceText(years) {\n      if (!years) return 'Experience not specified';\n      return years === 1 ? '1 year experience' : `${years} years experience`;\n    }\n    static {\n      this.ɵfac = function DoctorSearchComponent_Factory(t) {\n        return new (t || DoctorSearchComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AppointmentService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DoctorSearchComponent,\n        selectors: [[\"app-doctor-search\"]],\n        decls: 29,\n        vars: 8,\n        consts: [[1, \"container-fluid\", \"py-4\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\", \"mb-0\"], [1, \"fas\", \"fa-user-md\", \"me-2\"], [1, \"card-body\"], [1, \"mb-4\", 3, \"formGroup\", \"ngSubmit\"], [1, \"col-md-6\"], [\"for\", \"specialization\", 1, \"form-label\"], [\"id\", \"specialization\", \"formControlName\", \"specialization\", 1, \"form-select\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-6\", \"d-flex\", \"align-items-end\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"me-2\", 3, \"disabled\"], [1, \"fas\", \"fa-search\", \"me-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"row\", 4, \"ngIf\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [3, \"value\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-2\"], [1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\", \"text-muted\"], [\"class\", \"col-md-6 col-lg-4 mb-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-6\", \"col-lg-4\", \"mb-4\"], [1, \"card\", \"h-100\", \"doctor-card\"], [1, \"d-flex\", \"align-items-center\", \"mb-3\"], [1, \"avatar-circle\", \"me-3\"], [1, \"fas\", \"fa-user-md\"], [1, \"card-title\", \"mb-1\"], [1, \"text-muted\", \"mb-0\"], [1, \"doctor-info\"], [\"class\", \"mb-2\", 4, \"ngIf\"], [1, \"mb-2\"], [1, \"fas\", \"fa-clock\", \"me-2\", \"text-muted\"], [1, \"mb-3\"], [1, \"fas\", \"fa-envelope\", \"me-2\", \"text-muted\"], [1, \"btn\", \"btn-primary\", \"w-100\", 3, \"click\"], [1, \"fas\", \"fa-calendar-plus\", \"me-2\"], [1, \"fas\", \"fa-hospital\", \"me-2\", \"text-muted\"], [1, \"text-center\", \"py-5\"], [1, \"fas\", \"fa-user-md\", \"fa-3x\", \"text-muted\", \"mb-3\"], [1, \"text-muted\"]],\n        template: function DoctorSearchComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h4\", 5);\n            i0.ɵɵelement(6, \"i\", 6);\n            i0.ɵɵtext(7, \"Find a Doctor \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"div\", 7)(9, \"form\", 8);\n            i0.ɵɵlistener(\"ngSubmit\", function DoctorSearchComponent_Template_form_ngSubmit_9_listener() {\n              return ctx.onSearch();\n            });\n            i0.ɵɵelementStart(10, \"div\", 1)(11, \"div\", 9)(12, \"label\", 10);\n            i0.ɵɵtext(13, \"Specialization\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"select\", 11)(15, \"option\", 12);\n            i0.ɵɵtext(16, \"All Specializations\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(17, DoctorSearchComponent_option_17_Template, 2, 2, \"option\", 13);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(18, \"div\", 14)(19, \"button\", 15);\n            i0.ɵɵelement(20, \"i\", 16);\n            i0.ɵɵtext(21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function DoctorSearchComponent_Template_button_click_22_listener() {\n              return ctx.onClearSearch();\n            });\n            i0.ɵɵelement(23, \"i\", 18);\n            i0.ɵɵtext(24, \" Clear \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(25, DoctorSearchComponent_div_25_Template, 3, 1, \"div\", 19);\n            i0.ɵɵtemplate(26, DoctorSearchComponent_div_26_Template, 6, 0, \"div\", 20);\n            i0.ɵɵtemplate(27, DoctorSearchComponent_div_27_Template, 2, 1, \"div\", 21);\n            i0.ɵɵtemplate(28, DoctorSearchComponent_div_28_Template, 6, 0, \"div\", 22);\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngForOf\", ctx.specializations);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.loading);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Searching...\" : \"Search\", \" \");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.doctors.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.doctors.length === 0);\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n        styles: [\".doctor-card[_ngcontent-%COMP%]{transition:transform .2s ease-in-out,box-shadow .2s ease-in-out;border:1px solid #e3e6f0}.doctor-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 .5rem 1rem #00000026}.avatar-circle[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);display:flex;align-items:center;justify-content:center;color:#fff;font-size:1.5rem;flex-shrink:0}.doctor-info[_ngcontent-%COMP%]{font-size:.9rem}.doctor-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:16px;text-align:center}.card-title[_ngcontent-%COMP%]{color:#5a5c69;font-weight:600}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);border:none;transition:all .3s ease}.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#5a6fd8 0%,#6a4190 100%);transform:translateY(-1px)}.form-select[_ngcontent-%COMP%]:focus, .form-control[_ngcontent-%COMP%]:focus{border-color:#667eea;box-shadow:0 0 0 .2rem #667eea40}.alert-danger[_ngcontent-%COMP%]{border-left:4px solid #e74a3b}.spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}\"]\n      });\n    }\n  }\n  return DoctorSearchComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}