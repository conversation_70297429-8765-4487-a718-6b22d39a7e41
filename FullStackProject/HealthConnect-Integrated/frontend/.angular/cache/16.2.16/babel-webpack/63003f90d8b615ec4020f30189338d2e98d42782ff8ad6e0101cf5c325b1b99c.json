{"ast": null, "code": "import { AppointmentStatus, AppointmentType } from '../../core/models/appointment.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/appointment.service\";\nimport * as i2 from \"../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../../shared/components/chat-access/chat-access.component\";\nfunction AppointmentListComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵelement(1, \"i\", 24);\n    i0.ɵɵtext(2, \" Book Appointment \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentListComponent_option_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r7.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r7.label, \" \");\n  }\n}\nfunction AppointmentListComponent_option_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r8.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r8.label, \" \");\n  }\n}\nfunction AppointmentListComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"i\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.error, \" \");\n  }\n}\nfunction AppointmentListComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"span\", 30);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 31);\n    i0.ɵɵtext(5, \"Loading appointments...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppointmentListComponent_div_27_div_2_i_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 53);\n  }\n}\nfunction AppointmentListComponent_div_27_div_2_i_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 54);\n  }\n}\nfunction AppointmentListComponent_div_27_div_2_p_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 55);\n    i0.ɵɵelement(1, \"i\", 56);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const appointment_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", appointment_r10.reasonForVisit, \" \");\n  }\n}\nfunction AppointmentListComponent_div_27_div_2_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"a\", 57);\n    i0.ɵɵelement(2, \"i\", 58);\n    i0.ɵɵtext(3, \" Join Video Call \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const appointment_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"href\", appointment_r10.meetingLink, i0.ɵɵsanitizeUrl);\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    appointmentId: a0,\n    doctorId: a1,\n    patientId: a2,\n    chatType: \"PRE_APPOINTMENT\",\n    buttonText: \"Chat\",\n    buttonClass: \"btn-info\",\n    size: \"sm\",\n    showIcon: false\n  };\n};\nfunction AppointmentListComponent_div_27_div_2_app_chat_access_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-chat-access\", 59);\n  }\n  if (rf & 2) {\n    const appointment_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"config\", i0.ɵɵpureFunction3(1, _c0, appointment_r10.id, appointment_r10.doctor.id, appointment_r10.patient.id));\n  }\n}\nconst _c1 = function (a0, a1, a2) {\n  return {\n    appointmentId: a0,\n    doctorId: a1,\n    patientId: a2,\n    chatType: \"POST_APPOINTMENT\",\n    buttonText: \"Follow-up\",\n    buttonClass: \"btn-success\",\n    size: \"sm\",\n    showIcon: false\n  };\n};\nfunction AppointmentListComponent_div_27_div_2_app_chat_access_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-chat-access\", 59);\n  }\n  if (rf & 2) {\n    const appointment_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"config\", i0.ɵɵpureFunction3(1, _c1, appointment_r10.id, appointment_r10.doctor.id, appointment_r10.patient.id));\n  }\n}\nfunction AppointmentListComponent_div_27_div_2_button_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function AppointmentListComponent_div_27_div_2_button_32_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const appointment_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.cancelAppointment(appointment_r10));\n    });\n    i0.ɵɵelement(1, \"i\", 61);\n    i0.ɵɵtext(2, \" Cancel \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentListComponent_div_27_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 8)(3, \"div\", 36)(4, \"div\")(5, \"h6\", 37);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"small\", 38);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 39);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 40)(12, \"p\", 41);\n    i0.ɵɵelement(13, \"i\", 42);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 41);\n    i0.ɵɵelement(16, \"i\", 43);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 41);\n    i0.ɵɵtemplate(19, AppointmentListComponent_div_27_div_2_i_19_Template, 1, 0, \"i\", 44);\n    i0.ɵɵtemplate(20, AppointmentListComponent_div_27_div_2_i_20_Template, 1, 0, \"i\", 45);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, AppointmentListComponent_div_27_div_2_p_22_Template, 3, 1, \"p\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, AppointmentListComponent_div_27_div_2_div_23_Template, 4, 1, \"div\", 46);\n    i0.ɵɵelementStart(24, \"div\", 47)(25, \"div\", 48)(26, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function AppointmentListComponent_div_27_div_2_Template_button_click_26_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r26);\n      const appointment_r10 = restoredCtx.$implicit;\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.viewAppointment(appointment_r10));\n    });\n    i0.ɵɵelement(27, \"i\", 50);\n    i0.ɵɵtext(28, \" View \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(29, AppointmentListComponent_div_27_div_2_app_chat_access_29_Template, 1, 5, \"app-chat-access\", 51);\n    i0.ɵɵtemplate(30, AppointmentListComponent_div_27_div_2_app_chat_access_30_Template, 1, 5, \"app-chat-access\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\");\n    i0.ɵɵtemplate(32, AppointmentListComponent_div_27_div_2_button_32_Template, 3, 0, \"button\", 52);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const appointment_r10 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.getOtherParty(appointment_r10), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r9.getOtherPartyRole(appointment_r10));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r9.getStatusBadgeClass(appointment_r10.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.getStatusDisplayName(appointment_r10.status), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.formatDate(appointment_r10.date), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r9.formatTime(appointment_r10.startTime), \" - \", ctx_r9.formatTime(appointment_r10.endTime), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", appointment_r10.type === \"VIDEO_CALL\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", appointment_r10.type === \"IN_PERSON\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.getTypeDisplayName(appointment_r10.type), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", appointment_r10.reasonForVisit);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", appointment_r10.meetingLink && appointment_r10.type === \"VIDEO_CALL\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isBeforeAppointment(appointment_r10));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isAfterAppointment(appointment_r10));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.canCancelAppointment(appointment_r10));\n  }\n}\nfunction AppointmentListComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 1);\n    i0.ɵɵtemplate(2, AppointmentListComponent_div_27_div_2_Template, 33, 15, \"div\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.filteredAppointments);\n  }\n}\nfunction AppointmentListComponent_div_28_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"You haven't booked any appointments yet.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentListComponent_div_28_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"You don't have any appointments scheduled.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentListComponent_div_28_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵelement(1, \"i\", 24);\n    i0.ɵɵtext(2, \" Book Your First Appointment \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentListComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelement(1, \"i\", 63);\n    i0.ɵɵelementStart(2, \"h5\", 38);\n    i0.ɵɵtext(3, \"No appointments found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 64);\n    i0.ɵɵtemplate(5, AppointmentListComponent_div_28_span_5_Template, 2, 0, \"span\", 65);\n    i0.ɵɵtemplate(6, AppointmentListComponent_div_28_span_6_Template, 2, 0, \"span\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, AppointmentListComponent_div_28_button_7_Template, 3, 0, \"button\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isPatient());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isDoctor());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isPatient());\n  }\n}\nexport let AppointmentListComponent = /*#__PURE__*/(() => {\n  class AppointmentListComponent {\n    constructor(appointmentService, authService, router) {\n      this.appointmentService = appointmentService;\n      this.authService = authService;\n      this.router = router;\n      this.appointments = [];\n      this.filteredAppointments = [];\n      this.currentUser = null;\n      this.loading = false;\n      this.error = null;\n      // Filter options\n      this.statusFilter = '';\n      this.typeFilter = '';\n      this.statusOptions = [{\n        value: '',\n        label: 'All Statuses'\n      }, {\n        value: AppointmentStatus.PENDING,\n        label: 'Pending'\n      }, {\n        value: AppointmentStatus.SCHEDULED,\n        label: 'Scheduled'\n      }, {\n        value: AppointmentStatus.CONFIRMED,\n        label: 'Confirmed'\n      }, {\n        value: AppointmentStatus.COMPLETED,\n        label: 'Completed'\n      }, {\n        value: AppointmentStatus.CANCELLED,\n        label: 'Cancelled'\n      }];\n      this.typeOptions = [{\n        value: '',\n        label: 'All Types'\n      }, {\n        value: AppointmentType.IN_PERSON,\n        label: 'In Person'\n      }, {\n        value: AppointmentType.VIDEO_CALL,\n        label: 'Video Call'\n      }];\n    }\n    ngOnInit() {\n      this.currentUser = this.authService.getCurrentUser();\n      this.loadAppointments();\n    }\n    loadAppointments() {\n      this.loading = true;\n      this.error = null;\n      this.appointmentService.getAppointments().subscribe({\n        next: appointments => {\n          this.appointments = appointments;\n          this.applyFilters();\n          this.loading = false;\n        },\n        error: error => {\n          this.error = 'Failed to load appointments. Please try again.';\n          this.loading = false;\n          console.error('Error loading appointments:', error);\n        }\n      });\n    }\n    applyFilters() {\n      this.filteredAppointments = this.appointments.filter(appointment => {\n        const statusMatch = !this.statusFilter || appointment.status === this.statusFilter;\n        const typeMatch = !this.typeFilter || appointment.type === this.typeFilter;\n        return statusMatch && typeMatch;\n      });\n    }\n    onStatusFilterChange() {\n      this.applyFilters();\n    }\n    onTypeFilterChange() {\n      this.applyFilters();\n    }\n    viewAppointment(appointment) {\n      this.router.navigate(['/appointments', appointment.id]);\n    }\n    editAppointment(appointment) {\n      // For now, navigate to details page where editing can be done\n      this.router.navigate(['/appointments', appointment.id]);\n    }\n    cancelAppointment(appointment) {\n      if (confirm('Are you sure you want to cancel this appointment?')) {\n        this.appointmentService.cancelAppointment(appointment.id).subscribe({\n          next: () => {\n            this.loadAppointments(); // Reload the list\n          },\n\n          error: error => {\n            this.error = 'Failed to cancel appointment. Please try again.';\n            console.error('Error canceling appointment:', error);\n          }\n        });\n      }\n    }\n    getStatusDisplayName(status) {\n      return this.appointmentService.getStatusDisplayName(status);\n    }\n    getTypeDisplayName(type) {\n      return this.appointmentService.getTypeDisplayName(type);\n    }\n    isBeforeAppointment(appointment) {\n      const appointmentDateTime = new Date(`${appointment.date}T${appointment.startTime}`);\n      return appointmentDateTime > new Date();\n    }\n    isAfterAppointment(appointment) {\n      const appointmentDateTime = new Date(`${appointment.date}T${appointment.endTime}`);\n      return appointmentDateTime < new Date();\n    }\n    getStatusBadgeClass(status) {\n      return this.appointmentService.getStatusBadgeClass(status);\n    }\n    formatDate(dateString) {\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        weekday: 'short',\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    }\n    formatTime(timeString) {\n      const [hours, minutes] = timeString.split(':');\n      const hour = parseInt(hours);\n      const ampm = hour >= 12 ? 'PM' : 'AM';\n      const displayHour = hour % 12 || 12;\n      return `${displayHour}:${minutes} ${ampm}`;\n    }\n    isDoctor() {\n      return this.currentUser?.role === 'DOCTOR';\n    }\n    isPatient() {\n      return this.currentUser?.role === 'PATIENT';\n    }\n    canCancelAppointment(appointment) {\n      return appointment.status === AppointmentStatus.PENDING || appointment.status === AppointmentStatus.SCHEDULED || appointment.status === AppointmentStatus.CONFIRMED;\n    }\n    getOtherParty(appointment) {\n      if (this.isDoctor()) {\n        return appointment.patient.fullName;\n      } else {\n        return appointment.doctor.fullName;\n      }\n    }\n    getOtherPartyRole(appointment) {\n      if (this.isDoctor()) {\n        return 'Patient';\n      } else {\n        return 'Doctor';\n      }\n    }\n    static {\n      this.ɵfac = function AppointmentListComponent_Factory(t) {\n        return new (t || AppointmentListComponent)(i0.ɵɵdirectiveInject(i1.AppointmentService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppointmentListComponent,\n        selectors: [[\"app-appointment-list\"]],\n        decls: 29,\n        vars: 9,\n        consts: [[1, \"container-fluid\", \"py-4\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"card-title\", \"mb-0\"], [1, \"fas\", \"fa-calendar-alt\", \"me-2\"], [\"class\", \"btn btn-primary\", \"routerLink\", \"/appointments/book\", 4, \"ngIf\"], [1, \"card-body\"], [1, \"row\", \"mb-4\"], [1, \"col-md-4\"], [\"for\", \"statusFilter\", 1, \"form-label\"], [\"id\", \"statusFilter\", 1, \"form-select\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"typeFilter\", 1, \"form-label\"], [\"id\", \"typeFilter\", 1, \"form-select\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [1, \"col-md-4\", \"d-flex\", \"align-items-end\"], [1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\", \"me-2\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"appointments-list\", 4, \"ngIf\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [\"routerLink\", \"/appointments/book\", 1, \"btn\", \"btn-primary\"], [1, \"fas\", \"fa-plus\", \"me-2\"], [3, \"value\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-2\"], [1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\", \"text-muted\"], [1, \"appointments-list\"], [\"class\", \"col-lg-6 mb-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-lg-6\", \"mb-4\"], [1, \"card\", \"appointment-card\", \"h-100\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-start\", \"mb-3\"], [1, \"card-title\", \"mb-1\"], [1, \"text-muted\"], [1, \"badge\", 3, \"ngClass\"], [1, \"appointment-details\"], [1, \"mb-2\"], [1, \"fas\", \"fa-calendar\", \"me-2\", \"text-muted\"], [1, \"fas\", \"fa-clock\", \"me-2\", \"text-muted\"], [\"class\", \"fas fa-video me-2 text-muted\", 4, \"ngIf\"], [\"class\", \"fas fa-user-friends me-2 text-muted\", 4, \"ngIf\"], [\"class\", \"mb-3\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"role\", \"group\", 1, \"btn-group\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"fas\", \"fa-eye\", \"me-1\"], [3, \"config\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-outline-danger\", 3, \"click\", 4, \"ngIf\"], [1, \"fas\", \"fa-video\", \"me-2\", \"text-muted\"], [1, \"fas\", \"fa-user-friends\", \"me-2\", \"text-muted\"], [1, \"mb-3\"], [1, \"fas\", \"fa-notes-medical\", \"me-2\", \"text-muted\"], [\"target\", \"_blank\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"href\"], [1, \"fas\", \"fa-video\", \"me-2\"], [3, \"config\"], [1, \"btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [1, \"text-center\", \"py-5\"], [1, \"fas\", \"fa-calendar-times\", \"fa-3x\", \"text-muted\", \"mb-3\"], [1, \"text-muted\", \"mb-4\"], [4, \"ngIf\"]],\n        template: function AppointmentListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h4\", 5);\n            i0.ɵɵelement(6, \"i\", 6);\n            i0.ɵɵtext(7, \"My Appointments \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(8, AppointmentListComponent_button_8_Template, 3, 0, \"button\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"div\", 8)(10, \"div\", 9)(11, \"div\", 10)(12, \"label\", 11);\n            i0.ɵɵtext(13, \"Filter by Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"select\", 12);\n            i0.ɵɵlistener(\"ngModelChange\", function AppointmentListComponent_Template_select_ngModelChange_14_listener($event) {\n              return ctx.statusFilter = $event;\n            })(\"change\", function AppointmentListComponent_Template_select_change_14_listener() {\n              return ctx.onStatusFilterChange();\n            });\n            i0.ɵɵtemplate(15, AppointmentListComponent_option_15_Template, 2, 2, \"option\", 13);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 10)(17, \"label\", 14);\n            i0.ɵɵtext(18, \"Filter by Type\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"select\", 15);\n            i0.ɵɵlistener(\"ngModelChange\", function AppointmentListComponent_Template_select_ngModelChange_19_listener($event) {\n              return ctx.typeFilter = $event;\n            })(\"change\", function AppointmentListComponent_Template_select_change_19_listener() {\n              return ctx.onTypeFilterChange();\n            });\n            i0.ɵɵtemplate(20, AppointmentListComponent_option_20_Template, 2, 2, \"option\", 13);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(21, \"div\", 16)(22, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function AppointmentListComponent_Template_button_click_22_listener() {\n              return ctx.loadAppointments();\n            });\n            i0.ɵɵelement(23, \"i\", 18);\n            i0.ɵɵtext(24, \" Refresh \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(25, AppointmentListComponent_div_25_Template, 3, 1, \"div\", 19);\n            i0.ɵɵtemplate(26, AppointmentListComponent_div_26_Template, 6, 0, \"div\", 20);\n            i0.ɵɵtemplate(27, AppointmentListComponent_div_27_Template, 3, 1, \"div\", 21);\n            i0.ɵɵtemplate(28, AppointmentListComponent_div_28_Template, 8, 3, \"div\", 22);\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.isPatient());\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngModel\", ctx.statusFilter);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngForOf\", ctx.statusOptions);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngModel\", ctx.typeFilter);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngForOf\", ctx.typeOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.filteredAppointments.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.filteredAppointments.length === 0);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel, i3.RouterLink, i6.ChatAccessComponent],\n        styles: [\".appointment-card[_ngcontent-%COMP%]{transition:transform .2s ease-in-out,box-shadow .2s ease-in-out;border:1px solid #e3e6f0}.appointment-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 .5rem 1rem #00000026}.appointment-details[_ngcontent-%COMP%]{font-size:.9rem}.appointment-details[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:16px;text-align:center}.badge-warning[_ngcontent-%COMP%]{background-color:#f6c23e;color:#1a1a1a}.badge-info[_ngcontent-%COMP%]{background-color:#36b9cc}.badge-primary[_ngcontent-%COMP%]{background-color:#4e73df}.badge-success[_ngcontent-%COMP%]{background-color:#1cc88a}.badge-danger[_ngcontent-%COMP%]{background-color:#e74a3b}.badge-secondary[_ngcontent-%COMP%]{background-color:#858796}.form-select[_ngcontent-%COMP%]:focus{border-color:#667eea;box-shadow:0 0 0 .2rem #667eea40}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);border:none;transition:all .3s ease}.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#5a6fd8 0%,#6a4190 100%);transform:translateY(-1px)}.btn-outline-primary[_ngcontent-%COMP%]{border-color:#667eea;color:#667eea}.btn-outline-primary[_ngcontent-%COMP%]:hover{background-color:#667eea;border-color:#667eea}.btn-outline-danger[_ngcontent-%COMP%]{border-color:#e74a3b;color:#e74a3b}.btn-outline-danger[_ngcontent-%COMP%]:hover{background-color:#e74a3b;border-color:#e74a3b}.btn-outline-secondary[_ngcontent-%COMP%]{border-color:#858796;color:#858796}.btn-outline-secondary[_ngcontent-%COMP%]:hover{background-color:#858796;border-color:#858796}.card[_ngcontent-%COMP%]{border:1px solid #e3e6f0;box-shadow:0 .15rem 1.75rem #3a3b4526}.card-title[_ngcontent-%COMP%]{color:#5a5c69;font-weight:600}.alert-danger[_ngcontent-%COMP%]{border-left:4px solid #e74a3b}.spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}\"]\n      });\n    }\n  }\n  return AppointmentListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}