{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  AjaxBasedTransport = require('./lib/ajax-based'),\n  XdrStreamingTransport = require('./xdr-streaming'),\n  XhrReceiver = require('./receiver/xhr'),\n  XDRObject = require('./sender/xdr');\nfunction XdrPollingTransport(transUrl) {\n  if (!XDRObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr', XhrReceiver, XDRObject);\n}\ninherits(XdrPollingTransport, AjaxBasedTransport);\nXdrPollingTransport.enabled = XdrStreamingTransport.enabled;\nXdrPollingTransport.transportName = 'xdr-polling';\nXdrPollingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XdrPollingTransport;", "map": {"version": 3, "names": ["inherits", "require", "AjaxBasedTransport", "XdrStreamingTransport", "XhrReceiver", "XDRObject", "XdrPollingTransport", "transUrl", "enabled", "Error", "call", "transportName", "roundTrips", "module", "exports"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/sockjs-client/lib/transport/xdr-polling.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , XdrStreamingTransport = require('./xdr-streaming')\n  , XhrReceiver = require('./receiver/xhr')\n  , XDRObject = require('./sender/xdr')\n  ;\n\nfunction XdrPollingTransport(transUrl) {\n  if (!XDRObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr', XhrReceiver, XDRObject);\n}\n\ninherits(XdrPollingTransport, AjaxBasedTransport);\n\nXdrPollingTransport.enabled = XdrStreamingTransport.enabled;\nXdrPollingTransport.transportName = 'xdr-polling';\nXdrPollingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XdrPollingTransport;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,kBAAkB,GAAGD,OAAO,CAAC,kBAAkB,CAAC;EAChDE,qBAAqB,GAAGF,OAAO,CAAC,iBAAiB,CAAC;EAClDG,WAAW,GAAGH,OAAO,CAAC,gBAAgB,CAAC;EACvCI,SAAS,GAAGJ,OAAO,CAAC,cAAc,CAAC;AAGvC,SAASK,mBAAmBA,CAACC,QAAQ,EAAE;EACrC,IAAI,CAACF,SAAS,CAACG,OAAO,EAAE;IACtB,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC;EACpD;EACAP,kBAAkB,CAACQ,IAAI,CAAC,IAAI,EAAEH,QAAQ,EAAE,MAAM,EAAEH,WAAW,EAAEC,SAAS,CAAC;AACzE;AAEAL,QAAQ,CAACM,mBAAmB,EAAEJ,kBAAkB,CAAC;AAEjDI,mBAAmB,CAACE,OAAO,GAAGL,qBAAqB,CAACK,OAAO;AAC3DF,mBAAmB,CAACK,aAAa,GAAG,aAAa;AACjDL,mBAAmB,CAACM,UAAU,GAAG,CAAC,CAAC,CAAC;;AAEpCC,MAAM,CAACC,OAAO,GAAGR,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}