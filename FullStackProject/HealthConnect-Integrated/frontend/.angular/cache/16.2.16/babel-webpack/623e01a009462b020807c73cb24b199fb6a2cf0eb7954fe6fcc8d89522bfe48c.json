{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  XhrDriver = require('../driver/xhr');\nfunction XHRCorsObject(method, url, payload, opts) {\n  XhrDriver.call(this, method, url, payload, opts);\n}\ninherits(XHRCorsObject, XhrDriver);\nXHRCorsObject.enabled = XhrDriver.enabled && XhrDriver.supportsCORS;\nmodule.exports = XHRCorsObject;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}