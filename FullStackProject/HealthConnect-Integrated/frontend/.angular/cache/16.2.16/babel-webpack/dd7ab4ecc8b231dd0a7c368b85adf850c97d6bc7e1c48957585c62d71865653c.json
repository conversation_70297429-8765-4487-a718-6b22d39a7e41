{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SharedModule } from '../shared/shared.module';\nimport { LoginComponent } from './login/login.component';\nimport { RegisterComponent } from './register/register.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'register',\n  component: RegisterComponent\n}];\nexport let AuthModule = /*#__PURE__*/(() => {\n  class AuthModule {\n    static {\n      this.ɵfac = function AuthModule_Factory(t) {\n        return new (t || AuthModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AuthModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [SharedModule, RouterModule.forChild(routes)]\n      });\n    }\n  }\n  return AuthModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}