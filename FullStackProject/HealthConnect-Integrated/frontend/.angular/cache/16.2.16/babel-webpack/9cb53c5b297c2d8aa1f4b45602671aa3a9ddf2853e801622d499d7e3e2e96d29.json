{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nexport let SharedModule = /*#__PURE__*/(() => {\n  class SharedModule {\n    static {\n      this.ɵfac = function SharedModule_Factory(t) {\n        return new (t || SharedModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: SharedModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule, CommonModule, ReactiveFormsModule, FormsModule, RouterModule]\n      });\n    }\n  }\n  return SharedModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}