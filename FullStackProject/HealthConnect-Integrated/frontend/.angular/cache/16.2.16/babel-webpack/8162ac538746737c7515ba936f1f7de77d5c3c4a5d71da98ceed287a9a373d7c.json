{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { ChatRoutingModule } from './chat-routing.module';\nimport { SharedModule } from '../shared/shared.module';\nimport { ChatListComponent } from './chat-list/chat-list.component';\nimport { ChatWindowComponent } from './chat-window/chat-window.component';\nimport { MessageItemComponent } from './message-item/message-item.component';\nimport { AppointmentContextComponent } from './appointment-context/appointment-context.component';\nimport * as i0 from \"@angular/core\";\nexport class ChatModule {\n  static {\n    this.ɵfac = function ChatModule_Factory(t) {\n      return new (t || ChatModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ChatModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, ChatRoutingModule, SharedModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ChatModule, {\n    declarations: [ChatListComponent, ChatWindowComponent, MessageItemComponent, AppointmentContextComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, ChatRoutingModule, SharedModule],\n    exports: [ChatListComponent, ChatWindowComponent, MessageItemComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "RouterModule", "ChatRoutingModule", "SharedModule", "ChatListComponent", "ChatWindowComponent", "MessageItemComponent", "AppointmentContextComponent", "ChatModule", "declarations", "imports", "exports"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/chat/chat.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n\nimport { ChatRoutingModule } from './chat-routing.module';\nimport { SharedModule } from '../shared/shared.module';\nimport { ChatListComponent } from './chat-list/chat-list.component';\nimport { ChatWindowComponent } from './chat-window/chat-window.component';\nimport { MessageItemComponent } from './message-item/message-item.component';\nimport { AppointmentContextComponent } from './appointment-context/appointment-context.component';\n\n@NgModule({\n  declarations: [\n    ChatListComponent,\n    ChatWindowComponent,\n    MessageItemComponent,\n    AppointmentContextComponent\n  ],\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    RouterModule,\n    ChatRoutingModule,\n    SharedModule\n  ],\n  exports: [\n    ChatListComponent,\n    ChatWindowComponent,\n    MessageItemComponent\n  ]\n})\nexport class ChatModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,2BAA2B,QAAQ,qDAAqD;;AAuBjG,OAAM,MAAOC,UAAU;;;uBAAVA,UAAU;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBAbnBV,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,YAAY,EACZC,iBAAiB,EACjBC,YAAY;IAAA;EAAA;;;2EAQHK,UAAU;IAAAC,YAAA,GAnBnBL,iBAAiB,EACjBC,mBAAmB,EACnBC,oBAAoB,EACpBC,2BAA2B;IAAAG,OAAA,GAG3BZ,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,YAAY,EACZC,iBAAiB,EACjBC,YAAY;IAAAQ,OAAA,GAGZP,iBAAiB,EACjBC,mBAAmB,EACnBC,oBAAoB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}