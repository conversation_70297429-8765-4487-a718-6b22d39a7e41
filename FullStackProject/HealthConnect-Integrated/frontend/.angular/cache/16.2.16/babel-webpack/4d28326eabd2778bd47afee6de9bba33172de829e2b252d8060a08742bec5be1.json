{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nexport class AuthInterceptor {\n  constructor(authService) {\n    this.authService = authService;\n  }\n  intercept(req, next) {\n    // Get the auth token from the service\n    const authToken = this.authService.getToken();\n    // Clone the request and add the authorization header if token exists\n    if (authToken) {\n      const authReq = req.clone({\n        headers: req.headers.set('Authorization', `Bearer ${authToken}`)\n      });\n      return next.handle(authReq);\n    }\n    // If no token, proceed with the original request\n    return next.handle(req);\n  }\n  static {\n    this.ɵfac = function AuthInterceptor_Factory(t) {\n      return new (t || AuthInterceptor)(i0.ɵɵinject(i1.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthInterceptor,\n      factory: AuthInterceptor.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["AuthInterceptor", "constructor", "authService", "intercept", "req", "next", "authToken", "getToken", "authReq", "clone", "headers", "set", "handle", "i0", "ɵɵinject", "i1", "AuthService", "factory", "ɵfac"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/core/interceptors/auth.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable()\nexport class AuthInterceptor implements HttpInterceptor {\n\n  constructor(private authService: AuthService) {}\n\n  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\n    // Get the auth token from the service\n    const authToken = this.authService.getToken();\n\n    // Clone the request and add the authorization header if token exists\n    if (authToken) {\n      const authReq = req.clone({\n        headers: req.headers.set('Authorization', `Bearer ${authToken}`)\n      });\n      return next.handle(authReq);\n    }\n\n    // If no token, proceed with the original request\n    return next.handle(req);\n  }\n}\n"], "mappings": ";;AAMA,OAAM,MAAOA,eAAe;EAE1BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;EAAgB;EAE/CC,SAASA,CAACC,GAAqB,EAAEC,IAAiB;IAChD;IACA,MAAMC,SAAS,GAAG,IAAI,CAACJ,WAAW,CAACK,QAAQ,EAAE;IAE7C;IACA,IAAID,SAAS,EAAE;MACb,MAAME,OAAO,GAAGJ,GAAG,CAACK,KAAK,CAAC;QACxBC,OAAO,EAAEN,GAAG,CAACM,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUL,SAAS,EAAE;OAChE,CAAC;MACF,OAAOD,IAAI,CAACO,MAAM,CAACJ,OAAO,CAAC;;IAG7B;IACA,OAAOH,IAAI,CAACO,MAAM,CAACR,GAAG,CAAC;EACzB;;;uBAlBWJ,eAAe,EAAAa,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAfhB,eAAe;MAAAiB,OAAA,EAAfjB,eAAe,CAAAkB;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}