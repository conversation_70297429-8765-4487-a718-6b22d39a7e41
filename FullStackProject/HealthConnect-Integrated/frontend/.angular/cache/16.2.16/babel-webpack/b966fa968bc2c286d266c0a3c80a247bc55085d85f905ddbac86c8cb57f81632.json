{"ast": null, "code": "import { BYTE } from './byte.js';\n/**\n * Frame class represents a STOMP frame.\n *\n * @internal\n */\nexport class FrameImpl {\n  /**\n   * body of the frame\n   */\n  get body() {\n    if (!this._body && this.isBinaryBody) {\n      this._body = new TextDecoder().decode(this._binaryBody);\n    }\n    return this._body || '';\n  }\n  /**\n   * body as Uint8Array\n   */\n  get binaryBody() {\n    if (!this._binaryBody && !this.isBinaryBody) {\n      this._binaryBody = new TextEncoder().encode(this._body);\n    }\n    // At this stage it will definitely have a valid value\n    return this._binaryBody;\n  }\n  /**\n   * Frame constructor. `command`, `headers` and `body` are available as properties.\n   *\n   * @internal\n   */\n  constructor(params) {\n    const {\n      command,\n      headers,\n      body,\n      binaryBody,\n      escapeHeaderValues,\n      skipContentLengthHeader\n    } = params;\n    this.command = command;\n    this.headers = Object.assign({}, headers || {});\n    if (binaryBody) {\n      this._binaryBody = binaryBody;\n      this.isBinaryBody = true;\n    } else {\n      this._body = body || '';\n      this.isBinaryBody = false;\n    }\n    this.escapeHeaderValues = escapeHeaderValues || false;\n    this.skipContentLengthHeader = skipContentLengthHeader || false;\n  }\n  /**\n   * deserialize a STOMP Frame from raw data.\n   *\n   * @internal\n   */\n  static fromRawFrame(rawFrame, escapeHeaderValues) {\n    const headers = {};\n    const trim = str => str.replace(/^\\s+|\\s+$/g, '');\n    // In case of repeated headers, as per standards, first value need to be used\n    for (const header of rawFrame.headers.reverse()) {\n      const idx = header.indexOf(':');\n      const key = trim(header[0]);\n      let value = trim(header[1]);\n      if (escapeHeaderValues && rawFrame.command !== 'CONNECT' && rawFrame.command !== 'CONNECTED') {\n        value = FrameImpl.hdrValueUnEscape(value);\n      }\n      headers[key] = value;\n    }\n    return new FrameImpl({\n      command: rawFrame.command,\n      headers,\n      binaryBody: rawFrame.binaryBody,\n      escapeHeaderValues\n    });\n  }\n  /**\n   * @internal\n   */\n  toString() {\n    return this.serializeCmdAndHeaders();\n  }\n  /**\n   * serialize this Frame in a format suitable to be passed to WebSocket.\n   * If the body is string the output will be string.\n   * If the body is binary (i.e. of type Unit8Array) it will be serialized to ArrayBuffer.\n   *\n   * @internal\n   */\n  serialize() {\n    const cmdAndHeaders = this.serializeCmdAndHeaders();\n    if (this.isBinaryBody) {\n      return FrameImpl.toUnit8Array(cmdAndHeaders, this._binaryBody).buffer;\n    } else {\n      return cmdAndHeaders + this._body + BYTE.NULL;\n    }\n  }\n  serializeCmdAndHeaders() {\n    const lines = [this.command];\n    if (this.skipContentLengthHeader) {\n      delete this.headers['content-length'];\n    }\n    for (const name of Object.keys(this.headers || {})) {\n      const value = this.headers[name];\n      if (this.escapeHeaderValues && this.command !== 'CONNECT' && this.command !== 'CONNECTED') {\n        lines.push(`${name}:${FrameImpl.hdrValueEscape(`${value}`)}`);\n      } else {\n        lines.push(`${name}:${value}`);\n      }\n    }\n    if (this.isBinaryBody || !this.isBodyEmpty() && !this.skipContentLengthHeader) {\n      lines.push(`content-length:${this.bodyLength()}`);\n    }\n    return lines.join(BYTE.LF) + BYTE.LF + BYTE.LF;\n  }\n  isBodyEmpty() {\n    return this.bodyLength() === 0;\n  }\n  bodyLength() {\n    const binaryBody = this.binaryBody;\n    return binaryBody ? binaryBody.length : 0;\n  }\n  /**\n   * Compute the size of a UTF-8 string by counting its number of bytes\n   * (and not the number of characters composing the string)\n   */\n  static sizeOfUTF8(s) {\n    return s ? new TextEncoder().encode(s).length : 0;\n  }\n  static toUnit8Array(cmdAndHeaders, binaryBody) {\n    const uint8CmdAndHeaders = new TextEncoder().encode(cmdAndHeaders);\n    const nullTerminator = new Uint8Array([0]);\n    const uint8Frame = new Uint8Array(uint8CmdAndHeaders.length + binaryBody.length + nullTerminator.length);\n    uint8Frame.set(uint8CmdAndHeaders);\n    uint8Frame.set(binaryBody, uint8CmdAndHeaders.length);\n    uint8Frame.set(nullTerminator, uint8CmdAndHeaders.length + binaryBody.length);\n    return uint8Frame;\n  }\n  /**\n   * Serialize a STOMP frame as per STOMP standards, suitable to be sent to the STOMP broker.\n   *\n   * @internal\n   */\n  static marshall(params) {\n    const frame = new FrameImpl(params);\n    return frame.serialize();\n  }\n  /**\n   *  Escape header values\n   */\n  static hdrValueEscape(str) {\n    return str.replace(/\\\\/g, '\\\\\\\\').replace(/\\r/g, '\\\\r').replace(/\\n/g, '\\\\n').replace(/:/g, '\\\\c');\n  }\n  /**\n   * UnEscape header values\n   */\n  static hdrValueUnEscape(str) {\n    return str.replace(/\\\\r/g, '\\r').replace(/\\\\n/g, '\\n').replace(/\\\\c/g, ':').replace(/\\\\\\\\/g, '\\\\');\n  }\n}\n//# sourceMappingURL=frame-impl.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}