{"ast": null, "code": "'use strict';\n\nvar EventEmitter = require('events').EventEmitter,\n  inherits = require('inherits'),\n  utils = require('../../utils/event'),\n  urlUtils = require('../../utils/url'),\n  XHR = global.XMLHttpRequest;\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:browser:xhr');\n}\nfunction AbstractXHRObject(method, url, payload, opts) {\n  debug(method, url);\n  var self = this;\n  EventEmitter.call(this);\n  setTimeout(function () {\n    self._start(method, url, payload, opts);\n  }, 0);\n}\ninherits(AbstractXHRObject, EventEmitter);\nAbstractXHRObject.prototype._start = function (method, url, payload, opts) {\n  var self = this;\n  try {\n    this.xhr = new XHR();\n  } catch (x) {\n    // intentionally empty\n  }\n  if (!this.xhr) {\n    debug('no xhr');\n    this.emit('finish', 0, 'no xhr support');\n    this._cleanup();\n    return;\n  }\n\n  // several browsers cache POSTs\n  url = urlUtils.addQuery(url, 't=' + +new Date());\n\n  // Explorer tends to keep connection open, even after the\n  // tab gets closed: http://bugs.jquery.com/ticket/5280\n  this.unloadRef = utils.unloadAdd(function () {\n    debug('unload cleanup');\n    self._cleanup(true);\n  });\n  try {\n    this.xhr.open(method, url, true);\n    if (this.timeout && 'timeout' in this.xhr) {\n      this.xhr.timeout = this.timeout;\n      this.xhr.ontimeout = function () {\n        debug('xhr timeout');\n        self.emit('finish', 0, '');\n        self._cleanup(false);\n      };\n    }\n  } catch (e) {\n    debug('exception', e);\n    // IE raises an exception on wrong port.\n    this.emit('finish', 0, '');\n    this._cleanup(false);\n    return;\n  }\n  if ((!opts || !opts.noCredentials) && AbstractXHRObject.supportsCORS) {\n    debug('withCredentials');\n    // Mozilla docs says https://developer.mozilla.org/en/XMLHttpRequest :\n    // \"This never affects same-site requests.\"\n\n    this.xhr.withCredentials = true;\n  }\n  if (opts && opts.headers) {\n    for (var key in opts.headers) {\n      this.xhr.setRequestHeader(key, opts.headers[key]);\n    }\n  }\n  this.xhr.onreadystatechange = function () {\n    if (self.xhr) {\n      var x = self.xhr;\n      var text, status;\n      debug('readyState', x.readyState);\n      switch (x.readyState) {\n        case 3:\n          // IE doesn't like peeking into responseText or status\n          // on Microsoft.XMLHTTP and readystate=3\n          try {\n            status = x.status;\n            text = x.responseText;\n          } catch (e) {\n            // intentionally empty\n          }\n          debug('status', status);\n          // IE returns 1223 for 204: http://bugs.jquery.com/ticket/1450\n          if (status === 1223) {\n            status = 204;\n          }\n\n          // IE does return readystate == 3 for 404 answers.\n          if (status === 200 && text && text.length > 0) {\n            debug('chunk');\n            self.emit('chunk', status, text);\n          }\n          break;\n        case 4:\n          status = x.status;\n          debug('status', status);\n          // IE returns 1223 for 204: http://bugs.jquery.com/ticket/1450\n          if (status === 1223) {\n            status = 204;\n          }\n          // IE returns this for a bad port\n          // http://msdn.microsoft.com/en-us/library/windows/desktop/aa383770(v=vs.85).aspx\n          if (status === 12005 || status === 12029) {\n            status = 0;\n          }\n          debug('finish', status, x.responseText);\n          self.emit('finish', status, x.responseText);\n          self._cleanup(false);\n          break;\n      }\n    }\n  };\n  try {\n    self.xhr.send(payload);\n  } catch (e) {\n    self.emit('finish', 0, '');\n    self._cleanup(false);\n  }\n};\nAbstractXHRObject.prototype._cleanup = function (abort) {\n  debug('cleanup');\n  if (!this.xhr) {\n    return;\n  }\n  this.removeAllListeners();\n  utils.unloadDel(this.unloadRef);\n\n  // IE needs this field to be a function\n  this.xhr.onreadystatechange = function () {};\n  if (this.xhr.ontimeout) {\n    this.xhr.ontimeout = null;\n  }\n  if (abort) {\n    try {\n      this.xhr.abort();\n    } catch (x) {\n      // intentionally empty\n    }\n  }\n  this.unloadRef = this.xhr = null;\n};\nAbstractXHRObject.prototype.close = function () {\n  debug('close');\n  this._cleanup(true);\n};\nAbstractXHRObject.enabled = !!XHR;\n// override XMLHttpRequest for IE6/7\n// obfuscate to avoid firewalls\nvar axo = ['Active'].concat('Object').join('X');\nif (!AbstractXHRObject.enabled && axo in global) {\n  debug('overriding xmlhttprequest');\n  XHR = function () {\n    try {\n      return new global[axo]('Microsoft.XMLHTTP');\n    } catch (e) {\n      return null;\n    }\n  };\n  AbstractXHRObject.enabled = !!new XHR();\n}\nvar cors = false;\ntry {\n  cors = 'withCredentials' in new XHR();\n} catch (ignored) {\n  // intentionally empty\n}\nAbstractXHRObject.supportsCORS = cors;\nmodule.exports = AbstractXHRObject;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}