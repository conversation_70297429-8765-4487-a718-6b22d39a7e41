{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/appointment.service\";\nimport * as i2 from \"../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction AppointmentCalendarComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵelement(1, \"i\", 27);\n    i0.ɵɵtext(2, \" Book Appointment \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentCalendarComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"i\", 29);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction AppointmentCalendarComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31)(2, \"span\", 32);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 33);\n    i0.ɵɵtext(5, \"Loading calendar...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppointmentCalendarComponent_div_22_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dayName_r6 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", dayName_r6, \" \");\n  }\n}\nfunction AppointmentCalendarComponent_div_22_div_4_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"span\", 48);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const appointment_r12 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r10.getStatusBadgeClass(appointment_r12.status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.formatTime(appointment_r12.startTime), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.isDoctor() ? appointment_r12.patient.fullName : appointment_r12.doctor.fullName, \" \");\n  }\n}\nfunction AppointmentCalendarComponent_div_22_div_4_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" +\", day_r7.appointments.length - 2, \" more \");\n  }\n}\nfunction AppointmentCalendarComponent_div_22_div_4_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, AppointmentCalendarComponent_div_22_div_4_div_3_div_1_Template, 5, 3, \"div\", 45);\n    i0.ɵɵtemplate(2, AppointmentCalendarComponent_div_22_div_4_div_3_div_2_Template, 2, 1, \"div\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", day_r7.appointments.slice(0, 2));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", day_r7.appointments.length > 2);\n  }\n}\nfunction AppointmentCalendarComponent_div_22_div_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵelement(1, \"i\", 52);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0, a1, a2, a3) {\n  return {\n    \"other-month\": a0,\n    \"today\": a1,\n    \"has-appointments\": a2,\n    \"clickable\": a3\n  };\n};\nfunction AppointmentCalendarComponent_div_22_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵlistener(\"click\", function AppointmentCalendarComponent_div_22_div_4_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r16);\n      const day_r7 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.onDayClick(day_r7));\n    });\n    i0.ɵɵelementStart(1, \"div\", 41);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AppointmentCalendarComponent_div_22_div_4_div_3_Template, 3, 2, \"div\", 42);\n    i0.ɵɵtemplate(4, AppointmentCalendarComponent_div_22_div_4_div_4_Template, 2, 0, \"div\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r7 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(4, _c0, !day_r7.isCurrentMonth, day_r7.isToday, day_r7.appointments.length > 0, day_r7.isCurrentMonth));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", day_r7.date.getDate(), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", day_r7.appointments.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", day_r7.appointments.length === 0 && day_r7.isCurrentMonth && day_r7.date >= ctx_r5.currentDate);\n  }\n}\nfunction AppointmentCalendarComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵtemplate(2, AppointmentCalendarComponent_div_22_div_2_Template, 2, 1, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 37);\n    i0.ɵɵtemplate(4, AppointmentCalendarComponent_div_22_div_4_Template, 5, 9, \"div\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.dayNames);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.calendarDays);\n  }\n}\nexport class AppointmentCalendarComponent {\n  constructor(appointmentService, authService, router) {\n    this.appointmentService = appointmentService;\n    this.authService = authService;\n    this.router = router;\n    this.currentDate = new Date();\n    this.currentMonth = new Date();\n    this.calendarDays = [];\n    this.appointments = [];\n    this.currentUser = null;\n    this.loading = false;\n    this.error = null;\n    this.monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];\n    this.dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\n  }\n  ngOnInit() {\n    this.currentUser = this.authService.getCurrentUser();\n    this.loadAppointments();\n    this.generateCalendar();\n  }\n  loadAppointments() {\n    this.loading = true;\n    this.error = null;\n    // Get appointments for the current month\n    const startDate = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth(), 1);\n    const endDate = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() + 1, 0);\n    this.appointmentService.getAppointments(undefined,\n    // status\n    undefined,\n    // type\n    startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0]).subscribe({\n      next: appointments => {\n        this.appointments = appointments;\n        this.generateCalendar();\n        this.loading = false;\n      },\n      error: error => {\n        this.error = 'Failed to load appointments.';\n        this.loading = false;\n        console.error('Error loading appointments:', error);\n      }\n    });\n  }\n  generateCalendar() {\n    const year = this.currentMonth.getFullYear();\n    const month = this.currentMonth.getMonth();\n    // First day of the month\n    const firstDay = new Date(year, month, 1);\n    // Last day of the month\n    const lastDay = new Date(year, month + 1, 0);\n    // Start from the first Sunday of the week containing the first day\n    const startDate = new Date(firstDay);\n    startDate.setDate(startDate.getDate() - startDate.getDay());\n    // End at the last Saturday of the week containing the last day\n    const endDate = new Date(lastDay);\n    endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));\n    this.calendarDays = [];\n    const currentDate = new Date(startDate);\n    while (currentDate <= endDate) {\n      const dayAppointments = this.getAppointmentsForDate(currentDate);\n      this.calendarDays.push({\n        date: new Date(currentDate),\n        isCurrentMonth: currentDate.getMonth() === month,\n        isToday: this.isSameDay(currentDate, new Date()),\n        appointments: dayAppointments\n      });\n      currentDate.setDate(currentDate.getDate() + 1);\n    }\n  }\n  getAppointmentsForDate(date) {\n    const dateString = date.toISOString().split('T')[0];\n    return this.appointments.filter(appointment => appointment.date === dateString);\n  }\n  isSameDay(date1, date2) {\n    return date1.getDate() === date2.getDate() && date1.getMonth() === date2.getMonth() && date1.getFullYear() === date2.getFullYear();\n  }\n  previousMonth() {\n    this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() - 1, 1);\n    this.loadAppointments();\n  }\n  nextMonth() {\n    this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() + 1, 1);\n    this.loadAppointments();\n  }\n  goToToday() {\n    this.currentMonth = new Date();\n    this.loadAppointments();\n  }\n  onDayClick(day) {\n    if (day.appointments.length > 0) {\n      // If there are appointments, show the first one\n      this.router.navigate(['/appointments', day.appointments[0].id]);\n    } else if (day.isCurrentMonth && day.date >= new Date()) {\n      // If it's a future date in current month, navigate to booking\n      this.router.navigate(['/appointments/book'], {\n        queryParams: {\n          date: day.date.toISOString().split('T')[0]\n        }\n      });\n    }\n  }\n  getStatusDisplayName(status) {\n    return this.appointmentService.getStatusDisplayName(status);\n  }\n  getStatusBadgeClass(status) {\n    return this.appointmentService.getStatusBadgeClass(status);\n  }\n  formatTime(timeString) {\n    const [hours, minutes] = timeString.split(':');\n    const hour = parseInt(hours);\n    const ampm = hour >= 12 ? 'PM' : 'AM';\n    const displayHour = hour % 12 || 12;\n    return `${displayHour}:${minutes} ${ampm}`;\n  }\n  getCurrentMonthYear() {\n    return `${this.monthNames[this.currentMonth.getMonth()]} ${this.currentMonth.getFullYear()}`;\n  }\n  isDoctor() {\n    return this.currentUser?.role === 'DOCTOR';\n  }\n  isPatient() {\n    return this.currentUser?.role === 'PATIENT';\n  }\n  static {\n    this.ɵfac = function AppointmentCalendarComponent_Factory(t) {\n      return new (t || AppointmentCalendarComponent)(i0.ɵɵdirectiveInject(i1.AppointmentService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppointmentCalendarComponent,\n      selectors: [[\"app-appointment-calendar\"]],\n      decls: 43,\n      vars: 5,\n      consts: [[1, \"container-fluid\", \"py-4\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"card-title\", \"mb-0\"], [1, \"fas\", \"fa-calendar\", \"me-2\"], [\"class\", \"btn btn-primary\", \"routerLink\", \"/appointments/book\", 4, \"ngIf\"], [1, \"card-body\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [1, \"calendar-nav\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"d-flex\", \"align-items-center\"], [1, \"mb-0\", \"me-3\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-chevron-right\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"calendar-grid\", 4, \"ngIf\"], [1, \"calendar-legend\", \"mt-4\"], [1, \"d-flex\", \"flex-wrap\", \"gap-3\"], [1, \"legend-item\"], [1, \"legend-color\", \"badge-primary\"], [1, \"legend-color\", \"badge-warning\"], [1, \"legend-color\", \"badge-success\"], [1, \"legend-color\", \"badge-danger\"], [\"routerLink\", \"/appointments/book\", 1, \"btn\", \"btn-primary\"], [1, \"fas\", \"fa-plus\", \"me-2\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-2\"], [1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\", \"text-muted\"], [1, \"calendar-grid\"], [1, \"calendar-header\"], [\"class\", \"day-header\", 4, \"ngFor\", \"ngForOf\"], [1, \"calendar-body\"], [\"class\", \"calendar-day\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"day-header\"], [1, \"calendar-day\", 3, \"ngClass\", \"click\"], [1, \"day-number\"], [\"class\", \"appointments-container\", 4, \"ngIf\"], [\"class\", \"add-appointment\", 4, \"ngIf\"], [1, \"appointments-container\"], [\"class\", \"appointment-item\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"more-appointments\", 4, \"ngIf\"], [1, \"appointment-item\", 3, \"ngClass\"], [1, \"appointment-time\"], [1, \"appointment-title\"], [1, \"more-appointments\"], [1, \"add-appointment\"], [1, \"fas\", \"fa-plus\"]],\n      template: function AppointmentCalendarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h4\", 5);\n          i0.ɵɵelement(6, \"i\", 6);\n          i0.ɵɵtext(7, \"Appointment Calendar \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, AppointmentCalendarComponent_button_8_Template, 3, 0, \"button\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 8);\n          i0.ɵɵtemplate(10, AppointmentCalendarComponent_div_10_Template, 3, 1, \"div\", 9);\n          i0.ɵɵelementStart(11, \"div\", 10)(12, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function AppointmentCalendarComponent_Template_button_click_12_listener() {\n            return ctx.previousMonth();\n          });\n          i0.ɵɵelement(13, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 13)(15, \"h5\", 14);\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function AppointmentCalendarComponent_Template_button_click_17_listener() {\n            return ctx.goToToday();\n          });\n          i0.ɵɵtext(18, \" Today \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function AppointmentCalendarComponent_Template_button_click_19_listener() {\n            return ctx.nextMonth();\n          });\n          i0.ɵɵelement(20, \"i\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(21, AppointmentCalendarComponent_div_21_Template, 6, 0, \"div\", 17);\n          i0.ɵɵtemplate(22, AppointmentCalendarComponent_div_22_Template, 5, 2, \"div\", 18);\n          i0.ɵɵelementStart(23, \"div\", 19)(24, \"h6\");\n          i0.ɵɵtext(25, \"Legend:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 20)(27, \"div\", 21);\n          i0.ɵɵelement(28, \"span\", 22);\n          i0.ɵɵelementStart(29, \"span\");\n          i0.ɵɵtext(30, \"Confirmed\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 21);\n          i0.ɵɵelement(32, \"span\", 23);\n          i0.ɵɵelementStart(33, \"span\");\n          i0.ɵɵtext(34, \"Pending\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"div\", 21);\n          i0.ɵɵelement(36, \"span\", 24);\n          i0.ɵɵelementStart(37, \"span\");\n          i0.ɵɵtext(38, \"Completed\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 21);\n          i0.ɵɵelement(40, \"span\", 25);\n          i0.ɵɵelementStart(41, \"span\");\n          i0.ɵɵtext(42, \"Cancelled\");\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isPatient());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.getCurrentMonthYear());\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i3.RouterLink],\n      styles: [\".calendar-grid[_ngcontent-%COMP%] {\\n  border: 1px solid #e3e6f0;\\n  border-radius: 0.5rem;\\n  overflow: hidden;\\n}\\n\\n.calendar-header[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n  background: #f8f9fc;\\n  border-bottom: 1px solid #e3e6f0;\\n}\\n\\n.day-header[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  text-align: center;\\n  font-weight: 600;\\n  color: #5a5c69;\\n  border-right: 1px solid #e3e6f0;\\n}\\n\\n.day-header[_ngcontent-%COMP%]:last-child {\\n  border-right: none;\\n}\\n\\n.calendar-body[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n}\\n\\n.calendar-day[_ngcontent-%COMP%] {\\n  min-height: 120px;\\n  padding: 0.5rem;\\n  border-right: 1px solid #e3e6f0;\\n  border-bottom: 1px solid #e3e6f0;\\n  position: relative;\\n  background: white;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.calendar-day[_ngcontent-%COMP%]:nth-child(7n) {\\n  border-right: none;\\n}\\n\\n.calendar-day.clickable[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\n.calendar-day.clickable[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fc;\\n}\\n\\n.calendar-day.other-month[_ngcontent-%COMP%] {\\n  background: #f8f9fc;\\n  color: #858796;\\n}\\n\\n.calendar-day.today[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  border: 2px solid #667eea;\\n}\\n\\n.calendar-day.has-appointments[_ngcontent-%COMP%] {\\n  background: #fff3cd;\\n}\\n\\n.day-number[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n  color: #5a5c69;\\n}\\n\\n.appointments-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n\\n.appointment-item[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-size: 0.75rem;\\n  color: white;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.appointment-time[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  display: block;\\n}\\n\\n.appointment-title[_ngcontent-%COMP%] {\\n  display: block;\\n  opacity: 0.9;\\n}\\n\\n.more-appointments[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: #858796;\\n  text-align: center;\\n  margin-top: 0.25rem;\\n}\\n\\n.add-appointment[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0.5rem;\\n  right: 0.5rem;\\n  color: #858796;\\n  font-size: 0.8rem;\\n  opacity: 0;\\n  transition: opacity 0.2s ease;\\n}\\n\\n.calendar-day.clickable[_ngcontent-%COMP%]:hover   .add-appointment[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.badge-primary[_ngcontent-%COMP%] {\\n  background-color: #4e73df;\\n}\\n\\n.badge-warning[_ngcontent-%COMP%] {\\n  background-color: #f6c23e;\\n  color: #1a1a1a;\\n}\\n\\n.badge-success[_ngcontent-%COMP%] {\\n  background-color: #1cc88a;\\n}\\n\\n.badge-danger[_ngcontent-%COMP%] {\\n  background-color: #e74a3b;\\n}\\n\\n.badge-info[_ngcontent-%COMP%] {\\n  background-color: #36b9cc;\\n}\\n\\n.badge-secondary[_ngcontent-%COMP%] {\\n  background-color: #858796;\\n}\\n\\n.calendar-legend[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e3e6f0;\\n  padding-top: 1rem;\\n}\\n\\n.legend-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-size: 0.9rem;\\n}\\n\\n.legend-color[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  border-radius: 0.25rem;\\n  display: inline-block;\\n}\\n\\n.calendar-nav[_ngcontent-%COMP%] {\\n  background: #f8f9fc;\\n  padding: 1rem;\\n  border-radius: 0.5rem;\\n  border: 1px solid #e3e6f0;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border: none;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\\n  transform: translateY(-1px);\\n}\\n\\n.btn-outline-primary[_ngcontent-%COMP%] {\\n  border-color: #667eea;\\n  color: #667eea;\\n}\\n\\n.btn-outline-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #667eea;\\n  border-color: #667eea;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  border: 1px solid #e3e6f0;\\n  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);\\n}\\n\\n.alert-danger[_ngcontent-%COMP%] {\\n  border-left: 4px solid #e74a3b;\\n}\\n\\n.spinner-border[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "dayName_r6", "ɵɵproperty", "ctx_r10", "getStatusBadgeClass", "appointment_r12", "status", "formatTime", "startTime", "isDoctor", "patient", "fullName", "doctor", "day_r7", "appointments", "length", "ɵɵtemplate", "AppointmentCalendarComponent_div_22_div_4_div_3_div_1_Template", "AppointmentCalendarComponent_div_22_div_4_div_3_div_2_Template", "slice", "ɵɵlistener", "AppointmentCalendarComponent_div_22_div_4_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r16", "$implicit", "ctx_r15", "ɵɵnextContext", "ɵɵresetView", "onDayClick", "AppointmentCalendarComponent_div_22_div_4_div_3_Template", "AppointmentCalendarComponent_div_22_div_4_div_4_Template", "ɵɵpureFunction4", "_c0", "isCurrentMonth", "isToday", "date", "getDate", "ctx_r5", "currentDate", "AppointmentCalendarComponent_div_22_div_2_Template", "AppointmentCalendarComponent_div_22_div_4_Template", "ctx_r3", "dayNames", "calendarDays", "AppointmentCalendarComponent", "constructor", "appointmentService", "authService", "router", "Date", "currentMonth", "currentUser", "loading", "monthNames", "ngOnInit", "getCurrentUser", "loadAppointments", "generateCalendar", "startDate", "getFullYear", "getMonth", "endDate", "getAppointments", "undefined", "toISOString", "split", "subscribe", "next", "console", "year", "month", "firstDay", "lastDay", "setDate", "getDay", "dayAppointments", "getAppointmentsForDate", "push", "isSameDay", "dateString", "filter", "appointment", "date1", "date2", "previousMonth", "nextMonth", "goToToday", "day", "navigate", "id", "queryParams", "getStatusDisplayName", "timeString", "hours", "minutes", "hour", "parseInt", "ampm", "displayHour", "getCurrentMonthYear", "role", "isPatient", "ɵɵdirectiveInject", "i1", "AppointmentService", "i2", "AuthService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "AppointmentCalendarComponent_Template", "rf", "ctx", "AppointmentCalendarComponent_button_8_Template", "AppointmentCalendarComponent_div_10_Template", "AppointmentCalendarComponent_Template_button_click_12_listener", "AppointmentCalendarComponent_Template_button_click_17_listener", "AppointmentCalendarComponent_Template_button_click_19_listener", "AppointmentCalendarComponent_div_21_Template", "AppointmentCalendarComponent_div_22_Template", "ɵɵtextInterpolate"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/appointments/appointment-calendar/appointment-calendar.component.ts", "/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/appointments/appointment-calendar/appointment-calendar.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AppointmentService } from '../../core/services/appointment.service';\nimport { AuthService } from '../../core/services/auth.service';\nimport { Appointment, AppointmentStatus } from '../../core/models/appointment.model';\nimport { User } from '../../core/models/user.model';\n\ninterface CalendarDay {\n  date: Date;\n  isCurrentMonth: boolean;\n  isToday: boolean;\n  appointments: Appointment[];\n}\n\n@Component({\n  selector: 'app-appointment-calendar',\n  templateUrl: './appointment-calendar.component.html',\n  styleUrls: ['./appointment-calendar.component.scss']\n})\nexport class AppointmentCalendarComponent implements OnInit {\n  currentDate = new Date();\n  currentMonth = new Date();\n  calendarDays: CalendarDay[] = [];\n  appointments: Appointment[] = [];\n  currentUser: User | null = null;\n  loading = false;\n  error: string | null = null;\n\n  monthNames = [\n    'January', 'February', 'March', 'April', 'May', 'June',\n    'July', 'August', 'September', 'October', 'November', 'December'\n  ];\n\n  dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\n\n  constructor(\n    private appointmentService: AppointmentService,\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.currentUser = this.authService.getCurrentUser();\n    this.loadAppointments();\n    this.generateCalendar();\n  }\n\n  loadAppointments(): void {\n    this.loading = true;\n    this.error = null;\n\n    // Get appointments for the current month\n    const startDate = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth(), 1);\n    const endDate = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() + 1, 0);\n\n    this.appointmentService.getAppointments(\n      undefined, // status\n      undefined, // type\n      startDate.toISOString().split('T')[0],\n      endDate.toISOString().split('T')[0]\n    ).subscribe({\n      next: (appointments) => {\n        this.appointments = appointments;\n        this.generateCalendar();\n        this.loading = false;\n      },\n      error: (error) => {\n        this.error = 'Failed to load appointments.';\n        this.loading = false;\n        console.error('Error loading appointments:', error);\n      }\n    });\n  }\n\n  generateCalendar(): void {\n    const year = this.currentMonth.getFullYear();\n    const month = this.currentMonth.getMonth();\n    \n    // First day of the month\n    const firstDay = new Date(year, month, 1);\n    // Last day of the month\n    const lastDay = new Date(year, month + 1, 0);\n    \n    // Start from the first Sunday of the week containing the first day\n    const startDate = new Date(firstDay);\n    startDate.setDate(startDate.getDate() - startDate.getDay());\n    \n    // End at the last Saturday of the week containing the last day\n    const endDate = new Date(lastDay);\n    endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));\n    \n    this.calendarDays = [];\n    const currentDate = new Date(startDate);\n    \n    while (currentDate <= endDate) {\n      const dayAppointments = this.getAppointmentsForDate(currentDate);\n      \n      this.calendarDays.push({\n        date: new Date(currentDate),\n        isCurrentMonth: currentDate.getMonth() === month,\n        isToday: this.isSameDay(currentDate, new Date()),\n        appointments: dayAppointments\n      });\n      \n      currentDate.setDate(currentDate.getDate() + 1);\n    }\n  }\n\n  getAppointmentsForDate(date: Date): Appointment[] {\n    const dateString = date.toISOString().split('T')[0];\n    return this.appointments.filter(appointment => appointment.date === dateString);\n  }\n\n  isSameDay(date1: Date, date2: Date): boolean {\n    return date1.getDate() === date2.getDate() &&\n           date1.getMonth() === date2.getMonth() &&\n           date1.getFullYear() === date2.getFullYear();\n  }\n\n  previousMonth(): void {\n    this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() - 1, 1);\n    this.loadAppointments();\n  }\n\n  nextMonth(): void {\n    this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() + 1, 1);\n    this.loadAppointments();\n  }\n\n  goToToday(): void {\n    this.currentMonth = new Date();\n    this.loadAppointments();\n  }\n\n  onDayClick(day: CalendarDay): void {\n    if (day.appointments.length > 0) {\n      // If there are appointments, show the first one\n      this.router.navigate(['/appointments', day.appointments[0].id]);\n    } else if (day.isCurrentMonth && day.date >= new Date()) {\n      // If it's a future date in current month, navigate to booking\n      this.router.navigate(['/appointments/book'], {\n        queryParams: { date: day.date.toISOString().split('T')[0] }\n      });\n    }\n  }\n\n  getStatusDisplayName(status: AppointmentStatus): string {\n    return this.appointmentService.getStatusDisplayName(status);\n  }\n\n  getStatusBadgeClass(status: AppointmentStatus): string {\n    return this.appointmentService.getStatusBadgeClass(status);\n  }\n\n  formatTime(timeString: string): string {\n    const [hours, minutes] = timeString.split(':');\n    const hour = parseInt(hours);\n    const ampm = hour >= 12 ? 'PM' : 'AM';\n    const displayHour = hour % 12 || 12;\n    return `${displayHour}:${minutes} ${ampm}`;\n  }\n\n  getCurrentMonthYear(): string {\n    return `${this.monthNames[this.currentMonth.getMonth()]} ${this.currentMonth.getFullYear()}`;\n  }\n\n  isDoctor(): boolean {\n    return this.currentUser?.role === 'DOCTOR';\n  }\n\n  isPatient(): boolean {\n    return this.currentUser?.role === 'PATIENT';\n  }\n}\n", "<div class=\"container-fluid py-4\">\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"card\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h4 class=\"card-title mb-0\">\n            <i class=\"fas fa-calendar me-2\"></i>Appointment Calendar\n          </h4>\n          <button \n            *ngIf=\"isPatient()\" \n            class=\"btn btn-primary\"\n            routerLink=\"/appointments/book\">\n            <i class=\"fas fa-plus me-2\"></i>\n            Book Appointment\n          </button>\n        </div>\n        <div class=\"card-body\">\n          <!-- Error Message -->\n          <div *ngIf=\"error\" class=\"alert alert-danger\" role=\"alert\">\n            <i class=\"fas fa-exclamation-triangle me-2\"></i>\n            {{ error }}\n          </div>\n\n          <!-- Calendar Navigation -->\n          <div class=\"calendar-nav d-flex justify-content-between align-items-center mb-4\">\n            <button class=\"btn btn-outline-primary\" (click)=\"previousMonth()\">\n              <i class=\"fas fa-chevron-left\"></i>\n            </button>\n            \n            <div class=\"d-flex align-items-center\">\n              <h5 class=\"mb-0 me-3\">{{ getCurrentMonthYear() }}</h5>\n              <button class=\"btn btn-sm btn-outline-secondary\" (click)=\"goToToday()\">\n                Today\n              </button>\n            </div>\n            \n            <button class=\"btn btn-outline-primary\" (click)=\"nextMonth()\">\n              <i class=\"fas fa-chevron-right\"></i>\n            </button>\n          </div>\n\n          <!-- Loading Spinner -->\n          <div *ngIf=\"loading\" class=\"text-center py-4\">\n            <div class=\"spinner-border text-primary\" role=\"status\">\n              <span class=\"visually-hidden\">Loading...</span>\n            </div>\n            <p class=\"mt-2 text-muted\">Loading calendar...</p>\n          </div>\n\n          <!-- Calendar Grid -->\n          <div *ngIf=\"!loading\" class=\"calendar-grid\">\n            <!-- Day Headers -->\n            <div class=\"calendar-header\">\n              <div class=\"day-header\" *ngFor=\"let dayName of dayNames\">\n                {{ dayName }}\n              </div>\n            </div>\n\n            <!-- Calendar Days -->\n            <div class=\"calendar-body\">\n              <div \n                class=\"calendar-day\"\n                *ngFor=\"let day of calendarDays\"\n                [ngClass]=\"{\n                  'other-month': !day.isCurrentMonth,\n                  'today': day.isToday,\n                  'has-appointments': day.appointments.length > 0,\n                  'clickable': day.isCurrentMonth\n                }\"\n                (click)=\"onDayClick(day)\">\n                \n                <div class=\"day-number\">\n                  {{ day.date.getDate() }}\n                </div>\n                \n                <div class=\"appointments-container\" *ngIf=\"day.appointments.length > 0\">\n                  <div \n                    class=\"appointment-item\"\n                    *ngFor=\"let appointment of day.appointments.slice(0, 2)\"\n                    [ngClass]=\"getStatusBadgeClass(appointment.status)\">\n                    <span class=\"appointment-time\">\n                      {{ formatTime(appointment.startTime) }}\n                    </span>\n                    <span class=\"appointment-title\">\n                      {{ isDoctor() ? appointment.patient.fullName : appointment.doctor.fullName }}\n                    </span>\n                  </div>\n                  \n                  <div \n                    *ngIf=\"day.appointments.length > 2\" \n                    class=\"more-appointments\">\n                    +{{ day.appointments.length - 2 }} more\n                  </div>\n                </div>\n                \n                <div \n                  *ngIf=\"day.appointments.length === 0 && day.isCurrentMonth && day.date >= currentDate\"\n                  class=\"add-appointment\">\n                  <i class=\"fas fa-plus\"></i>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Legend -->\n          <div class=\"calendar-legend mt-4\">\n            <h6>Legend:</h6>\n            <div class=\"d-flex flex-wrap gap-3\">\n              <div class=\"legend-item\">\n                <span class=\"legend-color badge-primary\"></span>\n                <span>Confirmed</span>\n              </div>\n              <div class=\"legend-item\">\n                <span class=\"legend-color badge-warning\"></span>\n                <span>Pending</span>\n              </div>\n              <div class=\"legend-item\">\n                <span class=\"legend-color badge-success\"></span>\n                <span>Completed</span>\n              </div>\n              <div class=\"legend-item\">\n                <span class=\"legend-color badge-danger\"></span>\n                <span>Cancelled</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;ICQUA,EAAA,CAAAC,cAAA,iBAGkC;IAChCD,EAAA,CAAAE,SAAA,YAAgC;IAChCF,EAAA,CAAAG,MAAA,yBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IAITJ,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAE,SAAA,YAAgD;IAChDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAqBAR,EAAA,CAAAC,cAAA,cAA8C;IAEZD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEjDJ,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAOhDJ,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAG,UAAA,MACF;;;;;IAqBIT,EAAA,CAAAC,cAAA,cAGsD;IAElDD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAC,cAAA,eAAgC;IAC9BD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IANPJ,EAAA,CAAAU,UAAA,YAAAC,OAAA,CAAAC,mBAAA,CAAAC,eAAA,CAAAC,MAAA,EAAmD;IAEjDd,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAK,OAAA,CAAAI,UAAA,CAAAF,eAAA,CAAAG,SAAA,OACF;IAEEhB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAK,OAAA,CAAAM,QAAA,KAAAJ,eAAA,CAAAK,OAAA,CAAAC,QAAA,GAAAN,eAAA,CAAAO,MAAA,CAAAD,QAAA,MACF;;;;;IAGFnB,EAAA,CAAAC,cAAA,cAE4B;IAC1BD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,OAAAe,MAAA,CAAAC,YAAA,CAAAC,MAAA,eACF;;;;;IAjBFvB,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAwB,UAAA,IAAAC,8DAAA,kBAUM;IAENzB,EAAA,CAAAwB,UAAA,IAAAE,8DAAA,kBAIM;IACR1B,EAAA,CAAAI,YAAA,EAAM;;;;IAfsBJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAU,UAAA,YAAAW,MAAA,CAAAC,YAAA,CAAAK,KAAA,OAA+B;IAWtD3B,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAU,UAAA,SAAAW,MAAA,CAAAC,YAAA,CAAAC,MAAA,KAAiC;;;;;IAMtCvB,EAAA,CAAAC,cAAA,cAE0B;IACxBD,EAAA,CAAAE,SAAA,YAA2B;IAC7BF,EAAA,CAAAI,YAAA,EAAM;;;;;;;;;;;;;;IAvCRJ,EAAA,CAAAC,cAAA,cAS4B;IAA1BD,EAAA,CAAA4B,UAAA,mBAAAC,wEAAA;MAAA,MAAAC,WAAA,GAAA9B,EAAA,CAAA+B,aAAA,CAAAC,IAAA;MAAA,MAAAX,MAAA,GAAAS,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAlC,EAAA,CAAAmC,aAAA;MAAA,OAASnC,EAAA,CAAAoC,WAAA,CAAAF,OAAA,CAAAG,UAAA,CAAAhB,MAAA,CAAe;IAAA,EAAC;IAEzBrB,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAwB,UAAA,IAAAc,wDAAA,kBAkBM;IAENtC,EAAA,CAAAwB,UAAA,IAAAe,wDAAA,kBAIM;IACRvC,EAAA,CAAAI,YAAA,EAAM;;;;;IArCJJ,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAwC,eAAA,IAAAC,GAAA,GAAApB,MAAA,CAAAqB,cAAA,EAAArB,MAAA,CAAAsB,OAAA,EAAAtB,MAAA,CAAAC,YAAA,CAAAC,MAAA,MAAAF,MAAA,CAAAqB,cAAA,EAKE;IAIA1C,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAe,MAAA,CAAAuB,IAAA,CAAAC,OAAA,QACF;IAEqC7C,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAU,UAAA,SAAAW,MAAA,CAAAC,YAAA,CAAAC,MAAA,KAAiC;IAqBnEvB,EAAA,CAAAK,SAAA,GAAoF;IAApFL,EAAA,CAAAU,UAAA,SAAAW,MAAA,CAAAC,YAAA,CAAAC,MAAA,UAAAF,MAAA,CAAAqB,cAAA,IAAArB,MAAA,CAAAuB,IAAA,IAAAE,MAAA,CAAAC,WAAA,CAAoF;;;;;IA9C7F/C,EAAA,CAAAC,cAAA,cAA4C;IAGxCD,EAAA,CAAAwB,UAAA,IAAAwB,kDAAA,kBAEM;IACRhD,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAwB,UAAA,IAAAyB,kDAAA,kBAwCM;IACRjD,EAAA,CAAAI,YAAA,EAAM;;;;IAhDwCJ,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAU,UAAA,YAAAwC,MAAA,CAAAC,QAAA,CAAW;IASrCnD,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAU,UAAA,YAAAwC,MAAA,CAAAE,YAAA,CAAe;;;AD3C/C,OAAM,MAAOC,4BAA4B;EAgBvCC,YACUC,kBAAsC,EACtCC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAlBhB,KAAAV,WAAW,GAAG,IAAIW,IAAI,EAAE;IACxB,KAAAC,YAAY,GAAG,IAAID,IAAI,EAAE;IACzB,KAAAN,YAAY,GAAkB,EAAE;IAChC,KAAA9B,YAAY,GAAkB,EAAE;IAChC,KAAAsC,WAAW,GAAgB,IAAI;IAC/B,KAAAC,OAAO,GAAG,KAAK;IACf,KAAArD,KAAK,GAAkB,IAAI;IAE3B,KAAAsD,UAAU,GAAG,CACX,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EACtD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CACjE;IAED,KAAAX,QAAQ,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAMzD;EAEHY,QAAQA,CAAA;IACN,IAAI,CAACH,WAAW,GAAG,IAAI,CAACJ,WAAW,CAACQ,cAAc,EAAE;IACpD,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAD,gBAAgBA,CAAA;IACd,IAAI,CAACJ,OAAO,GAAG,IAAI;IACnB,IAAI,CAACrD,KAAK,GAAG,IAAI;IAEjB;IACA,MAAM2D,SAAS,GAAG,IAAIT,IAAI,CAAC,IAAI,CAACC,YAAY,CAACS,WAAW,EAAE,EAAE,IAAI,CAACT,YAAY,CAACU,QAAQ,EAAE,EAAE,CAAC,CAAC;IAC5F,MAAMC,OAAO,GAAG,IAAIZ,IAAI,CAAC,IAAI,CAACC,YAAY,CAACS,WAAW,EAAE,EAAE,IAAI,CAACT,YAAY,CAACU,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;IAE9F,IAAI,CAACd,kBAAkB,CAACgB,eAAe,CACrCC,SAAS;IAAE;IACXA,SAAS;IAAE;IACXL,SAAS,CAACM,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EACrCJ,OAAO,CAACG,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACpC,CAACC,SAAS,CAAC;MACVC,IAAI,EAAGtD,YAAY,IAAI;QACrB,IAAI,CAACA,YAAY,GAAGA,YAAY;QAChC,IAAI,CAAC4C,gBAAgB,EAAE;QACvB,IAAI,CAACL,OAAO,GAAG,KAAK;MACtB,CAAC;MACDrD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,8BAA8B;QAC3C,IAAI,CAACqD,OAAO,GAAG,KAAK;QACpBgB,OAAO,CAACrE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;KACD,CAAC;EACJ;EAEA0D,gBAAgBA,CAAA;IACd,MAAMY,IAAI,GAAG,IAAI,CAACnB,YAAY,CAACS,WAAW,EAAE;IAC5C,MAAMW,KAAK,GAAG,IAAI,CAACpB,YAAY,CAACU,QAAQ,EAAE;IAE1C;IACA,MAAMW,QAAQ,GAAG,IAAItB,IAAI,CAACoB,IAAI,EAAEC,KAAK,EAAE,CAAC,CAAC;IACzC;IACA,MAAME,OAAO,GAAG,IAAIvB,IAAI,CAACoB,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;IAE5C;IACA,MAAMZ,SAAS,GAAG,IAAIT,IAAI,CAACsB,QAAQ,CAAC;IACpCb,SAAS,CAACe,OAAO,CAACf,SAAS,CAACtB,OAAO,EAAE,GAAGsB,SAAS,CAACgB,MAAM,EAAE,CAAC;IAE3D;IACA,MAAMb,OAAO,GAAG,IAAIZ,IAAI,CAACuB,OAAO,CAAC;IACjCX,OAAO,CAACY,OAAO,CAACZ,OAAO,CAACzB,OAAO,EAAE,IAAI,CAAC,GAAGyB,OAAO,CAACa,MAAM,EAAE,CAAC,CAAC;IAE3D,IAAI,CAAC/B,YAAY,GAAG,EAAE;IACtB,MAAML,WAAW,GAAG,IAAIW,IAAI,CAACS,SAAS,CAAC;IAEvC,OAAOpB,WAAW,IAAIuB,OAAO,EAAE;MAC7B,MAAMc,eAAe,GAAG,IAAI,CAACC,sBAAsB,CAACtC,WAAW,CAAC;MAEhE,IAAI,CAACK,YAAY,CAACkC,IAAI,CAAC;QACrB1C,IAAI,EAAE,IAAIc,IAAI,CAACX,WAAW,CAAC;QAC3BL,cAAc,EAAEK,WAAW,CAACsB,QAAQ,EAAE,KAAKU,KAAK;QAChDpC,OAAO,EAAE,IAAI,CAAC4C,SAAS,CAACxC,WAAW,EAAE,IAAIW,IAAI,EAAE,CAAC;QAChDpC,YAAY,EAAE8D;OACf,CAAC;MAEFrC,WAAW,CAACmC,OAAO,CAACnC,WAAW,CAACF,OAAO,EAAE,GAAG,CAAC,CAAC;;EAElD;EAEAwC,sBAAsBA,CAACzC,IAAU;IAC/B,MAAM4C,UAAU,GAAG5C,IAAI,CAAC6B,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACnD,OAAO,IAAI,CAACpD,YAAY,CAACmE,MAAM,CAACC,WAAW,IAAIA,WAAW,CAAC9C,IAAI,KAAK4C,UAAU,CAAC;EACjF;EAEAD,SAASA,CAACI,KAAW,EAAEC,KAAW;IAChC,OAAOD,KAAK,CAAC9C,OAAO,EAAE,KAAK+C,KAAK,CAAC/C,OAAO,EAAE,IACnC8C,KAAK,CAACtB,QAAQ,EAAE,KAAKuB,KAAK,CAACvB,QAAQ,EAAE,IACrCsB,KAAK,CAACvB,WAAW,EAAE,KAAKwB,KAAK,CAACxB,WAAW,EAAE;EACpD;EAEAyB,aAAaA,CAAA;IACX,IAAI,CAAClC,YAAY,GAAG,IAAID,IAAI,CAAC,IAAI,CAACC,YAAY,CAACS,WAAW,EAAE,EAAE,IAAI,CAACT,YAAY,CAACU,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;IAClG,IAAI,CAACJ,gBAAgB,EAAE;EACzB;EAEA6B,SAASA,CAAA;IACP,IAAI,CAACnC,YAAY,GAAG,IAAID,IAAI,CAAC,IAAI,CAACC,YAAY,CAACS,WAAW,EAAE,EAAE,IAAI,CAACT,YAAY,CAACU,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;IAClG,IAAI,CAACJ,gBAAgB,EAAE;EACzB;EAEA8B,SAASA,CAAA;IACP,IAAI,CAACpC,YAAY,GAAG,IAAID,IAAI,EAAE;IAC9B,IAAI,CAACO,gBAAgB,EAAE;EACzB;EAEA5B,UAAUA,CAAC2D,GAAgB;IACzB,IAAIA,GAAG,CAAC1E,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;MAC/B;MACA,IAAI,CAACkC,MAAM,CAACwC,QAAQ,CAAC,CAAC,eAAe,EAAED,GAAG,CAAC1E,YAAY,CAAC,CAAC,CAAC,CAAC4E,EAAE,CAAC,CAAC;KAChE,MAAM,IAAIF,GAAG,CAACtD,cAAc,IAAIsD,GAAG,CAACpD,IAAI,IAAI,IAAIc,IAAI,EAAE,EAAE;MACvD;MACA,IAAI,CAACD,MAAM,CAACwC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE;QAC3CE,WAAW,EAAE;UAAEvD,IAAI,EAAEoD,GAAG,CAACpD,IAAI,CAAC6B,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAAC;OAC1D,CAAC;;EAEN;EAEA0B,oBAAoBA,CAACtF,MAAyB;IAC5C,OAAO,IAAI,CAACyC,kBAAkB,CAAC6C,oBAAoB,CAACtF,MAAM,CAAC;EAC7D;EAEAF,mBAAmBA,CAACE,MAAyB;IAC3C,OAAO,IAAI,CAACyC,kBAAkB,CAAC3C,mBAAmB,CAACE,MAAM,CAAC;EAC5D;EAEAC,UAAUA,CAACsF,UAAkB;IAC3B,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGF,UAAU,CAAC3B,KAAK,CAAC,GAAG,CAAC;IAC9C,MAAM8B,IAAI,GAAGC,QAAQ,CAACH,KAAK,CAAC;IAC5B,MAAMI,IAAI,GAAGF,IAAI,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI;IACrC,MAAMG,WAAW,GAAGH,IAAI,GAAG,EAAE,IAAI,EAAE;IACnC,OAAO,GAAGG,WAAW,IAAIJ,OAAO,IAAIG,IAAI,EAAE;EAC5C;EAEAE,mBAAmBA,CAAA;IACjB,OAAO,GAAG,IAAI,CAAC9C,UAAU,CAAC,IAAI,CAACH,YAAY,CAACU,QAAQ,EAAE,CAAC,IAAI,IAAI,CAACV,YAAY,CAACS,WAAW,EAAE,EAAE;EAC9F;EAEAnD,QAAQA,CAAA;IACN,OAAO,IAAI,CAAC2C,WAAW,EAAEiD,IAAI,KAAK,QAAQ;EAC5C;EAEAC,SAASA,CAAA;IACP,OAAO,IAAI,CAAClD,WAAW,EAAEiD,IAAI,KAAK,SAAS;EAC7C;;;uBAzJWxD,4BAA4B,EAAArD,EAAA,CAAA+G,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAjH,EAAA,CAAA+G,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAnH,EAAA,CAAA+G,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAA5BhE,4BAA4B;MAAAiE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnBzC5H,EAAA,CAAAC,cAAA,aAAkC;UAMtBD,EAAA,CAAAE,SAAA,WAAoC;UAAAF,EAAA,CAAAG,MAAA,4BACtC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAwB,UAAA,IAAAsG,8CAAA,oBAMS;UACX9H,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,aAAuB;UAErBD,EAAA,CAAAwB,UAAA,KAAAuG,4CAAA,iBAGM;UAGN/H,EAAA,CAAAC,cAAA,eAAiF;UACvCD,EAAA,CAAA4B,UAAA,mBAAAoG,+DAAA;YAAA,OAASH,GAAA,CAAAhC,aAAA,EAAe;UAAA,EAAC;UAC/D7F,EAAA,CAAAE,SAAA,aAAmC;UACrCF,EAAA,CAAAI,YAAA,EAAS;UAETJ,EAAA,CAAAC,cAAA,eAAuC;UACfD,EAAA,CAAAG,MAAA,IAA2B;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACtDJ,EAAA,CAAAC,cAAA,kBAAuE;UAAtBD,EAAA,CAAA4B,UAAA,mBAAAqG,+DAAA;YAAA,OAASJ,GAAA,CAAA9B,SAAA,EAAW;UAAA,EAAC;UACpE/F,EAAA,CAAAG,MAAA,eACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAGXJ,EAAA,CAAAC,cAAA,kBAA8D;UAAtBD,EAAA,CAAA4B,UAAA,mBAAAsG,+DAAA;YAAA,OAASL,GAAA,CAAA/B,SAAA,EAAW;UAAA,EAAC;UAC3D9F,EAAA,CAAAE,SAAA,aAAoC;UACtCF,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAwB,UAAA,KAAA2G,4CAAA,kBAKM;UAGNnI,EAAA,CAAAwB,UAAA,KAAA4G,4CAAA,kBAoDM;UAGNpI,EAAA,CAAAC,cAAA,eAAkC;UAC5BD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChBJ,EAAA,CAAAC,cAAA,eAAoC;UAEhCD,EAAA,CAAAE,SAAA,gBAAgD;UAChDF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAExBJ,EAAA,CAAAC,cAAA,eAAyB;UACvBD,EAAA,CAAAE,SAAA,gBAAgD;UAChDF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAEtBJ,EAAA,CAAAC,cAAA,eAAyB;UACvBD,EAAA,CAAAE,SAAA,gBAAgD;UAChDF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAExBJ,EAAA,CAAAC,cAAA,eAAyB;UACvBD,EAAA,CAAAE,SAAA,gBAA+C;UAC/CF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAO;;;UAjHzBJ,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAU,UAAA,SAAAmH,GAAA,CAAAf,SAAA,GAAiB;UASd9G,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAU,UAAA,SAAAmH,GAAA,CAAArH,KAAA,CAAW;UAYSR,EAAA,CAAAK,SAAA,GAA2B;UAA3BL,EAAA,CAAAqI,iBAAA,CAAAR,GAAA,CAAAjB,mBAAA,GAA2B;UAY/C5G,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAU,UAAA,SAAAmH,GAAA,CAAAhE,OAAA,CAAa;UAQb7D,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAU,UAAA,UAAAmH,GAAA,CAAAhE,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}