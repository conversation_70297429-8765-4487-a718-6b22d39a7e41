{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  urlUtils = require('../../utils/url'),\n  SenderReceiver = require('./sender-receiver');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:ajax-based');\n}\nfunction createAjaxSender(AjaxObject) {\n  return function (url, payload, callback) {\n    debug('create ajax sender', url, payload);\n    var opt = {};\n    if (typeof payload === 'string') {\n      opt.headers = {\n        'Content-type': 'text/plain'\n      };\n    }\n    var ajaxUrl = urlUtils.addPath(url, '/xhr_send');\n    var xo = new AjaxObject('POST', ajaxUrl, payload, opt);\n    xo.once('finish', function (status) {\n      debug('finish', status);\n      xo = null;\n      if (status !== 200 && status !== 204) {\n        return callback(new Error('http status ' + status));\n      }\n      callback();\n    });\n    return function () {\n      debug('abort');\n      xo.close();\n      xo = null;\n      var err = new Error('Aborted');\n      err.code = 1000;\n      callback(err);\n    };\n  };\n}\nfunction AjaxBasedTransport(transUrl, urlSuffix, Receiver, AjaxObject) {\n  SenderReceiver.call(this, transUrl, urlSuffix, createAjaxSender(AjaxObject), Receiver, AjaxObject);\n}\ninherits(AjaxBasedTransport, SenderReceiver);\nmodule.exports = AjaxBasedTransport;", "map": {"version": 3, "names": ["inherits", "require", "urlUtils", "SenderReceiver", "debug", "process", "env", "NODE_ENV", "createAjaxSender", "AjaxObject", "url", "payload", "callback", "opt", "headers", "ajaxUrl", "addPath", "xo", "once", "status", "Error", "close", "err", "code", "AjaxBasedTransport", "transUrl", "urlSuffix", "Receiver", "call", "module", "exports"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/sockjs-client/lib/transport/lib/ajax-based.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , urlUtils = require('../../utils/url')\n  , SenderReceiver = require('./sender-receiver')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:ajax-based');\n}\n\nfunction createAjaxSender(AjaxObject) {\n  return function(url, payload, callback) {\n    debug('create ajax sender', url, payload);\n    var opt = {};\n    if (typeof payload === 'string') {\n      opt.headers = {'Content-type': 'text/plain'};\n    }\n    var ajaxUrl = urlUtils.addPath(url, '/xhr_send');\n    var xo = new AjaxObject('POST', ajaxUrl, payload, opt);\n    xo.once('finish', function(status) {\n      debug('finish', status);\n      xo = null;\n\n      if (status !== 200 && status !== 204) {\n        return callback(new Error('http status ' + status));\n      }\n      callback();\n    });\n    return function() {\n      debug('abort');\n      xo.close();\n      xo = null;\n\n      var err = new Error('Aborted');\n      err.code = 1000;\n      callback(err);\n    };\n  };\n}\n\nfunction AjaxBasedTransport(transUrl, urlSuffix, Receiver, AjaxObject) {\n  SenderReceiver.call(this, transUrl, urlSuffix, createAjaxSender(AjaxObject), Receiver, AjaxObject);\n}\n\ninherits(AjaxBasedTransport, SenderReceiver);\n\nmodule.exports = AjaxBasedTransport;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,QAAQ,GAAGD,OAAO,CAAC,iBAAiB,CAAC;EACrCE,cAAc,GAAGF,OAAO,CAAC,mBAAmB,CAAC;AAGjD,IAAIG,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGH,OAAO,CAAC,OAAO,CAAC,CAAC,0BAA0B,CAAC;AACtD;AAEA,SAASO,gBAAgBA,CAACC,UAAU,EAAE;EACpC,OAAO,UAASC,GAAG,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IACtCR,KAAK,CAAC,oBAAoB,EAAEM,GAAG,EAAEC,OAAO,CAAC;IACzC,IAAIE,GAAG,GAAG,CAAC,CAAC;IACZ,IAAI,OAAOF,OAAO,KAAK,QAAQ,EAAE;MAC/BE,GAAG,CAACC,OAAO,GAAG;QAAC,cAAc,EAAE;MAAY,CAAC;IAC9C;IACA,IAAIC,OAAO,GAAGb,QAAQ,CAACc,OAAO,CAACN,GAAG,EAAE,WAAW,CAAC;IAChD,IAAIO,EAAE,GAAG,IAAIR,UAAU,CAAC,MAAM,EAAEM,OAAO,EAAEJ,OAAO,EAAEE,GAAG,CAAC;IACtDI,EAAE,CAACC,IAAI,CAAC,QAAQ,EAAE,UAASC,MAAM,EAAE;MACjCf,KAAK,CAAC,QAAQ,EAAEe,MAAM,CAAC;MACvBF,EAAE,GAAG,IAAI;MAET,IAAIE,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,EAAE;QACpC,OAAOP,QAAQ,CAAC,IAAIQ,KAAK,CAAC,cAAc,GAAGD,MAAM,CAAC,CAAC;MACrD;MACAP,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC;IACF,OAAO,YAAW;MAChBR,KAAK,CAAC,OAAO,CAAC;MACda,EAAE,CAACI,KAAK,CAAC,CAAC;MACVJ,EAAE,GAAG,IAAI;MAET,IAAIK,GAAG,GAAG,IAAIF,KAAK,CAAC,SAAS,CAAC;MAC9BE,GAAG,CAACC,IAAI,GAAG,IAAI;MACfX,QAAQ,CAACU,GAAG,CAAC;IACf,CAAC;EACH,CAAC;AACH;AAEA,SAASE,kBAAkBA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAElB,UAAU,EAAE;EACrEN,cAAc,CAACyB,IAAI,CAAC,IAAI,EAAEH,QAAQ,EAAEC,SAAS,EAAElB,gBAAgB,CAACC,UAAU,CAAC,EAAEkB,QAAQ,EAAElB,UAAU,CAAC;AACpG;AAEAT,QAAQ,CAACwB,kBAAkB,EAAErB,cAAc,CAAC;AAE5C0B,MAAM,CAACC,OAAO,GAAGN,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}