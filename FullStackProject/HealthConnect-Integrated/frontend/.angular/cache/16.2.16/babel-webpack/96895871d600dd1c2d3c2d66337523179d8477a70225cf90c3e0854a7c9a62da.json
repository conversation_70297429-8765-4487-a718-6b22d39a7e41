{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  AjaxBasedTransport = require('./lib/ajax-based'),\n  XdrStreamingTransport = require('./xdr-streaming'),\n  XhrReceiver = require('./receiver/xhr'),\n  XDRObject = require('./sender/xdr');\nfunction XdrPollingTransport(transUrl) {\n  if (!XDRObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr', XhrReceiver, XDRObject);\n}\ninherits(XdrPollingTransport, AjaxBasedTransport);\nXdrPollingTransport.enabled = XdrStreamingTransport.enabled;\nXdrPollingTransport.transportName = 'xdr-polling';\nXdrPollingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XdrPollingTransport;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}