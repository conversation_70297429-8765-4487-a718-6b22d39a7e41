{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { AppointmentType } from '../../core/models/appointment.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../core/services/appointment.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction AppointmentBookingComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"i\", 41);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.success, \" \");\n  }\n}\nfunction AppointmentBookingComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction AppointmentBookingComponent_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const doctor_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", doctor_r14.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", doctor_r14.fullName, \" - \", doctor_r14.specialization || \"General Practice\", \" \");\n  }\n}\nfunction AppointmentBookingComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \" Please select a doctor. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_div_20_p_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.selectedDoctor.affiliation, \" \");\n  }\n}\nfunction AppointmentBookingComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 7)(2, \"div\", 46)(3, \"div\", 47);\n    i0.ɵɵelement(4, \"i\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h6\", 49);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 50);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, AppointmentBookingComponent_div_20_p_10_Template, 2, 1, \"p\", 51);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedDoctor.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedDoctor.specialization || \"General Practice\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedDoctor.affiliation);\n  }\n}\nfunction AppointmentBookingComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \" Please select a date. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_option_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r16.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r16.label, \" \");\n  }\n}\nfunction AppointmentBookingComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \" Please select an appointment type. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54)(2, \"span\", 55);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtext(4, \" Loading available time slots... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_div_37_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"label\", 59);\n    i0.ɵɵelement(2, \"input\", 60);\n    i0.ɵɵelementStart(3, \"span\", 61);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const slot_r18 = ctx.$implicit;\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r17.getTimeSlotValue(slot_r18));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r17.getTimeSlotDisplay(slot_r18), \" \");\n  }\n}\nfunction AppointmentBookingComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 18);\n    i0.ɵɵtemplate(2, AppointmentBookingComponent_div_37_div_2_Template, 5, 2, \"div\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.availableSlots);\n  }\n}\nfunction AppointmentBookingComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelement(1, \"i\", 63);\n    i0.ɵɵtext(2, \" No available time slots for the selected date. Please choose a different date. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \" Please provide a reason for the visit. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_span_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64)(1, \"span\", 55);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppointmentBookingComponent_i_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 65);\n  }\n}\nexport class AppointmentBookingComponent {\n  constructor(fb, appointmentService, route, router) {\n    this.fb = fb;\n    this.appointmentService = appointmentService;\n    this.route = route;\n    this.router = router;\n    this.doctors = [];\n    this.selectedDoctor = null;\n    this.timeSlots = [];\n    this.availableSlots = [];\n    this.loading = false;\n    this.submitting = false;\n    this.error = null;\n    this.success = null;\n    this.appointmentTypes = [{\n      value: AppointmentType.IN_PERSON,\n      label: 'In Person'\n    }, {\n      value: AppointmentType.VIDEO_CALL,\n      label: 'Video Call'\n    }];\n    this.initializeForm();\n  }\n  initializeForm() {\n    this.bookingForm = this.fb.group({\n      doctorId: ['', Validators.required],\n      date: ['', Validators.required],\n      timeSlot: ['', Validators.required],\n      type: [AppointmentType.IN_PERSON, Validators.required],\n      reasonForVisit: ['', Validators.required],\n      notes: ['']\n    });\n  }\n  ngOnInit() {\n    this.loadDoctors();\n    // Check if doctor ID is provided in query params\n    this.route.queryParams.subscribe(params => {\n      if (params['doctorId']) {\n        this.bookingForm.patchValue({\n          doctorId: params['doctorId']\n        });\n        this.onDoctorChange();\n      }\n    });\n    // Set minimum date to tomorrow (backend requires future dates)\n    const tomorrow = new Date();\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    const tomorrowStr = tomorrow.toISOString().split('T')[0];\n    this.bookingForm.get('date')?.setValue(tomorrowStr);\n    // Watch for form changes\n    this.bookingForm.get('doctorId')?.valueChanges.subscribe(() => this.onDoctorChange());\n    this.bookingForm.get('date')?.valueChanges.subscribe(() => this.onDateChange());\n  }\n  loadDoctors() {\n    this.appointmentService.getDoctors().subscribe({\n      next: doctors => {\n        this.doctors = doctors;\n      },\n      error: error => {\n        this.error = 'Failed to load doctors. Please try again.';\n        console.error('Error loading doctors:', error);\n      }\n    });\n  }\n  onDoctorChange() {\n    const doctorId = this.bookingForm.get('doctorId')?.value;\n    if (doctorId) {\n      this.selectedDoctor = this.doctors.find(d => d.id == doctorId) || null;\n      this.loadTimeSlots();\n    } else {\n      this.selectedDoctor = null;\n      this.timeSlots = [];\n      this.availableSlots = [];\n    }\n  }\n  onDateChange() {\n    if (this.selectedDoctor) {\n      this.loadTimeSlots();\n    }\n  }\n  loadTimeSlots() {\n    const doctorId = this.bookingForm.get('doctorId')?.value;\n    const date = this.bookingForm.get('date')?.value;\n    if (!doctorId || !date) return;\n    this.loading = true;\n    this.appointmentService.getAvailableTimeSlots(doctorId, date).subscribe({\n      next: slots => {\n        this.timeSlots = slots;\n        this.availableSlots = slots.filter(slot => slot.available);\n        this.loading = false;\n        // Clear selected time slot if it's no longer available\n        const currentSlot = this.bookingForm.get('timeSlot')?.value;\n        if (currentSlot && !this.availableSlots.find(slot => slot.startTime === currentSlot.split('-')[0] && slot.endTime === currentSlot.split('-')[1])) {\n          this.bookingForm.patchValue({\n            timeSlot: ''\n          });\n        }\n      },\n      error: error => {\n        this.error = 'Failed to load available time slots. Please try again.';\n        this.loading = false;\n        console.error('Error loading time slots:', error);\n      }\n    });\n  }\n  onSubmit() {\n    if (this.bookingForm.valid) {\n      this.submitting = true;\n      this.error = null;\n      this.success = null;\n      const formValue = this.bookingForm.value;\n      const [startTime, endTime] = formValue.timeSlot.split('-');\n      const request = {\n        doctorId: parseInt(formValue.doctorId),\n        date: formValue.date,\n        startTime: startTime,\n        endTime: endTime,\n        type: formValue.type,\n        reasonForVisit: formValue.reasonForVisit,\n        notes: formValue.notes || undefined\n      };\n      this.appointmentService.createAppointment(request).subscribe({\n        next: appointment => {\n          this.success = 'Appointment booked successfully!';\n          this.submitting = false;\n          // Redirect to appointment details after 2 seconds\n          setTimeout(() => {\n            this.router.navigate(['/appointments', appointment.id]);\n          }, 2000);\n        },\n        error: error => {\n          this.error = 'Failed to book appointment. Please try again.';\n          this.submitting = false;\n          console.error('Error booking appointment:', error);\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n  markFormGroupTouched() {\n    Object.keys(this.bookingForm.controls).forEach(key => {\n      const control = this.bookingForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  getTimeSlotDisplay(slot) {\n    return `${this.formatTime(slot.startTime)} - ${this.formatTime(slot.endTime)}`;\n  }\n  getTimeSlotValue(slot) {\n    return `${slot.startTime}-${slot.endTime}`;\n  }\n  formatTime(time) {\n    const [hours, minutes] = time.split(':');\n    const hour = parseInt(hours);\n    const ampm = hour >= 12 ? 'PM' : 'AM';\n    const displayHour = hour % 12 || 12;\n    return `${displayHour}:${minutes} ${ampm}`;\n  }\n  getTodayDate() {\n    // Return tomorrow's date as minimum (backend requires future dates)\n    const tomorrow = new Date();\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    return tomorrow.toISOString().split('T')[0];\n  }\n  static {\n    this.ɵfac = function AppointmentBookingComponent_Factory(t) {\n      return new (t || AppointmentBookingComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AppointmentService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppointmentBookingComponent,\n      selectors: [[\"app-appointment-booking\"]],\n      decls: 58,\n      vars: 28,\n      consts: [[1, \"container-fluid\", \"py-4\"], [1, \"row\", \"justify-content-center\"], [1, \"col-lg-8\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\", \"mb-0\"], [1, \"fas\", \"fa-calendar-plus\", \"me-2\"], [1, \"card-body\"], [\"class\", \"alert alert-success\", \"role\", \"alert\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-4\"], [\"for\", \"doctorId\", 1, \"form-label\"], [\"id\", \"doctorId\", \"formControlName\", \"doctorId\", 1, \"form-select\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"class\", \"card bg-light mb-4\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-md-6\", \"mb-3\"], [\"for\", \"date\", 1, \"form-label\"], [\"type\", \"date\", \"id\", \"date\", \"formControlName\", \"date\", 1, \"form-control\", 3, \"min\"], [\"for\", \"type\", 1, \"form-label\"], [\"id\", \"type\", \"formControlName\", \"type\", 1, \"form-select\"], [1, \"mb-3\"], [\"for\", \"timeSlot\", 1, \"form-label\"], [\"class\", \"text-center py-3\", 4, \"ngIf\"], [\"class\", \"time-slots-grid\", 4, \"ngIf\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [1, \"invalid-feedback\"], [\"for\", \"reasonForVisit\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"reasonForVisit\", \"formControlName\", \"reasonForVisit\", \"placeholder\", \"e.g., Regular checkup, Follow-up, Consultation\", 1, \"form-control\"], [\"for\", \"notes\", 1, \"form-label\"], [\"id\", \"notes\", \"formControlName\", \"notes\", \"rows\", \"3\", \"placeholder\", \"Any additional information or special requests...\", 1, \"form-control\"], [1, \"d-flex\", \"justify-content-between\"], [\"type\", \"button\", \"routerLink\", \"/appointments/doctors\", 1, \"btn\", \"btn-outline-secondary\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", 4, \"ngIf\"], [\"class\", \"fas fa-calendar-check me-2\", 4, \"ngIf\"], [\"role\", \"alert\", 1, \"alert\", \"alert-success\"], [1, \"fas\", \"fa-check-circle\", \"me-2\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-2\"], [3, \"value\"], [1, \"card\", \"bg-light\", \"mb-4\"], [1, \"d-flex\", \"align-items-center\"], [1, \"avatar-circle\", \"me-3\"], [1, \"fas\", \"fa-user-md\"], [1, \"mb-1\"], [1, \"text-muted\", \"mb-1\"], [\"class\", \"text-muted mb-0\", 4, \"ngIf\"], [1, \"text-muted\", \"mb-0\"], [1, \"text-center\", \"py-3\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"text-primary\", \"me-2\"], [1, \"visually-hidden\"], [1, \"time-slots-grid\"], [\"class\", \"col-md-4 col-sm-6 mb-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\", \"col-sm-6\", \"mb-2\"], [1, \"time-slot-option\"], [\"type\", \"radio\", \"name\", \"timeSlot\", \"formControlName\", \"timeSlot\", 3, \"value\"], [1, \"time-slot-label\"], [1, \"alert\", \"alert-info\"], [1, \"fas\", \"fa-info-circle\", \"me-2\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [1, \"fas\", \"fa-calendar-check\", \"me-2\"]],\n      template: function AppointmentBookingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h4\", 5);\n          i0.ɵɵelement(6, \"i\", 6);\n          i0.ɵɵtext(7, \"Book an Appointment \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 7);\n          i0.ɵɵtemplate(9, AppointmentBookingComponent_div_9_Template, 3, 1, \"div\", 8);\n          i0.ɵɵtemplate(10, AppointmentBookingComponent_div_10_Template, 3, 1, \"div\", 9);\n          i0.ɵɵelementStart(11, \"form\", 10);\n          i0.ɵɵlistener(\"ngSubmit\", function AppointmentBookingComponent_Template_form_ngSubmit_11_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(12, \"div\", 11)(13, \"label\", 12);\n          i0.ɵɵtext(14, \"Select Doctor *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"select\", 13)(16, \"option\", 14);\n          i0.ɵɵtext(17, \"Choose a doctor...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(18, AppointmentBookingComponent_option_18_Template, 2, 3, \"option\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, AppointmentBookingComponent_div_19_Template, 2, 0, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(20, AppointmentBookingComponent_div_20_Template, 11, 3, \"div\", 17);\n          i0.ɵɵelementStart(21, \"div\", 18)(22, \"div\", 19)(23, \"label\", 20);\n          i0.ɵɵtext(24, \"Appointment Date *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(25, \"input\", 21);\n          i0.ɵɵtemplate(26, AppointmentBookingComponent_div_26_Template, 2, 0, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"div\", 19)(28, \"label\", 22);\n          i0.ɵɵtext(29, \"Appointment Type *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"select\", 23);\n          i0.ɵɵtemplate(31, AppointmentBookingComponent_option_31_Template, 2, 2, \"option\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, AppointmentBookingComponent_div_32_Template, 2, 0, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 24)(34, \"label\", 25);\n          i0.ɵɵtext(35, \"Available Time Slots *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(36, AppointmentBookingComponent_div_36_Template, 5, 0, \"div\", 26);\n          i0.ɵɵtemplate(37, AppointmentBookingComponent_div_37_Template, 3, 1, \"div\", 27);\n          i0.ɵɵtemplate(38, AppointmentBookingComponent_div_38_Template, 3, 0, \"div\", 28);\n          i0.ɵɵelementStart(39, \"div\", 29);\n          i0.ɵɵtext(40, \" Please select a time slot. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 24)(42, \"label\", 30);\n          i0.ɵɵtext(43, \"Reason for Visit *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(44, \"input\", 31);\n          i0.ɵɵtemplate(45, AppointmentBookingComponent_div_45_Template, 2, 0, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"div\", 11)(47, \"label\", 32);\n          i0.ɵɵtext(48, \"Additional Notes (Optional)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(49, \"textarea\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"div\", 34)(51, \"button\", 35);\n          i0.ɵɵelement(52, \"i\", 36);\n          i0.ɵɵtext(53, \" Back to Doctors \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"button\", 37);\n          i0.ɵɵtemplate(55, AppointmentBookingComponent_span_55_Template, 3, 0, \"span\", 38);\n          i0.ɵɵtemplate(56, AppointmentBookingComponent_i_56_Template, 1, 0, \"i\", 39);\n          i0.ɵɵtext(57);\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_3_0;\n          let tmp_5_0;\n          let tmp_7_0;\n          let tmp_9_0;\n          let tmp_10_0;\n          let tmp_12_0;\n          let tmp_16_0;\n          let tmp_17_0;\n          let tmp_18_0;\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.success);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.bookingForm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_3_0 = ctx.bookingForm.get(\"doctorId\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.bookingForm.get(\"doctorId\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.doctors);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.bookingForm.get(\"doctorId\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.bookingForm.get(\"doctorId\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedDoctor);\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_7_0 = ctx.bookingForm.get(\"date\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.bookingForm.get(\"date\")) == null ? null : tmp_7_0.touched));\n          i0.ɵɵproperty(\"min\", ctx.getTodayDate());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx.bookingForm.get(\"date\")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx.bookingForm.get(\"date\")) == null ? null : tmp_9_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_10_0 = ctx.bookingForm.get(\"type\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx.bookingForm.get(\"type\")) == null ? null : tmp_10_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.appointmentTypes);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx.bookingForm.get(\"type\")) == null ? null : tmp_12_0.invalid) && ((tmp_12_0 = ctx.bookingForm.get(\"type\")) == null ? null : tmp_12_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.availableSlots.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.selectedDoctor && ctx.availableSlots.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"display\", ((tmp_16_0 = ctx.bookingForm.get(\"timeSlot\")) == null ? null : tmp_16_0.invalid) && ((tmp_16_0 = ctx.bookingForm.get(\"timeSlot\")) == null ? null : tmp_16_0.touched) ? \"block\" : \"none\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_17_0 = ctx.bookingForm.get(\"reasonForVisit\")) == null ? null : tmp_17_0.invalid) && ((tmp_17_0 = ctx.bookingForm.get(\"reasonForVisit\")) == null ? null : tmp_17_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_18_0 = ctx.bookingForm.get(\"reasonForVisit\")) == null ? null : tmp_18_0.invalid) && ((tmp_18_0 = ctx.bookingForm.get(\"reasonForVisit\")) == null ? null : tmp_18_0.touched));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"disabled\", ctx.bookingForm.invalid || ctx.submitting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.submitting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.submitting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.submitting ? \"Booking...\" : \"Book Appointment\", \" \");\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.RadioControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink],\n      styles: [\".avatar-circle[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-size: 1.2rem;\\n  flex-shrink: 0;\\n}\\n\\n.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%] {\\n  display: block;\\n  cursor: pointer;\\n  margin: 0;\\n}\\n.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%]   .time-slot-label[_ngcontent-%COMP%] {\\n  display: block;\\n  padding: 0.75rem 1rem;\\n  border: 2px solid #e3e6f0;\\n  border-radius: 0.5rem;\\n  text-align: center;\\n  transition: all 0.3s ease;\\n  background: white;\\n  font-weight: 500;\\n}\\n.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%]   .time-slot-label[_ngcontent-%COMP%]:hover {\\n  border-color: #667eea;\\n  background: #f8f9fc;\\n}\\n.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked    + .time-slot-label[_ngcontent-%COMP%] {\\n  border-color: #667eea;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n}\\n\\n.form-select[_ngcontent-%COMP%]:focus, .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #667eea;\\n  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border: none;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\\n  transform: translateY(-1px);\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:disabled {\\n  background: #6c757d;\\n  opacity: 0.65;\\n}\\n\\n.alert-success[_ngcontent-%COMP%] {\\n  border-left: 4px solid #28a745;\\n}\\n\\n.alert-danger[_ngcontent-%COMP%] {\\n  border-left: 4px solid #e74a3b;\\n}\\n\\n.alert-info[_ngcontent-%COMP%] {\\n  border-left: 4px solid #17a2b8;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  border: 1px solid #e3e6f0;\\n  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);\\n}\\n\\n.bg-light[_ngcontent-%COMP%] {\\n  background-color: #f8f9fc !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "AppointmentType", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "success", "ctx_r1", "error", "ɵɵproperty", "doctor_r14", "id", "ɵɵtextInterpolate2", "fullName", "specialization", "ctx_r15", "<PERSON><PERSON><PERSON><PERSON>", "affiliation", "ɵɵtemplate", "AppointmentBookingComponent_div_20_p_10_Template", "ɵɵtextInterpolate", "ctx_r4", "type_r16", "value", "label", "ctx_r17", "getTimeSlotValue", "slot_r18", "getTimeSlotDisplay", "AppointmentBookingComponent_div_37_div_2_Template", "ctx_r9", "availableSlots", "AppointmentBookingComponent", "constructor", "fb", "appointmentService", "route", "router", "doctors", "timeSlots", "loading", "submitting", "appointmentTypes", "IN_PERSON", "VIDEO_CALL", "initializeForm", "bookingForm", "group", "doctorId", "required", "date", "timeSlot", "type", "reasonForVisit", "notes", "ngOnInit", "loadDoctors", "queryParams", "subscribe", "params", "patchValue", "onDoctorChange", "tomorrow", "Date", "setDate", "getDate", "tomorrowStr", "toISOString", "split", "get", "setValue", "valueChanges", "onDateChange", "getDoctors", "next", "console", "find", "d", "loadTimeSlots", "getAvailableTimeSlots", "slots", "filter", "slot", "available", "currentSlot", "startTime", "endTime", "onSubmit", "valid", "formValue", "request", "parseInt", "undefined", "createAppointment", "appointment", "setTimeout", "navigate", "markFormGroupTouched", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "formatTime", "time", "hours", "minutes", "hour", "ampm", "displayHour", "getTodayDate", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AppointmentService", "i3", "ActivatedRoute", "Router", "selectors", "decls", "vars", "consts", "template", "AppointmentBookingComponent_Template", "rf", "ctx", "AppointmentBookingComponent_div_9_Template", "AppointmentBookingComponent_div_10_Template", "ɵɵlistener", "AppointmentBookingComponent_Template_form_ngSubmit_11_listener", "AppointmentBookingComponent_option_18_Template", "AppointmentBookingComponent_div_19_Template", "AppointmentBookingComponent_div_20_Template", "AppointmentBookingComponent_div_26_Template", "AppointmentBookingComponent_option_31_Template", "AppointmentBookingComponent_div_32_Template", "AppointmentBookingComponent_div_36_Template", "AppointmentBookingComponent_div_37_Template", "AppointmentBookingComponent_div_38_Template", "AppointmentBookingComponent_div_45_Template", "AppointmentBookingComponent_span_55_Template", "AppointmentBookingComponent_i_56_Template", "ɵɵclassProp", "tmp_3_0", "invalid", "touched", "tmp_5_0", "tmp_7_0", "tmp_9_0", "tmp_10_0", "tmp_12_0", "length", "ɵɵstyleProp", "tmp_16_0", "tmp_17_0", "tmp_18_0"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/appointments/appointment-booking/appointment-booking.component.ts", "/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/appointments/appointment-booking/appointment-booking.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { AppointmentService } from '../../core/services/appointment.service';\nimport { Doctor, TimeSlot, AppointmentType, AppointmentRequest } from '../../core/models/appointment.model';\n\n@Component({\n  selector: 'app-appointment-booking',\n  templateUrl: './appointment-booking.component.html',\n  styleUrls: ['./appointment-booking.component.scss']\n})\nexport class AppointmentBookingComponent implements OnInit {\n  bookingForm!: FormGroup;\n  doctors: Doctor[] = [];\n  selectedDoctor: Doctor | null = null;\n  timeSlots: TimeSlot[] = [];\n  availableSlots: TimeSlot[] = [];\n  loading = false;\n  submitting = false;\n  error: string | null = null;\n  success: string | null = null;\n\n  appointmentTypes = [\n    { value: AppointmentType.IN_PERSON, label: 'In Person' },\n    { value: AppointmentType.VIDEO_CALL, label: 'Video Call' }\n  ];\n\n  constructor(\n    private fb: FormBuilder,\n    private appointmentService: AppointmentService,\n    private route: ActivatedRoute,\n    private router: Router\n  ) {\n    this.initializeForm();\n  }\n\n  private initializeForm(): void {\n    this.bookingForm = this.fb.group({\n      doctorId: ['', Validators.required],\n      date: ['', Validators.required],\n      timeSlot: ['', Validators.required],\n      type: [AppointmentType.IN_PERSON, Validators.required],\n      reasonForVisit: ['', Validators.required],\n      notes: ['']\n    });\n  }\n\n  ngOnInit(): void {\n    this.loadDoctors();\n    \n    // Check if doctor ID is provided in query params\n    this.route.queryParams.subscribe(params => {\n      if (params['doctorId']) {\n        this.bookingForm.patchValue({ doctorId: params['doctorId'] });\n        this.onDoctorChange();\n      }\n    });\n\n    // Set minimum date to tomorrow (backend requires future dates)\n    const tomorrow = new Date();\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    const tomorrowStr = tomorrow.toISOString().split('T')[0];\n    this.bookingForm.get('date')?.setValue(tomorrowStr);\n\n    // Watch for form changes\n    this.bookingForm.get('doctorId')?.valueChanges.subscribe(() => this.onDoctorChange());\n    this.bookingForm.get('date')?.valueChanges.subscribe(() => this.onDateChange());\n  }\n\n  loadDoctors(): void {\n    this.appointmentService.getDoctors().subscribe({\n      next: (doctors) => {\n        this.doctors = doctors;\n      },\n      error: (error) => {\n        this.error = 'Failed to load doctors. Please try again.';\n        console.error('Error loading doctors:', error);\n      }\n    });\n  }\n\n  onDoctorChange(): void {\n    const doctorId = this.bookingForm.get('doctorId')?.value;\n    if (doctorId) {\n      this.selectedDoctor = this.doctors.find(d => d.id == doctorId) || null;\n      this.loadTimeSlots();\n    } else {\n      this.selectedDoctor = null;\n      this.timeSlots = [];\n      this.availableSlots = [];\n    }\n  }\n\n  onDateChange(): void {\n    if (this.selectedDoctor) {\n      this.loadTimeSlots();\n    }\n  }\n\n  loadTimeSlots(): void {\n    const doctorId = this.bookingForm.get('doctorId')?.value;\n    const date = this.bookingForm.get('date')?.value;\n    \n    if (!doctorId || !date) return;\n\n    this.loading = true;\n    this.appointmentService.getAvailableTimeSlots(doctorId, date).subscribe({\n      next: (slots) => {\n        this.timeSlots = slots;\n        this.availableSlots = slots.filter(slot => slot.available);\n        this.loading = false;\n        \n        // Clear selected time slot if it's no longer available\n        const currentSlot = this.bookingForm.get('timeSlot')?.value;\n        if (currentSlot && !this.availableSlots.find(slot => \n          slot.startTime === currentSlot.split('-')[0] && \n          slot.endTime === currentSlot.split('-')[1])) {\n          this.bookingForm.patchValue({ timeSlot: '' });\n        }\n      },\n      error: (error) => {\n        this.error = 'Failed to load available time slots. Please try again.';\n        this.loading = false;\n        console.error('Error loading time slots:', error);\n      }\n    });\n  }\n\n  onSubmit(): void {\n    if (this.bookingForm.valid) {\n      this.submitting = true;\n      this.error = null;\n      this.success = null;\n\n      const formValue = this.bookingForm.value;\n      const [startTime, endTime] = formValue.timeSlot.split('-');\n\n      const request: AppointmentRequest = {\n        doctorId: parseInt(formValue.doctorId),\n        date: formValue.date,\n        startTime: startTime,\n        endTime: endTime,\n        type: formValue.type,\n        reasonForVisit: formValue.reasonForVisit,\n        notes: formValue.notes || undefined\n      };\n\n      this.appointmentService.createAppointment(request).subscribe({\n        next: (appointment) => {\n          this.success = 'Appointment booked successfully!';\n          this.submitting = false;\n          \n          // Redirect to appointment details after 2 seconds\n          setTimeout(() => {\n            this.router.navigate(['/appointments', appointment.id]);\n          }, 2000);\n        },\n        error: (error) => {\n          this.error = 'Failed to book appointment. Please try again.';\n          this.submitting = false;\n          console.error('Error booking appointment:', error);\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.bookingForm.controls).forEach(key => {\n      const control = this.bookingForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  getTimeSlotDisplay(slot: TimeSlot): string {\n    return `${this.formatTime(slot.startTime)} - ${this.formatTime(slot.endTime)}`;\n  }\n\n  getTimeSlotValue(slot: TimeSlot): string {\n    return `${slot.startTime}-${slot.endTime}`;\n  }\n\n  private formatTime(time: string): string {\n    const [hours, minutes] = time.split(':');\n    const hour = parseInt(hours);\n    const ampm = hour >= 12 ? 'PM' : 'AM';\n    const displayHour = hour % 12 || 12;\n    return `${displayHour}:${minutes} ${ampm}`;\n  }\n\n  getTodayDate(): string {\n    // Return tomorrow's date as minimum (backend requires future dates)\n    const tomorrow = new Date();\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    return tomorrow.toISOString().split('T')[0];\n  }\n}\n", "<div class=\"container-fluid py-4\">\n  <div class=\"row justify-content-center\">\n    <div class=\"col-lg-8\">\n      <div class=\"card\">\n        <div class=\"card-header\">\n          <h4 class=\"card-title mb-0\">\n            <i class=\"fas fa-calendar-plus me-2\"></i>Book an Appointment\n          </h4>\n        </div>\n        <div class=\"card-body\">\n          <!-- Success Message -->\n          <div *ngIf=\"success\" class=\"alert alert-success\" role=\"alert\">\n            <i class=\"fas fa-check-circle me-2\"></i>\n            {{ success }}\n          </div>\n\n          <!-- Error Message -->\n          <div *ngIf=\"error\" class=\"alert alert-danger\" role=\"alert\">\n            <i class=\"fas fa-exclamation-triangle me-2\"></i>\n            {{ error }}\n          </div>\n\n          <form [formGroup]=\"bookingForm\" (ngSubmit)=\"onSubmit()\">\n            <!-- Doctor Selection -->\n            <div class=\"mb-4\">\n              <label for=\"doctorId\" class=\"form-label\">Select Doctor *</label>\n              <select \n                id=\"doctorId\" \n                class=\"form-select\"\n                formControlName=\"doctorId\"\n                [class.is-invalid]=\"bookingForm.get('doctorId')?.invalid && bookingForm.get('doctorId')?.touched\">\n                <option value=\"\">Choose a doctor...</option>\n                <option *ngFor=\"let doctor of doctors\" [value]=\"doctor.id\">\n                  {{ doctor.fullName }} - {{ doctor.specialization || 'General Practice' }}\n                </option>\n              </select>\n              <div class=\"invalid-feedback\" *ngIf=\"bookingForm.get('doctorId')?.invalid && bookingForm.get('doctorId')?.touched\">\n                Please select a doctor.\n              </div>\n            </div>\n\n            <!-- Selected Doctor Info -->\n            <div *ngIf=\"selectedDoctor\" class=\"card bg-light mb-4\">\n              <div class=\"card-body\">\n                <div class=\"d-flex align-items-center\">\n                  <div class=\"avatar-circle me-3\">\n                    <i class=\"fas fa-user-md\"></i>\n                  </div>\n                  <div>\n                    <h6 class=\"mb-1\">{{ selectedDoctor.fullName }}</h6>\n                    <p class=\"text-muted mb-1\">{{ selectedDoctor.specialization || 'General Practice' }}</p>\n                    <p class=\"text-muted mb-0\" *ngIf=\"selectedDoctor.affiliation\">\n                      {{ selectedDoctor.affiliation }}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"row\">\n              <!-- Date Selection -->\n              <div class=\"col-md-6 mb-3\">\n                <label for=\"date\" class=\"form-label\">Appointment Date *</label>\n                <input \n                  type=\"date\" \n                  id=\"date\"\n                  class=\"form-control\"\n                  formControlName=\"date\"\n                  [min]=\"getTodayDate()\"\n                  [class.is-invalid]=\"bookingForm.get('date')?.invalid && bookingForm.get('date')?.touched\">\n                <div class=\"invalid-feedback\" *ngIf=\"bookingForm.get('date')?.invalid && bookingForm.get('date')?.touched\">\n                  Please select a date.\n                </div>\n              </div>\n\n              <!-- Appointment Type -->\n              <div class=\"col-md-6 mb-3\">\n                <label for=\"type\" class=\"form-label\">Appointment Type *</label>\n                <select \n                  id=\"type\" \n                  class=\"form-select\"\n                  formControlName=\"type\"\n                  [class.is-invalid]=\"bookingForm.get('type')?.invalid && bookingForm.get('type')?.touched\">\n                  <option *ngFor=\"let type of appointmentTypes\" [value]=\"type.value\">\n                    {{ type.label }}\n                  </option>\n                </select>\n                <div class=\"invalid-feedback\" *ngIf=\"bookingForm.get('type')?.invalid && bookingForm.get('type')?.touched\">\n                  Please select an appointment type.\n                </div>\n              </div>\n            </div>\n\n            <!-- Time Slot Selection -->\n            <div class=\"mb-3\">\n              <label for=\"timeSlot\" class=\"form-label\">Available Time Slots *</label>\n              \n              <!-- Loading Time Slots -->\n              <div *ngIf=\"loading\" class=\"text-center py-3\">\n                <div class=\"spinner-border spinner-border-sm text-primary me-2\" role=\"status\">\n                  <span class=\"visually-hidden\">Loading...</span>\n                </div>\n                Loading available time slots...\n              </div>\n\n              <!-- Time Slots Grid -->\n              <div *ngIf=\"!loading && availableSlots.length > 0\" class=\"time-slots-grid\">\n                <div class=\"row\">\n                  <div class=\"col-md-4 col-sm-6 mb-2\" *ngFor=\"let slot of availableSlots\">\n                    <label class=\"time-slot-option\">\n                      <input \n                        type=\"radio\" \n                        name=\"timeSlot\"\n                        [value]=\"getTimeSlotValue(slot)\"\n                        formControlName=\"timeSlot\">\n                      <span class=\"time-slot-label\">\n                        {{ getTimeSlotDisplay(slot) }}\n                      </span>\n                    </label>\n                  </div>\n                </div>\n              </div>\n\n              <!-- No Time Slots Available -->\n              <div *ngIf=\"!loading && selectedDoctor && availableSlots.length === 0\" class=\"alert alert-info\">\n                <i class=\"fas fa-info-circle me-2\"></i>\n                No available time slots for the selected date. Please choose a different date.\n              </div>\n\n              <div class=\"invalid-feedback\" \n                   [style.display]=\"bookingForm.get('timeSlot')?.invalid && bookingForm.get('timeSlot')?.touched ? 'block' : 'none'\">\n                Please select a time slot.\n              </div>\n            </div>\n\n            <!-- Reason for Visit -->\n            <div class=\"mb-3\">\n              <label for=\"reasonForVisit\" class=\"form-label\">Reason for Visit *</label>\n              <input \n                type=\"text\" \n                id=\"reasonForVisit\"\n                class=\"form-control\"\n                formControlName=\"reasonForVisit\"\n                placeholder=\"e.g., Regular checkup, Follow-up, Consultation\"\n                [class.is-invalid]=\"bookingForm.get('reasonForVisit')?.invalid && bookingForm.get('reasonForVisit')?.touched\">\n              <div class=\"invalid-feedback\" *ngIf=\"bookingForm.get('reasonForVisit')?.invalid && bookingForm.get('reasonForVisit')?.touched\">\n                Please provide a reason for the visit.\n              </div>\n            </div>\n\n            <!-- Additional Notes -->\n            <div class=\"mb-4\">\n              <label for=\"notes\" class=\"form-label\">Additional Notes (Optional)</label>\n              <textarea \n                id=\"notes\"\n                class=\"form-control\"\n                formControlName=\"notes\"\n                rows=\"3\"\n                placeholder=\"Any additional information or special requests...\"></textarea>\n            </div>\n\n            <!-- Submit Button -->\n            <div class=\"d-flex justify-content-between\">\n              <button \n                type=\"button\" \n                class=\"btn btn-outline-secondary\"\n                routerLink=\"/appointments/doctors\">\n                <i class=\"fas fa-arrow-left me-2\"></i>\n                Back to Doctors\n              </button>\n              <button \n                type=\"submit\" \n                class=\"btn btn-primary\"\n                [disabled]=\"bookingForm.invalid || submitting\">\n                <span *ngIf=\"submitting\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\">\n                  <span class=\"visually-hidden\">Loading...</span>\n                </span>\n                <i *ngIf=\"!submitting\" class=\"fas fa-calendar-check me-2\"></i>\n                {{ submitting ? 'Booking...' : 'Book Appointment' }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAGnE,SAA2BC,eAAe,QAA4B,qCAAqC;;;;;;;;ICOjGC,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAE,SAAA,YAAwC;IACxCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,OAAA,MACF;;;;;IAGAR,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAE,SAAA,YAAgD;IAChDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAG,MAAA,CAAAC,KAAA,MACF;;;;;IAYMV,EAAA,CAAAC,cAAA,iBAA2D;IACzDD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAF8BJ,EAAA,CAAAW,UAAA,UAAAC,UAAA,CAAAC,EAAA,CAAmB;IACxDb,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAc,kBAAA,MAAAF,UAAA,CAAAG,QAAA,SAAAH,UAAA,CAAAI,cAAA,4BACF;;;;;IAEFhB,EAAA,CAAAC,cAAA,cAAmH;IACjHD,EAAA,CAAAG,MAAA,gCACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAaAJ,EAAA,CAAAC,cAAA,YAA8D;IAC5DD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAW,OAAA,CAAAC,cAAA,CAAAC,WAAA,MACF;;;;;IAXRnB,EAAA,CAAAC,cAAA,cAAuD;IAI/CD,EAAA,CAAAE,SAAA,YAA8B;IAChCF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,UAAK;IACcD,EAAA,CAAAG,MAAA,GAA6B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnDJ,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAyD;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACxFJ,EAAA,CAAAoB,UAAA,KAAAC,gDAAA,gBAEI;IACNrB,EAAA,CAAAI,YAAA,EAAM;;;;IALaJ,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAsB,iBAAA,CAAAC,MAAA,CAAAL,cAAA,CAAAH,QAAA,CAA6B;IACnBf,EAAA,CAAAK,SAAA,GAAyD;IAAzDL,EAAA,CAAAsB,iBAAA,CAAAC,MAAA,CAAAL,cAAA,CAAAF,cAAA,uBAAyD;IACxDhB,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAW,UAAA,SAAAY,MAAA,CAAAL,cAAA,CAAAC,WAAA,CAAgC;;;;;IAmBhEnB,EAAA,CAAAC,cAAA,cAA2G;IACzGD,EAAA,CAAAG,MAAA,8BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAWJJ,EAAA,CAAAC,cAAA,iBAAmE;IACjED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAFqCJ,EAAA,CAAAW,UAAA,UAAAa,QAAA,CAAAC,KAAA,CAAoB;IAChEzB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAkB,QAAA,CAAAE,KAAA,MACF;;;;;IAEF1B,EAAA,CAAAC,cAAA,cAA2G;IACzGD,EAAA,CAAAG,MAAA,2CACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IASRJ,EAAA,CAAAC,cAAA,cAA8C;IAEZD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEjDJ,EAAA,CAAAG,MAAA,wCACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAKFJ,EAAA,CAAAC,cAAA,cAAwE;IAEpED,EAAA,CAAAE,SAAA,gBAI6B;IAC7BF,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAJLJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAW,UAAA,UAAAgB,OAAA,CAAAC,gBAAA,CAAAC,QAAA,EAAgC;IAGhC7B,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAqB,OAAA,CAAAG,kBAAA,CAAAD,QAAA,OACF;;;;;IAXR7B,EAAA,CAAAC,cAAA,cAA2E;IAEvED,EAAA,CAAAoB,UAAA,IAAAW,iDAAA,kBAWM;IACR/B,EAAA,CAAAI,YAAA,EAAM;;;;IAZiDJ,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAW,UAAA,YAAAqB,MAAA,CAAAC,cAAA,CAAiB;;;;;IAgB1EjC,EAAA,CAAAC,cAAA,cAAgG;IAC9FD,EAAA,CAAAE,SAAA,YAAuC;IACvCF,EAAA,CAAAG,MAAA,uFACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAkBNJ,EAAA,CAAAC,cAAA,cAA+H;IAC7HD,EAAA,CAAAG,MAAA,+CACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IA2BJJ,EAAA,CAAAC,cAAA,eAAqF;IACrDD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAEjDJ,EAAA,CAAAE,SAAA,YAA8D;;;ADtK9E,OAAM,MAAOgC,2BAA2B;EAgBtCC,YACUC,EAAe,EACfC,kBAAsC,EACtCC,KAAqB,EACrBC,MAAc;IAHd,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IAlBhB,KAAAC,OAAO,GAAa,EAAE;IACtB,KAAAtB,cAAc,GAAkB,IAAI;IACpC,KAAAuB,SAAS,GAAe,EAAE;IAC1B,KAAAR,cAAc,GAAe,EAAE;IAC/B,KAAAS,OAAO,GAAG,KAAK;IACf,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAjC,KAAK,GAAkB,IAAI;IAC3B,KAAAF,OAAO,GAAkB,IAAI;IAE7B,KAAAoC,gBAAgB,GAAG,CACjB;MAAEnB,KAAK,EAAE1B,eAAe,CAAC8C,SAAS;MAAEnB,KAAK,EAAE;IAAW,CAAE,EACxD;MAAED,KAAK,EAAE1B,eAAe,CAAC+C,UAAU;MAAEpB,KAAK,EAAE;IAAY,CAAE,CAC3D;IAQC,IAAI,CAACqB,cAAc,EAAE;EACvB;EAEQA,cAAcA,CAAA;IACpB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACZ,EAAE,CAACa,KAAK,CAAC;MAC/BC,QAAQ,EAAE,CAAC,EAAE,EAAEpD,UAAU,CAACqD,QAAQ,CAAC;MACnCC,IAAI,EAAE,CAAC,EAAE,EAAEtD,UAAU,CAACqD,QAAQ,CAAC;MAC/BE,QAAQ,EAAE,CAAC,EAAE,EAAEvD,UAAU,CAACqD,QAAQ,CAAC;MACnCG,IAAI,EAAE,CAACvD,eAAe,CAAC8C,SAAS,EAAE/C,UAAU,CAACqD,QAAQ,CAAC;MACtDI,cAAc,EAAE,CAAC,EAAE,EAAEzD,UAAU,CAACqD,QAAQ,CAAC;MACzCK,KAAK,EAAE,CAAC,EAAE;KACX,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAElB;IACA,IAAI,CAACpB,KAAK,CAACqB,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACxC,IAAIA,MAAM,CAAC,UAAU,CAAC,EAAE;QACtB,IAAI,CAACb,WAAW,CAACc,UAAU,CAAC;UAAEZ,QAAQ,EAAEW,MAAM,CAAC,UAAU;QAAC,CAAE,CAAC;QAC7D,IAAI,CAACE,cAAc,EAAE;;IAEzB,CAAC,CAAC;IAEF;IACA,MAAMC,QAAQ,GAAG,IAAIC,IAAI,EAAE;IAC3BD,QAAQ,CAACE,OAAO,CAACF,QAAQ,CAACG,OAAO,EAAE,GAAG,CAAC,CAAC;IACxC,MAAMC,WAAW,GAAGJ,QAAQ,CAACK,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACxD,IAAI,CAACtB,WAAW,CAACuB,GAAG,CAAC,MAAM,CAAC,EAAEC,QAAQ,CAACJ,WAAW,CAAC;IAEnD;IACA,IAAI,CAACpB,WAAW,CAACuB,GAAG,CAAC,UAAU,CAAC,EAAEE,YAAY,CAACb,SAAS,CAAC,MAAM,IAAI,CAACG,cAAc,EAAE,CAAC;IACrF,IAAI,CAACf,WAAW,CAACuB,GAAG,CAAC,MAAM,CAAC,EAAEE,YAAY,CAACb,SAAS,CAAC,MAAM,IAAI,CAACc,YAAY,EAAE,CAAC;EACjF;EAEAhB,WAAWA,CAAA;IACT,IAAI,CAACrB,kBAAkB,CAACsC,UAAU,EAAE,CAACf,SAAS,CAAC;MAC7CgB,IAAI,EAAGpC,OAAO,IAAI;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;MACxB,CAAC;MACD9B,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,2CAA2C;QACxDmE,OAAO,CAACnE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEAqD,cAAcA,CAAA;IACZ,MAAMb,QAAQ,GAAG,IAAI,CAACF,WAAW,CAACuB,GAAG,CAAC,UAAU,CAAC,EAAE9C,KAAK;IACxD,IAAIyB,QAAQ,EAAE;MACZ,IAAI,CAAChC,cAAc,GAAG,IAAI,CAACsB,OAAO,CAACsC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClE,EAAE,IAAIqC,QAAQ,CAAC,IAAI,IAAI;MACtE,IAAI,CAAC8B,aAAa,EAAE;KACrB,MAAM;MACL,IAAI,CAAC9D,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACuB,SAAS,GAAG,EAAE;MACnB,IAAI,CAACR,cAAc,GAAG,EAAE;;EAE5B;EAEAyC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACxD,cAAc,EAAE;MACvB,IAAI,CAAC8D,aAAa,EAAE;;EAExB;EAEAA,aAAaA,CAAA;IACX,MAAM9B,QAAQ,GAAG,IAAI,CAACF,WAAW,CAACuB,GAAG,CAAC,UAAU,CAAC,EAAE9C,KAAK;IACxD,MAAM2B,IAAI,GAAG,IAAI,CAACJ,WAAW,CAACuB,GAAG,CAAC,MAAM,CAAC,EAAE9C,KAAK;IAEhD,IAAI,CAACyB,QAAQ,IAAI,CAACE,IAAI,EAAE;IAExB,IAAI,CAACV,OAAO,GAAG,IAAI;IACnB,IAAI,CAACL,kBAAkB,CAAC4C,qBAAqB,CAAC/B,QAAQ,EAAEE,IAAI,CAAC,CAACQ,SAAS,CAAC;MACtEgB,IAAI,EAAGM,KAAK,IAAI;QACd,IAAI,CAACzC,SAAS,GAAGyC,KAAK;QACtB,IAAI,CAACjD,cAAc,GAAGiD,KAAK,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,SAAS,CAAC;QAC1D,IAAI,CAAC3C,OAAO,GAAG,KAAK;QAEpB;QACA,MAAM4C,WAAW,GAAG,IAAI,CAACtC,WAAW,CAACuB,GAAG,CAAC,UAAU,CAAC,EAAE9C,KAAK;QAC3D,IAAI6D,WAAW,IAAI,CAAC,IAAI,CAACrD,cAAc,CAAC6C,IAAI,CAACM,IAAI,IAC/CA,IAAI,CAACG,SAAS,KAAKD,WAAW,CAAChB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAC5Cc,IAAI,CAACI,OAAO,KAAKF,WAAW,CAAChB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC7C,IAAI,CAACtB,WAAW,CAACc,UAAU,CAAC;YAAET,QAAQ,EAAE;UAAE,CAAE,CAAC;;MAEjD,CAAC;MACD3C,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,wDAAwD;QACrE,IAAI,CAACgC,OAAO,GAAG,KAAK;QACpBmC,OAAO,CAACnE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;KACD,CAAC;EACJ;EAEA+E,QAAQA,CAAA;IACN,IAAI,IAAI,CAACzC,WAAW,CAAC0C,KAAK,EAAE;MAC1B,IAAI,CAAC/C,UAAU,GAAG,IAAI;MACtB,IAAI,CAACjC,KAAK,GAAG,IAAI;MACjB,IAAI,CAACF,OAAO,GAAG,IAAI;MAEnB,MAAMmF,SAAS,GAAG,IAAI,CAAC3C,WAAW,CAACvB,KAAK;MACxC,MAAM,CAAC8D,SAAS,EAAEC,OAAO,CAAC,GAAGG,SAAS,CAACtC,QAAQ,CAACiB,KAAK,CAAC,GAAG,CAAC;MAE1D,MAAMsB,OAAO,GAAuB;QAClC1C,QAAQ,EAAE2C,QAAQ,CAACF,SAAS,CAACzC,QAAQ,CAAC;QACtCE,IAAI,EAAEuC,SAAS,CAACvC,IAAI;QACpBmC,SAAS,EAAEA,SAAS;QACpBC,OAAO,EAAEA,OAAO;QAChBlC,IAAI,EAAEqC,SAAS,CAACrC,IAAI;QACpBC,cAAc,EAAEoC,SAAS,CAACpC,cAAc;QACxCC,KAAK,EAAEmC,SAAS,CAACnC,KAAK,IAAIsC;OAC3B;MAED,IAAI,CAACzD,kBAAkB,CAAC0D,iBAAiB,CAACH,OAAO,CAAC,CAAChC,SAAS,CAAC;QAC3DgB,IAAI,EAAGoB,WAAW,IAAI;UACpB,IAAI,CAACxF,OAAO,GAAG,kCAAkC;UACjD,IAAI,CAACmC,UAAU,GAAG,KAAK;UAEvB;UACAsD,UAAU,CAAC,MAAK;YACd,IAAI,CAAC1D,MAAM,CAAC2D,QAAQ,CAAC,CAAC,eAAe,EAAEF,WAAW,CAACnF,EAAE,CAAC,CAAC;UACzD,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACDH,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACA,KAAK,GAAG,+CAA+C;UAC5D,IAAI,CAACiC,UAAU,GAAG,KAAK;UACvBkC,OAAO,CAACnE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACpD;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACyF,oBAAoB,EAAE;;EAE/B;EAEQA,oBAAoBA,CAAA;IAC1BC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrD,WAAW,CAACsD,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACnD,MAAMC,OAAO,GAAG,IAAI,CAACzD,WAAW,CAACuB,GAAG,CAACiC,GAAG,CAAC;MACzCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA5E,kBAAkBA,CAACsD,IAAc;IAC/B,OAAO,GAAG,IAAI,CAACuB,UAAU,CAACvB,IAAI,CAACG,SAAS,CAAC,MAAM,IAAI,CAACoB,UAAU,CAACvB,IAAI,CAACI,OAAO,CAAC,EAAE;EAChF;EAEA5D,gBAAgBA,CAACwD,IAAc;IAC7B,OAAO,GAAGA,IAAI,CAACG,SAAS,IAAIH,IAAI,CAACI,OAAO,EAAE;EAC5C;EAEQmB,UAAUA,CAACC,IAAY;IAC7B,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGF,IAAI,CAACtC,KAAK,CAAC,GAAG,CAAC;IACxC,MAAMyC,IAAI,GAAGlB,QAAQ,CAACgB,KAAK,CAAC;IAC5B,MAAMG,IAAI,GAAGD,IAAI,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI;IACrC,MAAME,WAAW,GAAGF,IAAI,GAAG,EAAE,IAAI,EAAE;IACnC,OAAO,GAAGE,WAAW,IAAIH,OAAO,IAAIE,IAAI,EAAE;EAC5C;EAEAE,YAAYA,CAAA;IACV;IACA,MAAMlD,QAAQ,GAAG,IAAIC,IAAI,EAAE;IAC3BD,QAAQ,CAACE,OAAO,CAACF,QAAQ,CAACG,OAAO,EAAE,GAAG,CAAC,CAAC;IACxC,OAAOH,QAAQ,CAACK,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC7C;;;uBAzLWpC,2BAA2B,EAAAlC,EAAA,CAAAmH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAArH,EAAA,CAAAmH,iBAAA,CAAAG,EAAA,CAAAC,kBAAA,GAAAvH,EAAA,CAAAmH,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAzH,EAAA,CAAAmH,iBAAA,CAAAK,EAAA,CAAAE,MAAA;IAAA;EAAA;;;YAA3BxF,2BAA2B;MAAAyF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXxCjI,EAAA,CAAAC,cAAA,aAAkC;UAMtBD,EAAA,CAAAE,SAAA,WAAyC;UAAAF,EAAA,CAAAG,MAAA,2BAC3C;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAEPJ,EAAA,CAAAC,cAAA,aAAuB;UAErBD,EAAA,CAAAoB,UAAA,IAAA+G,0CAAA,iBAGM;UAGNnI,EAAA,CAAAoB,UAAA,KAAAgH,2CAAA,iBAGM;UAENpI,EAAA,CAAAC,cAAA,gBAAwD;UAAxBD,EAAA,CAAAqI,UAAA,sBAAAC,+DAAA;YAAA,OAAYJ,GAAA,CAAAzC,QAAA,EAAU;UAAA,EAAC;UAErDzF,EAAA,CAAAC,cAAA,eAAkB;UACyBD,EAAA,CAAAG,MAAA,uBAAe;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAChEJ,EAAA,CAAAC,cAAA,kBAIoG;UACjFD,EAAA,CAAAG,MAAA,0BAAkB;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAC5CJ,EAAA,CAAAoB,UAAA,KAAAmH,8CAAA,qBAES;UACXvI,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAoB,UAAA,KAAAoH,2CAAA,kBAEM;UACRxI,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAoB,UAAA,KAAAqH,2CAAA,mBAeM;UAENzI,EAAA,CAAAC,cAAA,eAAiB;UAGwBD,EAAA,CAAAG,MAAA,0BAAkB;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC/DJ,EAAA,CAAAE,SAAA,iBAM4F;UAC5FF,EAAA,CAAAoB,UAAA,KAAAsH,2CAAA,kBAEM;UACR1I,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,eAA2B;UACYD,EAAA,CAAAG,MAAA,0BAAkB;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC/DJ,EAAA,CAAAC,cAAA,kBAI4F;UAC1FD,EAAA,CAAAoB,UAAA,KAAAuH,8CAAA,qBAES;UACX3I,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAoB,UAAA,KAAAwH,2CAAA,kBAEM;UACR5I,EAAA,CAAAI,YAAA,EAAM;UAIRJ,EAAA,CAAAC,cAAA,eAAkB;UACyBD,EAAA,CAAAG,MAAA,8BAAsB;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAGvEJ,EAAA,CAAAoB,UAAA,KAAAyH,2CAAA,kBAKM;UAGN7I,EAAA,CAAAoB,UAAA,KAAA0H,2CAAA,kBAeM;UAGN9I,EAAA,CAAAoB,UAAA,KAAA2H,2CAAA,kBAGM;UAEN/I,EAAA,CAAAC,cAAA,eACuH;UACrHD,EAAA,CAAAG,MAAA,oCACF;UAAAH,EAAA,CAAAI,YAAA,EAAM;UAIRJ,EAAA,CAAAC,cAAA,eAAkB;UAC+BD,EAAA,CAAAG,MAAA,0BAAkB;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACzEJ,EAAA,CAAAE,SAAA,iBAMgH;UAChHF,EAAA,CAAAoB,UAAA,KAAA4H,2CAAA,kBAEM;UACRhJ,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,eAAkB;UACsBD,EAAA,CAAAG,MAAA,mCAA2B;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACzEJ,EAAA,CAAAE,SAAA,oBAK6E;UAC/EF,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,eAA4C;UAKxCD,EAAA,CAAAE,SAAA,aAAsC;UACtCF,EAAA,CAAAG,MAAA,yBACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAC,cAAA,kBAGiD;UAC/CD,EAAA,CAAAoB,UAAA,KAAA6H,4CAAA,mBAEO;UACPjJ,EAAA,CAAAoB,UAAA,KAAA8H,yCAAA,gBAA8D;UAC9DlJ,EAAA,CAAAG,MAAA,IACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;;;;;;;;UAxKPJ,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAW,UAAA,SAAAuH,GAAA,CAAA1H,OAAA,CAAa;UAMbR,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAW,UAAA,SAAAuH,GAAA,CAAAxH,KAAA,CAAW;UAKXV,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAW,UAAA,cAAAuH,GAAA,CAAAlF,WAAA,CAAyB;UAQzBhD,EAAA,CAAAK,SAAA,GAAiG;UAAjGL,EAAA,CAAAmJ,WAAA,iBAAAC,OAAA,GAAAlB,GAAA,CAAAlF,WAAA,CAAAuB,GAAA,+BAAA6E,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAlB,GAAA,CAAAlF,WAAA,CAAAuB,GAAA,+BAAA6E,OAAA,CAAAE,OAAA,EAAiG;UAEtEtJ,EAAA,CAAAK,SAAA,GAAU;UAAVL,EAAA,CAAAW,UAAA,YAAAuH,GAAA,CAAA1F,OAAA,CAAU;UAIRxC,EAAA,CAAAK,SAAA,GAAkF;UAAlFL,EAAA,CAAAW,UAAA,WAAA4I,OAAA,GAAArB,GAAA,CAAAlF,WAAA,CAAAuB,GAAA,+BAAAgF,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAArB,GAAA,CAAAlF,WAAA,CAAAuB,GAAA,+BAAAgF,OAAA,CAAAD,OAAA,EAAkF;UAM7GtJ,EAAA,CAAAK,SAAA,GAAoB;UAApBL,EAAA,CAAAW,UAAA,SAAAuH,GAAA,CAAAhH,cAAA,CAAoB;UA2BpBlB,EAAA,CAAAK,SAAA,GAAyF;UAAzFL,EAAA,CAAAmJ,WAAA,iBAAAK,OAAA,GAAAtB,GAAA,CAAAlF,WAAA,CAAAuB,GAAA,2BAAAiF,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAtB,GAAA,CAAAlF,WAAA,CAAAuB,GAAA,2BAAAiF,OAAA,CAAAF,OAAA,EAAyF;UADzFtJ,EAAA,CAAAW,UAAA,QAAAuH,GAAA,CAAAhB,YAAA,GAAsB;UAEOlH,EAAA,CAAAK,SAAA,GAA0E;UAA1EL,EAAA,CAAAW,UAAA,WAAA8I,OAAA,GAAAvB,GAAA,CAAAlF,WAAA,CAAAuB,GAAA,2BAAAkF,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAvB,GAAA,CAAAlF,WAAA,CAAAuB,GAAA,2BAAAkF,OAAA,CAAAH,OAAA,EAA0E;UAYvGtJ,EAAA,CAAAK,SAAA,GAAyF;UAAzFL,EAAA,CAAAmJ,WAAA,iBAAAO,QAAA,GAAAxB,GAAA,CAAAlF,WAAA,CAAAuB,GAAA,2BAAAmF,QAAA,CAAAL,OAAA,OAAAK,QAAA,GAAAxB,GAAA,CAAAlF,WAAA,CAAAuB,GAAA,2BAAAmF,QAAA,CAAAJ,OAAA,EAAyF;UAChEtJ,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAAW,UAAA,YAAAuH,GAAA,CAAAtF,gBAAA,CAAmB;UAIf5C,EAAA,CAAAK,SAAA,GAA0E;UAA1EL,EAAA,CAAAW,UAAA,WAAAgJ,QAAA,GAAAzB,GAAA,CAAAlF,WAAA,CAAAuB,GAAA,2BAAAoF,QAAA,CAAAN,OAAA,OAAAM,QAAA,GAAAzB,GAAA,CAAAlF,WAAA,CAAAuB,GAAA,2BAAAoF,QAAA,CAAAL,OAAA,EAA0E;UAWrGtJ,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAW,UAAA,SAAAuH,GAAA,CAAAxF,OAAA,CAAa;UAQb1C,EAAA,CAAAK,SAAA,GAA2C;UAA3CL,EAAA,CAAAW,UAAA,UAAAuH,GAAA,CAAAxF,OAAA,IAAAwF,GAAA,CAAAjG,cAAA,CAAA2H,MAAA,KAA2C;UAkB3C5J,EAAA,CAAAK,SAAA,GAA+D;UAA/DL,EAAA,CAAAW,UAAA,UAAAuH,GAAA,CAAAxF,OAAA,IAAAwF,GAAA,CAAAhH,cAAA,IAAAgH,GAAA,CAAAjG,cAAA,CAAA2H,MAAA,OAA+D;UAMhE5J,EAAA,CAAAK,SAAA,GAAiH;UAAjHL,EAAA,CAAA6J,WAAA,cAAAC,QAAA,GAAA5B,GAAA,CAAAlF,WAAA,CAAAuB,GAAA,+BAAAuF,QAAA,CAAAT,OAAA,OAAAS,QAAA,GAAA5B,GAAA,CAAAlF,WAAA,CAAAuB,GAAA,+BAAAuF,QAAA,CAAAR,OAAA,qBAAiH;UAcpHtJ,EAAA,CAAAK,SAAA,GAA6G;UAA7GL,EAAA,CAAAmJ,WAAA,iBAAAY,QAAA,GAAA7B,GAAA,CAAAlF,WAAA,CAAAuB,GAAA,qCAAAwF,QAAA,CAAAV,OAAA,OAAAU,QAAA,GAAA7B,GAAA,CAAAlF,WAAA,CAAAuB,GAAA,qCAAAwF,QAAA,CAAAT,OAAA,EAA6G;UAChFtJ,EAAA,CAAAK,SAAA,GAA8F;UAA9FL,EAAA,CAAAW,UAAA,WAAAqJ,QAAA,GAAA9B,GAAA,CAAAlF,WAAA,CAAAuB,GAAA,qCAAAyF,QAAA,CAAAX,OAAA,OAAAW,QAAA,GAAA9B,GAAA,CAAAlF,WAAA,CAAAuB,GAAA,qCAAAyF,QAAA,CAAAV,OAAA,EAA8F;UA4B3HtJ,EAAA,CAAAK,SAAA,GAA8C;UAA9CL,EAAA,CAAAW,UAAA,aAAAuH,GAAA,CAAAlF,WAAA,CAAAqG,OAAA,IAAAnB,GAAA,CAAAvF,UAAA,CAA8C;UACvC3C,EAAA,CAAAK,SAAA,GAAgB;UAAhBL,EAAA,CAAAW,UAAA,SAAAuH,GAAA,CAAAvF,UAAA,CAAgB;UAGnB3C,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAW,UAAA,UAAAuH,GAAA,CAAAvF,UAAA,CAAiB;UACrB3C,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAM,kBAAA,MAAA4H,GAAA,CAAAvF,UAAA,0CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}