{"ast": null, "code": "import { AppointmentStatus, AppointmentType } from '../../core/models/appointment.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/appointment.service\";\nimport * as i2 from \"../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../../shared/components/chat-access/chat-access.component\";\nfunction AppointmentListComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵelement(1, \"i\", 24);\n    i0.ɵɵtext(2, \" Book Appointment \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentListComponent_option_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r7.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r7.label, \" \");\n  }\n}\nfunction AppointmentListComponent_option_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r8.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r8.label, \" \");\n  }\n}\nfunction AppointmentListComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"i\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.error, \" \");\n  }\n}\nfunction AppointmentListComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"span\", 30);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 31);\n    i0.ɵɵtext(5, \"Loading appointments...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppointmentListComponent_div_27_div_2_i_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 53);\n  }\n}\nfunction AppointmentListComponent_div_27_div_2_i_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 54);\n  }\n}\nfunction AppointmentListComponent_div_27_div_2_p_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 55);\n    i0.ɵɵelement(1, \"i\", 56);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const appointment_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", appointment_r10.reasonForVisit, \" \");\n  }\n}\nfunction AppointmentListComponent_div_27_div_2_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"a\", 57);\n    i0.ɵɵelement(2, \"i\", 58);\n    i0.ɵɵtext(3, \" Join Video Call \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const appointment_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"href\", appointment_r10.meetingLink, i0.ɵɵsanitizeUrl);\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    appointmentId: a0,\n    doctorId: a1,\n    patientId: a2,\n    chatType: \"PRE_APPOINTMENT\",\n    buttonText: \"Chat\",\n    buttonClass: \"btn-info\",\n    size: \"sm\",\n    showIcon: false\n  };\n};\nfunction AppointmentListComponent_div_27_div_2_app_chat_access_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-chat-access\", 59);\n  }\n  if (rf & 2) {\n    const appointment_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"config\", i0.ɵɵpureFunction3(1, _c0, appointment_r10.id, appointment_r10.doctor.id, appointment_r10.patient.id));\n  }\n}\nconst _c1 = function (a0, a1, a2) {\n  return {\n    appointmentId: a0,\n    doctorId: a1,\n    patientId: a2,\n    chatType: \"POST_APPOINTMENT\",\n    buttonText: \"Follow-up\",\n    buttonClass: \"btn-success\",\n    size: \"sm\",\n    showIcon: false\n  };\n};\nfunction AppointmentListComponent_div_27_div_2_app_chat_access_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-chat-access\", 59);\n  }\n  if (rf & 2) {\n    const appointment_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"config\", i0.ɵɵpureFunction3(1, _c1, appointment_r10.id, appointment_r10.doctor.id, appointment_r10.patient.id));\n  }\n}\nfunction AppointmentListComponent_div_27_div_2_button_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function AppointmentListComponent_div_27_div_2_button_32_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const appointment_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.cancelAppointment(appointment_r10));\n    });\n    i0.ɵɵelement(1, \"i\", 61);\n    i0.ɵɵtext(2, \" Cancel \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentListComponent_div_27_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 8)(3, \"div\", 36)(4, \"div\")(5, \"h6\", 37);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"small\", 38);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 39);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 40)(12, \"p\", 41);\n    i0.ɵɵelement(13, \"i\", 42);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 41);\n    i0.ɵɵelement(16, \"i\", 43);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 41);\n    i0.ɵɵtemplate(19, AppointmentListComponent_div_27_div_2_i_19_Template, 1, 0, \"i\", 44);\n    i0.ɵɵtemplate(20, AppointmentListComponent_div_27_div_2_i_20_Template, 1, 0, \"i\", 45);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, AppointmentListComponent_div_27_div_2_p_22_Template, 3, 1, \"p\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, AppointmentListComponent_div_27_div_2_div_23_Template, 4, 1, \"div\", 46);\n    i0.ɵɵelementStart(24, \"div\", 47)(25, \"div\", 48)(26, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function AppointmentListComponent_div_27_div_2_Template_button_click_26_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r26);\n      const appointment_r10 = restoredCtx.$implicit;\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.viewAppointment(appointment_r10));\n    });\n    i0.ɵɵelement(27, \"i\", 50);\n    i0.ɵɵtext(28, \" View \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(29, AppointmentListComponent_div_27_div_2_app_chat_access_29_Template, 1, 5, \"app-chat-access\", 51);\n    i0.ɵɵtemplate(30, AppointmentListComponent_div_27_div_2_app_chat_access_30_Template, 1, 5, \"app-chat-access\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\");\n    i0.ɵɵtemplate(32, AppointmentListComponent_div_27_div_2_button_32_Template, 3, 0, \"button\", 52);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const appointment_r10 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.getOtherParty(appointment_r10), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r9.getOtherPartyRole(appointment_r10));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r9.getStatusBadgeClass(appointment_r10.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.getStatusDisplayName(appointment_r10.status), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.formatDate(appointment_r10.date), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r9.formatTime(appointment_r10.startTime), \" - \", ctx_r9.formatTime(appointment_r10.endTime), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", appointment_r10.type === \"VIDEO_CALL\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", appointment_r10.type === \"IN_PERSON\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.getTypeDisplayName(appointment_r10.type), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", appointment_r10.reasonForVisit);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", appointment_r10.meetingLink && appointment_r10.type === \"VIDEO_CALL\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isBeforeAppointment(appointment_r10));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isAfterAppointment(appointment_r10));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.canCancelAppointment(appointment_r10));\n  }\n}\nfunction AppointmentListComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 1);\n    i0.ɵɵtemplate(2, AppointmentListComponent_div_27_div_2_Template, 33, 15, \"div\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.filteredAppointments);\n  }\n}\nfunction AppointmentListComponent_div_28_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"You haven't booked any appointments yet.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentListComponent_div_28_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"You don't have any appointments scheduled.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentListComponent_div_28_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵelement(1, \"i\", 24);\n    i0.ɵɵtext(2, \" Book Your First Appointment \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentListComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelement(1, \"i\", 63);\n    i0.ɵɵelementStart(2, \"h5\", 38);\n    i0.ɵɵtext(3, \"No appointments found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 64);\n    i0.ɵɵtemplate(5, AppointmentListComponent_div_28_span_5_Template, 2, 0, \"span\", 65);\n    i0.ɵɵtemplate(6, AppointmentListComponent_div_28_span_6_Template, 2, 0, \"span\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, AppointmentListComponent_div_28_button_7_Template, 3, 0, \"button\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isPatient());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isDoctor());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isPatient());\n  }\n}\nexport class AppointmentListComponent {\n  constructor(appointmentService, authService, router) {\n    this.appointmentService = appointmentService;\n    this.authService = authService;\n    this.router = router;\n    this.appointments = [];\n    this.filteredAppointments = [];\n    this.currentUser = null;\n    this.loading = false;\n    this.error = null;\n    // Filter options\n    this.statusFilter = '';\n    this.typeFilter = '';\n    this.statusOptions = [{\n      value: '',\n      label: 'All Statuses'\n    }, {\n      value: AppointmentStatus.PENDING,\n      label: 'Pending'\n    }, {\n      value: AppointmentStatus.SCHEDULED,\n      label: 'Scheduled'\n    }, {\n      value: AppointmentStatus.CONFIRMED,\n      label: 'Confirmed'\n    }, {\n      value: AppointmentStatus.COMPLETED,\n      label: 'Completed'\n    }, {\n      value: AppointmentStatus.CANCELLED,\n      label: 'Cancelled'\n    }];\n    this.typeOptions = [{\n      value: '',\n      label: 'All Types'\n    }, {\n      value: AppointmentType.IN_PERSON,\n      label: 'In Person'\n    }, {\n      value: AppointmentType.VIDEO_CALL,\n      label: 'Video Call'\n    }];\n  }\n  ngOnInit() {\n    this.currentUser = this.authService.getCurrentUser();\n    this.loadAppointments();\n  }\n  loadAppointments() {\n    this.loading = true;\n    this.error = null;\n    this.appointmentService.getAppointments().subscribe({\n      next: appointments => {\n        this.appointments = appointments;\n        this.applyFilters();\n        this.loading = false;\n      },\n      error: error => {\n        this.error = 'Failed to load appointments. Please try again.';\n        this.loading = false;\n        console.error('Error loading appointments:', error);\n      }\n    });\n  }\n  applyFilters() {\n    this.filteredAppointments = this.appointments.filter(appointment => {\n      const statusMatch = !this.statusFilter || appointment.status === this.statusFilter;\n      const typeMatch = !this.typeFilter || appointment.type === this.typeFilter;\n      return statusMatch && typeMatch;\n    });\n  }\n  onStatusFilterChange() {\n    this.applyFilters();\n  }\n  onTypeFilterChange() {\n    this.applyFilters();\n  }\n  viewAppointment(appointment) {\n    this.router.navigate(['/appointments', appointment.id]);\n  }\n  editAppointment(appointment) {\n    // For now, navigate to details page where editing can be done\n    this.router.navigate(['/appointments', appointment.id]);\n  }\n  cancelAppointment(appointment) {\n    if (confirm('Are you sure you want to cancel this appointment?')) {\n      this.appointmentService.cancelAppointment(appointment.id).subscribe({\n        next: () => {\n          this.loadAppointments(); // Reload the list\n        },\n\n        error: error => {\n          this.error = 'Failed to cancel appointment. Please try again.';\n          console.error('Error canceling appointment:', error);\n        }\n      });\n    }\n  }\n  getStatusDisplayName(status) {\n    return this.appointmentService.getStatusDisplayName(status);\n  }\n  getTypeDisplayName(type) {\n    return this.appointmentService.getTypeDisplayName(type);\n  }\n  getStatusBadgeClass(status) {\n    return this.appointmentService.getStatusBadgeClass(status);\n  }\n  formatDate(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'short',\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  }\n  formatTime(timeString) {\n    const [hours, minutes] = timeString.split(':');\n    const hour = parseInt(hours);\n    const ampm = hour >= 12 ? 'PM' : 'AM';\n    const displayHour = hour % 12 || 12;\n    return `${displayHour}:${minutes} ${ampm}`;\n  }\n  isDoctor() {\n    return this.currentUser?.role === 'DOCTOR';\n  }\n  isPatient() {\n    return this.currentUser?.role === 'PATIENT';\n  }\n  canCancelAppointment(appointment) {\n    return appointment.status === AppointmentStatus.PENDING || appointment.status === AppointmentStatus.SCHEDULED || appointment.status === AppointmentStatus.CONFIRMED;\n  }\n  getOtherParty(appointment) {\n    if (this.isDoctor()) {\n      return appointment.patient.fullName;\n    } else {\n      return appointment.doctor.fullName;\n    }\n  }\n  getOtherPartyRole(appointment) {\n    if (this.isDoctor()) {\n      return 'Patient';\n    } else {\n      return 'Doctor';\n    }\n  }\n  static {\n    this.ɵfac = function AppointmentListComponent_Factory(t) {\n      return new (t || AppointmentListComponent)(i0.ɵɵdirectiveInject(i1.AppointmentService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppointmentListComponent,\n      selectors: [[\"app-appointment-list\"]],\n      decls: 29,\n      vars: 9,\n      consts: [[1, \"container-fluid\", \"py-4\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"card-title\", \"mb-0\"], [1, \"fas\", \"fa-calendar-alt\", \"me-2\"], [\"class\", \"btn btn-primary\", \"routerLink\", \"/appointments/book\", 4, \"ngIf\"], [1, \"card-body\"], [1, \"row\", \"mb-4\"], [1, \"col-md-4\"], [\"for\", \"statusFilter\", 1, \"form-label\"], [\"id\", \"statusFilter\", 1, \"form-select\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"typeFilter\", 1, \"form-label\"], [\"id\", \"typeFilter\", 1, \"form-select\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [1, \"col-md-4\", \"d-flex\", \"align-items-end\"], [1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\", \"me-2\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"appointments-list\", 4, \"ngIf\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [\"routerLink\", \"/appointments/book\", 1, \"btn\", \"btn-primary\"], [1, \"fas\", \"fa-plus\", \"me-2\"], [3, \"value\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-2\"], [1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\", \"text-muted\"], [1, \"appointments-list\"], [\"class\", \"col-lg-6 mb-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-lg-6\", \"mb-4\"], [1, \"card\", \"appointment-card\", \"h-100\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-start\", \"mb-3\"], [1, \"card-title\", \"mb-1\"], [1, \"text-muted\"], [1, \"badge\", 3, \"ngClass\"], [1, \"appointment-details\"], [1, \"mb-2\"], [1, \"fas\", \"fa-calendar\", \"me-2\", \"text-muted\"], [1, \"fas\", \"fa-clock\", \"me-2\", \"text-muted\"], [\"class\", \"fas fa-video me-2 text-muted\", 4, \"ngIf\"], [\"class\", \"fas fa-user-friends me-2 text-muted\", 4, \"ngIf\"], [\"class\", \"mb-3\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"role\", \"group\", 1, \"btn-group\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"fas\", \"fa-eye\", \"me-1\"], [3, \"config\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-outline-danger\", 3, \"click\", 4, \"ngIf\"], [1, \"fas\", \"fa-video\", \"me-2\", \"text-muted\"], [1, \"fas\", \"fa-user-friends\", \"me-2\", \"text-muted\"], [1, \"mb-3\"], [1, \"fas\", \"fa-notes-medical\", \"me-2\", \"text-muted\"], [\"target\", \"_blank\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"href\"], [1, \"fas\", \"fa-video\", \"me-2\"], [3, \"config\"], [1, \"btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [1, \"text-center\", \"py-5\"], [1, \"fas\", \"fa-calendar-times\", \"fa-3x\", \"text-muted\", \"mb-3\"], [1, \"text-muted\", \"mb-4\"], [4, \"ngIf\"]],\n      template: function AppointmentListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h4\", 5);\n          i0.ɵɵelement(6, \"i\", 6);\n          i0.ɵɵtext(7, \"My Appointments \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, AppointmentListComponent_button_8_Template, 3, 0, \"button\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 8)(10, \"div\", 9)(11, \"div\", 10)(12, \"label\", 11);\n          i0.ɵɵtext(13, \"Filter by Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"select\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function AppointmentListComponent_Template_select_ngModelChange_14_listener($event) {\n            return ctx.statusFilter = $event;\n          })(\"change\", function AppointmentListComponent_Template_select_change_14_listener() {\n            return ctx.onStatusFilterChange();\n          });\n          i0.ɵɵtemplate(15, AppointmentListComponent_option_15_Template, 2, 2, \"option\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 10)(17, \"label\", 14);\n          i0.ɵɵtext(18, \"Filter by Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"select\", 15);\n          i0.ɵɵlistener(\"ngModelChange\", function AppointmentListComponent_Template_select_ngModelChange_19_listener($event) {\n            return ctx.typeFilter = $event;\n          })(\"change\", function AppointmentListComponent_Template_select_change_19_listener() {\n            return ctx.onTypeFilterChange();\n          });\n          i0.ɵɵtemplate(20, AppointmentListComponent_option_20_Template, 2, 2, \"option\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 16)(22, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function AppointmentListComponent_Template_button_click_22_listener() {\n            return ctx.loadAppointments();\n          });\n          i0.ɵɵelement(23, \"i\", 18);\n          i0.ɵɵtext(24, \" Refresh \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(25, AppointmentListComponent_div_25_Template, 3, 1, \"div\", 19);\n          i0.ɵɵtemplate(26, AppointmentListComponent_div_26_Template, 6, 0, \"div\", 20);\n          i0.ɵɵtemplate(27, AppointmentListComponent_div_27_Template, 3, 1, \"div\", 21);\n          i0.ɵɵtemplate(28, AppointmentListComponent_div_28_Template, 8, 3, \"div\", 22);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isPatient());\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.statusFilter);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.statusOptions);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.typeFilter);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.typeOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.filteredAppointments.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.filteredAppointments.length === 0);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel, i3.RouterLink, i6.ChatAccessComponent],\n      styles: [\".appointment-card[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;\\n  border: 1px solid #e3e6f0;\\n}\\n\\n.appointment-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\\n}\\n\\n.appointment-details[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n.appointment-details[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  width: 16px;\\n  text-align: center;\\n}\\n\\n.badge-warning[_ngcontent-%COMP%] {\\n  background-color: #f6c23e;\\n  color: #1a1a1a;\\n}\\n\\n.badge-info[_ngcontent-%COMP%] {\\n  background-color: #36b9cc;\\n}\\n\\n.badge-primary[_ngcontent-%COMP%] {\\n  background-color: #4e73df;\\n}\\n\\n.badge-success[_ngcontent-%COMP%] {\\n  background-color: #1cc88a;\\n}\\n\\n.badge-danger[_ngcontent-%COMP%] {\\n  background-color: #e74a3b;\\n}\\n\\n.badge-secondary[_ngcontent-%COMP%] {\\n  background-color: #858796;\\n}\\n\\n.form-select[_ngcontent-%COMP%]:focus {\\n  border-color: #667eea;\\n  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border: none;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\\n  transform: translateY(-1px);\\n}\\n\\n.btn-outline-primary[_ngcontent-%COMP%] {\\n  border-color: #667eea;\\n  color: #667eea;\\n}\\n\\n.btn-outline-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #667eea;\\n  border-color: #667eea;\\n}\\n\\n.btn-outline-danger[_ngcontent-%COMP%] {\\n  border-color: #e74a3b;\\n  color: #e74a3b;\\n}\\n\\n.btn-outline-danger[_ngcontent-%COMP%]:hover {\\n  background-color: #e74a3b;\\n  border-color: #e74a3b;\\n}\\n\\n.btn-outline-secondary[_ngcontent-%COMP%] {\\n  border-color: #858796;\\n  color: #858796;\\n}\\n\\n.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #858796;\\n  border-color: #858796;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  border: 1px solid #e3e6f0;\\n  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);\\n}\\n\\n.card-title[_ngcontent-%COMP%] {\\n  color: #5a5c69;\\n  font-weight: 600;\\n}\\n\\n.alert-danger[_ngcontent-%COMP%] {\\n  border-left: 4px solid #e74a3b;\\n}\\n\\n.spinner-border[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwb2ludG1lbnRzL2FwcG9pbnRtZW50LWxpc3QvYXBwb2ludG1lbnQtbGlzdC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLG1FQUFBO0VBQ0EseUJBQUE7QUFDRjs7QUFFQTtFQUNFLDJCQUFBO0VBQ0EsNkNBQUE7QUFDRjs7QUFFQTtFQUNFLGlCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7QUFDRjs7QUFFQTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtBQUNGOztBQUVBO0VBQ0UseUJBQUE7QUFDRjs7QUFFQTtFQUNFLHlCQUFBO0FBQ0Y7O0FBRUE7RUFDRSx5QkFBQTtBQUNGOztBQUVBO0VBQ0UseUJBQUE7QUFDRjs7QUFFQTtFQUNFLHlCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxxQkFBQTtFQUNBLGtEQUFBO0FBQ0Y7O0FBRUE7RUFDRSw2REFBQTtFQUNBLFlBQUE7RUFDQSx5QkFBQTtBQUNGOztBQUVBO0VBQ0UsNkRBQUE7RUFDQSwyQkFBQTtBQUNGOztBQUVBO0VBQ0UscUJBQUE7RUFDQSxjQUFBO0FBQ0Y7O0FBRUE7RUFDRSx5QkFBQTtFQUNBLHFCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxxQkFBQTtFQUNBLGNBQUE7QUFDRjs7QUFFQTtFQUNFLHlCQUFBO0VBQ0EscUJBQUE7QUFDRjs7QUFFQTtFQUNFLHFCQUFBO0VBQ0EsY0FBQTtBQUNGOztBQUVBO0VBQ0UseUJBQUE7RUFDQSxxQkFBQTtBQUNGOztBQUVBO0VBQ0UseUJBQUE7RUFDQSxzREFBQTtBQUNGOztBQUVBO0VBQ0UsY0FBQTtFQUNBLGdCQUFBO0FBQ0Y7O0FBRUE7RUFDRSw4QkFBQTtBQUNGOztBQUVBO0VBQ0UsV0FBQTtFQUNBLFlBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5hcHBvaW50bWVudC1jYXJkIHtcbiAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnMgZWFzZS1pbi1vdXQsIGJveC1zaGFkb3cgMC4ycyBlYXNlLWluLW91dDtcbiAgYm9yZGVyOiAxcHggc29saWQgI2UzZTZmMDtcbn1cblxuLmFwcG9pbnRtZW50LWNhcmQ6aG92ZXIge1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XG4gIGJveC1zaGFkb3c6IDAgMC41cmVtIDFyZW0gcmdiYSgwLCAwLCAwLCAwLjE1KTtcbn1cblxuLmFwcG9pbnRtZW50LWRldGFpbHMge1xuICBmb250LXNpemU6IDAuOXJlbTtcbn1cblxuLmFwcG9pbnRtZW50LWRldGFpbHMgaSB7XG4gIHdpZHRoOiAxNnB4O1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG59XG5cbi5iYWRnZS13YXJuaW5nIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2Y2YzIzZTtcbiAgY29sb3I6ICMxYTFhMWE7XG59XG5cbi5iYWRnZS1pbmZvIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogIzM2YjljYztcbn1cblxuLmJhZGdlLXByaW1hcnkge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjNGU3M2RmO1xufVxuXG4uYmFkZ2Utc3VjY2VzcyB7XG4gIGJhY2tncm91bmQtY29sb3I6ICMxY2M4OGE7XG59XG5cbi5iYWRnZS1kYW5nZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTc0YTNiO1xufVxuXG4uYmFkZ2Utc2Vjb25kYXJ5IHtcbiAgYmFja2dyb3VuZC1jb2xvcjogIzg1ODc5Njtcbn1cblxuLmZvcm0tc2VsZWN0OmZvY3VzIHtcbiAgYm9yZGVyLWNvbG9yOiAjNjY3ZWVhO1xuICBib3gtc2hhZG93OiAwIDAgMCAwLjJyZW0gcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjI1KTtcbn1cblxuLmJ0bi1wcmltYXJ5IHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTtcbiAgYm9yZGVyOiBub25lO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xufVxuXG4uYnRuLXByaW1hcnk6aG92ZXIge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNWE2ZmQ4IDAlLCAjNmE0MTkwIDEwMCUpO1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XG59XG5cbi5idG4tb3V0bGluZS1wcmltYXJ5IHtcbiAgYm9yZGVyLWNvbG9yOiAjNjY3ZWVhO1xuICBjb2xvcjogIzY2N2VlYTtcbn1cblxuLmJ0bi1vdXRsaW5lLXByaW1hcnk6aG92ZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjNjY3ZWVhO1xuICBib3JkZXItY29sb3I6ICM2NjdlZWE7XG59XG5cbi5idG4tb3V0bGluZS1kYW5nZXIge1xuICBib3JkZXItY29sb3I6ICNlNzRhM2I7XG4gIGNvbG9yOiAjZTc0YTNiO1xufVxuXG4uYnRuLW91dGxpbmUtZGFuZ2VyOmhvdmVyIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2U3NGEzYjtcbiAgYm9yZGVyLWNvbG9yOiAjZTc0YTNiO1xufVxuXG4uYnRuLW91dGxpbmUtc2Vjb25kYXJ5IHtcbiAgYm9yZGVyLWNvbG9yOiAjODU4Nzk2O1xuICBjb2xvcjogIzg1ODc5Njtcbn1cblxuLmJ0bi1vdXRsaW5lLXNlY29uZGFyeTpob3ZlciB7XG4gIGJhY2tncm91bmQtY29sb3I6ICM4NTg3OTY7XG4gIGJvcmRlci1jb2xvcjogIzg1ODc5Njtcbn1cblxuLmNhcmQge1xuICBib3JkZXI6IDFweCBzb2xpZCAjZTNlNmYwO1xuICBib3gtc2hhZG93OiAwIDAuMTVyZW0gMS43NXJlbSAwIHJnYmEoNTgsIDU5LCA2OSwgMC4xNSk7XG59XG5cbi5jYXJkLXRpdGxlIHtcbiAgY29sb3I6ICM1YTVjNjk7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG59XG5cbi5hbGVydC1kYW5nZXIge1xuICBib3JkZXItbGVmdDogNHB4IHNvbGlkICNlNzRhM2I7XG59XG5cbi5zcGlubmVyLWJvcmRlciB7XG4gIHdpZHRoOiAzcmVtO1xuICBoZWlnaHQ6IDNyZW07XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AppointmentStatus", "AppointmentType", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "option_r7", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "option_r8", "ctx_r3", "error", "appointment_r10", "reasonForVisit", "meetingLink", "ɵɵsanitizeUrl", "ɵɵpureFunction3", "_c0", "id", "doctor", "patient", "_c1", "ɵɵlistener", "AppointmentListComponent_div_27_div_2_button_32_Template_button_click_0_listener", "ɵɵrestoreView", "_r24", "ɵɵnextContext", "$implicit", "ctx_r22", "ɵɵresetView", "cancelAppointment", "ɵɵtemplate", "AppointmentListComponent_div_27_div_2_i_19_Template", "AppointmentListComponent_div_27_div_2_i_20_Template", "AppointmentListComponent_div_27_div_2_p_22_Template", "AppointmentListComponent_div_27_div_2_div_23_Template", "AppointmentListComponent_div_27_div_2_Template_button_click_26_listener", "restoredCtx", "_r26", "ctx_r25", "viewAppointment", "AppointmentListComponent_div_27_div_2_app_chat_access_29_Template", "AppointmentListComponent_div_27_div_2_app_chat_access_30_Template", "AppointmentListComponent_div_27_div_2_button_32_Template", "ctx_r9", "getOther<PERSON><PERSON>y", "ɵɵtextInterpolate", "getOtherPartyRole", "getStatusBadgeClass", "status", "getStatusDisplayName", "formatDate", "date", "ɵɵtextInterpolate2", "formatTime", "startTime", "endTime", "type", "getTypeDisplayName", "isBeforeAppointment", "isAfterAppointment", "canCancelAppointment", "AppointmentListComponent_div_27_div_2_Template", "ctx_r5", "filteredAppointments", "AppointmentListComponent_div_28_span_5_Template", "AppointmentListComponent_div_28_span_6_Template", "AppointmentListComponent_div_28_button_7_Template", "ctx_r6", "isPatient", "isDoctor", "AppointmentListComponent", "constructor", "appointmentService", "authService", "router", "appointments", "currentUser", "loading", "statusFilter", "typeFilter", "statusOptions", "PENDING", "SCHEDULED", "CONFIRMED", "COMPLETED", "CANCELLED", "typeOptions", "IN_PERSON", "VIDEO_CALL", "ngOnInit", "getCurrentUser", "loadAppointments", "getAppointments", "subscribe", "next", "applyFilters", "console", "filter", "appointment", "statusMatch", "typeMatch", "onStatusFilterChange", "onTypeFilterChange", "navigate", "editAppointment", "confirm", "dateString", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "timeString", "hours", "minutes", "split", "hour", "parseInt", "ampm", "displayHour", "role", "fullName", "ɵɵdirectiveInject", "i1", "AppointmentService", "i2", "AuthService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "AppointmentListComponent_Template", "rf", "ctx", "AppointmentListComponent_button_8_Template", "AppointmentListComponent_Template_select_ngModelChange_14_listener", "$event", "AppointmentListComponent_Template_select_change_14_listener", "AppointmentListComponent_option_15_Template", "AppointmentListComponent_Template_select_ngModelChange_19_listener", "AppointmentListComponent_Template_select_change_19_listener", "AppointmentListComponent_option_20_Template", "AppointmentListComponent_Template_button_click_22_listener", "AppointmentListComponent_div_25_Template", "AppointmentListComponent_div_26_Template", "AppointmentListComponent_div_27_Template", "AppointmentListComponent_div_28_Template", "length"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/appointments/appointment-list/appointment-list.component.ts", "/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/appointments/appointment-list/appointment-list.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AppointmentService } from '../../core/services/appointment.service';\nimport { AuthService } from '../../core/services/auth.service';\nimport { Appointment, AppointmentStatus, AppointmentType } from '../../core/models/appointment.model';\nimport { User } from '../../core/models/user.model';\n\n@Component({\n  selector: 'app-appointment-list',\n  templateUrl: './appointment-list.component.html',\n  styleUrls: ['./appointment-list.component.scss']\n})\nexport class AppointmentListComponent implements OnInit {\n  appointments: Appointment[] = [];\n  filteredAppointments: Appointment[] = [];\n  currentUser: User | null = null;\n  loading = false;\n  error: string | null = null;\n\n  // Filter options\n  statusFilter: AppointmentStatus | '' = '';\n  typeFilter: AppointmentType | '' = '';\n  \n  statusOptions = [\n    { value: '', label: 'All Statuses' },\n    { value: AppointmentStatus.PENDING, label: 'Pending' },\n    { value: AppointmentStatus.SCHEDULED, label: 'Scheduled' },\n    { value: AppointmentStatus.CONFIRMED, label: 'Confirmed' },\n    { value: AppointmentStatus.COMPLETED, label: 'Completed' },\n    { value: AppointmentStatus.CANCELLED, label: 'Cancelled' }\n  ];\n\n  typeOptions = [\n    { value: '', label: 'All Types' },\n    { value: AppointmentType.IN_PERSON, label: 'In Person' },\n    { value: AppointmentType.VIDEO_CALL, label: 'Video Call' }\n  ];\n\n  constructor(\n    private appointmentService: AppointmentService,\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.currentUser = this.authService.getCurrentUser();\n    this.loadAppointments();\n  }\n\n  loadAppointments(): void {\n    this.loading = true;\n    this.error = null;\n\n    this.appointmentService.getAppointments().subscribe({\n      next: (appointments) => {\n        this.appointments = appointments;\n        this.applyFilters();\n        this.loading = false;\n      },\n      error: (error) => {\n        this.error = 'Failed to load appointments. Please try again.';\n        this.loading = false;\n        console.error('Error loading appointments:', error);\n      }\n    });\n  }\n\n  applyFilters(): void {\n    this.filteredAppointments = this.appointments.filter(appointment => {\n      const statusMatch = !this.statusFilter || appointment.status === this.statusFilter;\n      const typeMatch = !this.typeFilter || appointment.type === this.typeFilter;\n      return statusMatch && typeMatch;\n    });\n  }\n\n  onStatusFilterChange(): void {\n    this.applyFilters();\n  }\n\n  onTypeFilterChange(): void {\n    this.applyFilters();\n  }\n\n  viewAppointment(appointment: Appointment): void {\n    this.router.navigate(['/appointments', appointment.id]);\n  }\n\n  editAppointment(appointment: Appointment): void {\n    // For now, navigate to details page where editing can be done\n    this.router.navigate(['/appointments', appointment.id]);\n  }\n\n  cancelAppointment(appointment: Appointment): void {\n    if (confirm('Are you sure you want to cancel this appointment?')) {\n      this.appointmentService.cancelAppointment(appointment.id).subscribe({\n        next: () => {\n          this.loadAppointments(); // Reload the list\n        },\n        error: (error) => {\n          this.error = 'Failed to cancel appointment. Please try again.';\n          console.error('Error canceling appointment:', error);\n        }\n      });\n    }\n  }\n\n  getStatusDisplayName(status: AppointmentStatus): string {\n    return this.appointmentService.getStatusDisplayName(status);\n  }\n\n  getTypeDisplayName(type: AppointmentType): string {\n    return this.appointmentService.getTypeDisplayName(type);\n  }\n\n  getStatusBadgeClass(status: AppointmentStatus): string {\n    return this.appointmentService.getStatusBadgeClass(status);\n  }\n\n  formatDate(dateString: string): string {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'short',\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  }\n\n  formatTime(timeString: string): string {\n    const [hours, minutes] = timeString.split(':');\n    const hour = parseInt(hours);\n    const ampm = hour >= 12 ? 'PM' : 'AM';\n    const displayHour = hour % 12 || 12;\n    return `${displayHour}:${minutes} ${ampm}`;\n  }\n\n  isDoctor(): boolean {\n    return this.currentUser?.role === 'DOCTOR';\n  }\n\n  isPatient(): boolean {\n    return this.currentUser?.role === 'PATIENT';\n  }\n\n  canCancelAppointment(appointment: Appointment): boolean {\n    return appointment.status === AppointmentStatus.PENDING || \n           appointment.status === AppointmentStatus.SCHEDULED ||\n           appointment.status === AppointmentStatus.CONFIRMED;\n  }\n\n  getOtherParty(appointment: Appointment): string {\n    if (this.isDoctor()) {\n      return appointment.patient.fullName;\n    } else {\n      return appointment.doctor.fullName;\n    }\n  }\n\n  getOtherPartyRole(appointment: Appointment): string {\n    if (this.isDoctor()) {\n      return 'Patient';\n    } else {\n      return 'Doctor';\n    }\n  }\n}\n", "<div class=\"container-fluid py-4\">\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"card\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h4 class=\"card-title mb-0\">\n            <i class=\"fas fa-calendar-alt me-2\"></i>My Appointments\n          </h4>\n          <button \n            *ngIf=\"isPatient()\" \n            class=\"btn btn-primary\"\n            routerLink=\"/appointments/book\">\n            <i class=\"fas fa-plus me-2\"></i>\n            Book Appointment\n          </button>\n        </div>\n        <div class=\"card-body\">\n          <!-- Filters -->\n          <div class=\"row mb-4\">\n            <div class=\"col-md-4\">\n              <label for=\"statusFilter\" class=\"form-label\">Filter by Status</label>\n              <select \n                id=\"statusFilter\" \n                class=\"form-select\"\n                [(ngModel)]=\"statusFilter\"\n                (change)=\"onStatusFilterChange()\">\n                <option *ngFor=\"let option of statusOptions\" [value]=\"option.value\">\n                  {{ option.label }}\n                </option>\n              </select>\n            </div>\n            <div class=\"col-md-4\">\n              <label for=\"typeFilter\" class=\"form-label\">Filter by Type</label>\n              <select \n                id=\"typeFilter\" \n                class=\"form-select\"\n                [(ngModel)]=\"typeFilter\"\n                (change)=\"onTypeFilterChange()\">\n                <option *ngFor=\"let option of typeOptions\" [value]=\"option.value\">\n                  {{ option.label }}\n                </option>\n              </select>\n            </div>\n            <div class=\"col-md-4 d-flex align-items-end\">\n              <button \n                class=\"btn btn-outline-secondary\"\n                (click)=\"loadAppointments()\">\n                <i class=\"fas fa-sync-alt me-2\"></i>\n                Refresh\n              </button>\n            </div>\n          </div>\n\n          <!-- Error Message -->\n          <div *ngIf=\"error\" class=\"alert alert-danger\" role=\"alert\">\n            <i class=\"fas fa-exclamation-triangle me-2\"></i>\n            {{ error }}\n          </div>\n\n          <!-- Loading Spinner -->\n          <div *ngIf=\"loading\" class=\"text-center py-4\">\n            <div class=\"spinner-border text-primary\" role=\"status\">\n              <span class=\"visually-hidden\">Loading...</span>\n            </div>\n            <p class=\"mt-2 text-muted\">Loading appointments...</p>\n          </div>\n\n          <!-- Appointments List -->\n          <div *ngIf=\"!loading && filteredAppointments.length > 0\" class=\"appointments-list\">\n            <div class=\"row\">\n              <div class=\"col-lg-6 mb-4\" *ngFor=\"let appointment of filteredAppointments\">\n                <div class=\"card appointment-card h-100\">\n                  <div class=\"card-body\">\n                    <!-- Header -->\n                    <div class=\"d-flex justify-content-between align-items-start mb-3\">\n                      <div>\n                        <h6 class=\"card-title mb-1\">\n                          {{ getOtherParty(appointment) }}\n                        </h6>\n                        <small class=\"text-muted\">{{ getOtherPartyRole(appointment) }}</small>\n                      </div>\n                      <span class=\"badge\" [ngClass]=\"getStatusBadgeClass(appointment.status)\">\n                        {{ getStatusDisplayName(appointment.status) }}\n                      </span>\n                    </div>\n\n                    <!-- Appointment Details -->\n                    <div class=\"appointment-details\">\n                      <p class=\"mb-2\">\n                        <i class=\"fas fa-calendar me-2 text-muted\"></i>\n                        {{ formatDate(appointment.date) }}\n                      </p>\n                      <p class=\"mb-2\">\n                        <i class=\"fas fa-clock me-2 text-muted\"></i>\n                        {{ formatTime(appointment.startTime) }} - {{ formatTime(appointment.endTime) }}\n                      </p>\n                      <p class=\"mb-2\">\n                        <i class=\"fas fa-video me-2 text-muted\" *ngIf=\"appointment.type === 'VIDEO_CALL'\"></i>\n                        <i class=\"fas fa-user-friends me-2 text-muted\" *ngIf=\"appointment.type === 'IN_PERSON'\"></i>\n                        {{ getTypeDisplayName(appointment.type) }}\n                      </p>\n                      <p class=\"mb-3\" *ngIf=\"appointment.reasonForVisit\">\n                        <i class=\"fas fa-notes-medical me-2 text-muted\"></i>\n                        {{ appointment.reasonForVisit }}\n                      </p>\n                    </div>\n\n                    <!-- Meeting Link -->\n                    <div *ngIf=\"appointment.meetingLink && appointment.type === 'VIDEO_CALL'\" class=\"mb-3\">\n                      <a \n                        [href]=\"appointment.meetingLink\" \n                        target=\"_blank\" \n                        class=\"btn btn-sm btn-outline-primary\">\n                        <i class=\"fas fa-video me-2\"></i>\n                        Join Video Call\n                      </a>\n                    </div>\n\n                    <!-- Actions -->\n                    <div class=\"d-flex justify-content-between align-items-center\">\n                      <div class=\"btn-group\" role=\"group\">\n                        <button\n                          class=\"btn btn-sm btn-outline-primary\"\n                          (click)=\"viewAppointment(appointment)\">\n                          <i class=\"fas fa-eye me-1\"></i>\n                          View\n                        </button>\n\n                        <!-- Chat Access Buttons -->\n                        <app-chat-access\n                          *ngIf=\"isBeforeAppointment(appointment)\"\n                          [config]=\"{\n                            appointmentId: appointment.id,\n                            doctorId: appointment.doctor.id,\n                            patientId: appointment.patient.id,\n                            chatType: 'PRE_APPOINTMENT',\n                            buttonText: 'Chat',\n                            buttonClass: 'btn-info',\n                            size: 'sm',\n                            showIcon: false\n                          }\">\n                        </app-chat-access>\n\n                        <app-chat-access\n                          *ngIf=\"isAfterAppointment(appointment)\"\n                          [config]=\"{\n                            appointmentId: appointment.id,\n                            doctorId: appointment.doctor.id,\n                            patientId: appointment.patient.id,\n                            chatType: 'POST_APPOINTMENT',\n                            buttonText: 'Follow-up',\n                            buttonClass: 'btn-success',\n                            size: 'sm',\n                            showIcon: false\n                          }\">\n                        </app-chat-access>\n                      </div>\n\n                      <div>\n                        <button\n                          *ngIf=\"canCancelAppointment(appointment)\"\n                          class=\"btn btn-sm btn-outline-danger\"\n                          (click)=\"cancelAppointment(appointment)\">\n                          <i class=\"fas fa-times me-1\"></i>\n                          Cancel\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- No Appointments -->\n          <div *ngIf=\"!loading && filteredAppointments.length === 0\" class=\"text-center py-5\">\n            <i class=\"fas fa-calendar-times fa-3x text-muted mb-3\"></i>\n            <h5 class=\"text-muted\">No appointments found</h5>\n            <p class=\"text-muted mb-4\">\n              <span *ngIf=\"isPatient()\">You haven't booked any appointments yet.</span>\n              <span *ngIf=\"isDoctor()\">You don't have any appointments scheduled.</span>\n            </p>\n            <button \n              *ngIf=\"isPatient()\" \n              class=\"btn btn-primary\"\n              routerLink=\"/appointments/book\">\n              <i class=\"fas fa-plus me-2\"></i>\n              Book Your First Appointment\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAIA,SAAsBA,iBAAiB,EAAEC,eAAe,QAAQ,qCAAqC;;;;;;;;;;ICI3FC,EAAA,CAAAC,cAAA,iBAGkC;IAChCD,EAAA,CAAAE,SAAA,YAAgC;IAChCF,EAAA,CAAAG,MAAA,yBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IAYHJ,EAAA,CAAAC,cAAA,iBAAoE;IAClED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAFoCJ,EAAA,CAAAK,UAAA,UAAAC,SAAA,CAAAC,KAAA,CAAsB;IACjEP,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAH,SAAA,CAAAI,KAAA,MACF;;;;;IAUAV,EAAA,CAAAC,cAAA,iBAAkE;IAChED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAFkCJ,EAAA,CAAAK,UAAA,UAAAM,SAAA,CAAAJ,KAAA,CAAsB;IAC/DP,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAE,SAAA,CAAAD,KAAA,MACF;;;;;IAcNV,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAE,SAAA,YAAgD;IAChDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAG,MAAA,CAAAC,KAAA,MACF;;;;;IAGAb,EAAA,CAAAC,cAAA,cAA8C;IAEZD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEjDJ,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAG,MAAA,8BAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAiC1CJ,EAAA,CAAAE,SAAA,YAAsF;;;;;IACtFF,EAAA,CAAAE,SAAA,YAA4F;;;;;IAG9FF,EAAA,CAAAC,cAAA,YAAmD;IACjDD,EAAA,CAAAE,SAAA,YAAoD;IACpDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAK,eAAA,CAAAC,cAAA,MACF;;;;;IAIFf,EAAA,CAAAC,cAAA,cAAuF;IAKnFD,EAAA,CAAAE,SAAA,YAAiC;IACjCF,EAAA,CAAAG,MAAA,wBACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IALFJ,EAAA,CAAAQ,SAAA,GAAgC;IAAhCR,EAAA,CAAAK,UAAA,SAAAS,eAAA,CAAAE,WAAA,EAAAhB,EAAA,CAAAiB,aAAA,CAAgC;;;;;;;;;;;;;;;;;IAmBhCjB,EAAA,CAAAE,SAAA,0BAYkB;;;;IAVhBF,EAAA,CAAAK,UAAA,WAAAL,EAAA,CAAAkB,eAAA,IAAAC,GAAA,EAAAL,eAAA,CAAAM,EAAA,EAAAN,eAAA,CAAAO,MAAA,CAAAD,EAAA,EAAAN,eAAA,CAAAQ,OAAA,CAAAF,EAAA,EASE;;;;;;;;;;;;;;;;;IAGJpB,EAAA,CAAAE,SAAA,0BAYkB;;;;IAVhBF,EAAA,CAAAK,UAAA,WAAAL,EAAA,CAAAkB,eAAA,IAAAK,GAAA,EAAAT,eAAA,CAAAM,EAAA,EAAAN,eAAA,CAAAO,MAAA,CAAAD,EAAA,EAAAN,eAAA,CAAAQ,OAAA,CAAAF,EAAA,EASE;;;;;;IAKJpB,EAAA,CAAAC,cAAA,iBAG2C;IAAzCD,EAAA,CAAAwB,UAAA,mBAAAC,iFAAA;MAAAzB,EAAA,CAAA0B,aAAA,CAAAC,IAAA;MAAA,MAAAb,eAAA,GAAAd,EAAA,CAAA4B,aAAA,GAAAC,SAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA+B,WAAA,CAAAD,OAAA,CAAAE,iBAAA,CAAAlB,eAAA,CAA8B;IAAA,EAAC;IACxCd,EAAA,CAAAE,SAAA,YAAiC;IACjCF,EAAA,CAAAG,MAAA,eACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;;IA/FnBJ,EAAA,CAAAC,cAAA,cAA4E;IAOhED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,GAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAExEJ,EAAA,CAAAC,cAAA,eAAwE;IACtED,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAITJ,EAAA,CAAAC,cAAA,eAAiC;IAE7BD,EAAA,CAAAE,SAAA,aAA+C;IAC/CF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAC,cAAA,aAAgB;IACdD,EAAA,CAAAE,SAAA,aAA4C;IAC5CF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAC,cAAA,aAAgB;IACdD,EAAA,CAAAiC,UAAA,KAAAC,mDAAA,gBAAsF;IACtFlC,EAAA,CAAAiC,UAAA,KAAAE,mDAAA,gBAA4F;IAC5FnC,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAiC,UAAA,KAAAG,mDAAA,gBAGI;IACNpC,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAiC,UAAA,KAAAI,qDAAA,kBAQM;IAGNrC,EAAA,CAAAC,cAAA,eAA+D;IAIzDD,EAAA,CAAAwB,UAAA,mBAAAc,wEAAA;MAAA,MAAAC,WAAA,GAAAvC,EAAA,CAAA0B,aAAA,CAAAc,IAAA;MAAA,MAAA1B,eAAA,GAAAyB,WAAA,CAAAV,SAAA;MAAA,MAAAY,OAAA,GAAAzC,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA+B,WAAA,CAAAU,OAAA,CAAAC,eAAA,CAAA5B,eAAA,CAA4B;IAAA,EAAC;IACtCd,EAAA,CAAAE,SAAA,aAA+B;IAC/BF,EAAA,CAAAG,MAAA,cACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAiC,UAAA,KAAAU,iEAAA,8BAYkB;IAElB3C,EAAA,CAAAiC,UAAA,KAAAW,iEAAA,8BAYkB;IACpB5C,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,WAAK;IACHD,EAAA,CAAAiC,UAAA,KAAAY,wDAAA,qBAMS;IACX7C,EAAA,CAAAI,YAAA,EAAM;;;;;IAzFFJ,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAqC,MAAA,CAAAC,aAAA,CAAAjC,eAAA,OACF;IAC0Bd,EAAA,CAAAQ,SAAA,GAAoC;IAApCR,EAAA,CAAAgD,iBAAA,CAAAF,MAAA,CAAAG,iBAAA,CAAAnC,eAAA,EAAoC;IAE5Cd,EAAA,CAAAQ,SAAA,GAAmD;IAAnDR,EAAA,CAAAK,UAAA,YAAAyC,MAAA,CAAAI,mBAAA,CAAApC,eAAA,CAAAqC,MAAA,EAAmD;IACrEnD,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAqC,MAAA,CAAAM,oBAAA,CAAAtC,eAAA,CAAAqC,MAAA,OACF;IAOEnD,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAqC,MAAA,CAAAO,UAAA,CAAAvC,eAAA,CAAAwC,IAAA,OACF;IAGEtD,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAuD,kBAAA,MAAAT,MAAA,CAAAU,UAAA,CAAA1C,eAAA,CAAA2C,SAAA,UAAAX,MAAA,CAAAU,UAAA,CAAA1C,eAAA,CAAA4C,OAAA,OACF;IAE2C1D,EAAA,CAAAQ,SAAA,GAAuC;IAAvCR,EAAA,CAAAK,UAAA,SAAAS,eAAA,CAAA6C,IAAA,kBAAuC;IAChC3D,EAAA,CAAAQ,SAAA,GAAsC;IAAtCR,EAAA,CAAAK,UAAA,SAAAS,eAAA,CAAA6C,IAAA,iBAAsC;IACtF3D,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAqC,MAAA,CAAAc,kBAAA,CAAA9C,eAAA,CAAA6C,IAAA,OACF;IACiB3D,EAAA,CAAAQ,SAAA,GAAgC;IAAhCR,EAAA,CAAAK,UAAA,SAAAS,eAAA,CAAAC,cAAA,CAAgC;IAO7Cf,EAAA,CAAAQ,SAAA,GAAkE;IAAlER,EAAA,CAAAK,UAAA,SAAAS,eAAA,CAAAE,WAAA,IAAAF,eAAA,CAAA6C,IAAA,kBAAkE;IAsBjE3D,EAAA,CAAAQ,SAAA,GAAsC;IAAtCR,EAAA,CAAAK,UAAA,SAAAyC,MAAA,CAAAe,mBAAA,CAAA/C,eAAA,EAAsC;IActCd,EAAA,CAAAQ,SAAA,GAAqC;IAArCR,EAAA,CAAAK,UAAA,SAAAyC,MAAA,CAAAgB,kBAAA,CAAAhD,eAAA,EAAqC;IAgBrCd,EAAA,CAAAQ,SAAA,GAAuC;IAAvCR,EAAA,CAAAK,UAAA,SAAAyC,MAAA,CAAAiB,oBAAA,CAAAjD,eAAA,EAAuC;;;;;IA5FxDd,EAAA,CAAAC,cAAA,cAAmF;IAE/ED,EAAA,CAAAiC,UAAA,IAAA+B,8CAAA,oBAoGM;IACRhE,EAAA,CAAAI,YAAA,EAAM;;;;IArG+CJ,EAAA,CAAAQ,SAAA,GAAuB;IAAvBR,EAAA,CAAAK,UAAA,YAAA4D,MAAA,CAAAC,oBAAA,CAAuB;;;;;IA6G1ElE,EAAA,CAAAC,cAAA,WAA0B;IAAAD,EAAA,CAAAG,MAAA,+CAAwC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IACzEJ,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAG,MAAA,iDAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAE5EJ,EAAA,CAAAC,cAAA,iBAGkC;IAChCD,EAAA,CAAAE,SAAA,YAAgC;IAChCF,EAAA,CAAAG,MAAA,oCACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IAbXJ,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAE,SAAA,YAA2D;IAC3DF,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAG,MAAA,4BAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjDJ,EAAA,CAAAC,cAAA,YAA2B;IACzBD,EAAA,CAAAiC,UAAA,IAAAkC,+CAAA,mBAAyE;IACzEnE,EAAA,CAAAiC,UAAA,IAAAmC,+CAAA,mBAA0E;IAC5EpE,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAiC,UAAA,IAAAoC,iDAAA,oBAMS;IACXrE,EAAA,CAAAI,YAAA,EAAM;;;;IAVKJ,EAAA,CAAAQ,SAAA,GAAiB;IAAjBR,EAAA,CAAAK,UAAA,SAAAiE,MAAA,CAAAC,SAAA,GAAiB;IACjBvE,EAAA,CAAAQ,SAAA,GAAgB;IAAhBR,EAAA,CAAAK,UAAA,SAAAiE,MAAA,CAAAE,QAAA,GAAgB;IAGtBxE,EAAA,CAAAQ,SAAA,GAAiB;IAAjBR,EAAA,CAAAK,UAAA,SAAAiE,MAAA,CAAAC,SAAA,GAAiB;;;AD3KhC,OAAM,MAAOE,wBAAwB;EA0BnCC,YACUC,kBAAsC,EACtCC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IA5BhB,KAAAC,YAAY,GAAkB,EAAE;IAChC,KAAAZ,oBAAoB,GAAkB,EAAE;IACxC,KAAAa,WAAW,GAAgB,IAAI;IAC/B,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAnE,KAAK,GAAkB,IAAI;IAE3B;IACA,KAAAoE,YAAY,GAA2B,EAAE;IACzC,KAAAC,UAAU,GAAyB,EAAE;IAErC,KAAAC,aAAa,GAAG,CACd;MAAE5E,KAAK,EAAE,EAAE;MAAEG,KAAK,EAAE;IAAc,CAAE,EACpC;MAAEH,KAAK,EAAET,iBAAiB,CAACsF,OAAO;MAAE1E,KAAK,EAAE;IAAS,CAAE,EACtD;MAAEH,KAAK,EAAET,iBAAiB,CAACuF,SAAS;MAAE3E,KAAK,EAAE;IAAW,CAAE,EAC1D;MAAEH,KAAK,EAAET,iBAAiB,CAACwF,SAAS;MAAE5E,KAAK,EAAE;IAAW,CAAE,EAC1D;MAAEH,KAAK,EAAET,iBAAiB,CAACyF,SAAS;MAAE7E,KAAK,EAAE;IAAW,CAAE,EAC1D;MAAEH,KAAK,EAAET,iBAAiB,CAAC0F,SAAS;MAAE9E,KAAK,EAAE;IAAW,CAAE,CAC3D;IAED,KAAA+E,WAAW,GAAG,CACZ;MAAElF,KAAK,EAAE,EAAE;MAAEG,KAAK,EAAE;IAAW,CAAE,EACjC;MAAEH,KAAK,EAAER,eAAe,CAAC2F,SAAS;MAAEhF,KAAK,EAAE;IAAW,CAAE,EACxD;MAAEH,KAAK,EAAER,eAAe,CAAC4F,UAAU;MAAEjF,KAAK,EAAE;IAAY,CAAE,CAC3D;EAME;EAEHkF,QAAQA,CAAA;IACN,IAAI,CAACb,WAAW,GAAG,IAAI,CAACH,WAAW,CAACiB,cAAc,EAAE;IACpD,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACd,OAAO,GAAG,IAAI;IACnB,IAAI,CAACnE,KAAK,GAAG,IAAI;IAEjB,IAAI,CAAC8D,kBAAkB,CAACoB,eAAe,EAAE,CAACC,SAAS,CAAC;MAClDC,IAAI,EAAGnB,YAAY,IAAI;QACrB,IAAI,CAACA,YAAY,GAAGA,YAAY;QAChC,IAAI,CAACoB,YAAY,EAAE;QACnB,IAAI,CAAClB,OAAO,GAAG,KAAK;MACtB,CAAC;MACDnE,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,gDAAgD;QAC7D,IAAI,CAACmE,OAAO,GAAG,KAAK;QACpBmB,OAAO,CAACtF,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;KACD,CAAC;EACJ;EAEAqF,YAAYA,CAAA;IACV,IAAI,CAAChC,oBAAoB,GAAG,IAAI,CAACY,YAAY,CAACsB,MAAM,CAACC,WAAW,IAAG;MACjE,MAAMC,WAAW,GAAG,CAAC,IAAI,CAACrB,YAAY,IAAIoB,WAAW,CAAClD,MAAM,KAAK,IAAI,CAAC8B,YAAY;MAClF,MAAMsB,SAAS,GAAG,CAAC,IAAI,CAACrB,UAAU,IAAImB,WAAW,CAAC1C,IAAI,KAAK,IAAI,CAACuB,UAAU;MAC1E,OAAOoB,WAAW,IAAIC,SAAS;IACjC,CAAC,CAAC;EACJ;EAEAC,oBAAoBA,CAAA;IAClB,IAAI,CAACN,YAAY,EAAE;EACrB;EAEAO,kBAAkBA,CAAA;IAChB,IAAI,CAACP,YAAY,EAAE;EACrB;EAEAxD,eAAeA,CAAC2D,WAAwB;IACtC,IAAI,CAACxB,MAAM,CAAC6B,QAAQ,CAAC,CAAC,eAAe,EAAEL,WAAW,CAACjF,EAAE,CAAC,CAAC;EACzD;EAEAuF,eAAeA,CAACN,WAAwB;IACtC;IACA,IAAI,CAACxB,MAAM,CAAC6B,QAAQ,CAAC,CAAC,eAAe,EAAEL,WAAW,CAACjF,EAAE,CAAC,CAAC;EACzD;EAEAY,iBAAiBA,CAACqE,WAAwB;IACxC,IAAIO,OAAO,CAAC,mDAAmD,CAAC,EAAE;MAChE,IAAI,CAACjC,kBAAkB,CAAC3C,iBAAiB,CAACqE,WAAW,CAACjF,EAAE,CAAC,CAAC4E,SAAS,CAAC;QAClEC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACH,gBAAgB,EAAE,CAAC,CAAC;QAC3B,CAAC;;QACDjF,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACA,KAAK,GAAG,iDAAiD;UAC9DsF,OAAO,CAACtF,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACtD;OACD,CAAC;;EAEN;EAEAuC,oBAAoBA,CAACD,MAAyB;IAC5C,OAAO,IAAI,CAACwB,kBAAkB,CAACvB,oBAAoB,CAACD,MAAM,CAAC;EAC7D;EAEAS,kBAAkBA,CAACD,IAAqB;IACtC,OAAO,IAAI,CAACgB,kBAAkB,CAACf,kBAAkB,CAACD,IAAI,CAAC;EACzD;EAEAT,mBAAmBA,CAACC,MAAyB;IAC3C,OAAO,IAAI,CAACwB,kBAAkB,CAACzB,mBAAmB,CAACC,MAAM,CAAC;EAC5D;EAEAE,UAAUA,CAACwD,UAAkB;IAC3B,MAAMvD,IAAI,GAAG,IAAIwD,IAAI,CAACD,UAAU,CAAC;IACjC,OAAOvD,IAAI,CAACyD,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,OAAO;MAChBC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;KACN,CAAC;EACJ;EAEA3D,UAAUA,CAAC4D,UAAkB;IAC3B,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGF,UAAU,CAACG,KAAK,CAAC,GAAG,CAAC;IAC9C,MAAMC,IAAI,GAAGC,QAAQ,CAACJ,KAAK,CAAC;IAC5B,MAAMK,IAAI,GAAGF,IAAI,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI;IACrC,MAAMG,WAAW,GAAGH,IAAI,GAAG,EAAE,IAAI,EAAE;IACnC,OAAO,GAAGG,WAAW,IAAIL,OAAO,IAAII,IAAI,EAAE;EAC5C;EAEAlD,QAAQA,CAAA;IACN,OAAO,IAAI,CAACO,WAAW,EAAE6C,IAAI,KAAK,QAAQ;EAC5C;EAEArD,SAASA,CAAA;IACP,OAAO,IAAI,CAACQ,WAAW,EAAE6C,IAAI,KAAK,SAAS;EAC7C;EAEA7D,oBAAoBA,CAACsC,WAAwB;IAC3C,OAAOA,WAAW,CAAClD,MAAM,KAAKrD,iBAAiB,CAACsF,OAAO,IAChDiB,WAAW,CAAClD,MAAM,KAAKrD,iBAAiB,CAACuF,SAAS,IAClDgB,WAAW,CAAClD,MAAM,KAAKrD,iBAAiB,CAACwF,SAAS;EAC3D;EAEAvC,aAAaA,CAACsD,WAAwB;IACpC,IAAI,IAAI,CAAC7B,QAAQ,EAAE,EAAE;MACnB,OAAO6B,WAAW,CAAC/E,OAAO,CAACuG,QAAQ;KACpC,MAAM;MACL,OAAOxB,WAAW,CAAChF,MAAM,CAACwG,QAAQ;;EAEtC;EAEA5E,iBAAiBA,CAACoD,WAAwB;IACxC,IAAI,IAAI,CAAC7B,QAAQ,EAAE,EAAE;MACnB,OAAO,SAAS;KACjB,MAAM;MACL,OAAO,QAAQ;;EAEnB;;;uBAxJWC,wBAAwB,EAAAzE,EAAA,CAAA8H,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAhI,EAAA,CAAA8H,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAlI,EAAA,CAAA8H,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAxB3D,wBAAwB;MAAA4D,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZrC3I,EAAA,CAAAC,cAAA,aAAkC;UAMtBD,EAAA,CAAAE,SAAA,WAAwC;UAAAF,EAAA,CAAAG,MAAA,uBAC1C;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAiC,UAAA,IAAA4G,0CAAA,oBAMS;UACX7I,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,aAAuB;UAI4BD,EAAA,CAAAG,MAAA,wBAAgB;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACrEJ,EAAA,CAAAC,cAAA,kBAIoC;UADlCD,EAAA,CAAAwB,UAAA,2BAAAsH,mEAAAC,MAAA;YAAA,OAAAH,GAAA,CAAA3D,YAAA,GAAA8D,MAAA;UAAA,EAA0B,oBAAAC,4DAAA;YAAA,OAChBJ,GAAA,CAAApC,oBAAA,EAAsB;UAAA,EADN;UAE1BxG,EAAA,CAAAiC,UAAA,KAAAgH,2CAAA,qBAES;UACXjJ,EAAA,CAAAI,YAAA,EAAS;UAEXJ,EAAA,CAAAC,cAAA,eAAsB;UACuBD,EAAA,CAAAG,MAAA,sBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACjEJ,EAAA,CAAAC,cAAA,kBAIkC;UADhCD,EAAA,CAAAwB,UAAA,2BAAA0H,mEAAAH,MAAA;YAAA,OAAAH,GAAA,CAAA1D,UAAA,GAAA6D,MAAA;UAAA,EAAwB,oBAAAI,4DAAA;YAAA,OACdP,GAAA,CAAAnC,kBAAA,EAAoB;UAAA,EADN;UAExBzG,EAAA,CAAAiC,UAAA,KAAAmH,2CAAA,qBAES;UACXpJ,EAAA,CAAAI,YAAA,EAAS;UAEXJ,EAAA,CAAAC,cAAA,eAA6C;UAGzCD,EAAA,CAAAwB,UAAA,mBAAA6H,2DAAA;YAAA,OAAST,GAAA,CAAA9C,gBAAA,EAAkB;UAAA,EAAC;UAC5B9F,EAAA,CAAAE,SAAA,aAAoC;UACpCF,EAAA,CAAAG,MAAA,iBACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAKbJ,EAAA,CAAAiC,UAAA,KAAAqH,wCAAA,kBAGM;UAGNtJ,EAAA,CAAAiC,UAAA,KAAAsH,wCAAA,kBAKM;UAGNvJ,EAAA,CAAAiC,UAAA,KAAAuH,wCAAA,kBAwGM;UAGNxJ,EAAA,CAAAiC,UAAA,KAAAwH,wCAAA,kBAcM;UACRzJ,EAAA,CAAAI,YAAA,EAAM;;;UArLDJ,EAAA,CAAAQ,SAAA,GAAiB;UAAjBR,EAAA,CAAAK,UAAA,SAAAuI,GAAA,CAAArE,SAAA,GAAiB;UAedvE,EAAA,CAAAQ,SAAA,GAA0B;UAA1BR,EAAA,CAAAK,UAAA,YAAAuI,GAAA,CAAA3D,YAAA,CAA0B;UAECjF,EAAA,CAAAQ,SAAA,GAAgB;UAAhBR,EAAA,CAAAK,UAAA,YAAAuI,GAAA,CAAAzD,aAAA,CAAgB;UAU3CnF,EAAA,CAAAQ,SAAA,GAAwB;UAAxBR,EAAA,CAAAK,UAAA,YAAAuI,GAAA,CAAA1D,UAAA,CAAwB;UAEGlF,EAAA,CAAAQ,SAAA,GAAc;UAAdR,EAAA,CAAAK,UAAA,YAAAuI,GAAA,CAAAnD,WAAA,CAAc;UAgBzCzF,EAAA,CAAAQ,SAAA,GAAW;UAAXR,EAAA,CAAAK,UAAA,SAAAuI,GAAA,CAAA/H,KAAA,CAAW;UAMXb,EAAA,CAAAQ,SAAA,GAAa;UAAbR,EAAA,CAAAK,UAAA,SAAAuI,GAAA,CAAA5D,OAAA,CAAa;UAQbhF,EAAA,CAAAQ,SAAA,GAAiD;UAAjDR,EAAA,CAAAK,UAAA,UAAAuI,GAAA,CAAA5D,OAAA,IAAA4D,GAAA,CAAA1E,oBAAA,CAAAwF,MAAA,KAAiD;UA2GjD1J,EAAA,CAAAQ,SAAA,GAAmD;UAAnDR,EAAA,CAAAK,UAAA,UAAAuI,GAAA,CAAA5D,OAAA,IAAA4D,GAAA,CAAA1E,oBAAA,CAAAwF,MAAA,OAAmD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}