{"ast": null, "code": "'use strict';\n\nvar random = require('./random');\nvar onUnload = {},\n  afterUnload = false\n  // detect google chrome packaged apps because they don't allow the 'unload' event\n  ,\n  isChromePackagedApp = global.chrome && global.chrome.app && global.chrome.app.runtime;\nmodule.exports = {\n  attachEvent: function (event, listener) {\n    if (typeof global.addEventListener !== 'undefined') {\n      global.addEventListener(event, listener, false);\n    } else if (global.document && global.attachEvent) {\n      // IE quirks.\n      // According to: http://stevesouders.com/misc/test-postmessage.php\n      // the message gets delivered only to 'document', not 'window'.\n      global.document.attachEvent('on' + event, listener);\n      // I get 'window' for ie8.\n      global.attachEvent('on' + event, listener);\n    }\n  },\n  detachEvent: function (event, listener) {\n    if (typeof global.addEventListener !== 'undefined') {\n      global.removeEventListener(event, listener, false);\n    } else if (global.document && global.detachEvent) {\n      global.document.detachEvent('on' + event, listener);\n      global.detachEvent('on' + event, listener);\n    }\n  },\n  unloadAdd: function (listener) {\n    if (isChromePackagedApp) {\n      return null;\n    }\n    var ref = random.string(8);\n    onUnload[ref] = listener;\n    if (afterUnload) {\n      setTimeout(this.triggerUnloadCallbacks, 0);\n    }\n    return ref;\n  },\n  unloadDel: function (ref) {\n    if (ref in onUnload) {\n      delete onUnload[ref];\n    }\n  },\n  triggerUnloadCallbacks: function () {\n    for (var ref in onUnload) {\n      onUnload[ref]();\n      delete onUnload[ref];\n    }\n  }\n};\nvar unloadTriggered = function () {\n  if (afterUnload) {\n    return;\n  }\n  afterUnload = true;\n  module.exports.triggerUnloadCallbacks();\n};\n\n// 'unload' alone is not reliable in opera within an iframe, but we\n// can't use `beforeunload` as IE fires it on javascript: links.\nif (!isChromePackagedApp) {\n  module.exports.attachEvent('unload', unloadTriggered);\n}", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}