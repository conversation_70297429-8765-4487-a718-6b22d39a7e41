{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    if (this.authService.isAuthenticated()) {\n      // Check if the route has any role requirements\n      const requiredRoles = route.data['roles'];\n      if (requiredRoles && requiredRoles.length > 0) {\n        // Check if the user has the required role\n        const hasRequiredRole = this.authService.hasRole(requiredRoles);\n        if (!hasRequiredRole) {\n          // If user doesn't have the required role, redirect to appropriate dashboard\n          const currentUser = this.authService.getCurrentUser();\n          if (currentUser?.role === 'DOCTOR') {\n            this.router.navigate(['/doctor/dashboard']);\n          } else if (currentUser?.role === 'PATIENT') {\n            this.router.navigate(['/patient/dashboard']);\n          } else {\n            this.router.navigate(['/auth/login']);\n          }\n          return false;\n        }\n      }\n      return true;\n    }\n    // Not authenticated, redirect to login\n    this.router.navigate(['/auth/login'], {\n      queryParams: {\n        returnUrl: state.url\n      }\n    });\n    return false;\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(t) {\n      return new (t || AuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "route", "state", "isAuthenticated", "requiredRoles", "data", "length", "hasRequiredRole", "hasRole", "currentUser", "getCurrentUser", "role", "navigate", "queryParams", "returnUrl", "url", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/core/guards/auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';\nimport { Observable } from 'rxjs';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard implements CanActivate {\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> | Promise<boolean> | boolean {\n    \n    if (this.authService.isAuthenticated()) {\n      // Check if the route has any role requirements\n      const requiredRoles = route.data['roles'] as string[];\n      if (requiredRoles && requiredRoles.length > 0) {\n        // Check if the user has the required role\n        const hasRequiredRole = this.authService.hasRole(requiredRoles);\n        if (!hasRequiredRole) {\n          // If user doesn't have the required role, redirect to appropriate dashboard\n          const currentUser = this.authService.getCurrentUser();\n          if (currentUser?.role === 'DOCTOR') {\n            this.router.navigate(['/doctor/dashboard']);\n          } else if (currentUser?.role === 'PATIENT') {\n            this.router.navigate(['/patient/dashboard']);\n          } else {\n            this.router.navigate(['/auth/login']);\n          }\n          return false;\n        }\n      }\n      return true;\n    }\n\n    // Not authenticated, redirect to login\n    this.router.navigate(['/auth/login'], { \n      queryParams: { returnUrl: state.url } \n    });\n    return false;\n  }\n}\n"], "mappings": ";;;AAQA,OAAM,MAAOA,SAAS;EAEpBC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAG1B,IAAI,IAAI,CAACJ,WAAW,CAACK,eAAe,EAAE,EAAE;MACtC;MACA,MAAMC,aAAa,GAAGH,KAAK,CAACI,IAAI,CAAC,OAAO,CAAa;MACrD,IAAID,aAAa,IAAIA,aAAa,CAACE,MAAM,GAAG,CAAC,EAAE;QAC7C;QACA,MAAMC,eAAe,GAAG,IAAI,CAACT,WAAW,CAACU,OAAO,CAACJ,aAAa,CAAC;QAC/D,IAAI,CAACG,eAAe,EAAE;UACpB;UACA,MAAME,WAAW,GAAG,IAAI,CAACX,WAAW,CAACY,cAAc,EAAE;UACrD,IAAID,WAAW,EAAEE,IAAI,KAAK,QAAQ,EAAE;YAClC,IAAI,CAACZ,MAAM,CAACa,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;WAC5C,MAAM,IAAIH,WAAW,EAAEE,IAAI,KAAK,SAAS,EAAE;YAC1C,IAAI,CAACZ,MAAM,CAACa,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;WAC7C,MAAM;YACL,IAAI,CAACb,MAAM,CAACa,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;;UAEvC,OAAO,KAAK;;;MAGhB,OAAO,IAAI;;IAGb;IACA,IAAI,CAACb,MAAM,CAACa,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;MACpCC,WAAW,EAAE;QAAEC,SAAS,EAAEZ,KAAK,CAACa;MAAG;KACpC,CAAC;IACF,OAAO,KAAK;EACd;;;uBAvCWnB,SAAS,EAAAoB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAATzB,SAAS;MAAA0B,OAAA,EAAT1B,SAAS,CAAA2B,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}