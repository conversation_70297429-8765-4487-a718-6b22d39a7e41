{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthGuard } from './core/guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: '/auth/login',\n  pathMatch: 'full'\n}, {\n  path: 'auth',\n  loadChildren: () => import('./auth/auth.module').then(m => m.AuthModule)\n}, {\n  path: 'patient',\n  canActivate: [AuthGuard],\n  data: {\n    roles: ['PATIENT']\n  },\n  loadChildren: () => import('./patient/patient.module').then(m => m.PatientModule)\n}, {\n  path: 'doctor',\n  canActivate: [AuthGuard],\n  data: {\n    roles: ['DOCTOR']\n  },\n  loadChildren: () => import('./doctor/doctor.module').then(m => m.DoctorModule)\n}, {\n  path: 'profile',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./profile/profile.module').then(m => m.ProfileModule)\n}, {\n  path: 'appointments',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./appointments/appointments.module').then(m => m.AppointmentsModule)\n}, {\n  path: 'chat',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./chat/chat.module').then(m => m.ChatModule)\n}, {\n  path: '**',\n  redirectTo: '/auth/login'\n}];\nexport let AppRoutingModule = /*#__PURE__*/(() => {\n  class AppRoutingModule {\n    static {\n      this.ɵfac = function AppRoutingModule_Factory(t) {\n        return new (t || AppRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AppRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forRoot(routes), RouterModule]\n      });\n    }\n  }\n  return AppRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}