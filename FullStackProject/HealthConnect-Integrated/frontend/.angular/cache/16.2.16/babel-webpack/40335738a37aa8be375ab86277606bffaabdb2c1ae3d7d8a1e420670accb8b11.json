{"ast": null, "code": "'use strict';\n\nvar URL = require('url-parse');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:utils:url');\n}\nmodule.exports = {\n  getOrigin: function (url) {\n    if (!url) {\n      return null;\n    }\n    var p = new URL(url);\n    if (p.protocol === 'file:') {\n      return null;\n    }\n    var port = p.port;\n    if (!port) {\n      port = p.protocol === 'https:' ? '443' : '80';\n    }\n    return p.protocol + '//' + p.hostname + ':' + port;\n  },\n  isOriginEqual: function (a, b) {\n    var res = this.getOrigin(a) === this.getOrigin(b);\n    debug('same', a, b, res);\n    return res;\n  },\n  isSchemeEqual: function (a, b) {\n    return a.split(':')[0] === b.split(':')[0];\n  },\n  addPath: function (url, path) {\n    var qs = url.split('?');\n    return qs[0] + path + (qs[1] ? '?' + qs[1] : '');\n  },\n  addQuery: function (url, q) {\n    return url + (url.indexOf('?') === -1 ? '?' + q : '&' + q);\n  },\n  isLoopbackAddr: function (addr) {\n    return /^127\\.([0-9]{1,3})\\.([0-9]{1,3})\\.([0-9]{1,3})$/i.test(addr) || /^\\[::1\\]$/.test(addr);\n  }\n};", "map": {"version": 3, "names": ["URL", "require", "debug", "process", "env", "NODE_ENV", "module", "exports", "<PERSON><PERSON><PERSON><PERSON>", "url", "p", "protocol", "port", "hostname", "isOriginEqual", "a", "b", "res", "isSchemeEqual", "split", "addPath", "path", "qs", "<PERSON><PERSON><PERSON><PERSON>", "q", "indexOf", "isLoopbackAddr", "addr", "test"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/sockjs-client/lib/utils/url.js"], "sourcesContent": ["'use strict';\n\nvar URL = require('url-parse');\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:utils:url');\n}\n\nmodule.exports = {\n  getOrigin: function(url) {\n    if (!url) {\n      return null;\n    }\n\n    var p = new URL(url);\n    if (p.protocol === 'file:') {\n      return null;\n    }\n\n    var port = p.port;\n    if (!port) {\n      port = (p.protocol === 'https:') ? '443' : '80';\n    }\n\n    return p.protocol + '//' + p.hostname + ':' + port;\n  }\n\n, isOriginEqual: function(a, b) {\n    var res = this.getOrigin(a) === this.getOrigin(b);\n    debug('same', a, b, res);\n    return res;\n  }\n\n, isSchemeEqual: function(a, b) {\n    return (a.split(':')[0] === b.split(':')[0]);\n  }\n\n, addPath: function (url, path) {\n    var qs = url.split('?');\n    return qs[0] + path + (qs[1] ? '?' + qs[1] : '');\n  }\n\n, addQuery: function (url, q) {\n    return url + (url.indexOf('?') === -1 ? ('?' + q) : ('&' + q));\n  }\n\n, isLoopbackAddr: function (addr) {\n    return /^127\\.([0-9]{1,3})\\.([0-9]{1,3})\\.([0-9]{1,3})$/i.test(addr) || /^\\[::1\\]$/.test(addr);\n  }\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,GAAG,GAAGC,OAAO,CAAC,WAAW,CAAC;AAE9B,IAAIC,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGD,OAAO,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAC;AACrD;AAEAK,MAAM,CAACC,OAAO,GAAG;EACfC,SAAS,EAAE,SAAAA,CAASC,GAAG,EAAE;IACvB,IAAI,CAACA,GAAG,EAAE;MACR,OAAO,IAAI;IACb;IAEA,IAAIC,CAAC,GAAG,IAAIV,GAAG,CAACS,GAAG,CAAC;IACpB,IAAIC,CAAC,CAACC,QAAQ,KAAK,OAAO,EAAE;MAC1B,OAAO,IAAI;IACb;IAEA,IAAIC,IAAI,GAAGF,CAAC,CAACE,IAAI;IACjB,IAAI,CAACA,IAAI,EAAE;MACTA,IAAI,GAAIF,CAAC,CAACC,QAAQ,KAAK,QAAQ,GAAI,KAAK,GAAG,IAAI;IACjD;IAEA,OAAOD,CAAC,CAACC,QAAQ,GAAG,IAAI,GAAGD,CAAC,CAACG,QAAQ,GAAG,GAAG,GAAGD,IAAI;EACpD,CAAC;EAEDE,aAAa,EAAE,SAAAA,CAASC,CAAC,EAAEC,CAAC,EAAE;IAC5B,IAAIC,GAAG,GAAG,IAAI,CAACT,SAAS,CAACO,CAAC,CAAC,KAAK,IAAI,CAACP,SAAS,CAACQ,CAAC,CAAC;IACjDd,KAAK,CAAC,MAAM,EAAEa,CAAC,EAAEC,CAAC,EAAEC,GAAG,CAAC;IACxB,OAAOA,GAAG;EACZ,CAAC;EAEDC,aAAa,EAAE,SAAAA,CAASH,CAAC,EAAEC,CAAC,EAAE;IAC5B,OAAQD,CAAC,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAKH,CAAC,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC7C,CAAC;EAEDC,OAAO,EAAE,SAAAA,CAAUX,GAAG,EAAEY,IAAI,EAAE;IAC5B,IAAIC,EAAE,GAAGb,GAAG,CAACU,KAAK,CAAC,GAAG,CAAC;IACvB,OAAOG,EAAE,CAAC,CAAC,CAAC,GAAGD,IAAI,IAAIC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;EAClD,CAAC;EAEDC,QAAQ,EAAE,SAAAA,CAAUd,GAAG,EAAEe,CAAC,EAAE;IAC1B,OAAOf,GAAG,IAAIA,GAAG,CAACgB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAI,GAAG,GAAGD,CAAC,GAAK,GAAG,GAAGA,CAAE,CAAC;EAChE,CAAC;EAEDE,cAAc,EAAE,SAAAA,CAAUC,IAAI,EAAE;IAC9B,OAAO,kDAAkD,CAACC,IAAI,CAACD,IAAI,CAAC,IAAI,WAAW,CAACC,IAAI,CAACD,IAAI,CAAC;EAChG;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}