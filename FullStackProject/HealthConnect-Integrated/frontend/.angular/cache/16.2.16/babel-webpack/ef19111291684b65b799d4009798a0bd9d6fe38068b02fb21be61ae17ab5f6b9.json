{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/auth.service\";\nimport * as i2 from \"../../core/services/user.service\";\nimport * as i3 from \"../../core/services/appointment.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nfunction DoctorDashboardComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"span\", 6);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 7);\n    i0.ɵɵtext(5, \"Loading your dashboard...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DoctorDashboardComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"i\", 9);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction DoctorDashboardComponent_div_3_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \\u2022 \", ctx_r3.currentUser == null ? null : ctx_r3.currentUser.affiliation, \"\");\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 21)(2, \"div\", 26)(3, \"div\", 44)(4, \"div\", 45);\n    i0.ɵɵelement(5, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 46)(7, \"h6\", 47);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"h3\", 48);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"small\");\n    i0.ɵɵelement(12, \"i\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const stat_r8 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassMapInterpolate2(\"bi bi-\", stat_r8.icon, \" fs-2 \", stat_r8.color, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(stat_r8.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r8.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r4.getChangeClass(stat_r8.changeType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r4.getChangeIcon(stat_r8.changeType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", stat_r8.change, \" \");\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵelement(1, \"i\", 50);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No appointments scheduled for today\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 44)(2, \"div\", 45)(3, \"div\", 52);\n    i0.ɵɵelement(4, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h6\", 48);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 53);\n    i0.ɵɵelement(9, \"i\", 54);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementStart(11, \"span\", 55)(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"small\", 56);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 57)(18, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_div_27_Template_button_click_18_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const appointment_r9 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.startAppointment(appointment_r9));\n    });\n    i0.ɵɵelement(19, \"i\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const appointment_r9 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMapInterpolate2(\"bi bi-\", ctx_r6.getAppointmentTypeIcon(appointment_r9.type), \" \", ctx_r6.getAppointmentTypeClass(appointment_r9.type), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(appointment_r9.patientName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", appointment_r9.time, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r6.getStatusBadgeClass(appointment_r9.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 15, appointment_r9.status), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", appointment_r9.type === \"VIDEO_CALL\" ? \"Video Consultation\" : \"In-Person Visit\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", appointment_r9.status !== \"SCHEDULED\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMapInterpolate1(\"bi bi-\", appointment_r9.type === \"VIDEO_CALL\" ? \"camera-video\" : \"person\", \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", appointment_r9.type === \"VIDEO_CALL\" ? \"Join Call\" : \"View Details\", \" \");\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_35_hr_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"hr\", 64);\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60)(2, \"div\", 45)(3, \"div\", 61);\n    i0.ɵɵelement(4, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 46)(6, \"h6\", 62);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 53);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"small\", 56);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(12, DoctorDashboardComponent_div_3_div_35_hr_12_Template, 1, 0, \"hr\", 63);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const activity_r12 = ctx.$implicit;\n    const isLast_r13 = ctx.last;\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMapInterpolate2(\"bi bi-\", activity_r12.icon, \" \", activity_r12.color, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r12.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r12.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r12.time);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !isLast_r13);\n  }\n}\nfunction DoctorDashboardComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 10)(2, \"div\", 11)(3, \"div\", 12)(4, \"div\")(5, \"h1\", 13);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 14);\n    i0.ɵɵelement(8, \"i\", 15);\n    i0.ɵɵtext(9);\n    i0.ɵɵtemplate(10, DoctorDashboardComponent_div_3_span_10_Template, 2, 1, \"span\", 3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.refreshData());\n    });\n    i0.ɵɵelement(12, \"i\", 17);\n    i0.ɵɵtext(13, \"Refresh \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"div\", 10);\n    i0.ɵɵtemplate(15, DoctorDashboardComponent_div_3_div_15_Template, 14, 11, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 19)(17, \"div\", 20)(18, \"div\", 21)(19, \"div\", 22)(20, \"h6\", 23);\n    i0.ɵɵelement(21, \"i\", 24);\n    i0.ɵɵtext(22, \"Today's Schedule \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.navigateTo(\"/appointments\"));\n    });\n    i0.ɵɵtext(24, \" View All \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 26);\n    i0.ɵɵtemplate(26, DoctorDashboardComponent_div_3_div_26_Template, 4, 0, \"div\", 27);\n    i0.ɵɵtemplate(27, DoctorDashboardComponent_div_3_div_27_Template, 21, 17, \"div\", 28);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 29)(29, \"div\", 21)(30, \"div\", 30)(31, \"h6\", 23);\n    i0.ɵɵelement(32, \"i\", 31);\n    i0.ɵɵtext(33, \"Recent Activities \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 26);\n    i0.ɵɵtemplate(35, DoctorDashboardComponent_div_3_div_35_Template, 13, 8, \"div\", 32);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(36, \"div\", 19)(37, \"div\", 11)(38, \"div\", 33)(39, \"div\", 30)(40, \"h6\", 23);\n    i0.ɵɵelement(41, \"i\", 34);\n    i0.ɵɵtext(42, \"Quick Actions \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 26)(44, \"div\", 19)(45, \"div\", 35)(46, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_46_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.navigateTo(\"/patients\"));\n    });\n    i0.ɵɵelement(47, \"i\", 37);\n    i0.ɵɵelementStart(48, \"span\");\n    i0.ɵɵtext(49, \"Manage Patients\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(50, \"div\", 35)(51, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_51_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.navigateTo(\"/appointments\"));\n    });\n    i0.ɵɵelement(52, \"i\", 39);\n    i0.ɵɵelementStart(53, \"span\");\n    i0.ɵɵtext(54, \"Schedule Appointment\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(55, \"div\", 35)(56, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_56_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.navigateTo(\"/chat\"));\n    });\n    i0.ɵɵelement(57, \"i\", 41);\n    i0.ɵɵelementStart(58, \"span\");\n    i0.ɵɵtext(59, \"Messages\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(60, \"div\", 35)(61, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_61_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.navigateTo(\"/reports\"));\n    });\n    i0.ɵɵelement(62, \"i\", 43);\n    i0.ɵɵelementStart(63, \"span\");\n    i0.ɵɵtext(64, \"View Reports\");\n    i0.ɵɵelementEnd()()()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.getGreeting(), \", Dr. \", ctx_r2.currentUser == null ? null : ctx_r2.currentUser.fullName, \"!\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r2.currentUser == null ? null : ctx_r2.currentUser.specialization) || \"General Practice\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentUser == null ? null : ctx_r2.currentUser.affiliation);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.dashboardStats);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.todayAppointments.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.todayAppointments);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.recentActivities);\n  }\n}\nexport class DoctorDashboardComponent {\n  constructor(authService, userService, appointmentService, router) {\n    this.authService = authService;\n    this.userService = userService;\n    this.appointmentService = appointmentService;\n    this.router = router;\n    this.currentUser = null;\n    this.isLoading = true;\n    this.error = '';\n    this.realTodayAppointments = [];\n    this.dashboardStats = [{\n      title: 'Total Patients',\n      value: '156',\n      change: '+12%',\n      changeType: 'increase',\n      icon: 'people',\n      color: 'text-primary'\n    }, {\n      title: 'Today\\'s Appointments',\n      value: '8',\n      change: '+2',\n      changeType: 'increase',\n      icon: 'calendar-check',\n      color: 'text-success'\n    }, {\n      title: 'Pending Reviews',\n      value: '5',\n      change: '-3',\n      changeType: 'decrease',\n      icon: 'clipboard-check',\n      color: 'text-warning'\n    }, {\n      title: 'Messages',\n      value: '12',\n      change: '+4',\n      changeType: 'increase',\n      icon: 'chat-dots',\n      color: 'text-info'\n    }];\n    this.todayAppointments = [{\n      id: 1,\n      patientName: 'John Smith',\n      time: '09:00 AM',\n      type: 'VIDEO_CALL',\n      status: 'SCHEDULED'\n    }, {\n      id: 2,\n      patientName: 'Sarah Johnson',\n      time: '10:30 AM',\n      type: 'IN_PERSON',\n      status: 'SCHEDULED'\n    }, {\n      id: 3,\n      patientName: 'Michael Brown',\n      time: '02:00 PM',\n      type: 'VIDEO_CALL',\n      status: 'SCHEDULED'\n    }, {\n      id: 4,\n      patientName: 'Emily Davis',\n      time: '03:30 PM',\n      type: 'IN_PERSON',\n      status: 'SCHEDULED'\n    }];\n    this.recentActivities = [{\n      title: 'New Patient Registration',\n      description: 'Alice Wilson registered as a new patient',\n      time: '30 minutes ago',\n      icon: 'person-plus',\n      color: 'text-success'\n    }, {\n      title: 'Appointment Rescheduled',\n      description: 'Tom Anderson moved appointment to tomorrow',\n      time: '1 hour ago',\n      icon: 'calendar-event',\n      color: 'text-warning'\n    }, {\n      title: 'Message Received',\n      description: 'New message from Lisa Parker about medication',\n      time: '2 hours ago',\n      icon: 'envelope',\n      color: 'text-info'\n    }, {\n      title: 'Lab Results Available',\n      description: 'Blood test results for David Miller are ready',\n      time: '3 hours ago',\n      icon: 'file-medical',\n      color: 'text-primary'\n    }];\n  }\n  ngOnInit() {\n    this.loadUserData();\n    this.loadTodayAppointments();\n  }\n  loadUserData() {\n    this.authService.currentUser$.subscribe({\n      next: user => {\n        this.currentUser = user;\n        this.isLoading = false;\n      },\n      error: error => {\n        this.error = 'Failed to load user data';\n        this.isLoading = false;\n      }\n    });\n  }\n  loadTodayAppointments() {\n    this.appointmentService.getTodayAppointments().subscribe({\n      next: appointments => {\n        this.realTodayAppointments = appointments;\n        this.updateDashboardStats(appointments.length);\n      },\n      error: error => {\n        console.error('Failed to load today appointments:', error);\n      }\n    });\n  }\n  updateDashboardStats(todayCount) {\n    // Update the \"Today's Appointments\" stat with real data\n    const todayStatIndex = this.dashboardStats.findIndex(stat => stat.title === \"Today's Appointments\");\n    if (todayStatIndex !== -1) {\n      this.dashboardStats[todayStatIndex].value = todayCount.toString();\n    }\n  }\n  getGreeting() {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Good morning';\n    if (hour < 18) return 'Good afternoon';\n    return 'Good evening';\n  }\n  getChangeClass(changeType) {\n    switch (changeType) {\n      case 'increase':\n        return 'text-success';\n      case 'decrease':\n        return 'text-danger';\n      default:\n        return 'text-muted';\n    }\n  }\n  getChangeIcon(changeType) {\n    switch (changeType) {\n      case 'increase':\n        return 'bi-arrow-up';\n      case 'decrease':\n        return 'bi-arrow-down';\n      default:\n        return 'bi-dash';\n    }\n  }\n  getAppointmentTypeIcon(type) {\n    return type === 'VIDEO_CALL' ? 'camera-video' : 'geo-alt';\n  }\n  getAppointmentTypeClass(type) {\n    return type === 'VIDEO_CALL' ? 'text-primary' : 'text-success';\n  }\n  getStatusBadgeClass(status) {\n    switch (status) {\n      case 'SCHEDULED':\n        return 'badge bg-primary';\n      case 'COMPLETED':\n        return 'badge bg-success';\n      case 'CANCELLED':\n        return 'badge bg-danger';\n      default:\n        return 'badge bg-secondary';\n    }\n  }\n  startAppointment(appointment) {\n    if (appointment.type === 'VIDEO_CALL') {\n      this.router.navigate(['/video-call'], {\n        queryParams: {\n          appointmentId: appointment.id\n        }\n      });\n    } else {\n      // For in-person appointments, navigate to patient details or appointment view\n      this.router.navigate(['/appointments', appointment.id]);\n    }\n  }\n  navigateTo(route) {\n    this.router.navigate([route]);\n  }\n  refreshData() {\n    this.isLoading = true;\n    this.loadTodayAppointments();\n    setTimeout(() => {\n      this.isLoading = false;\n    }, 1000);\n  }\n  static {\n    this.ɵfac = function DoctorDashboardComponent_Factory(t) {\n      return new (t || DoctorDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.AppointmentService), i0.ɵɵdirectiveInject(i4.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DoctorDashboardComponent,\n      selectors: [[\"app-doctor-dashboard\"]],\n      decls: 4,\n      vars: 3,\n      consts: [[1, \"container-fluid\", \"py-4\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-3\", \"text-muted\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"bi\", \"bi-exclamation-triangle\", \"me-2\"], [1, \"row\", \"mb-4\"], [1, \"col-12\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"h3\", \"mb-1\"], [1, \"text-muted\", \"mb-0\"], [1, \"bi\", \"bi-hospital\", \"me-2\"], [1, \"btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\", \"me-2\"], [\"class\", \"col-md-3 col-sm-6 mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\"], [1, \"col-md-8\", \"mb-4\"], [1, \"card\", \"h-100\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"bi\", \"bi-calendar-day\", \"me-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"card-body\"], [\"class\", \"text-center py-4 text-muted\", 4, \"ngIf\"], [\"class\", \"appointment-item d-flex align-items-center justify-content-between p-3 mb-2 rounded\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\", \"mb-4\"], [1, \"card-header\"], [1, \"bi\", \"bi-activity\", \"me-2\"], [\"class\", \"activity-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"card\"], [1, \"bi\", \"bi-lightning\", \"me-2\"], [1, \"col-md-3\", \"col-sm-6\", \"mb-3\"], [1, \"btn\", \"btn-outline-primary\", \"w-100\", \"py-3\", 3, \"click\"], [1, \"bi\", \"bi-people\", \"d-block\", \"fs-4\", \"mb-2\"], [1, \"btn\", \"btn-outline-success\", \"w-100\", \"py-3\", 3, \"click\"], [1, \"bi\", \"bi-calendar-plus\", \"d-block\", \"fs-4\", \"mb-2\"], [1, \"btn\", \"btn-outline-info\", \"w-100\", \"py-3\", 3, \"click\"], [1, \"bi\", \"bi-chat-dots\", \"d-block\", \"fs-4\", \"mb-2\"], [1, \"btn\", \"btn-outline-warning\", \"w-100\", \"py-3\", 3, \"click\"], [1, \"bi\", \"bi-graph-up\", \"d-block\", \"fs-4\", \"mb-2\"], [1, \"d-flex\", \"align-items-center\"], [1, \"flex-shrink-0\", \"me-3\"], [1, \"flex-grow-1\"], [1, \"card-title\", \"text-muted\", \"mb-1\"], [1, \"mb-1\"], [1, \"text-center\", \"py-4\", \"text-muted\"], [1, \"bi\", \"bi-calendar-x\", \"display-6\", \"mb-3\"], [1, \"appointment-item\", \"d-flex\", \"align-items-center\", \"justify-content-between\", \"p-3\", \"mb-2\", \"rounded\"], [1, \"rounded-circle\", \"bg-light\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"45px\", \"height\", \"45px\"], [1, \"mb-1\", \"text-muted\", \"small\"], [1, \"bi\", \"bi-clock\", \"me-1\"], [1, \"ms-2\"], [1, \"text-muted\"], [1, \"flex-shrink-0\"], [1, \"btn\", \"btn-sm\", \"btn-primary\", 3, \"disabled\", \"click\"], [1, \"activity-item\"], [1, \"d-flex\", \"align-items-start\"], [1, \"rounded-circle\", \"bg-light\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"35px\", \"height\", \"35px\"], [1, \"mb-1\", \"small\"], [\"class\", \"my-3\", 4, \"ngIf\"], [1, \"my-3\"]],\n      template: function DoctorDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, DoctorDashboardComponent_div_1_Template, 6, 0, \"div\", 1);\n          i0.ɵɵtemplate(2, DoctorDashboardComponent_div_2_Template, 3, 1, \"div\", 2);\n          i0.ɵɵtemplate(3, DoctorDashboardComponent_div_3_Template, 65, 8, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i5.TitleCasePipe],\n      styles: [\".appointment-item[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  transition: all 0.3s ease;\\n}\\n\\n.appointment-item[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n  border-color: #dee2e6;\\n}\\n\\n.activity-item[_ngcontent-%COMP%] {\\n  padding-bottom: 1rem;\\n}\\n\\n.activity-item[_ngcontent-%COMP%]:last-child {\\n  padding-bottom: 0;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  border-bottom: 1px solid #e9ecef;\\n  font-weight: 600;\\n}\\n\\n.btn-outline-primary[_ngcontent-%COMP%], .btn-outline-success[_ngcontent-%COMP%], .btn-outline-info[_ngcontent-%COMP%], .btn-outline-warning[_ngcontent-%COMP%] {\\n  border-width: 2px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-outline-primary[_ngcontent-%COMP%]:hover, .btn-outline-success[_ngcontent-%COMP%]:hover, .btn-outline-info[_ngcontent-%COMP%]:hover, .btn-outline-warning[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n\\n.text-primary[_ngcontent-%COMP%] {\\n  color: #0d6efd !important;\\n}\\n\\n.text-success[_ngcontent-%COMP%] {\\n  color: #198754 !important;\\n}\\n\\n.text-warning[_ngcontent-%COMP%] {\\n  color: #ffc107 !important;\\n}\\n\\n.text-info[_ngcontent-%COMP%] {\\n  color: #0dcaf0 !important;\\n}\\n\\n.text-danger[_ngcontent-%COMP%] {\\n  color: #dc3545 !important;\\n}\\n\\n.spinner-border[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .container-fluid[_ngcontent-%COMP%] {\\n    padding-left: 1rem;\\n    padding-right: 1rem;\\n  }\\n  .card-body[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .appointment-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start !important;\\n  }\\n  .appointment-item[_ngcontent-%COMP%]   .flex-shrink-0[_ngcontent-%COMP%]:last-child {\\n    margin-top: 1rem;\\n    align-self: stretch;\\n  }\\n  .appointment-item[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ctx_r3", "currentUser", "affiliation", "ɵɵclassMapInterpolate2", "stat_r8", "icon", "color", "ɵɵtextInterpolate", "title", "value", "ɵɵclassMap", "ctx_r4", "getChangeClass", "changeType", "getChangeIcon", "change", "ɵɵlistener", "DoctorDashboardComponent_div_3_div_27_Template_button_click_18_listener", "restoredCtx", "ɵɵrestoreView", "_r11", "appointment_r9", "$implicit", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "startAppointment", "ctx_r6", "getAppointmentTypeIcon", "type", "getAppointmentTypeClass", "patientName", "time", "getStatusBadgeClass", "status", "ɵɵpipeBind1", "ɵɵproperty", "ɵɵclassMapInterpolate1", "ɵɵtemplate", "DoctorDashboardComponent_div_3_div_35_hr_12_Template", "activity_r12", "description", "isLast_r13", "DoctorDashboardComponent_div_3_span_10_Template", "DoctorDashboardComponent_div_3_Template_button_click_11_listener", "_r16", "ctx_r15", "refreshData", "DoctorDashboardComponent_div_3_div_15_Template", "DoctorDashboardComponent_div_3_Template_button_click_23_listener", "ctx_r17", "navigateTo", "DoctorDashboardComponent_div_3_div_26_Template", "DoctorDashboardComponent_div_3_div_27_Template", "DoctorDashboardComponent_div_3_div_35_Template", "DoctorDashboardComponent_div_3_Template_button_click_46_listener", "ctx_r18", "DoctorDashboardComponent_div_3_Template_button_click_51_listener", "ctx_r19", "DoctorDashboardComponent_div_3_Template_button_click_56_listener", "ctx_r20", "DoctorDashboardComponent_div_3_Template_button_click_61_listener", "ctx_r21", "ɵɵtextInterpolate2", "ctx_r2", "getGreeting", "fullName", "specialization", "dashboardStats", "todayAppointments", "length", "recentActivities", "DoctorDashboardComponent", "constructor", "authService", "userService", "appointmentService", "router", "isLoading", "realTodayAppointments", "id", "ngOnInit", "loadUserData", "loadTodayAppointments", "currentUser$", "subscribe", "next", "user", "getTodayAppointments", "appointments", "updateDashboardStats", "console", "todayCount", "todayStatIndex", "findIndex", "stat", "toString", "hour", "Date", "getHours", "appointment", "navigate", "queryParams", "appointmentId", "route", "setTimeout", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "UserService", "i3", "AppointmentService", "i4", "Router", "selectors", "decls", "vars", "consts", "template", "DoctorDashboardComponent_Template", "rf", "ctx", "DoctorDashboardComponent_div_1_Template", "DoctorDashboardComponent_div_2_Template", "DoctorDashboardComponent_div_3_Template"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/doctor/dashboard/dashboard.component.ts", "/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/doctor/dashboard/dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../core/services/auth.service';\nimport { UserService } from '../../core/services/user.service';\nimport { AppointmentService } from '../../core/services/appointment.service';\nimport { User } from '../../core/models/user.model';\nimport { Appointment } from '../../core/models/appointment.model';\n\ninterface DashboardStats {\n  title: string;\n  value: string;\n  change: string;\n  changeType: 'increase' | 'decrease' | 'neutral';\n  icon: string;\n  color: string;\n}\n\ninterface TodayAppointment {\n  id: number;\n  patientName: string;\n  time: string;\n  type: 'VIDEO_CALL' | 'IN_PERSON';\n  status: 'SCHEDULED' | 'COMPLETED' | 'CANCELLED';\n}\n\ninterface RecentActivity {\n  title: string;\n  description: string;\n  time: string;\n  icon: string;\n  color: string;\n}\n\n@Component({\n  selector: 'app-doctor-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class DoctorDashboardComponent implements OnInit {\n  currentUser: User | null = null;\n  isLoading = true;\n  error = '';\n  realTodayAppointments: Appointment[] = [];\n\n  dashboardStats: DashboardStats[] = [\n    {\n      title: 'Total Patients',\n      value: '156',\n      change: '+12%',\n      changeType: 'increase',\n      icon: 'people',\n      color: 'text-primary'\n    },\n    {\n      title: 'Today\\'s Appointments',\n      value: '8',\n      change: '+2',\n      changeType: 'increase',\n      icon: 'calendar-check',\n      color: 'text-success'\n    },\n    {\n      title: 'Pending Reviews',\n      value: '5',\n      change: '-3',\n      changeType: 'decrease',\n      icon: 'clipboard-check',\n      color: 'text-warning'\n    },\n    {\n      title: 'Messages',\n      value: '12',\n      change: '+4',\n      changeType: 'increase',\n      icon: 'chat-dots',\n      color: 'text-info'\n    }\n  ];\n\n  todayAppointments: TodayAppointment[] = [\n    {\n      id: 1,\n      patientName: 'John Smith',\n      time: '09:00 AM',\n      type: 'VIDEO_CALL',\n      status: 'SCHEDULED'\n    },\n    {\n      id: 2,\n      patientName: 'Sarah Johnson',\n      time: '10:30 AM',\n      type: 'IN_PERSON',\n      status: 'SCHEDULED'\n    },\n    {\n      id: 3,\n      patientName: 'Michael Brown',\n      time: '02:00 PM',\n      type: 'VIDEO_CALL',\n      status: 'SCHEDULED'\n    },\n    {\n      id: 4,\n      patientName: 'Emily Davis',\n      time: '03:30 PM',\n      type: 'IN_PERSON',\n      status: 'SCHEDULED'\n    }\n  ];\n\n  recentActivities: RecentActivity[] = [\n    {\n      title: 'New Patient Registration',\n      description: 'Alice Wilson registered as a new patient',\n      time: '30 minutes ago',\n      icon: 'person-plus',\n      color: 'text-success'\n    },\n    {\n      title: 'Appointment Rescheduled',\n      description: 'Tom Anderson moved appointment to tomorrow',\n      time: '1 hour ago',\n      icon: 'calendar-event',\n      color: 'text-warning'\n    },\n    {\n      title: 'Message Received',\n      description: 'New message from Lisa Parker about medication',\n      time: '2 hours ago',\n      icon: 'envelope',\n      color: 'text-info'\n    },\n    {\n      title: 'Lab Results Available',\n      description: 'Blood test results for David Miller are ready',\n      time: '3 hours ago',\n      icon: 'file-medical',\n      color: 'text-primary'\n    }\n  ];\n\n  constructor(\n    private authService: AuthService,\n    private userService: UserService,\n    private appointmentService: AppointmentService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.loadUserData();\n    this.loadTodayAppointments();\n  }\n\n  private loadUserData(): void {\n    this.authService.currentUser$.subscribe({\n      next: (user) => {\n        this.currentUser = user;\n        this.isLoading = false;\n      },\n      error: (error) => {\n        this.error = 'Failed to load user data';\n        this.isLoading = false;\n      }\n    });\n  }\n\n  private loadTodayAppointments(): void {\n    this.appointmentService.getTodayAppointments().subscribe({\n      next: (appointments) => {\n        this.realTodayAppointments = appointments;\n        this.updateDashboardStats(appointments.length);\n      },\n      error: (error) => {\n        console.error('Failed to load today appointments:', error);\n      }\n    });\n  }\n\n  private updateDashboardStats(todayCount: number): void {\n    // Update the \"Today's Appointments\" stat with real data\n    const todayStatIndex = this.dashboardStats.findIndex(stat => stat.title === \"Today's Appointments\");\n    if (todayStatIndex !== -1) {\n      this.dashboardStats[todayStatIndex].value = todayCount.toString();\n    }\n  }\n\n  getGreeting(): string {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Good morning';\n    if (hour < 18) return 'Good afternoon';\n    return 'Good evening';\n  }\n\n  getChangeClass(changeType: string): string {\n    switch (changeType) {\n      case 'increase': return 'text-success';\n      case 'decrease': return 'text-danger';\n      default: return 'text-muted';\n    }\n  }\n\n  getChangeIcon(changeType: string): string {\n    switch (changeType) {\n      case 'increase': return 'bi-arrow-up';\n      case 'decrease': return 'bi-arrow-down';\n      default: return 'bi-dash';\n    }\n  }\n\n  getAppointmentTypeIcon(type: string): string {\n    return type === 'VIDEO_CALL' ? 'camera-video' : 'geo-alt';\n  }\n\n  getAppointmentTypeClass(type: string): string {\n    return type === 'VIDEO_CALL' ? 'text-primary' : 'text-success';\n  }\n\n  getStatusBadgeClass(status: string): string {\n    switch (status) {\n      case 'SCHEDULED': return 'badge bg-primary';\n      case 'COMPLETED': return 'badge bg-success';\n      case 'CANCELLED': return 'badge bg-danger';\n      default: return 'badge bg-secondary';\n    }\n  }\n\n  startAppointment(appointment: TodayAppointment): void {\n    if (appointment.type === 'VIDEO_CALL') {\n      this.router.navigate(['/video-call'], { \n        queryParams: { appointmentId: appointment.id } \n      });\n    } else {\n      // For in-person appointments, navigate to patient details or appointment view\n      this.router.navigate(['/appointments', appointment.id]);\n    }\n  }\n\n  navigateTo(route: string): void {\n    this.router.navigate([route]);\n  }\n\n  refreshData(): void {\n    this.isLoading = true;\n    this.loadTodayAppointments();\n    setTimeout(() => {\n      this.isLoading = false;\n    }, 1000);\n  }\n}\n", "<div class=\"container-fluid py-4\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"text-center py-5\">\n    <div class=\"spinner-border text-primary\" role=\"status\">\n      <span class=\"visually-hidden\">Loading...</span>\n    </div>\n    <p class=\"mt-3 text-muted\">Loading your dashboard...</p>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"alert alert-danger\" role=\"alert\">\n    <i class=\"bi bi-exclamation-triangle me-2\"></i>\n    {{ error }}\n  </div>\n\n  <!-- Dashboard Content -->\n  <div *ngIf=\"!isLoading && !error\">\n    <!-- Welcome Header -->\n    <div class=\"row mb-4\">\n      <div class=\"col-12\">\n        <div class=\"d-flex justify-content-between align-items-center\">\n          <div>\n            <h1 class=\"h3 mb-1\">{{ getGreeting() }}, Dr. {{ currentUser?.fullName }}!</h1>\n            <p class=\"text-muted mb-0\">\n              <i class=\"bi bi-hospital me-2\"></i>{{ currentUser?.specialization || 'General Practice' }}\n              <span *ngIf=\"currentUser?.affiliation\"> • {{ currentUser?.affiliation }}</span>\n            </p>\n          </div>\n          <button class=\"btn btn-outline-primary\" (click)=\"refreshData()\">\n            <i class=\"bi bi-arrow-clockwise me-2\"></i>Refresh\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Dashboard Statistics -->\n    <div class=\"row mb-4\">\n      <div class=\"col-md-3 col-sm-6 mb-3\" *ngFor=\"let stat of dashboardStats\">\n        <div class=\"card h-100\">\n          <div class=\"card-body\">\n            <div class=\"d-flex align-items-center\">\n              <div class=\"flex-shrink-0 me-3\">\n                <i class=\"bi bi-{{ stat.icon }} fs-2 {{ stat.color }}\"></i>\n              </div>\n              <div class=\"flex-grow-1\">\n                <h6 class=\"card-title text-muted mb-1\">{{ stat.title }}</h6>\n                <h3 class=\"mb-1\">{{ stat.value }}</h3>\n                <small [class]=\"getChangeClass(stat.changeType)\">\n                  <i [class]=\"getChangeIcon(stat.changeType)\"></i>\n                  {{ stat.change }}\n                </small>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Today's Schedule & Recent Activities -->\n    <div class=\"row\">\n      <!-- Today's Appointments -->\n      <div class=\"col-md-8 mb-4\">\n        <div class=\"card h-100\">\n          <div class=\"card-header d-flex justify-content-between align-items-center\">\n            <h6 class=\"mb-0\">\n              <i class=\"bi bi-calendar-day me-2\"></i>Today's Schedule\n            </h6>\n            <button class=\"btn btn-sm btn-outline-primary\" (click)=\"navigateTo('/appointments')\">\n              View All\n            </button>\n          </div>\n          <div class=\"card-body\">\n            <div *ngIf=\"todayAppointments.length === 0\" class=\"text-center py-4 text-muted\">\n              <i class=\"bi bi-calendar-x display-6 mb-3\"></i>\n              <p>No appointments scheduled for today</p>\n            </div>\n            \n            <div *ngFor=\"let appointment of todayAppointments\" class=\"appointment-item d-flex align-items-center justify-content-between p-3 mb-2 rounded\">\n              <div class=\"d-flex align-items-center\">\n                <div class=\"flex-shrink-0 me-3\">\n                  <div class=\"rounded-circle bg-light d-flex align-items-center justify-content-center\" \n                       style=\"width: 45px; height: 45px;\">\n                    <i class=\"bi bi-{{ getAppointmentTypeIcon(appointment.type) }} {{ getAppointmentTypeClass(appointment.type) }}\"></i>\n                  </div>\n                </div>\n                <div>\n                  <h6 class=\"mb-1\">{{ appointment.patientName }}</h6>\n                  <p class=\"mb-1 text-muted small\">\n                    <i class=\"bi bi-clock me-1\"></i>{{ appointment.time }}\n                    <span class=\"ms-2\">\n                      <span [class]=\"getStatusBadgeClass(appointment.status)\">\n                        {{ appointment.status | titlecase }}\n                      </span>\n                    </span>\n                  </p>\n                  <small class=\"text-muted\">\n                    {{ appointment.type === 'VIDEO_CALL' ? 'Video Consultation' : 'In-Person Visit' }}\n                  </small>\n                </div>\n              </div>\n              <div class=\"flex-shrink-0\">\n                <button \n                  class=\"btn btn-sm btn-primary\"\n                  (click)=\"startAppointment(appointment)\"\n                  [disabled]=\"appointment.status !== 'SCHEDULED'\"\n                >\n                  <i class=\"bi bi-{{ appointment.type === 'VIDEO_CALL' ? 'camera-video' : 'person' }} me-1\"></i>\n                  {{ appointment.type === 'VIDEO_CALL' ? 'Join Call' : 'View Details' }}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Recent Activities -->\n      <div class=\"col-md-4 mb-4\">\n        <div class=\"card h-100\">\n          <div class=\"card-header\">\n            <h6 class=\"mb-0\">\n              <i class=\"bi bi-activity me-2\"></i>Recent Activities\n            </h6>\n          </div>\n          <div class=\"card-body\">\n            <div *ngFor=\"let activity of recentActivities; last as isLast\" class=\"activity-item\">\n              <div class=\"d-flex align-items-start\">\n                <div class=\"flex-shrink-0 me-3\">\n                  <div class=\"rounded-circle bg-light d-flex align-items-center justify-content-center\" \n                       style=\"width: 35px; height: 35px;\">\n                    <i class=\"bi bi-{{ activity.icon }} {{ activity.color }}\"></i>\n                  </div>\n                </div>\n                <div class=\"flex-grow-1\">\n                  <h6 class=\"mb-1 small\">{{ activity.title }}</h6>\n                  <p class=\"mb-1 text-muted small\">{{ activity.description }}</p>\n                  <small class=\"text-muted\">{{ activity.time }}</small>\n                </div>\n              </div>\n              <hr *ngIf=\"!isLast\" class=\"my-3\">\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Quick Actions -->\n    <div class=\"row\">\n      <div class=\"col-12\">\n        <div class=\"card\">\n          <div class=\"card-header\">\n            <h6 class=\"mb-0\">\n              <i class=\"bi bi-lightning me-2\"></i>Quick Actions\n            </h6>\n          </div>\n          <div class=\"card-body\">\n            <div class=\"row\">\n              <div class=\"col-md-3 col-sm-6 mb-3\">\n                <button class=\"btn btn-outline-primary w-100 py-3\" (click)=\"navigateTo('/patients')\">\n                  <i class=\"bi bi-people d-block fs-4 mb-2\"></i>\n                  <span>Manage Patients</span>\n                </button>\n              </div>\n              <div class=\"col-md-3 col-sm-6 mb-3\">\n                <button class=\"btn btn-outline-success w-100 py-3\" (click)=\"navigateTo('/appointments')\">\n                  <i class=\"bi bi-calendar-plus d-block fs-4 mb-2\"></i>\n                  <span>Schedule Appointment</span>\n                </button>\n              </div>\n              <div class=\"col-md-3 col-sm-6 mb-3\">\n                <button class=\"btn btn-outline-info w-100 py-3\" (click)=\"navigateTo('/chat')\">\n                  <i class=\"bi bi-chat-dots d-block fs-4 mb-2\"></i>\n                  <span>Messages</span>\n                </button>\n              </div>\n              <div class=\"col-md-3 col-sm-6 mb-3\">\n                <button class=\"btn btn-outline-warning w-100 py-3\" (click)=\"navigateTo('/reports')\">\n                  <i class=\"bi bi-graph-up d-block fs-4 mb-2\"></i>\n                  <span>View Reports</span>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;ICEEA,EAAA,CAAAC,cAAA,aAAgD;IAEdD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEjDH,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAI1DH,EAAA,CAAAC,cAAA,aAAyE;IACvED,EAAA,CAAAI,SAAA,WAA+C;IAC/CJ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAYYR,EAAA,CAAAC,cAAA,WAAuC;IAACD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAvCH,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,kBAAA,aAAAG,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,WAAA,KAAgC;;;;;IAYhFX,EAAA,CAAAC,cAAA,cAAwE;IAK9DD,EAAA,CAAAI,SAAA,QAA2D;IAC7DJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyB;IACgBD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5DH,EAAA,CAAAC,cAAA,aAAiB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAI,SAAA,SAAgD;IAChDJ,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IARLH,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAY,sBAAA,WAAAC,OAAA,CAAAC,IAAA,YAAAD,OAAA,CAAAE,KAAA,KAAmD;IAGff,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAgB,iBAAA,CAAAH,OAAA,CAAAI,KAAA,CAAgB;IACtCjB,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAgB,iBAAA,CAAAH,OAAA,CAAAK,KAAA,CAAgB;IAC1BlB,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAmB,UAAA,CAAAC,MAAA,CAAAC,cAAA,CAAAR,OAAA,CAAAS,UAAA,EAAyC;IAC3CtB,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAmB,UAAA,CAAAC,MAAA,CAAAG,aAAA,CAAAV,OAAA,CAAAS,UAAA,EAAwC;IAC3CtB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAO,OAAA,CAAAW,MAAA,MACF;;;;;IAsBJxB,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAI,SAAA,YAA+C;IAC/CJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,0CAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAG5CH,EAAA,CAAAC,cAAA,cAA+I;IAKvID,EAAA,CAAAI,SAAA,QAAoH;IACtHJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,UAAK;IACcD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnDH,EAAA,CAAAC,cAAA,YAAiC;IAC/BD,EAAA,CAAAI,SAAA,YAAgC;IAAAJ,EAAA,CAAAE,MAAA,IAChC;IAAAF,EAAA,CAAAC,cAAA,gBAAmB;IAEfD,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGXH,EAAA,CAAAC,cAAA,iBAA0B;IACxBD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAGZH,EAAA,CAAAC,cAAA,eAA2B;IAGvBD,EAAA,CAAAyB,UAAA,mBAAAC,wEAAA;MAAA,MAAAC,WAAA,GAAA3B,EAAA,CAAA4B,aAAA,CAAAC,IAAA;MAAA,MAAAC,cAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAAhC,EAAA,CAAAiC,aAAA;MAAA,OAASjC,EAAA,CAAAkC,WAAA,CAAAF,OAAA,CAAAG,gBAAA,CAAAL,cAAA,CAA6B;IAAA,EAAC;IAGvC9B,EAAA,CAAAI,SAAA,SAA8F;IAC9FJ,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IA1BFH,EAAA,CAAAK,SAAA,GAA4G;IAA5GL,EAAA,CAAAY,sBAAA,WAAAwB,MAAA,CAAAC,sBAAA,CAAAP,cAAA,CAAAQ,IAAA,QAAAF,MAAA,CAAAG,uBAAA,CAAAT,cAAA,CAAAQ,IAAA,MAA4G;IAIhGtC,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAgB,iBAAA,CAAAc,cAAA,CAAAU,WAAA,CAA6B;IAEZxC,EAAA,CAAAK,SAAA,GAChC;IADgCL,EAAA,CAAAM,kBAAA,KAAAwB,cAAA,CAAAW,IAAA,MAChC;IACQzC,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAmB,UAAA,CAAAiB,MAAA,CAAAM,mBAAA,CAAAZ,cAAA,CAAAa,MAAA,EAAiD;IACrD3C,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAA4C,WAAA,SAAAd,cAAA,CAAAa,MAAA,OACF;IAIF3C,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAwB,cAAA,CAAAQ,IAAA,kEACF;IAOAtC,EAAA,CAAAK,SAAA,GAA+C;IAA/CL,EAAA,CAAA6C,UAAA,aAAAf,cAAA,CAAAa,MAAA,iBAA+C;IAE5C3C,EAAA,CAAAK,SAAA,GAAsF;IAAtFL,EAAA,CAAA8C,sBAAA,WAAAhB,cAAA,CAAAQ,IAAA,uDAAsF;IACzFtC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAwB,cAAA,CAAAQ,IAAA,sDACF;;;;;IA8BFtC,EAAA,CAAAI,SAAA,aAAiC;;;;;IAdnCJ,EAAA,CAAAC,cAAA,cAAqF;IAK7ED,EAAA,CAAAI,SAAA,QAA8D;IAChEJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAAyB;IACAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/DH,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAGzDH,EAAA,CAAA+C,UAAA,KAAAC,oDAAA,iBAAiC;IACnChD,EAAA,CAAAG,YAAA,EAAM;;;;;IAVKH,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAAY,sBAAA,WAAAqC,YAAA,CAAAnC,IAAA,OAAAmC,YAAA,CAAAlC,KAAA,KAAsD;IAIpCf,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAgB,iBAAA,CAAAiC,YAAA,CAAAhC,KAAA,CAAoB;IACVjB,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAgB,iBAAA,CAAAiC,YAAA,CAAAC,WAAA,CAA0B;IACjClD,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAgB,iBAAA,CAAAiC,YAAA,CAAAR,IAAA,CAAmB;IAG5CzC,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAA6C,UAAA,UAAAM,UAAA,CAAa;;;;;;IA1H9BnD,EAAA,CAAAC,cAAA,UAAkC;IAMJD,EAAA,CAAAE,MAAA,GAAqD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9EH,EAAA,CAAAC,cAAA,YAA2B;IACzBD,EAAA,CAAAI,SAAA,YAAmC;IAAAJ,EAAA,CAAAE,MAAA,GACnC;IAAAF,EAAA,CAAA+C,UAAA,KAAAK,+CAAA,kBAA+E;IACjFpD,EAAA,CAAAG,YAAA,EAAI;IAENH,EAAA,CAAAC,cAAA,kBAAgE;IAAxBD,EAAA,CAAAyB,UAAA,mBAAA4B,iEAAA;MAAArD,EAAA,CAAA4B,aAAA,CAAA0B,IAAA;MAAA,MAAAC,OAAA,GAAAvD,EAAA,CAAAiC,aAAA;MAAA,OAASjC,EAAA,CAAAkC,WAAA,CAAAqB,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAC7DxD,EAAA,CAAAI,SAAA,aAA0C;IAAAJ,EAAA,CAAAE,MAAA,gBAC5C;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAMfH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAA+C,UAAA,KAAAU,8CAAA,oBAkBM;IACRzD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAiB;IAMPD,EAAA,CAAAI,SAAA,aAAuC;IAAAJ,EAAA,CAAAE,MAAA,yBACzC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,kBAAqF;IAAtCD,EAAA,CAAAyB,UAAA,mBAAAiC,iEAAA;MAAA1D,EAAA,CAAA4B,aAAA,CAAA0B,IAAA;MAAA,MAAAK,OAAA,GAAA3D,EAAA,CAAAiC,aAAA;MAAA,OAASjC,EAAA,CAAAkC,WAAA,CAAAyB,OAAA,CAAAC,UAAA,CAAW,eAAe,CAAC;IAAA,EAAC;IAClF5D,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAA+C,UAAA,KAAAc,8CAAA,kBAGM;IAEN7D,EAAA,CAAA+C,UAAA,KAAAe,8CAAA,oBAiCM;IACR9D,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,eAA2B;IAInBD,EAAA,CAAAI,SAAA,aAAmC;IAAAJ,EAAA,CAAAE,MAAA,0BACrC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAA+C,UAAA,KAAAgB,8CAAA,mBAeM;IACR/D,EAAA,CAAAG,YAAA,EAAM;IAMZH,EAAA,CAAAC,cAAA,eAAiB;IAKPD,EAAA,CAAAI,SAAA,aAAoC;IAAAJ,EAAA,CAAAE,MAAA,sBACtC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAC,cAAA,eAAuB;IAGkCD,EAAA,CAAAyB,UAAA,mBAAAuC,iEAAA;MAAAhE,EAAA,CAAA4B,aAAA,CAAA0B,IAAA;MAAA,MAAAW,OAAA,GAAAjE,EAAA,CAAAiC,aAAA;MAAA,OAASjC,EAAA,CAAAkC,WAAA,CAAA+B,OAAA,CAAAL,UAAA,CAAW,WAAW,CAAC;IAAA,EAAC;IAClF5D,EAAA,CAAAI,SAAA,aAA8C;IAC9CJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGhCH,EAAA,CAAAC,cAAA,eAAoC;IACiBD,EAAA,CAAAyB,UAAA,mBAAAyC,iEAAA;MAAAlE,EAAA,CAAA4B,aAAA,CAAA0B,IAAA;MAAA,MAAAa,OAAA,GAAAnE,EAAA,CAAAiC,aAAA;MAAA,OAASjC,EAAA,CAAAkC,WAAA,CAAAiC,OAAA,CAAAP,UAAA,CAAW,eAAe,CAAC;IAAA,EAAC;IACtF5D,EAAA,CAAAI,SAAA,aAAqD;IACrDJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGrCH,EAAA,CAAAC,cAAA,eAAoC;IACcD,EAAA,CAAAyB,UAAA,mBAAA2C,iEAAA;MAAApE,EAAA,CAAA4B,aAAA,CAAA0B,IAAA;MAAA,MAAAe,OAAA,GAAArE,EAAA,CAAAiC,aAAA;MAAA,OAASjC,EAAA,CAAAkC,WAAA,CAAAmC,OAAA,CAAAT,UAAA,CAAW,OAAO,CAAC;IAAA,EAAC;IAC3E5D,EAAA,CAAAI,SAAA,aAAiD;IACjDJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGzBH,EAAA,CAAAC,cAAA,eAAoC;IACiBD,EAAA,CAAAyB,UAAA,mBAAA6C,iEAAA;MAAAtE,EAAA,CAAA4B,aAAA,CAAA0B,IAAA;MAAA,MAAAiB,OAAA,GAAAvE,EAAA,CAAAiC,aAAA;MAAA,OAASjC,EAAA,CAAAkC,WAAA,CAAAqC,OAAA,CAAAX,UAAA,CAAW,UAAU,CAAC;IAAA,EAAC;IACjF5D,EAAA,CAAAI,SAAA,aAAgD;IAChDJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IA3JXH,EAAA,CAAAK,SAAA,GAAqD;IAArDL,EAAA,CAAAwE,kBAAA,KAAAC,MAAA,CAAAC,WAAA,cAAAD,MAAA,CAAA/D,WAAA,kBAAA+D,MAAA,CAAA/D,WAAA,CAAAiE,QAAA,MAAqD;IAEpC3E,EAAA,CAAAK,SAAA,GACnC;IADmCL,EAAA,CAAAM,kBAAA,MAAAmE,MAAA,CAAA/D,WAAA,kBAAA+D,MAAA,CAAA/D,WAAA,CAAAkE,cAAA,6BACnC;IAAO5E,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA6C,UAAA,SAAA4B,MAAA,CAAA/D,WAAA,kBAAA+D,MAAA,CAAA/D,WAAA,CAAAC,WAAA,CAA8B;IAYQX,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAA6C,UAAA,YAAA4B,MAAA,CAAAI,cAAA,CAAiB;IAmC1D7E,EAAA,CAAAK,SAAA,IAAoC;IAApCL,EAAA,CAAA6C,UAAA,SAAA4B,MAAA,CAAAK,iBAAA,CAAAC,MAAA,OAAoC;IAKb/E,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAA6C,UAAA,YAAA4B,MAAA,CAAAK,iBAAA,CAAoB;IA+CvB9E,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAA6C,UAAA,YAAA4B,MAAA,CAAAO,gBAAA,CAAqB;;;ADtF3D,OAAM,MAAOC,wBAAwB;EAuGnCC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,kBAAsC,EACtCC,MAAc;IAHd,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;IA1GhB,KAAA5E,WAAW,GAAgB,IAAI;IAC/B,KAAA6E,SAAS,GAAG,IAAI;IAChB,KAAA/E,KAAK,GAAG,EAAE;IACV,KAAAgF,qBAAqB,GAAkB,EAAE;IAEzC,KAAAX,cAAc,GAAqB,CACjC;MACE5D,KAAK,EAAE,gBAAgB;MACvBC,KAAK,EAAE,KAAK;MACZM,MAAM,EAAE,MAAM;MACdF,UAAU,EAAE,UAAU;MACtBR,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,uBAAuB;MAC9BC,KAAK,EAAE,GAAG;MACVM,MAAM,EAAE,IAAI;MACZF,UAAU,EAAE,UAAU;MACtBR,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,iBAAiB;MACxBC,KAAK,EAAE,GAAG;MACVM,MAAM,EAAE,IAAI;MACZF,UAAU,EAAE,UAAU;MACtBR,IAAI,EAAE,iBAAiB;MACvBC,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,UAAU;MACjBC,KAAK,EAAE,IAAI;MACXM,MAAM,EAAE,IAAI;MACZF,UAAU,EAAE,UAAU;MACtBR,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE;KACR,CACF;IAED,KAAA+D,iBAAiB,GAAuB,CACtC;MACEW,EAAE,EAAE,CAAC;MACLjD,WAAW,EAAE,YAAY;MACzBC,IAAI,EAAE,UAAU;MAChBH,IAAI,EAAE,YAAY;MAClBK,MAAM,EAAE;KACT,EACD;MACE8C,EAAE,EAAE,CAAC;MACLjD,WAAW,EAAE,eAAe;MAC5BC,IAAI,EAAE,UAAU;MAChBH,IAAI,EAAE,WAAW;MACjBK,MAAM,EAAE;KACT,EACD;MACE8C,EAAE,EAAE,CAAC;MACLjD,WAAW,EAAE,eAAe;MAC5BC,IAAI,EAAE,UAAU;MAChBH,IAAI,EAAE,YAAY;MAClBK,MAAM,EAAE;KACT,EACD;MACE8C,EAAE,EAAE,CAAC;MACLjD,WAAW,EAAE,aAAa;MAC1BC,IAAI,EAAE,UAAU;MAChBH,IAAI,EAAE,WAAW;MACjBK,MAAM,EAAE;KACT,CACF;IAED,KAAAqC,gBAAgB,GAAqB,CACnC;MACE/D,KAAK,EAAE,0BAA0B;MACjCiC,WAAW,EAAE,0CAA0C;MACvDT,IAAI,EAAE,gBAAgB;MACtB3B,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,yBAAyB;MAChCiC,WAAW,EAAE,4CAA4C;MACzDT,IAAI,EAAE,YAAY;MAClB3B,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,kBAAkB;MACzBiC,WAAW,EAAE,+CAA+C;MAC5DT,IAAI,EAAE,aAAa;MACnB3B,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,uBAAuB;MAC9BiC,WAAW,EAAE,+CAA+C;MAC5DT,IAAI,EAAE,aAAa;MACnB3B,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE;KACR,CACF;EAOE;EAEH2E,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,qBAAqB,EAAE;EAC9B;EAEQD,YAAYA,CAAA;IAClB,IAAI,CAACR,WAAW,CAACU,YAAY,CAACC,SAAS,CAAC;MACtCC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACtF,WAAW,GAAGsF,IAAI;QACvB,IAAI,CAACT,SAAS,GAAG,KAAK;MACxB,CAAC;MACD/E,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,0BAA0B;QACvC,IAAI,CAAC+E,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEQK,qBAAqBA,CAAA;IAC3B,IAAI,CAACP,kBAAkB,CAACY,oBAAoB,EAAE,CAACH,SAAS,CAAC;MACvDC,IAAI,EAAGG,YAAY,IAAI;QACrB,IAAI,CAACV,qBAAqB,GAAGU,YAAY;QACzC,IAAI,CAACC,oBAAoB,CAACD,YAAY,CAACnB,MAAM,CAAC;MAChD,CAAC;MACDvE,KAAK,EAAGA,KAAK,IAAI;QACf4F,OAAO,CAAC5F,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC5D;KACD,CAAC;EACJ;EAEQ2F,oBAAoBA,CAACE,UAAkB;IAC7C;IACA,MAAMC,cAAc,GAAG,IAAI,CAACzB,cAAc,CAAC0B,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACvF,KAAK,KAAK,sBAAsB,CAAC;IACnG,IAAIqF,cAAc,KAAK,CAAC,CAAC,EAAE;MACzB,IAAI,CAACzB,cAAc,CAACyB,cAAc,CAAC,CAACpF,KAAK,GAAGmF,UAAU,CAACI,QAAQ,EAAE;;EAErE;EAEA/B,WAAWA,CAAA;IACT,MAAMgC,IAAI,GAAG,IAAIC,IAAI,EAAE,CAACC,QAAQ,EAAE;IAClC,IAAIF,IAAI,GAAG,EAAE,EAAE,OAAO,cAAc;IACpC,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,cAAc;EACvB;EAEArF,cAAcA,CAACC,UAAkB;IAC/B,QAAQA,UAAU;MAChB,KAAK,UAAU;QAAE,OAAO,cAAc;MACtC,KAAK,UAAU;QAAE,OAAO,aAAa;MACrC;QAAS,OAAO,YAAY;;EAEhC;EAEAC,aAAaA,CAACD,UAAkB;IAC9B,QAAQA,UAAU;MAChB,KAAK,UAAU;QAAE,OAAO,aAAa;MACrC,KAAK,UAAU;QAAE,OAAO,eAAe;MACvC;QAAS,OAAO,SAAS;;EAE7B;EAEAe,sBAAsBA,CAACC,IAAY;IACjC,OAAOA,IAAI,KAAK,YAAY,GAAG,cAAc,GAAG,SAAS;EAC3D;EAEAC,uBAAuBA,CAACD,IAAY;IAClC,OAAOA,IAAI,KAAK,YAAY,GAAG,cAAc,GAAG,cAAc;EAChE;EAEAI,mBAAmBA,CAACC,MAAc;IAChC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,kBAAkB;MAC3C,KAAK,WAAW;QAAE,OAAO,kBAAkB;MAC3C,KAAK,WAAW;QAAE,OAAO,iBAAiB;MAC1C;QAAS,OAAO,oBAAoB;;EAExC;EAEAR,gBAAgBA,CAAC0E,WAA6B;IAC5C,IAAIA,WAAW,CAACvE,IAAI,KAAK,YAAY,EAAE;MACrC,IAAI,CAACgD,MAAM,CAACwB,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;QACpCC,WAAW,EAAE;UAAEC,aAAa,EAAEH,WAAW,CAACpB;QAAE;OAC7C,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACH,MAAM,CAACwB,QAAQ,CAAC,CAAC,eAAe,EAAED,WAAW,CAACpB,EAAE,CAAC,CAAC;;EAE3D;EAEA7B,UAAUA,CAACqD,KAAa;IACtB,IAAI,CAAC3B,MAAM,CAACwB,QAAQ,CAAC,CAACG,KAAK,CAAC,CAAC;EAC/B;EAEAzD,WAAWA,CAAA;IACT,IAAI,CAAC+B,SAAS,GAAG,IAAI;IACrB,IAAI,CAACK,qBAAqB,EAAE;IAC5BsB,UAAU,CAAC,MAAK;MACd,IAAI,CAAC3B,SAAS,GAAG,KAAK;IACxB,CAAC,EAAE,IAAI,CAAC;EACV;;;uBAjNWN,wBAAwB,EAAAjF,EAAA,CAAAmH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAArH,EAAA,CAAAmH,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAvH,EAAA,CAAAmH,iBAAA,CAAAK,EAAA,CAAAC,kBAAA,GAAAzH,EAAA,CAAAmH,iBAAA,CAAAO,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAxB1C,wBAAwB;MAAA2C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtCrClI,EAAA,CAAAC,cAAA,aAAkC;UAEhCD,EAAA,CAAA+C,UAAA,IAAAqF,uCAAA,iBAKM;UAGNpI,EAAA,CAAA+C,UAAA,IAAAsF,uCAAA,iBAGM;UAGNrI,EAAA,CAAA+C,UAAA,IAAAuF,uCAAA,kBAyKM;UACRtI,EAAA,CAAAG,YAAA,EAAM;;;UAxLEH,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAA6C,UAAA,SAAAsF,GAAA,CAAA5C,SAAA,CAAe;UAQfvF,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAA6C,UAAA,SAAAsF,GAAA,CAAA3H,KAAA,KAAA2H,GAAA,CAAA5C,SAAA,CAAyB;UAMzBvF,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAA6C,UAAA,UAAAsF,GAAA,CAAA5C,SAAA,KAAA4C,GAAA,CAAA3H,KAAA,CAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}