{"ast": null, "code": "'use strict';\n\nmodule.exports = {\n  isObject: function (obj) {\n    var type = typeof obj;\n    return type === 'function' || type === 'object' && !!obj;\n  },\n  extend: function (obj) {\n    if (!this.isObject(obj)) {\n      return obj;\n    }\n    var source, prop;\n    for (var i = 1, length = arguments.length; i < length; i++) {\n      source = arguments[i];\n      for (prop in source) {\n        if (Object.prototype.hasOwnProperty.call(source, prop)) {\n          obj[prop] = source[prop];\n        }\n      }\n    }\n    return obj;\n  }\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}