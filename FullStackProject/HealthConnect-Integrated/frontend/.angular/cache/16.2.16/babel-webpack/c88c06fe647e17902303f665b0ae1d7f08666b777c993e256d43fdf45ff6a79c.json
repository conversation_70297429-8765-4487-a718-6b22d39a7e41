{"ast": null, "code": "/**\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class HeartbeatInfo {\n  constructor(client) {\n    this.client = client;\n  }\n  get outgoing() {\n    return this.client.heartbeatOutgoing;\n  }\n  set outgoing(value) {\n    this.client.heartbeatOutgoing = value;\n  }\n  get incoming() {\n    return this.client.heartbeatIncoming;\n  }\n  set incoming(value) {\n    this.client.heartbeatIncoming = value;\n  }\n}", "map": {"version": 3, "names": ["HeartbeatInfo", "constructor", "client", "outgoing", "heartbeatOutgoing", "value", "incoming", "heartbeatIncoming"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/@stomp/stompjs/esm6/compatibility/heartbeat-info.js"], "sourcesContent": ["/**\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class HeartbeatInfo {\n    constructor(client) {\n        this.client = client;\n    }\n    get outgoing() {\n        return this.client.heartbeatOutgoing;\n    }\n    set outgoing(value) {\n        this.client.heartbeatOutgoing = value;\n    }\n    get incoming() {\n        return this.client.heartbeatIncoming;\n    }\n    set incoming(value) {\n        this.client.heartbeatIncoming = value;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,aAAa,CAAC;EACvBC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACD,MAAM,CAACE,iBAAiB;EACxC;EACA,IAAID,QAAQA,CAACE,KAAK,EAAE;IAChB,IAAI,CAACH,MAAM,CAACE,iBAAiB,GAAGC,KAAK;EACzC;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACJ,MAAM,CAACK,iBAAiB;EACxC;EACA,IAAID,QAAQA,CAACD,KAAK,EAAE;IAChB,IAAI,CAACH,MAAM,CAACK,iBAAiB,GAAGF,KAAK;EACzC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}