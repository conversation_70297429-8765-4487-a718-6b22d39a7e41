{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  AjaxBasedTransport = require('./lib/ajax-based'),\n  XhrReceiver = require('./receiver/xhr'),\n  XHRCorsObject = require('./sender/xhr-cors'),\n  XHRLocalObject = require('./sender/xhr-local');\nfunction XhrPollingTransport(transUrl) {\n  if (!XHRLocalObject.enabled && !XHRCorsObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr', XhrReceiver, XHRCorsObject);\n}\ninherits(XhrPollingTransport, AjaxBasedTransport);\nXhrPollingTransport.enabled = function (info) {\n  if (info.nullOrigin) {\n    return false;\n  }\n  if (XHRLocalObject.enabled && info.sameOrigin) {\n    return true;\n  }\n  return XHRCorsObject.enabled;\n};\nXhrPollingTransport.transportName = 'xhr-polling';\nXhrPollingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XhrPollingTransport;", "map": {"version": 3, "names": ["inherits", "require", "AjaxBasedTransport", "XhrReceiver", "XHRCorsObject", "XHRLocalObject", "XhrPollingTransport", "transUrl", "enabled", "Error", "call", "info", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "transportName", "roundTrips", "module", "exports"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/sockjs-client/lib/transport/xhr-polling.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , XhrReceiver = require('./receiver/xhr')\n  , XHRCorsObject = require('./sender/xhr-cors')\n  , XHRLocalObject = require('./sender/xhr-local')\n  ;\n\nfunction XhrPollingTransport(transUrl) {\n  if (!XHRLocalObject.enabled && !XHRCorsObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr', XhrReceiver, XHRCorsObject);\n}\n\ninherits(XhrPollingTransport, AjaxBasedTransport);\n\nXhrPollingTransport.enabled = function(info) {\n  if (info.nullOrigin) {\n    return false;\n  }\n\n  if (XHRLocalObject.enabled && info.sameOrigin) {\n    return true;\n  }\n  return XHRCorsObject.enabled;\n};\n\nXhrPollingTransport.transportName = 'xhr-polling';\nXhrPollingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XhrPollingTransport;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,kBAAkB,GAAGD,OAAO,CAAC,kBAAkB,CAAC;EAChDE,WAAW,GAAGF,OAAO,CAAC,gBAAgB,CAAC;EACvCG,aAAa,GAAGH,OAAO,CAAC,mBAAmB,CAAC;EAC5CI,cAAc,GAAGJ,OAAO,CAAC,oBAAoB,CAAC;AAGlD,SAASK,mBAAmBA,CAACC,QAAQ,EAAE;EACrC,IAAI,CAACF,cAAc,CAACG,OAAO,IAAI,CAACJ,aAAa,CAACI,OAAO,EAAE;IACrD,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC;EACpD;EACAP,kBAAkB,CAACQ,IAAI,CAAC,IAAI,EAAEH,QAAQ,EAAE,MAAM,EAAEJ,WAAW,EAAEC,aAAa,CAAC;AAC7E;AAEAJ,QAAQ,CAACM,mBAAmB,EAAEJ,kBAAkB,CAAC;AAEjDI,mBAAmB,CAACE,OAAO,GAAG,UAASG,IAAI,EAAE;EAC3C,IAAIA,IAAI,CAACC,UAAU,EAAE;IACnB,OAAO,KAAK;EACd;EAEA,IAAIP,cAAc,CAACG,OAAO,IAAIG,IAAI,CAACE,UAAU,EAAE;IAC7C,OAAO,IAAI;EACb;EACA,OAAOT,aAAa,CAACI,OAAO;AAC9B,CAAC;AAEDF,mBAAmB,CAACQ,aAAa,GAAG,aAAa;AACjDR,mBAAmB,CAACS,UAAU,GAAG,CAAC,CAAC,CAAC;;AAEpCC,MAAM,CAACC,OAAO,GAAGX,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}