{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/notification.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction NotificationBellComponent_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.unreadCount > 99 ? \"99+\" : ctx_r0.unreadCount, \" \");\n  }\n}\nfunction NotificationBellComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function NotificationBellComponent_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.markAllAsRead());\n    });\n    i0.ɵɵtext(1, \" Mark all read \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationBellComponent_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function NotificationBellComponent_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.clearAll());\n    });\n    i0.ɵɵtext(1, \" Clear all \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationBellComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20)(2, \"span\", 21);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction NotificationBellComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵelement(2, \"i\", 24);\n    i0.ɵɵelementStart(3, \"p\", 25);\n    i0.ɵɵtext(4, \"No notifications\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction NotificationBellComponent_div_15_div_9_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 41);\n  }\n  if (rf & 2) {\n    const notification_r11 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"src\", notification_r11.fromUser.avatar, i0.ɵɵsanitizeUrl)(\"alt\", notification_r11.fromUser.name);\n  }\n}\nfunction NotificationBellComponent_div_15_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, NotificationBellComponent_div_15_div_9_img_1_Template, 1, 2, \"img\", 39);\n    i0.ɵɵelementStart(2, \"span\", 40);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const notification_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r11.fromUser.avatar);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(notification_r11.fromUser.name);\n  }\n}\nfunction NotificationBellComponent_div_15_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 42);\n  }\n}\nfunction NotificationBellComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function NotificationBellComponent_div_15_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const notification_r11 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.handleNotificationClick(notification_r11));\n    });\n    i0.ɵɵelementStart(1, \"div\", 27)(2, \"div\", 28);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 29)(5, \"div\", 30);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 31);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, NotificationBellComponent_div_15_div_9_Template, 4, 2, \"div\", 32);\n    i0.ɵɵelementStart(10, \"div\", 33);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 34)(13, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function NotificationBellComponent_div_15_Template_button_click_13_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const notification_r11 = restoredCtx.$implicit;\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.removeNotification(notification_r11, $event));\n    });\n    i0.ɵɵelement(14, \"i\", 36);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(15, NotificationBellComponent_div_15_div_15_Template, 1, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notification_r11 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r5.getNotificationClass(notification_r11));\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r5.getNotificationIcon(notification_r11.type));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(notification_r11.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(notification_r11.message);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r11.fromUser);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.formatTime(notification_r11.timestamp));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !notification_r11.read);\n  }\n}\nfunction NotificationBellComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"a\", 44);\n    i0.ɵɵtext(2, \" View All Notifications \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class NotificationBellComponent {\n  constructor(notificationService, router) {\n    this.notificationService = notificationService;\n    this.router = router;\n    this.notifications = [];\n    this.unreadCount = 0;\n    this.showDropdown = false;\n    this.loading = false;\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // Subscribe to notifications\n    const notificationsSub = this.notificationService.getNotifications().subscribe(notifications => {\n      this.notifications = notifications.slice(0, 10); // Show only latest 10\n    });\n\n    this.subscriptions.push(notificationsSub);\n    // Subscribe to unread count\n    const unreadSub = this.notificationService.getUnreadCount().subscribe(count => this.unreadCount = count);\n    this.subscriptions.push(unreadSub);\n    // Request notification permission\n    this.notificationService.requestNotificationPermission();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  toggleDropdown() {\n    this.showDropdown = !this.showDropdown;\n  }\n  closeDropdown() {\n    this.showDropdown = false;\n  }\n  markAsRead(notification) {\n    if (!notification.read) {\n      this.notificationService.markAsRead(notification.id);\n    }\n  }\n  markAllAsRead() {\n    this.notificationService.markAllAsRead();\n  }\n  handleNotificationClick(notification) {\n    this.markAsRead(notification);\n    if (notification.actionUrl) {\n      this.router.navigate([notification.actionUrl]);\n    }\n    this.closeDropdown();\n  }\n  removeNotification(notification, event) {\n    event.stopPropagation();\n    this.notificationService.removeNotification(notification.id);\n  }\n  clearAll() {\n    this.notificationService.clearAll();\n  }\n  getNotificationIcon(type) {\n    switch (type) {\n      case 'message':\n        return 'fas fa-comment';\n      case 'appointment':\n        return 'fas fa-calendar';\n      case 'urgent':\n        return 'fas fa-exclamation-triangle';\n      case 'system':\n        return 'fas fa-cog';\n      default:\n        return 'fas fa-bell';\n    }\n  }\n  getNotificationClass(notification) {\n    const classes = ['notification-item'];\n    if (!notification.read) {\n      classes.push('unread');\n    }\n    classes.push(`priority-${notification.priority}`);\n    return classes.join(' ');\n  }\n  formatTime(timestamp) {\n    const now = new Date();\n    const diffMs = now.getTime() - timestamp.getTime();\n    const diffMinutes = Math.floor(diffMs / (1000 * 60));\n    const diffHours = Math.floor(diffMinutes / 60);\n    const diffDays = Math.floor(diffHours / 24);\n    if (diffMinutes < 1) {\n      return 'Just now';\n    } else if (diffMinutes < 60) {\n      return `${diffMinutes}m ago`;\n    } else if (diffHours < 24) {\n      return `${diffHours}h ago`;\n    } else if (diffDays < 7) {\n      return `${diffDays}d ago`;\n    } else {\n      return timestamp.toLocaleDateString();\n    }\n  }\n  trackByNotificationId(index, notification) {\n    return notification.id;\n  }\n  static {\n    this.ɵfac = function NotificationBellComponent_Factory(t) {\n      return new (t || NotificationBellComponent)(i0.ɵɵdirectiveInject(i1.NotificationService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NotificationBellComponent,\n      selectors: [[\"app-notification-bell\"]],\n      decls: 17,\n      vars: 12,\n      consts: [[1, \"notification-bell\", 3, \"clickOutside\"], [\"type\", \"button\", 1, \"btn\", \"btn-link\", \"notification-trigger\", 3, \"click\"], [1, \"fas\", \"fa-bell\"], [\"class\", \"badge bg-danger notification-badge\", 4, \"ngIf\"], [1, \"notification-dropdown\", 3, \"click\"], [1, \"dropdown-header\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"dropdown-actions\"], [\"type\", \"button\", \"class\", \"btn btn-sm btn-link text-primary\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-sm btn-link text-danger\", 3, \"click\", 4, \"ngIf\"], [1, \"notifications-list\"], [\"class\", \"text-center py-3\", 4, \"ngIf\"], [\"class\", \"no-notifications\", 4, \"ngIf\"], [3, \"class\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"dropdown-footer\", 4, \"ngIf\"], [1, \"badge\", \"bg-danger\", \"notification-badge\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-link\", \"text-primary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-link\", \"text-danger\", 3, \"click\"], [1, \"text-center\", \"py-3\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\"], [1, \"visually-hidden\"], [1, \"no-notifications\"], [1, \"text-center\", \"py-4\"], [1, \"fas\", \"fa-bell-slash\", \"fa-2x\", \"text-muted\", \"mb-2\"], [1, \"text-muted\", \"mb-0\"], [3, \"click\"], [1, \"notification-content\"], [1, \"notification-icon\"], [1, \"notification-body\"], [1, \"notification-title\"], [1, \"notification-message\"], [\"class\", \"notification-from\", 4, \"ngIf\"], [1, \"notification-time\"], [1, \"notification-actions\"], [\"type\", \"button\", \"title\", \"Remove notification\", 1, \"btn\", \"btn-sm\", \"btn-link\", \"text-muted\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"class\", \"unread-indicator\", 4, \"ngIf\"], [1, \"notification-from\"], [\"class\", \"from-avatar\", 3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"from-name\"], [1, \"from-avatar\", 3, \"src\", \"alt\"], [1, \"unread-indicator\"], [1, \"dropdown-footer\"], [\"routerLink\", \"/notifications\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", \"w-100\"]],\n      template: function NotificationBellComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"clickOutside\", function NotificationBellComponent_Template_div_clickOutside_0_listener() {\n            return ctx.closeDropdown();\n          });\n          i0.ɵɵelementStart(1, \"button\", 1);\n          i0.ɵɵlistener(\"click\", function NotificationBellComponent_Template_button_click_1_listener() {\n            return ctx.toggleDropdown();\n          });\n          i0.ɵɵelement(2, \"i\", 2);\n          i0.ɵɵtemplate(3, NotificationBellComponent_span_3_Template, 2, 1, \"span\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵlistener(\"click\", function NotificationBellComponent_Template_div_click_4_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"h6\", 7);\n          i0.ɵɵtext(8, \"Notifications\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 8);\n          i0.ɵɵtemplate(10, NotificationBellComponent_button_10_Template, 2, 0, \"button\", 9);\n          i0.ɵɵtemplate(11, NotificationBellComponent_button_11_Template, 2, 0, \"button\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 11);\n          i0.ɵɵtemplate(13, NotificationBellComponent_div_13_Template, 4, 0, \"div\", 12);\n          i0.ɵɵtemplate(14, NotificationBellComponent_div_14_Template, 5, 0, \"div\", 13);\n          i0.ɵɵtemplate(15, NotificationBellComponent_div_15_Template, 16, 9, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(16, NotificationBellComponent_div_16_Template, 3, 0, \"div\", 15);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"has-notifications\", ctx.unreadCount > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.unreadCount > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"show\", ctx.showDropdown);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.unreadCount > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.notifications.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.notifications.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.notifications)(\"ngForTrackBy\", ctx.trackByNotificationId);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.notifications.length > 0);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i2.RouterLink],\n      styles: [\".notification-bell[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-trigger[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 0.5rem;\\n  color: #6c757d;\\n  border: none;\\n  background: none;\\n  font-size: 1.25rem;\\n  transition: color 0.2s ease;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-trigger[_ngcontent-%COMP%]:hover {\\n  color: #495057;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-trigger.has-notifications[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  animation: _ngcontent-%COMP%_bellShake 2s infinite;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-trigger[_ngcontent-%COMP%]   .notification-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  right: 0;\\n  font-size: 0.75rem;\\n  min-width: 18px;\\n  height: 18px;\\n  border-radius: 9px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transform: translate(25%, -25%);\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  right: 0;\\n  width: 380px;\\n  max-height: 500px;\\n  background: white;\\n  border: 1px solid #dee2e6;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  z-index: 1050;\\n  opacity: 0;\\n  visibility: hidden;\\n  transform: translateY(-10px);\\n  transition: all 0.2s ease;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown.show[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n  transform: translateY(0);\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  border-bottom: 1px solid #dee2e6;\\n  background: #f8f9fa;\\n  border-radius: 8px 8px 0 0;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%]   .dropdown-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.875rem;\\n  text-decoration: none;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%]   .dropdown-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%] {\\n  max-height: 350px;\\n  overflow-y: auto;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 1rem;\\n  border-bottom: 1px solid #f1f3f4;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.unread[_ngcontent-%COMP%] {\\n  background-color: #f0f8ff;\\n  border-left: 3px solid #007bff;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.unread[_ngcontent-%COMP%]   .notification-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.priority-urgent[_ngcontent-%COMP%] {\\n  border-left-color: #dc3545;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.priority-urgent[_ngcontent-%COMP%]   .notification-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.priority-high[_ngcontent-%COMP%] {\\n  border-left-color: #fd7e14;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.75rem;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-icon[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background: #e9ecef;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #6c757d;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #212529;\\n  margin-bottom: 0.25rem;\\n  line-height: 1.4;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-message[_ngcontent-%COMP%] {\\n  font-size: 0.8125rem;\\n  color: #6c757d;\\n  line-height: 1.4;\\n  margin-bottom: 0.5rem;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-from[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  margin-bottom: 0.25rem;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-from[_ngcontent-%COMP%]   .from-avatar[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-from[_ngcontent-%COMP%]   .from-name[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #495057;\\n  font-weight: 500;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-time[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #adb5bd;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-actions[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 0.25rem;\\n  border: none;\\n  background: none;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  border-radius: 4px;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .unread-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  right: 0.5rem;\\n  width: 8px;\\n  height: 8px;\\n  background: #007bff;\\n  border-radius: 50%;\\n  transform: translateY(-50%);\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .no-notifications[_ngcontent-%COMP%] {\\n  padding: 2rem 1rem;\\n}\\n.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1rem;\\n  border-top: 1px solid #dee2e6;\\n  background: #f8f9fa;\\n  border-radius: 0 0 8px 8px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_bellShake {\\n  0%, 50%, 100% {\\n    transform: rotate(0deg);\\n  }\\n  10%, 30% {\\n    transform: rotate(-10deg);\\n  }\\n  20%, 40% {\\n    transform: rotate(10deg);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.5;\\n  }\\n  100% {\\n    opacity: 1;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%] {\\n    width: 320px;\\n    right: -50px;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%] {\\n    width: 280px;\\n    right: -100px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "unreadCount", "ɵɵlistener", "NotificationBellComponent_button_10_Template_button_click_0_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "markAllAsRead", "NotificationBellComponent_button_11_Template_button_click_0_listener", "_r10", "ctx_r9", "clearAll", "ɵɵelement", "ɵɵproperty", "notification_r11", "fromUser", "avatar", "ɵɵsanitizeUrl", "name", "ɵɵtemplate", "NotificationBellComponent_div_15_div_9_img_1_Template", "ɵɵtextInterpolate", "NotificationBellComponent_div_15_Template_div_click_0_listener", "restoredCtx", "_r18", "$implicit", "ctx_r17", "handleNotificationClick", "NotificationBellComponent_div_15_div_9_Template", "NotificationBellComponent_div_15_Template_button_click_13_listener", "$event", "ctx_r19", "removeNotification", "NotificationBellComponent_div_15_div_15_Template", "ɵɵclassMap", "ctx_r5", "getNotificationClass", "getNotificationIcon", "type", "title", "message", "formatTime", "timestamp", "read", "NotificationBellComponent", "constructor", "notificationService", "router", "notifications", "showDropdown", "loading", "subscriptions", "ngOnInit", "notificationsSub", "getNotifications", "subscribe", "slice", "push", "unreadSub", "getUnreadCount", "count", "requestNotificationPermission", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "toggleDropdown", "closeDropdown", "mark<PERSON><PERSON><PERSON>", "notification", "id", "actionUrl", "navigate", "event", "stopPropagation", "classes", "priority", "join", "now", "Date", "diffMs", "getTime", "diffMinutes", "Math", "floor", "diffHours", "diffDays", "toLocaleDateString", "trackByNotificationId", "index", "ɵɵdirectiveInject", "i1", "NotificationService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "NotificationBellComponent_Template", "rf", "ctx", "NotificationBellComponent_Template_div_clickOutside_0_listener", "NotificationBellComponent_Template_button_click_1_listener", "NotificationBellComponent_span_3_Template", "NotificationBellComponent_Template_div_click_4_listener", "NotificationBellComponent_button_10_Template", "NotificationBellComponent_button_11_Template", "NotificationBellComponent_div_13_Template", "NotificationBellComponent_div_14_Template", "NotificationBellComponent_div_15_Template", "NotificationBellComponent_div_16_Template", "ɵɵclassProp", "length"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/shared/components/notification-bell/notification-bell.component.ts", "/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/shared/components/notification-bell/notification-bell.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { NotificationService, Notification } from '../../../core/services/notification.service';\n\n@Component({\n  selector: 'app-notification-bell',\n  templateUrl: './notification-bell.component.html',\n  styleUrls: ['./notification-bell.component.scss']\n})\nexport class NotificationBellComponent implements OnInit, OnDestroy {\n  notifications: Notification[] = [];\n  unreadCount = 0;\n  showDropdown = false;\n  loading = false;\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private notificationService: NotificationService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Subscribe to notifications\n    const notificationsSub = this.notificationService.getNotifications().subscribe(\n      notifications => {\n        this.notifications = notifications.slice(0, 10); // Show only latest 10\n      }\n    );\n    this.subscriptions.push(notificationsSub);\n\n    // Subscribe to unread count\n    const unreadSub = this.notificationService.getUnreadCount().subscribe(\n      count => this.unreadCount = count\n    );\n    this.subscriptions.push(unreadSub);\n\n    // Request notification permission\n    this.notificationService.requestNotificationPermission();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  toggleDropdown(): void {\n    this.showDropdown = !this.showDropdown;\n  }\n\n  closeDropdown(): void {\n    this.showDropdown = false;\n  }\n\n  markAsRead(notification: Notification): void {\n    if (!notification.read) {\n      this.notificationService.markAsRead(notification.id);\n    }\n  }\n\n  markAllAsRead(): void {\n    this.notificationService.markAllAsRead();\n  }\n\n  handleNotificationClick(notification: Notification): void {\n    this.markAsRead(notification);\n    \n    if (notification.actionUrl) {\n      this.router.navigate([notification.actionUrl]);\n    }\n    \n    this.closeDropdown();\n  }\n\n  removeNotification(notification: Notification, event: Event): void {\n    event.stopPropagation();\n    this.notificationService.removeNotification(notification.id);\n  }\n\n  clearAll(): void {\n    this.notificationService.clearAll();\n  }\n\n  getNotificationIcon(type: string): string {\n    switch (type) {\n      case 'message':\n        return 'fas fa-comment';\n      case 'appointment':\n        return 'fas fa-calendar';\n      case 'urgent':\n        return 'fas fa-exclamation-triangle';\n      case 'system':\n        return 'fas fa-cog';\n      default:\n        return 'fas fa-bell';\n    }\n  }\n\n  getNotificationClass(notification: Notification): string {\n    const classes = ['notification-item'];\n    \n    if (!notification.read) {\n      classes.push('unread');\n    }\n    \n    classes.push(`priority-${notification.priority}`);\n    \n    return classes.join(' ');\n  }\n\n  formatTime(timestamp: Date): string {\n    const now = new Date();\n    const diffMs = now.getTime() - timestamp.getTime();\n    const diffMinutes = Math.floor(diffMs / (1000 * 60));\n    const diffHours = Math.floor(diffMinutes / 60);\n    const diffDays = Math.floor(diffHours / 24);\n\n    if (diffMinutes < 1) {\n      return 'Just now';\n    } else if (diffMinutes < 60) {\n      return `${diffMinutes}m ago`;\n    } else if (diffHours < 24) {\n      return `${diffHours}h ago`;\n    } else if (diffDays < 7) {\n      return `${diffDays}d ago`;\n    } else {\n      return timestamp.toLocaleDateString();\n    }\n  }\n\n  trackByNotificationId(index: number, notification: Notification): string {\n    return notification.id;\n  }\n}\n", "<div class=\"notification-bell\" (clickOutside)=\"closeDropdown()\">\n  <!-- Bell Icon -->\n  <button \n    type=\"button\" \n    class=\"btn btn-link notification-trigger\"\n    (click)=\"toggleDropdown()\"\n    [class.has-notifications]=\"unreadCount > 0\">\n    \n    <i class=\"fas fa-bell\"></i>\n    \n    <!-- Unread Count Badge -->\n    <span \n      *ngIf=\"unreadCount > 0\" \n      class=\"badge bg-danger notification-badge\">\n      {{ unreadCount > 99 ? '99+' : unreadCount }}\n    </span>\n  </button>\n\n  <!-- Dropdown Menu -->\n  <div \n    class=\"notification-dropdown\"\n    [class.show]=\"showDropdown\"\n    (click)=\"$event.stopPropagation()\">\n    \n    <!-- Header -->\n    <div class=\"dropdown-header\">\n      <div class=\"d-flex justify-content-between align-items-center\">\n        <h6 class=\"mb-0\">Notifications</h6>\n        <div class=\"dropdown-actions\">\n          <button \n            *ngIf=\"unreadCount > 0\"\n            type=\"button\" \n            class=\"btn btn-sm btn-link text-primary\"\n            (click)=\"markAllAsRead()\">\n            Mark all read\n          </button>\n          <button \n            *ngIf=\"notifications.length > 0\"\n            type=\"button\" \n            class=\"btn btn-sm btn-link text-danger\"\n            (click)=\"clearAll()\">\n            Clear all\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Notifications List -->\n    <div class=\"notifications-list\">\n      <!-- Loading State -->\n      <div *ngIf=\"loading\" class=\"text-center py-3\">\n        <div class=\"spinner-border spinner-border-sm\" role=\"status\">\n          <span class=\"visually-hidden\">Loading...</span>\n        </div>\n      </div>\n\n      <!-- No Notifications -->\n      <div *ngIf=\"!loading && notifications.length === 0\" class=\"no-notifications\">\n        <div class=\"text-center py-4\">\n          <i class=\"fas fa-bell-slash fa-2x text-muted mb-2\"></i>\n          <p class=\"text-muted mb-0\">No notifications</p>\n        </div>\n      </div>\n\n      <!-- Notification Items -->\n      <div \n        *ngFor=\"let notification of notifications; trackBy: trackByNotificationId\"\n        [class]=\"getNotificationClass(notification)\"\n        (click)=\"handleNotificationClick(notification)\">\n        \n        <div class=\"notification-content\">\n          <!-- Icon -->\n          <div class=\"notification-icon\">\n            <i [class]=\"getNotificationIcon(notification.type)\"></i>\n          </div>\n\n          <!-- Content -->\n          <div class=\"notification-body\">\n            <div class=\"notification-title\">{{ notification.title }}</div>\n            <div class=\"notification-message\">{{ notification.message }}</div>\n            \n            <!-- From User -->\n            <div *ngIf=\"notification.fromUser\" class=\"notification-from\">\n              <img \n                *ngIf=\"notification.fromUser.avatar\"\n                [src]=\"notification.fromUser.avatar\" \n                [alt]=\"notification.fromUser.name\"\n                class=\"from-avatar\">\n              <span class=\"from-name\">{{ notification.fromUser.name }}</span>\n            </div>\n            \n            <!-- Timestamp -->\n            <div class=\"notification-time\">{{ formatTime(notification.timestamp) }}</div>\n          </div>\n\n          <!-- Actions -->\n          <div class=\"notification-actions\">\n            <button \n              type=\"button\"\n              class=\"btn btn-sm btn-link text-muted\"\n              (click)=\"removeNotification(notification, $event)\"\n              title=\"Remove notification\">\n              <i class=\"fas fa-times\"></i>\n            </button>\n          </div>\n        </div>\n\n        <!-- Unread Indicator -->\n        <div *ngIf=\"!notification.read\" class=\"unread-indicator\"></div>\n      </div>\n    </div>\n\n    <!-- Footer -->\n    <div *ngIf=\"notifications.length > 0\" class=\"dropdown-footer\">\n      <a \n        class=\"btn btn-sm btn-outline-primary w-100\"\n        routerLink=\"/notifications\">\n        View All Notifications\n      </a>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;ICWIA,EAAA,CAAAC,cAAA,eAE6C;IAC3CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,WAAA,gBAAAD,MAAA,CAAAC,WAAA,MACF;;;;;;IAcMP,EAAA,CAAAC,cAAA,iBAI4B;IAA1BD,EAAA,CAAAQ,UAAA,mBAAAC,qEAAA;MAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAF,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC;IACzBf,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAIuB;IAArBD,EAAA,CAAAQ,UAAA,mBAAAQ,qEAAA;MAAAhB,EAAA,CAAAU,aAAA,CAAAO,IAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAI,MAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IACpBnB,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAQbH,EAAA,CAAAC,cAAA,cAA8C;IAEZD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAKnDH,EAAA,CAAAC,cAAA,cAA6E;IAEzED,EAAA,CAAAoB,SAAA,YAAuD;IACvDpB,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAuB3CH,EAAA,CAAAoB,SAAA,cAIsB;;;;IAFpBpB,EAAA,CAAAqB,UAAA,QAAAC,gBAAA,CAAAC,QAAA,CAAAC,MAAA,EAAAxB,EAAA,CAAAyB,aAAA,CAAoC,QAAAH,gBAAA,CAAAC,QAAA,CAAAG,IAAA;;;;;IAHxC1B,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAA2B,UAAA,IAAAC,qDAAA,kBAIsB;IACtB5B,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAJ5DH,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAqB,UAAA,SAAAC,gBAAA,CAAAC,QAAA,CAAAC,MAAA,CAAkC;IAIbxB,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAA6B,iBAAA,CAAAP,gBAAA,CAAAC,QAAA,CAAAG,IAAA,CAAgC;;;;;IAoB9D1B,EAAA,CAAAoB,SAAA,cAA+D;;;;;;IA3CjEpB,EAAA,CAAAC,cAAA,cAGkD;IAAhDD,EAAA,CAAAQ,UAAA,mBAAAsB,+DAAA;MAAA,MAAAC,WAAA,GAAA/B,EAAA,CAAAU,aAAA,CAAAsB,IAAA;MAAA,MAAAV,gBAAA,GAAAS,WAAA,CAAAE,SAAA;MAAA,MAAAC,OAAA,GAAAlC,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAoB,OAAA,CAAAC,uBAAA,CAAAb,gBAAA,CAAqC;IAAA,EAAC;IAE/CtB,EAAA,CAAAC,cAAA,cAAkC;IAG9BD,EAAA,CAAAoB,SAAA,QAAwD;IAC1DpB,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAA+B;IACGD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9DH,EAAA,CAAAC,cAAA,cAAkC;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGlEH,EAAA,CAAA2B,UAAA,IAAAS,+CAAA,kBAOM;IAGNpC,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAE,MAAA,IAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAI/EH,EAAA,CAAAC,cAAA,eAAkC;IAI9BD,EAAA,CAAAQ,UAAA,mBAAA6B,mEAAAC,MAAA;MAAA,MAAAP,WAAA,GAAA/B,EAAA,CAAAU,aAAA,CAAAsB,IAAA;MAAA,MAAAV,gBAAA,GAAAS,WAAA,CAAAE,SAAA;MAAA,MAAAM,OAAA,GAAAvC,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAyB,OAAA,CAAAC,kBAAA,CAAAlB,gBAAA,EAAAgB,MAAA,CAAwC;IAAA,EAAC;IAElDtC,EAAA,CAAAoB,SAAA,aAA4B;IAC9BpB,EAAA,CAAAG,YAAA,EAAS;IAKbH,EAAA,CAAA2B,UAAA,KAAAc,gDAAA,kBAA+D;IACjEzC,EAAA,CAAAG,YAAA,EAAM;;;;;IA1CJH,EAAA,CAAA0C,UAAA,CAAAC,MAAA,CAAAC,oBAAA,CAAAtB,gBAAA,EAA4C;IAMrCtB,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAA0C,UAAA,CAAAC,MAAA,CAAAE,mBAAA,CAAAvB,gBAAA,CAAAwB,IAAA,EAAgD;IAKnB9C,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAA6B,iBAAA,CAAAP,gBAAA,CAAAyB,KAAA,CAAwB;IACtB/C,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAA6B,iBAAA,CAAAP,gBAAA,CAAA0B,OAAA,CAA0B;IAGtDhD,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAqB,UAAA,SAAAC,gBAAA,CAAAC,QAAA,CAA2B;IAUFvB,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAA6B,iBAAA,CAAAc,MAAA,CAAAM,UAAA,CAAA3B,gBAAA,CAAA4B,SAAA,EAAwC;IAgBrElD,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAqB,UAAA,UAAAC,gBAAA,CAAA6B,IAAA,CAAwB;;;;;IAKlCnD,EAAA,CAAAC,cAAA,cAA8D;IAI1DD,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;AD5GV,OAAM,MAAOiD,yBAAyB;EAQpCC,YACUC,mBAAwC,EACxCC,MAAc;IADd,KAAAD,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IAThB,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAjD,WAAW,GAAG,CAAC;IACf,KAAAkD,YAAY,GAAG,KAAK;IACpB,KAAAC,OAAO,GAAG,KAAK;IAEP,KAAAC,aAAa,GAAmB,EAAE;EAKvC;EAEHC,QAAQA,CAAA;IACN;IACA,MAAMC,gBAAgB,GAAG,IAAI,CAACP,mBAAmB,CAACQ,gBAAgB,EAAE,CAACC,SAAS,CAC5EP,aAAa,IAAG;MACd,IAAI,CAACA,aAAa,GAAGA,aAAa,CAACQ,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC,CACF;;IACD,IAAI,CAACL,aAAa,CAACM,IAAI,CAACJ,gBAAgB,CAAC;IAEzC;IACA,MAAMK,SAAS,GAAG,IAAI,CAACZ,mBAAmB,CAACa,cAAc,EAAE,CAACJ,SAAS,CACnEK,KAAK,IAAI,IAAI,CAAC7D,WAAW,GAAG6D,KAAK,CAClC;IACD,IAAI,CAACT,aAAa,CAACM,IAAI,CAACC,SAAS,CAAC;IAElC;IACA,IAAI,CAACZ,mBAAmB,CAACe,6BAA6B,EAAE;EAC1D;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACX,aAAa,CAACY,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACjB,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAkB,aAAaA,CAAA;IACX,IAAI,CAAClB,YAAY,GAAG,KAAK;EAC3B;EAEAmB,UAAUA,CAACC,YAA0B;IACnC,IAAI,CAACA,YAAY,CAAC1B,IAAI,EAAE;MACtB,IAAI,CAACG,mBAAmB,CAACsB,UAAU,CAACC,YAAY,CAACC,EAAE,CAAC;;EAExD;EAEA/D,aAAaA,CAAA;IACX,IAAI,CAACuC,mBAAmB,CAACvC,aAAa,EAAE;EAC1C;EAEAoB,uBAAuBA,CAAC0C,YAA0B;IAChD,IAAI,CAACD,UAAU,CAACC,YAAY,CAAC;IAE7B,IAAIA,YAAY,CAACE,SAAS,EAAE;MAC1B,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAACH,YAAY,CAACE,SAAS,CAAC,CAAC;;IAGhD,IAAI,CAACJ,aAAa,EAAE;EACtB;EAEAnC,kBAAkBA,CAACqC,YAA0B,EAAEI,KAAY;IACzDA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAAC5B,mBAAmB,CAACd,kBAAkB,CAACqC,YAAY,CAACC,EAAE,CAAC;EAC9D;EAEA3D,QAAQA,CAAA;IACN,IAAI,CAACmC,mBAAmB,CAACnC,QAAQ,EAAE;EACrC;EAEA0B,mBAAmBA,CAACC,IAAY;IAC9B,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,gBAAgB;MACzB,KAAK,aAAa;QAChB,OAAO,iBAAiB;MAC1B,KAAK,QAAQ;QACX,OAAO,6BAA6B;MACtC,KAAK,QAAQ;QACX,OAAO,YAAY;MACrB;QACE,OAAO,aAAa;;EAE1B;EAEAF,oBAAoBA,CAACiC,YAA0B;IAC7C,MAAMM,OAAO,GAAG,CAAC,mBAAmB,CAAC;IAErC,IAAI,CAACN,YAAY,CAAC1B,IAAI,EAAE;MACtBgC,OAAO,CAAClB,IAAI,CAAC,QAAQ,CAAC;;IAGxBkB,OAAO,CAAClB,IAAI,CAAC,YAAYY,YAAY,CAACO,QAAQ,EAAE,CAAC;IAEjD,OAAOD,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC;EAC1B;EAEApC,UAAUA,CAACC,SAAe;IACxB,MAAMoC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,MAAM,GAAGF,GAAG,CAACG,OAAO,EAAE,GAAGvC,SAAS,CAACuC,OAAO,EAAE;IAClD,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IACpD,MAAMK,SAAS,GAAGF,IAAI,CAACC,KAAK,CAACF,WAAW,GAAG,EAAE,CAAC;IAC9C,MAAMI,QAAQ,GAAGH,IAAI,CAACC,KAAK,CAACC,SAAS,GAAG,EAAE,CAAC;IAE3C,IAAIH,WAAW,GAAG,CAAC,EAAE;MACnB,OAAO,UAAU;KAClB,MAAM,IAAIA,WAAW,GAAG,EAAE,EAAE;MAC3B,OAAO,GAAGA,WAAW,OAAO;KAC7B,MAAM,IAAIG,SAAS,GAAG,EAAE,EAAE;MACzB,OAAO,GAAGA,SAAS,OAAO;KAC3B,MAAM,IAAIC,QAAQ,GAAG,CAAC,EAAE;MACvB,OAAO,GAAGA,QAAQ,OAAO;KAC1B,MAAM;MACL,OAAO5C,SAAS,CAAC6C,kBAAkB,EAAE;;EAEzC;EAEAC,qBAAqBA,CAACC,KAAa,EAAEpB,YAA0B;IAC7D,OAAOA,YAAY,CAACC,EAAE;EACxB;;;uBA1HW1B,yBAAyB,EAAApD,EAAA,CAAAkG,iBAAA,CAAAC,EAAA,CAAAC,mBAAA,GAAApG,EAAA,CAAAkG,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAzBlD,yBAAyB;MAAAmD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVtC7G,EAAA,CAAAC,cAAA,aAAgE;UAAjCD,EAAA,CAAAQ,UAAA,0BAAAuG,+DAAA;YAAA,OAAgBD,GAAA,CAAAnC,aAAA,EAAe;UAAA,EAAC;UAE7D3E,EAAA,CAAAC,cAAA,gBAI8C;UAD5CD,EAAA,CAAAQ,UAAA,mBAAAwG,2DAAA;YAAA,OAASF,GAAA,CAAApC,cAAA,EAAgB;UAAA,EAAC;UAG1B1E,EAAA,CAAAoB,SAAA,WAA2B;UAG3BpB,EAAA,CAAA2B,UAAA,IAAAsF,yCAAA,kBAIO;UACTjH,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAC,cAAA,aAGqC;UAAnCD,EAAA,CAAAQ,UAAA,mBAAA0G,wDAAA5E,MAAA;YAAA,OAASA,MAAA,CAAA4C,eAAA,EAAwB;UAAA,EAAC;UAGlClF,EAAA,CAAAC,cAAA,aAA6B;UAERD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnCH,EAAA,CAAAC,cAAA,aAA8B;UAC5BD,EAAA,CAAA2B,UAAA,KAAAwF,4CAAA,oBAMS;UACTnH,EAAA,CAAA2B,UAAA,KAAAyF,4CAAA,qBAMS;UACXpH,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,eAAgC;UAE9BD,EAAA,CAAA2B,UAAA,KAAA0F,yCAAA,kBAIM;UAGNrH,EAAA,CAAA2B,UAAA,KAAA2F,yCAAA,kBAKM;UAGNtH,EAAA,CAAA2B,UAAA,KAAA4F,yCAAA,mBA4CM;UACRvH,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAA2B,UAAA,KAAA6F,yCAAA,kBAMM;UACRxH,EAAA,CAAAG,YAAA,EAAM;;;UAlHJH,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAyH,WAAA,sBAAAX,GAAA,CAAAvG,WAAA,KAA2C;UAMxCP,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAqB,UAAA,SAAAyF,GAAA,CAAAvG,WAAA,KAAqB;UASxBP,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAyH,WAAA,SAAAX,GAAA,CAAArD,YAAA,CAA2B;UASlBzD,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAqB,UAAA,SAAAyF,GAAA,CAAAvG,WAAA,KAAqB;UAOrBP,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAqB,UAAA,SAAAyF,GAAA,CAAAtD,aAAA,CAAAkE,MAAA,KAA8B;UAa/B1H,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAAqB,UAAA,SAAAyF,GAAA,CAAApD,OAAA,CAAa;UAOb1D,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAqB,UAAA,UAAAyF,GAAA,CAAApD,OAAA,IAAAoD,GAAA,CAAAtD,aAAA,CAAAkE,MAAA,OAA4C;UASvB1H,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAqB,UAAA,YAAAyF,GAAA,CAAAtD,aAAA,CAAkB,iBAAAsD,GAAA,CAAAd,qBAAA;UA+CzChG,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAqB,UAAA,SAAAyF,GAAA,CAAAtD,aAAA,CAAAkE,MAAA,KAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}