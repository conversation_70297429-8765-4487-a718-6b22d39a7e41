{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthGuard } from '../core/guards/auth.guard';\nimport { ChatListComponent } from './chat-list/chat-list.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  canActivate: [AuthGuard],\n  component: ChatListComponent\n}];\nexport let ChatRoutingModule = /*#__PURE__*/(() => {\n  class ChatRoutingModule {\n    static {\n      this.ɵfac = function ChatRoutingModule_Factory(t) {\n        return new (t || ChatRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ChatRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return ChatRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}