{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  EventEmitter = require('events').EventEmitter;\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:xhr');\n}\nfunction XhrReceiver(url, AjaxObject) {\n  debug(url);\n  EventEmitter.call(this);\n  var self = this;\n  this.bufferPosition = 0;\n  this.xo = new AjaxObject('POST', url, null);\n  this.xo.on('chunk', this._chunkHandler.bind(this));\n  this.xo.once('finish', function (status, text) {\n    debug('finish', status, text);\n    self._chunkHandler(status, text);\n    self.xo = null;\n    var reason = status === 200 ? 'network' : 'permanent';\n    debug('close', reason);\n    self.emit('close', null, reason);\n    self._cleanup();\n  });\n}\ninherits(XhrReceiver, EventEmitter);\nXhrReceiver.prototype._chunkHandler = function (status, text) {\n  debug('_chunkHandler', status);\n  if (status !== 200 || !text) {\n    return;\n  }\n  for (var idx = -1;; this.bufferPosition += idx + 1) {\n    var buf = text.slice(this.bufferPosition);\n    idx = buf.indexOf('\\n');\n    if (idx === -1) {\n      break;\n    }\n    var msg = buf.slice(0, idx);\n    if (msg) {\n      debug('message', msg);\n      this.emit('message', msg);\n    }\n  }\n};\nXhrReceiver.prototype._cleanup = function () {\n  debug('_cleanup');\n  this.removeAllListeners();\n};\nXhrReceiver.prototype.abort = function () {\n  debug('abort');\n  if (this.xo) {\n    this.xo.close();\n    debug('close');\n    this.emit('close', null, 'user');\n    this.xo = null;\n  }\n  this._cleanup();\n};\nmodule.exports = XhrReceiver;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}