{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { HttpClientModule } from '@angular/common/http';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\n// Core modules\nimport { CoreModule } from './core/core.module';\nimport { SharedModule } from './shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport let AppModule = /*#__PURE__*/(() => {\n  class AppModule {\n    static {\n      this.ɵfac = function AppModule_Factory(t) {\n        return new (t || AppModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AppModule,\n        bootstrap: [AppComponent]\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [BrowserModule, BrowserAnimationsModule, HttpClientModule, ReactiveFormsModule, FormsModule, AppRoutingModule, CoreModule, SharedModule]\n      });\n    }\n  }\n  return AppModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}