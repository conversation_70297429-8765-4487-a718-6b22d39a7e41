{"ast": null, "code": "/**\n * Possible states for the IStompSocket\n */\nexport var StompSocketState;\n(function (StompSocketState) {\n  StompSocketState[StompSocketState[\"CONNECTING\"] = 0] = \"CONNECTING\";\n  StompSocketState[StompSocketState[\"OPEN\"] = 1] = \"OPEN\";\n  StompSocketState[StompSocketState[\"CLOSING\"] = 2] = \"CLOSING\";\n  StompSocketState[StompSocketState[\"CLOSED\"] = 3] = \"CLOSED\";\n})(StompSocketState || (StompSocketState = {}));\n/**\n * Possible activation state\n */\nexport var ActivationState;\n(function (ActivationState) {\n  ActivationState[ActivationState[\"ACTIVE\"] = 0] = \"ACTIVE\";\n  ActivationState[ActivationState[\"DEACTIVATING\"] = 1] = \"DEACTIVATING\";\n  ActivationState[ActivationState[\"INACTIVE\"] = 2] = \"INACTIVE\";\n})(ActivationState || (ActivationState = {}));\n/**\n * Possible reconnection wait time modes\n */\nexport var ReconnectionTimeMode;\n(function (ReconnectionTimeMode) {\n  ReconnectionTimeMode[ReconnectionTimeMode[\"LINEAR\"] = 0] = \"LINEAR\";\n  ReconnectionTimeMode[ReconnectionTimeMode[\"EXPONENTIAL\"] = 1] = \"EXPONENTIAL\";\n})(ReconnectionTimeMode || (ReconnectionTimeMode = {}));\n/**\n * Possible ticker strategies for outgoing heartbeat ping\n */\nexport var TickerStrategy;\n(function (TickerStrategy) {\n  TickerStrategy[\"Interval\"] = \"interval\";\n  TickerStrategy[\"Worker\"] = \"worker\";\n})(TickerStrategy || (TickerStrategy = {}));", "map": {"version": 3, "names": ["StompSocketState", "ActivationState", "ReconnectionTimeMode", "TickerStrategy"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/@stomp/stompjs/esm6/types.js"], "sourcesContent": ["/**\n * Possible states for the IStompSocket\n */\nexport var StompSocketState;\n(function (StompSocketState) {\n    StompSocketState[StompSocketState[\"CONNECTING\"] = 0] = \"CONNECTING\";\n    StompSocketState[StompSocketState[\"OPEN\"] = 1] = \"OPEN\";\n    StompSocketState[StompSocketState[\"CLOSING\"] = 2] = \"CLOSING\";\n    StompSocketState[StompSocketState[\"CLOSED\"] = 3] = \"CLOSED\";\n})(StompSocketState || (StompSocketState = {}));\n/**\n * Possible activation state\n */\nexport var ActivationState;\n(function (ActivationState) {\n    ActivationState[ActivationState[\"ACTIVE\"] = 0] = \"ACTIVE\";\n    ActivationState[ActivationState[\"DEACTIVATING\"] = 1] = \"DEACTIVATING\";\n    ActivationState[ActivationState[\"INACTIVE\"] = 2] = \"INACTIVE\";\n})(ActivationState || (ActivationState = {}));\n/**\n * Possible reconnection wait time modes\n */\nexport var ReconnectionTimeMode;\n(function (ReconnectionTimeMode) {\n    ReconnectionTimeMode[ReconnectionTimeMode[\"LINEAR\"] = 0] = \"LINEAR\";\n    ReconnectionTimeMode[ReconnectionTimeMode[\"EXPONENTIAL\"] = 1] = \"EXPONENTIAL\";\n})(ReconnectionTimeMode || (ReconnectionTimeMode = {}));\n/**\n * Possible ticker strategies for outgoing heartbeat ping\n */\nexport var TickerStrategy;\n(function (TickerStrategy) {\n    TickerStrategy[\"Interval\"] = \"interval\";\n    TickerStrategy[\"Worker\"] = \"worker\";\n})(TickerStrategy || (TickerStrategy = {}));\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,IAAIA,gBAAgB;AAC3B,CAAC,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAACA,gBAAgB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EACnEA,gBAAgB,CAACA,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACvDA,gBAAgB,CAACA,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EAC7DA,gBAAgB,CAACA,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;AAC/D,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C;AACA;AACA;AACA,OAAO,IAAIC,eAAe;AAC1B,CAAC,UAAUA,eAAe,EAAE;EACxBA,eAAe,CAACA,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACzDA,eAAe,CAACA,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EACrEA,eAAe,CAACA,eAAe,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;AACjE,CAAC,EAAEA,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C;AACA;AACA;AACA,OAAO,IAAIC,oBAAoB;AAC/B,CAAC,UAAUA,oBAAoB,EAAE;EAC7BA,oBAAoB,CAACA,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACnEA,oBAAoB,CAACA,oBAAoB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;AACjF,CAAC,EAAEA,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD;AACA;AACA;AACA,OAAO,IAAIC,cAAc;AACzB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAAC,UAAU,CAAC,GAAG,UAAU;EACvCA,cAAc,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACvC,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}