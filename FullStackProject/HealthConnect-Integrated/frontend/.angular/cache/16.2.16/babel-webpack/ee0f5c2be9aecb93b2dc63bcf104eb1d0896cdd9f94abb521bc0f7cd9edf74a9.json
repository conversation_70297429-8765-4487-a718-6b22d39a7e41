{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthGuard } from '../core/guards/auth.guard';\nimport { AppointmentListComponent } from './appointment-list/appointment-list.component';\nimport { AppointmentBookingComponent } from './appointment-booking/appointment-booking.component';\nimport { DoctorSearchComponent } from './doctor-search/doctor-search.component';\nimport { AppointmentDetailsComponent } from './appointment-details/appointment-details.component';\nimport { AppointmentCalendarComponent } from './appointment-calendar/appointment-calendar.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  canActivate: [AuthGuard],\n  children: [{\n    path: '',\n    redirectTo: 'list',\n    pathMatch: 'full'\n  }, {\n    path: 'list',\n    component: AppointmentListComponent\n  }, {\n    path: 'calendar',\n    component: AppointmentCalendarComponent\n  }, {\n    path: 'book',\n    component: AppointmentBookingComponent\n  }, {\n    path: 'doctors',\n    component: DoctorSearchComponent\n  }, {\n    path: ':id',\n    component: AppointmentDetailsComponent\n  }]\n}];\nexport class AppointmentsRoutingModule {\n  static {\n    this.ɵfac = function AppointmentsRoutingModule_Factory(t) {\n      return new (t || AppointmentsRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppointmentsRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppointmentsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "<PERSON><PERSON><PERSON><PERSON>", "AppointmentListComponent", "AppointmentBookingComponent", "Doctor<PERSON><PERSON>chComponent", "AppointmentDetailsComponent", "AppointmentCalendarComponent", "routes", "path", "canActivate", "children", "redirectTo", "pathMatch", "component", "AppointmentsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/appointments/appointments-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { AuthGuard } from '../core/guards/auth.guard';\n\nimport { AppointmentListComponent } from './appointment-list/appointment-list.component';\nimport { AppointmentBookingComponent } from './appointment-booking/appointment-booking.component';\nimport { DoctorSearchComponent } from './doctor-search/doctor-search.component';\nimport { AppointmentDetailsComponent } from './appointment-details/appointment-details.component';\nimport { AppointmentCalendarComponent } from './appointment-calendar/appointment-calendar.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    canActivate: [AuthGuard],\n    children: [\n      { path: '', redirectTo: 'list', pathMatch: 'full' },\n      { path: 'list', component: AppointmentListComponent },\n      { path: 'calendar', component: AppointmentCalendarComponent },\n      { path: 'book', component: AppointmentBookingComponent },\n      { path: 'doctors', component: DoctorSearchComponent },\n      { path: ':id', component: AppointmentDetailsComponent }\n    ]\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class AppointmentsRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,SAAS,QAAQ,2BAA2B;AAErD,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,2BAA2B,QAAQ,qDAAqD;AACjG,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,2BAA2B,QAAQ,qDAAqD;AACjG,SAASC,4BAA4B,QAAQ,uDAAuD;;;AAEpG,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,WAAW,EAAE,CAACR,SAAS,CAAC;EACxBS,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,MAAM;IAAEC,SAAS,EAAE;EAAM,CAAE,EACnD;IAAEJ,IAAI,EAAE,MAAM;IAAEK,SAAS,EAAEX;EAAwB,CAAE,EACrD;IAAEM,IAAI,EAAE,UAAU;IAAEK,SAAS,EAAEP;EAA4B,CAAE,EAC7D;IAAEE,IAAI,EAAE,MAAM;IAAEK,SAAS,EAAEV;EAA2B,CAAE,EACxD;IAAEK,IAAI,EAAE,SAAS;IAAEK,SAAS,EAAET;EAAqB,CAAE,EACrD;IAAEI,IAAI,EAAE,KAAK;IAAEK,SAAS,EAAER;EAA2B,CAAE;CAE1D,CACF;AAMD,OAAM,MAAOS,yBAAyB;;;uBAAzBA,yBAAyB;IAAA;EAAA;;;YAAzBA;IAAyB;EAAA;;;gBAH1Bd,YAAY,CAACe,QAAQ,CAACR,MAAM,CAAC,EAC7BP,YAAY;IAAA;EAAA;;;2EAEXc,yBAAyB;IAAAE,OAAA,GAAAC,EAAA,CAAAjB,YAAA;IAAAkB,OAAA,GAF1BlB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}