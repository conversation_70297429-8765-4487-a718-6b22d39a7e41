{"ast": null, "code": "/**\n * @internal\n */\nexport function augmentWebsocket(webSocket, debug) {\n  webSocket.terminate = function () {\n    const noOp = () => {};\n    // set all callbacks to no op\n    this.onerror = noOp;\n    this.onmessage = noOp;\n    this.onopen = noOp;\n    const ts = new Date();\n    const id = Math.random().toString().substring(2, 8); // A simulated id\n    const origOnClose = this.onclose;\n    // Track delay in actual closure of the socket\n    this.onclose = closeEvent => {\n      const delay = new Date().getTime() - ts.getTime();\n      debug(`Discarded socket (#${id})  closed after ${delay}ms, with code/reason: ${closeEvent.code}/${closeEvent.reason}`);\n    };\n    this.close();\n    origOnClose?.call(webSocket, {\n      code: 4001,\n      reason: `Quick discarding socket (#${id}) without waiting for the shutdown sequence.`,\n      wasClean: false\n    });\n  };\n}", "map": {"version": 3, "names": ["augmentWebsocket", "webSocket", "debug", "terminate", "noOp", "onerror", "onmessage", "onopen", "ts", "Date", "id", "Math", "random", "toString", "substring", "origOnClose", "onclose", "closeEvent", "delay", "getTime", "code", "reason", "close", "call", "<PERSON><PERSON><PERSON>"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/@stomp/stompjs/esm6/augment-websocket.js"], "sourcesContent": ["/**\n * @internal\n */\nexport function augmentWebsocket(webSocket, debug) {\n    webSocket.terminate = function () {\n        const noOp = () => { };\n        // set all callbacks to no op\n        this.onerror = noOp;\n        this.onmessage = noOp;\n        this.onopen = noOp;\n        const ts = new Date();\n        const id = Math.random().toString().substring(2, 8); // A simulated id\n        const origOnClose = this.onclose;\n        // Track delay in actual closure of the socket\n        this.onclose = closeEvent => {\n            const delay = new Date().getTime() - ts.getTime();\n            debug(`Discarded socket (#${id})  closed after ${delay}ms, with code/reason: ${closeEvent.code}/${closeEvent.reason}`);\n        };\n        this.close();\n        origOnClose?.call(webSocket, {\n            code: 4001,\n            reason: `Quick discarding socket (#${id}) without waiting for the shutdown sequence.`,\n            wasClean: false,\n        });\n    };\n}\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,SAASA,gBAAgBA,CAACC,SAAS,EAAEC,KAAK,EAAE;EAC/CD,SAAS,CAACE,SAAS,GAAG,YAAY;IAC9B,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAE,CAAC;IACtB;IACA,IAAI,CAACC,OAAO,GAAGD,IAAI;IACnB,IAAI,CAACE,SAAS,GAAGF,IAAI;IACrB,IAAI,CAACG,MAAM,GAAGH,IAAI;IAClB,MAAMI,EAAE,GAAG,IAAIC,IAAI,CAAC,CAAC;IACrB,MAAMC,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrD,MAAMC,WAAW,GAAG,IAAI,CAACC,OAAO;IAChC;IACA,IAAI,CAACA,OAAO,GAAGC,UAAU,IAAI;MACzB,MAAMC,KAAK,GAAG,IAAIT,IAAI,CAAC,CAAC,CAACU,OAAO,CAAC,CAAC,GAAGX,EAAE,CAACW,OAAO,CAAC,CAAC;MACjDjB,KAAK,CAAE,sBAAqBQ,EAAG,mBAAkBQ,KAAM,yBAAwBD,UAAU,CAACG,IAAK,IAAGH,UAAU,CAACI,MAAO,EAAC,CAAC;IAC1H,CAAC;IACD,IAAI,CAACC,KAAK,CAAC,CAAC;IACZP,WAAW,EAAEQ,IAAI,CAACtB,SAAS,EAAE;MACzBmB,IAAI,EAAE,IAAI;MACVC,MAAM,EAAG,6BAA4BX,EAAG,8CAA6C;MACrFc,QAAQ,EAAE;IACd,CAAC,CAAC;EACN,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}