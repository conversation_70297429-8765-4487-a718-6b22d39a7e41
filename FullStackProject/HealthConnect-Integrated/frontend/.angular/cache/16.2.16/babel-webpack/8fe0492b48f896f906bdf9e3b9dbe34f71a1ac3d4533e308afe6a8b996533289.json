{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  IframeTransport = require('../iframe'),\n  objectUtils = require('../../utils/object');\nmodule.exports = function (transport) {\n  function IframeWrapTransport(transUrl, baseUrl) {\n    IframeTransport.call(this, transport.transportName, transUrl, baseUrl);\n  }\n  inherits(IframeWrapTransport, IframeTransport);\n  IframeWrapTransport.enabled = function (url, info) {\n    if (!global.document) {\n      return false;\n    }\n    var iframeInfo = objectUtils.extend({}, info);\n    iframeInfo.sameOrigin = true;\n    return transport.enabled(iframeInfo) && IframeTransport.enabled();\n  };\n  IframeWrapTransport.transportName = 'iframe-' + transport.transportName;\n  IframeWrapTransport.needBody = true;\n  IframeWrapTransport.roundTrips = IframeTransport.roundTrips + transport.roundTrips - 1; // html, javascript (2) + transport - no CORS (1)\n\n  IframeWrapTransport.facadeTransport = transport;\n  return IframeWrapTransport;\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}