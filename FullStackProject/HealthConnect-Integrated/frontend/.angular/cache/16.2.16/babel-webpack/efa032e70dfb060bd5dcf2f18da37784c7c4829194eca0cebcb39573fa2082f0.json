{"ast": null, "code": "/**\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class HeartbeatInfo {\n  constructor(client) {\n    this.client = client;\n  }\n  get outgoing() {\n    return this.client.heartbeatOutgoing;\n  }\n  set outgoing(value) {\n    this.client.heartbeatOutgoing = value;\n  }\n  get incoming() {\n    return this.client.heartbeatIncoming;\n  }\n  set incoming(value) {\n    this.client.heartbeatIncoming = value;\n  }\n}\n//# sourceMappingURL=heartbeat-info.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}