{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../core/services/appointment.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction DoctorSearchComponent_option_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const spec_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", spec_r5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", spec_r5, \" \");\n  }\n}\nfunction DoctorSearchComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵelement(1, \"i\", 25);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction DoctorSearchComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"span\", 28);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 29);\n    i0.ɵɵtext(5, \"Searching for doctors...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DoctorSearchComponent_div_27_div_1_p_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 40);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const doctor_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", doctor_r7.affiliation, \" \");\n  }\n}\nfunction DoctorSearchComponent_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"div\", 7)(3, \"div\", 33)(4, \"div\", 34);\n    i0.ɵɵelement(5, \"i\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\")(7, \"h5\", 36);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 37);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 38);\n    i0.ɵɵtemplate(12, DoctorSearchComponent_div_27_div_1_p_12_Template, 3, 1, \"p\", 39);\n    i0.ɵɵelementStart(13, \"p\", 40);\n    i0.ɵɵelement(14, \"i\", 41);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\", 42);\n    i0.ɵɵelement(17, \"i\", 43);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function DoctorSearchComponent_div_27_div_1_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const doctor_r7 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.bookAppointment(doctor_r7));\n    });\n    i0.ɵɵelement(20, \"i\", 45);\n    i0.ɵɵtext(21, \" Book Appointment \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const doctor_r7 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(doctor_r7.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(doctor_r7.specialization || \"General Practice\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", doctor_r7.affiliation);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getExperienceText(doctor_r7.yearsOfExperience), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", doctor_r7.email, \" \");\n  }\n}\nfunction DoctorSearchComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, DoctorSearchComponent_div_27_div_1_Template, 22, 5, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.doctors);\n  }\n}\nfunction DoctorSearchComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵelementStart(2, \"h5\", 49);\n    i0.ɵɵtext(3, \"No doctors found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 49);\n    i0.ɵɵtext(5, \"Try adjusting your search criteria or check back later.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class DoctorSearchComponent {\n  constructor(fb, appointmentService, router) {\n    this.fb = fb;\n    this.appointmentService = appointmentService;\n    this.router = router;\n    this.doctors = [];\n    this.specializations = [];\n    this.loading = false;\n    this.error = null;\n    this.initializeForm();\n  }\n  initializeForm() {\n    this.searchForm = this.fb.group({\n      specialization: ['']\n    });\n  }\n  ngOnInit() {\n    this.loadSpecializations();\n    this.loadDoctors();\n  }\n  loadSpecializations() {\n    this.appointmentService.getSpecializations().subscribe({\n      next: specializations => {\n        this.specializations = specializations;\n      },\n      error: error => {\n        console.error('Error loading specializations:', error);\n      }\n    });\n  }\n  loadDoctors() {\n    this.loading = true;\n    this.error = null;\n    const specialization = this.searchForm.get('specialization')?.value;\n    this.appointmentService.getDoctors(specialization || undefined).subscribe({\n      next: doctors => {\n        this.doctors = doctors;\n        this.loading = false;\n      },\n      error: error => {\n        this.error = 'Failed to load doctors. Please try again.';\n        this.loading = false;\n        console.error('Error loading doctors:', error);\n      }\n    });\n  }\n  onSearch() {\n    this.loadDoctors();\n  }\n  onClearSearch() {\n    this.searchForm.reset();\n    this.loadDoctors();\n  }\n  bookAppointment(doctor) {\n    this.router.navigate(['/appointments/book'], {\n      queryParams: {\n        doctorId: doctor.id\n      }\n    });\n  }\n  getExperienceText(years) {\n    if (!years) return 'Experience not specified';\n    return years === 1 ? '1 year experience' : `${years} years experience`;\n  }\n  static {\n    this.ɵfac = function DoctorSearchComponent_Factory(t) {\n      return new (t || DoctorSearchComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AppointmentService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DoctorSearchComponent,\n      selectors: [[\"app-doctor-search\"]],\n      decls: 29,\n      vars: 8,\n      consts: [[1, \"container-fluid\", \"py-4\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\", \"mb-0\"], [1, \"fas\", \"fa-user-md\", \"me-2\"], [1, \"card-body\"], [1, \"mb-4\", 3, \"formGroup\", \"ngSubmit\"], [1, \"col-md-6\"], [\"for\", \"specialization\", 1, \"form-label\"], [\"id\", \"specialization\", \"formControlName\", \"specialization\", 1, \"form-select\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-6\", \"d-flex\", \"align-items-end\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"me-2\", 3, \"disabled\"], [1, \"fas\", \"fa-search\", \"me-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"row\", 4, \"ngIf\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [3, \"value\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-2\"], [1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\", \"text-muted\"], [\"class\", \"col-md-6 col-lg-4 mb-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-6\", \"col-lg-4\", \"mb-4\"], [1, \"card\", \"h-100\", \"doctor-card\"], [1, \"d-flex\", \"align-items-center\", \"mb-3\"], [1, \"avatar-circle\", \"me-3\"], [1, \"fas\", \"fa-user-md\"], [1, \"card-title\", \"mb-1\"], [1, \"text-muted\", \"mb-0\"], [1, \"doctor-info\"], [\"class\", \"mb-2\", 4, \"ngIf\"], [1, \"mb-2\"], [1, \"fas\", \"fa-clock\", \"me-2\", \"text-muted\"], [1, \"mb-3\"], [1, \"fas\", \"fa-envelope\", \"me-2\", \"text-muted\"], [1, \"btn\", \"btn-primary\", \"w-100\", 3, \"click\"], [1, \"fas\", \"fa-calendar-plus\", \"me-2\"], [1, \"fas\", \"fa-hospital\", \"me-2\", \"text-muted\"], [1, \"text-center\", \"py-5\"], [1, \"fas\", \"fa-user-md\", \"fa-3x\", \"text-muted\", \"mb-3\"], [1, \"text-muted\"]],\n      template: function DoctorSearchComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h4\", 5);\n          i0.ɵɵelement(6, \"i\", 6);\n          i0.ɵɵtext(7, \"Find a Doctor \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"form\", 8);\n          i0.ɵɵlistener(\"ngSubmit\", function DoctorSearchComponent_Template_form_ngSubmit_9_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementStart(10, \"div\", 1)(11, \"div\", 9)(12, \"label\", 10);\n          i0.ɵɵtext(13, \"Specialization\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"select\", 11)(15, \"option\", 12);\n          i0.ɵɵtext(16, \"All Specializations\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(17, DoctorSearchComponent_option_17_Template, 2, 2, \"option\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 14)(19, \"button\", 15);\n          i0.ɵɵelement(20, \"i\", 16);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function DoctorSearchComponent_Template_button_click_22_listener() {\n            return ctx.onClearSearch();\n          });\n          i0.ɵɵelement(23, \"i\", 18);\n          i0.ɵɵtext(24, \" Clear \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(25, DoctorSearchComponent_div_25_Template, 3, 1, \"div\", 19);\n          i0.ɵɵtemplate(26, DoctorSearchComponent_div_26_Template, 6, 0, \"div\", 20);\n          i0.ɵɵtemplate(27, DoctorSearchComponent_div_27_Template, 2, 1, \"div\", 21);\n          i0.ɵɵtemplate(28, DoctorSearchComponent_div_28_Template, 6, 0, \"div\", 22);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.specializations);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Searching...\" : \"Search\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.doctors.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.doctors.length === 0);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\".doctor-card[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;\\n  border: 1px solid #e3e6f0;\\n}\\n\\n.doctor-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\\n}\\n\\n.avatar-circle[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-size: 1.5rem;\\n  flex-shrink: 0;\\n}\\n\\n.doctor-info[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n.doctor-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  width: 16px;\\n  text-align: center;\\n}\\n\\n.card-title[_ngcontent-%COMP%] {\\n  color: #5a5c69;\\n  font-weight: 600;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border: none;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\\n  transform: translateY(-1px);\\n}\\n\\n.form-select[_ngcontent-%COMP%]:focus, .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #667eea;\\n  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);\\n}\\n\\n.alert-danger[_ngcontent-%COMP%] {\\n  border-left: 4px solid #e74a3b;\\n}\\n\\n.spinner-border[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "spec_r5", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵelement", "ctx_r1", "error", "doctor_r7", "affiliation", "ɵɵtemplate", "DoctorSearchComponent_div_27_div_1_p_12_Template", "ɵɵlistener", "DoctorSearchComponent_div_27_div_1_Template_button_click_19_listener", "restoredCtx", "ɵɵrestoreView", "_r11", "$implicit", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "bookAppointment", "ɵɵtextInterpolate", "fullName", "specialization", "ctx_r6", "getExperienceText", "yearsOfExperience", "email", "DoctorSearchComponent_div_27_div_1_Template", "ctx_r3", "doctors", "Doctor<PERSON><PERSON>chComponent", "constructor", "fb", "appointmentService", "router", "specializations", "loading", "initializeForm", "searchForm", "group", "ngOnInit", "loadSpecializations", "loadDoctors", "getSpecializations", "subscribe", "next", "console", "get", "value", "getDoctors", "undefined", "onSearch", "onClearSearch", "reset", "doctor", "navigate", "queryParams", "doctorId", "id", "years", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AppointmentService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "DoctorSearchComponent_Template", "rf", "ctx", "DoctorSearchComponent_Template_form_ngSubmit_9_listener", "DoctorSearchComponent_option_17_Template", "DoctorSearchComponent_Template_button_click_22_listener", "DoctorSearchComponent_div_25_Template", "DoctorSearchComponent_div_26_Template", "DoctorSearchComponent_div_27_Template", "DoctorSearchComponent_div_28_Template", "length"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/appointments/doctor-search/doctor-search.component.ts", "/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/appointments/doctor-search/doctor-search.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { FormBuilder, FormGroup } from '@angular/forms';\nimport { AppointmentService } from '../../core/services/appointment.service';\nimport { Doctor } from '../../core/models/appointment.model';\n\n@Component({\n  selector: 'app-doctor-search',\n  templateUrl: './doctor-search.component.html',\n  styleUrls: ['./doctor-search.component.scss']\n})\nexport class DoctorSearchComponent implements OnInit {\n  searchForm!: FormGroup;\n  doctors: Doctor[] = [];\n  specializations: string[] = [];\n  loading = false;\n  error: string | null = null;\n\n  constructor(\n    private fb: FormBuilder,\n    private appointmentService: AppointmentService,\n    private router: Router\n  ) {\n    this.initializeForm();\n  }\n\n  private initializeForm(): void {\n    this.searchForm = this.fb.group({\n      specialization: ['']\n    });\n  }\n\n  ngOnInit(): void {\n    this.loadSpecializations();\n    this.loadDoctors();\n  }\n\n  loadSpecializations(): void {\n    this.appointmentService.getSpecializations().subscribe({\n      next: (specializations) => {\n        this.specializations = specializations;\n      },\n      error: (error) => {\n        console.error('Error loading specializations:', error);\n      }\n    });\n  }\n\n  loadDoctors(): void {\n    this.loading = true;\n    this.error = null;\n    \n    const specialization = this.searchForm.get('specialization')?.value;\n    \n    this.appointmentService.getDoctors(specialization || undefined).subscribe({\n      next: (doctors) => {\n        this.doctors = doctors;\n        this.loading = false;\n      },\n      error: (error) => {\n        this.error = 'Failed to load doctors. Please try again.';\n        this.loading = false;\n        console.error('Error loading doctors:', error);\n      }\n    });\n  }\n\n  onSearch(): void {\n    this.loadDoctors();\n  }\n\n  onClearSearch(): void {\n    this.searchForm.reset();\n    this.loadDoctors();\n  }\n\n  bookAppointment(doctor: Doctor): void {\n    this.router.navigate(['/appointments/book'], { \n      queryParams: { doctorId: doctor.id } \n    });\n  }\n\n  getExperienceText(years?: number): string {\n    if (!years) return 'Experience not specified';\n    return years === 1 ? '1 year experience' : `${years} years experience`;\n  }\n}\n", "<div class=\"container-fluid py-4\">\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"card\">\n        <div class=\"card-header\">\n          <h4 class=\"card-title mb-0\">\n            <i class=\"fas fa-user-md me-2\"></i>Find a Doctor\n          </h4>\n        </div>\n        <div class=\"card-body\">\n          <!-- Search Form -->\n          <form [formGroup]=\"searchForm\" (ngSubmit)=\"onSearch()\" class=\"mb-4\">\n            <div class=\"row\">\n              <div class=\"col-md-6\">\n                <label for=\"specialization\" class=\"form-label\">Specialization</label>\n                <select \n                  id=\"specialization\" \n                  class=\"form-select\" \n                  formControlName=\"specialization\">\n                  <option value=\"\">All Specializations</option>\n                  <option *ngFor=\"let spec of specializations\" [value]=\"spec\">\n                    {{ spec }}\n                  </option>\n                </select>\n              </div>\n              <div class=\"col-md-6 d-flex align-items-end\">\n                <button \n                  type=\"submit\" \n                  class=\"btn btn-primary me-2\"\n                  [disabled]=\"loading\">\n                  <i class=\"fas fa-search me-1\"></i>\n                  {{ loading ? 'Searching...' : 'Search' }}\n                </button>\n                <button \n                  type=\"button\" \n                  class=\"btn btn-outline-secondary\"\n                  (click)=\"onClearSearch()\">\n                  <i class=\"fas fa-times me-1\"></i>\n                  Clear\n                </button>\n              </div>\n            </div>\n          </form>\n\n          <!-- Error Message -->\n          <div *ngIf=\"error\" class=\"alert alert-danger\" role=\"alert\">\n            <i class=\"fas fa-exclamation-triangle me-2\"></i>\n            {{ error }}\n          </div>\n\n          <!-- Loading Spinner -->\n          <div *ngIf=\"loading\" class=\"text-center py-4\">\n            <div class=\"spinner-border text-primary\" role=\"status\">\n              <span class=\"visually-hidden\">Loading...</span>\n            </div>\n            <p class=\"mt-2 text-muted\">Searching for doctors...</p>\n          </div>\n\n          <!-- Doctors List -->\n          <div *ngIf=\"!loading && doctors.length > 0\" class=\"row\">\n            <div class=\"col-md-6 col-lg-4 mb-4\" *ngFor=\"let doctor of doctors\">\n              <div class=\"card h-100 doctor-card\">\n                <div class=\"card-body\">\n                  <div class=\"d-flex align-items-center mb-3\">\n                    <div class=\"avatar-circle me-3\">\n                      <i class=\"fas fa-user-md\"></i>\n                    </div>\n                    <div>\n                      <h5 class=\"card-title mb-1\">{{ doctor.fullName }}</h5>\n                      <p class=\"text-muted mb-0\">{{ doctor.specialization || 'General Practice' }}</p>\n                    </div>\n                  </div>\n                  \n                  <div class=\"doctor-info\">\n                    <p class=\"mb-2\" *ngIf=\"doctor.affiliation\">\n                      <i class=\"fas fa-hospital me-2 text-muted\"></i>\n                      {{ doctor.affiliation }}\n                    </p>\n                    <p class=\"mb-2\">\n                      <i class=\"fas fa-clock me-2 text-muted\"></i>\n                      {{ getExperienceText(doctor.yearsOfExperience) }}\n                    </p>\n                    <p class=\"mb-3\">\n                      <i class=\"fas fa-envelope me-2 text-muted\"></i>\n                      {{ doctor.email }}\n                    </p>\n                  </div>\n                  \n                  <button \n                    class=\"btn btn-primary w-100\"\n                    (click)=\"bookAppointment(doctor)\">\n                    <i class=\"fas fa-calendar-plus me-2\"></i>\n                    Book Appointment\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- No Doctors Found -->\n          <div *ngIf=\"!loading && doctors.length === 0\" class=\"text-center py-5\">\n            <i class=\"fas fa-user-md fa-3x text-muted mb-3\"></i>\n            <h5 class=\"text-muted\">No doctors found</h5>\n            <p class=\"text-muted\">Try adjusting your search criteria or check back later.</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;ICoBkBA,EAAA,CAAAC,cAAA,iBAA4D;IAC1DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFoCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACzDL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,MACF;;;;;IAuBRL,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAQ,SAAA,YAAgD;IAChDR,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,MAAA,CAAAC,KAAA,MACF;;;;;IAGAV,EAAA,CAAAC,cAAA,cAA8C;IAEZD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEjDH,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAmB/CH,EAAA,CAAAC,cAAA,YAA2C;IACzCD,EAAA,CAAAQ,SAAA,YAA+C;IAC/CR,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAI,SAAA,CAAAC,WAAA,MACF;;;;;;IAjBRZ,EAAA,CAAAC,cAAA,cAAmE;IAKzDD,EAAA,CAAAQ,SAAA,YAA8B;IAChCR,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,UAAK;IACyBD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtDH,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAIpFH,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAa,UAAA,KAAAC,gDAAA,gBAGI;IACJd,EAAA,CAAAC,cAAA,aAAgB;IACdD,EAAA,CAAAQ,SAAA,aAA4C;IAC5CR,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,aAAgB;IACdD,EAAA,CAAAQ,SAAA,aAA+C;IAC/CR,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGNH,EAAA,CAAAC,cAAA,kBAEoC;IAAlCD,EAAA,CAAAe,UAAA,mBAAAC,qEAAA;MAAA,MAAAC,WAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAR,SAAA,GAAAM,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAuB,WAAA,CAAAF,OAAA,CAAAG,eAAA,CAAAb,SAAA,CAAuB;IAAA,EAAC;IACjCX,EAAA,CAAAQ,SAAA,aAAyC;IACzCR,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAzBuBH,EAAA,CAAAM,SAAA,GAAqB;IAArBN,EAAA,CAAAyB,iBAAA,CAAAd,SAAA,CAAAe,QAAA,CAAqB;IACtB1B,EAAA,CAAAM,SAAA,GAAiD;IAAjDN,EAAA,CAAAyB,iBAAA,CAAAd,SAAA,CAAAgB,cAAA,uBAAiD;IAK7D3B,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,SAAAO,SAAA,CAAAC,WAAA,CAAwB;IAMvCZ,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAqB,MAAA,CAAAC,iBAAA,CAAAlB,SAAA,CAAAmB,iBAAA,OACF;IAGE9B,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAI,SAAA,CAAAoB,KAAA,MACF;;;;;IA1BV/B,EAAA,CAAAC,cAAA,aAAwD;IACtDD,EAAA,CAAAa,UAAA,IAAAmB,2CAAA,mBAoCM;IACRhC,EAAA,CAAAG,YAAA,EAAM;;;;IArCmDH,EAAA,CAAAM,SAAA,GAAU;IAAVN,EAAA,CAAAI,UAAA,YAAA6B,MAAA,CAAAC,OAAA,CAAU;;;;;IAwCnElC,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAQ,SAAA,YAAoD;IACpDR,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,8DAAuD;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;AD5F7F,OAAM,MAAOgC,qBAAqB;EAOhCC,YACUC,EAAe,EACfC,kBAAsC,EACtCC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;IARhB,KAAAL,OAAO,GAAa,EAAE;IACtB,KAAAM,eAAe,GAAa,EAAE;IAC9B,KAAAC,OAAO,GAAG,KAAK;IACf,KAAA/B,KAAK,GAAkB,IAAI;IAOzB,IAAI,CAACgC,cAAc,EAAE;EACvB;EAEQA,cAAcA,CAAA;IACpB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACN,EAAE,CAACO,KAAK,CAAC;MAC9BjB,cAAc,EAAE,CAAC,EAAE;KACpB,CAAC;EACJ;EAEAkB,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAD,mBAAmBA,CAAA;IACjB,IAAI,CAACR,kBAAkB,CAACU,kBAAkB,EAAE,CAACC,SAAS,CAAC;MACrDC,IAAI,EAAGV,eAAe,IAAI;QACxB,IAAI,CAACA,eAAe,GAAGA,eAAe;MACxC,CAAC;MACD9B,KAAK,EAAGA,KAAK,IAAI;QACfyC,OAAO,CAACzC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD;KACD,CAAC;EACJ;EAEAqC,WAAWA,CAAA;IACT,IAAI,CAACN,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC/B,KAAK,GAAG,IAAI;IAEjB,MAAMiB,cAAc,GAAG,IAAI,CAACgB,UAAU,CAACS,GAAG,CAAC,gBAAgB,CAAC,EAAEC,KAAK;IAEnE,IAAI,CAACf,kBAAkB,CAACgB,UAAU,CAAC3B,cAAc,IAAI4B,SAAS,CAAC,CAACN,SAAS,CAAC;MACxEC,IAAI,EAAGhB,OAAO,IAAI;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACO,OAAO,GAAG,KAAK;MACtB,CAAC;MACD/B,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,2CAA2C;QACxD,IAAI,CAAC+B,OAAO,GAAG,KAAK;QACpBU,OAAO,CAACzC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEA8C,QAAQA,CAAA;IACN,IAAI,CAACT,WAAW,EAAE;EACpB;EAEAU,aAAaA,CAAA;IACX,IAAI,CAACd,UAAU,CAACe,KAAK,EAAE;IACvB,IAAI,CAACX,WAAW,EAAE;EACpB;EAEAvB,eAAeA,CAACmC,MAAc;IAC5B,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE;MAC3CC,WAAW,EAAE;QAAEC,QAAQ,EAAEH,MAAM,CAACI;MAAE;KACnC,CAAC;EACJ;EAEAlC,iBAAiBA,CAACmC,KAAc;IAC9B,IAAI,CAACA,KAAK,EAAE,OAAO,0BAA0B;IAC7C,OAAOA,KAAK,KAAK,CAAC,GAAG,mBAAmB,GAAG,GAAGA,KAAK,mBAAmB;EACxE;;;uBA1EW7B,qBAAqB,EAAAnC,EAAA,CAAAiE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnE,EAAA,CAAAiE,iBAAA,CAAAG,EAAA,CAAAC,kBAAA,GAAArE,EAAA,CAAAiE,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAArBpC,qBAAqB;MAAAqC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXlC9E,EAAA,CAAAC,cAAA,aAAkC;UAMtBD,EAAA,CAAAQ,SAAA,WAAmC;UAAAR,EAAA,CAAAE,MAAA,qBACrC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEPH,EAAA,CAAAC,cAAA,aAAuB;UAEUD,EAAA,CAAAe,UAAA,sBAAAiE,wDAAA;YAAA,OAAYD,GAAA,CAAAvB,QAAA,EAAU;UAAA,EAAC;UACpDxD,EAAA,CAAAC,cAAA,cAAiB;UAEkCD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrEH,EAAA,CAAAC,cAAA,kBAGmC;UAChBD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC7CH,EAAA,CAAAa,UAAA,KAAAoE,wCAAA,qBAES;UACXjF,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAC,cAAA,eAA6C;UAKzCD,EAAA,CAAAQ,SAAA,aAAkC;UAClCR,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAG4B;UAA1BD,EAAA,CAAAe,UAAA,mBAAAmE,wDAAA;YAAA,OAASH,GAAA,CAAAtB,aAAA,EAAe;UAAA,EAAC;UACzBzD,EAAA,CAAAQ,SAAA,aAAiC;UACjCR,EAAA,CAAAE,MAAA,eACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAa,UAAA,KAAAsE,qCAAA,kBAGM;UAGNnF,EAAA,CAAAa,UAAA,KAAAuE,qCAAA,kBAKM;UAGNpF,EAAA,CAAAa,UAAA,KAAAwE,qCAAA,kBAsCM;UAGNrF,EAAA,CAAAa,UAAA,KAAAyE,qCAAA,kBAIM;UACRtF,EAAA,CAAAG,YAAA,EAAM;;;UA9FEH,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,cAAA2E,GAAA,CAAApC,UAAA,CAAwB;UASG3C,EAAA,CAAAM,SAAA,GAAkB;UAAlBN,EAAA,CAAAI,UAAA,YAAA2E,GAAA,CAAAvC,eAAA,CAAkB;UAS3CxC,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAI,UAAA,aAAA2E,GAAA,CAAAtC,OAAA,CAAoB;UAEpBzC,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAO,kBAAA,MAAAwE,GAAA,CAAAtC,OAAA,kCACF;UAaAzC,EAAA,CAAAM,SAAA,GAAW;UAAXN,EAAA,CAAAI,UAAA,SAAA2E,GAAA,CAAArE,KAAA,CAAW;UAMXV,EAAA,CAAAM,SAAA,GAAa;UAAbN,EAAA,CAAAI,UAAA,SAAA2E,GAAA,CAAAtC,OAAA,CAAa;UAQbzC,EAAA,CAAAM,SAAA,GAAoC;UAApCN,EAAA,CAAAI,UAAA,UAAA2E,GAAA,CAAAtC,OAAA,IAAAsC,GAAA,CAAA7C,OAAA,CAAAqD,MAAA,KAAoC;UAyCpCvF,EAAA,CAAAM,SAAA,GAAsC;UAAtCN,EAAA,CAAAI,UAAA,UAAA2E,GAAA,CAAAtC,OAAA,IAAAsC,GAAA,CAAA7C,OAAA,CAAAqD,MAAA,OAAsC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}