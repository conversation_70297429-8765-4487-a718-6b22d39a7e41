{"ast": null, "code": "import _asyncToGenerator from \"/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let NotificationService = /*#__PURE__*/(() => {\n  class NotificationService {\n    constructor() {\n      this.notifications$ = new BehaviorSubject([]);\n      this.unreadCount$ = new BehaviorSubject(0);\n      this.loadNotifications();\n    }\n    getNotifications() {\n      return this.notifications$.asObservable();\n    }\n    getUnreadCount() {\n      return this.unreadCount$.asObservable();\n    }\n    addNotification(notification) {\n      const newNotification = {\n        ...notification,\n        id: this.generateId(),\n        timestamp: new Date(),\n        read: false\n      };\n      const currentNotifications = this.notifications$.value;\n      const updatedNotifications = [newNotification, ...currentNotifications];\n      this.notifications$.next(updatedNotifications);\n      this.updateUnreadCount();\n      this.saveNotifications(updatedNotifications);\n      // Show browser notification if permission granted\n      this.showBrowserNotification(newNotification);\n    }\n    markAsRead(notificationId) {\n      const notifications = this.notifications$.value.map(notification => notification.id === notificationId ? {\n        ...notification,\n        read: true\n      } : notification);\n      this.notifications$.next(notifications);\n      this.updateUnreadCount();\n      this.saveNotifications(notifications);\n    }\n    markAllAsRead() {\n      const notifications = this.notifications$.value.map(notification => ({\n        ...notification,\n        read: true\n      }));\n      this.notifications$.next(notifications);\n      this.updateUnreadCount();\n      this.saveNotifications(notifications);\n    }\n    removeNotification(notificationId) {\n      const notifications = this.notifications$.value.filter(notification => notification.id !== notificationId);\n      this.notifications$.next(notifications);\n      this.updateUnreadCount();\n      this.saveNotifications(notifications);\n    }\n    clearAll() {\n      this.notifications$.next([]);\n      this.unreadCount$.next(0);\n      this.saveNotifications([]);\n    }\n    // Specific notification types\n    addMessageNotification(fromUser, message, chatId) {\n      this.addNotification({\n        type: 'message',\n        title: `New message from ${fromUser.fullName}`,\n        message: message.length > 100 ? message.substring(0, 100) + '...' : message,\n        priority: 'medium',\n        fromUser: {\n          id: fromUser.id,\n          name: fromUser.fullName,\n          avatar: fromUser.avatar\n        },\n        actionUrl: `/chat?chatId=${chatId}`,\n        actionText: 'Reply'\n      });\n    }\n    addAppointmentNotification(type, appointment) {\n      let title = '';\n      let message = '';\n      let priority = 'medium';\n      switch (type) {\n        case 'booked':\n          title = 'Appointment Booked';\n          message = `Your appointment with ${appointment.doctor.fullName} is scheduled for ${appointment.date}`;\n          break;\n        case 'reminder':\n          title = 'Appointment Reminder';\n          message = `Your appointment with ${appointment.doctor.fullName} is in 1 hour`;\n          priority = 'high';\n          break;\n        case 'cancelled':\n          title = 'Appointment Cancelled';\n          message = `Your appointment with ${appointment.doctor.fullName} has been cancelled`;\n          priority = 'high';\n          break;\n      }\n      this.addNotification({\n        type: 'appointment',\n        title,\n        message,\n        priority,\n        actionUrl: `/appointments/${appointment.id}`,\n        actionText: 'View Details'\n      });\n    }\n    addUrgentNotification(title, message, actionUrl) {\n      this.addNotification({\n        type: 'urgent',\n        title,\n        message,\n        priority: 'urgent',\n        actionUrl,\n        actionText: actionUrl ? 'View' : undefined\n      });\n    }\n    loadNotifications() {\n      const stored = localStorage.getItem('healthconnect_notifications');\n      if (stored) {\n        try {\n          const notifications = JSON.parse(stored).map(n => ({\n            ...n,\n            timestamp: new Date(n.timestamp)\n          }));\n          this.notifications$.next(notifications);\n          this.updateUnreadCount();\n        } catch (error) {\n          console.error('Error loading notifications:', error);\n        }\n      }\n    }\n    saveNotifications(notifications) {\n      try {\n        localStorage.setItem('healthconnect_notifications', JSON.stringify(notifications));\n      } catch (error) {\n        console.error('Error saving notifications:', error);\n      }\n    }\n    updateUnreadCount() {\n      const unreadCount = this.notifications$.value.filter(n => !n.read).length;\n      this.unreadCount$.next(unreadCount);\n    }\n    generateId() {\n      return Date.now().toString(36) + Math.random().toString(36).substr(2);\n    }\n    showBrowserNotification(notification) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        if (!('Notification' in window)) {\n          return;\n        }\n        if (Notification.permission === 'granted') {\n          new Notification(notification.title, {\n            body: notification.message,\n            icon: '/assets/icons/icon-192x192.png',\n            badge: '/assets/icons/icon-72x72.png',\n            tag: notification.id\n          });\n        } else if (Notification.permission !== 'denied') {\n          const permission = yield Notification.requestPermission();\n          if (permission === 'granted') {\n            _this.showBrowserNotification(notification);\n          }\n        }\n      })();\n    }\n    // Request notification permission\n    requestNotificationPermission() {\n      return _asyncToGenerator(function* () {\n        if (!('Notification' in window)) {\n          return false;\n        }\n        if (Notification.permission === 'granted') {\n          return true;\n        }\n        if (Notification.permission !== 'denied') {\n          const permission = yield Notification.requestPermission();\n          return permission === 'granted';\n        }\n        return false;\n      })();\n    }\n    static {\n      this.ɵfac = function NotificationService_Factory(t) {\n        return new (t || NotificationService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: NotificationService,\n        factory: NotificationService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return NotificationService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}