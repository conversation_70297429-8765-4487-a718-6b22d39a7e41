{"ast": null, "code": "'use strict';\n\nmodule.exports = [\n// streaming transports\nrequire('./transport/websocket'), require('./transport/xhr-streaming'), require('./transport/xdr-streaming'), require('./transport/eventsource'), require('./transport/lib/iframe-wrap')(require('./transport/eventsource'))\n\n// polling transports\n, require('./transport/htmlfile'), require('./transport/lib/iframe-wrap')(require('./transport/htmlfile')), require('./transport/xhr-polling'), require('./transport/xdr-polling'), require('./transport/lib/iframe-wrap')(require('./transport/xhr-polling')), require('./transport/jsonp-polling')];", "map": {"version": 3, "names": ["module", "exports", "require"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/sockjs-client/lib/transport-list.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = [\n  // streaming transports\n  require('./transport/websocket')\n, require('./transport/xhr-streaming')\n, require('./transport/xdr-streaming')\n, require('./transport/eventsource')\n, require('./transport/lib/iframe-wrap')(require('./transport/eventsource'))\n\n  // polling transports\n, require('./transport/htmlfile')\n, require('./transport/lib/iframe-wrap')(require('./transport/htmlfile'))\n, require('./transport/xhr-polling')\n, require('./transport/xdr-polling')\n, require('./transport/lib/iframe-wrap')(require('./transport/xhr-polling'))\n, require('./transport/jsonp-polling')\n];\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG;AACf;AACAC,OAAO,CAAC,uBAAuB,CAAC,EAChCA,OAAO,CAAC,2BAA2B,CAAC,EACpCA,OAAO,CAAC,2BAA2B,CAAC,EACpCA,OAAO,CAAC,yBAAyB,CAAC,EAClCA,OAAO,CAAC,6BAA6B,CAAC,CAACA,OAAO,CAAC,yBAAyB,CAAC;;AAEzE;AAAA,EACAA,OAAO,CAAC,sBAAsB,CAAC,EAC/BA,OAAO,CAAC,6BAA6B,CAAC,CAACA,OAAO,CAAC,sBAAsB,CAAC,CAAC,EACvEA,OAAO,CAAC,yBAAyB,CAAC,EAClCA,OAAO,CAAC,yBAAyB,CAAC,EAClCA,OAAO,CAAC,6BAA6B,CAAC,CAACA,OAAO,CAAC,yBAAyB,CAAC,CAAC,EAC1EA,OAAO,CAAC,2BAA2B,CAAC,CACrC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}