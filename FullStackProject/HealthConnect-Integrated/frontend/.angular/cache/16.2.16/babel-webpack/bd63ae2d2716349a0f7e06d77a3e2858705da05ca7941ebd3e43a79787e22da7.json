{"ast": null, "code": "'use strict';\n\n// The simplest and most robust transport, using the well-know cross\n// domain hack - JSONP. This transport is quite inefficient - one\n// message could use up to one http request. But at least it works almost\n// everywhere.\n// Known limitations:\n//   o you will get a spinning cursor\n//   o for Konqueror a dumb timer is needed to detect errors\nvar inherits = require('inherits'),\n  SenderReceiver = require('./lib/sender-receiver'),\n  JsonpReceiver = require('./receiver/jsonp'),\n  jsonpSender = require('./sender/jsonp');\nfunction JsonPTransport(transUrl) {\n  if (!JsonPTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n  SenderReceiver.call(this, transUrl, '/jsonp', jsonpSender, JsonpReceiver);\n}\ninherits(JsonPTransport, SenderReceiver);\nJsonPTransport.enabled = function () {\n  return !!global.document;\n};\nJsonPTransport.transportName = 'jsonp-polling';\nJsonPTransport.roundTrips = 1;\nJsonPTransport.needBody = true;\nmodule.exports = JsonPTransport;", "map": {"version": 3, "names": ["inherits", "require", "SenderReceiver", "JsonpReceiver", "jsonpSender", "JsonPTransport", "transUrl", "enabled", "Error", "call", "global", "document", "transportName", "roundTrips", "needBody", "module", "exports"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/sockjs-client/lib/transport/jsonp-polling.js"], "sourcesContent": ["'use strict';\n\n// The simplest and most robust transport, using the well-know cross\n// domain hack - JSONP. This transport is quite inefficient - one\n// message could use up to one http request. But at least it works almost\n// everywhere.\n// Known limitations:\n//   o you will get a spinning cursor\n//   o for Konqueror a dumb timer is needed to detect errors\n\nvar inherits = require('inherits')\n  , SenderReceiver = require('./lib/sender-receiver')\n  , JsonpReceiver = require('./receiver/jsonp')\n  , jsonpSender = require('./sender/jsonp')\n  ;\n\nfunction JsonPTransport(transUrl) {\n  if (!JsonPTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n  SenderReceiver.call(this, transUrl, '/jsonp', jsonpSender, JsonpReceiver);\n}\n\ninherits(JsonPTransport, SenderReceiver);\n\nJsonPTransport.enabled = function() {\n  return !!global.document;\n};\n\nJsonPTransport.transportName = 'jsonp-polling';\nJsonPTransport.roundTrips = 1;\nJsonPTransport.needBody = true;\n\nmodule.exports = JsonPTransport;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,cAAc,GAAGD,OAAO,CAAC,uBAAuB,CAAC;EACjDE,aAAa,GAAGF,OAAO,CAAC,kBAAkB,CAAC;EAC3CG,WAAW,GAAGH,OAAO,CAAC,gBAAgB,CAAC;AAG3C,SAASI,cAAcA,CAACC,QAAQ,EAAE;EAChC,IAAI,CAACD,cAAc,CAACE,OAAO,CAAC,CAAC,EAAE;IAC7B,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC;EACpD;EACAN,cAAc,CAACO,IAAI,CAAC,IAAI,EAAEH,QAAQ,EAAE,QAAQ,EAAEF,WAAW,EAAED,aAAa,CAAC;AAC3E;AAEAH,QAAQ,CAACK,cAAc,EAAEH,cAAc,CAAC;AAExCG,cAAc,CAACE,OAAO,GAAG,YAAW;EAClC,OAAO,CAAC,CAACG,MAAM,CAACC,QAAQ;AAC1B,CAAC;AAEDN,cAAc,CAACO,aAAa,GAAG,eAAe;AAC9CP,cAAc,CAACQ,UAAU,GAAG,CAAC;AAC7BR,cAAc,CAACS,QAAQ,GAAG,IAAI;AAE9BC,MAAM,CAACC,OAAO,GAAGX,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}