{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  EventTarget = require('./eventtarget');\nfunction EventEmitter() {\n  EventTarget.call(this);\n}\ninherits(EventEmitter, EventTarget);\nEventEmitter.prototype.removeAllListeners = function (type) {\n  if (type) {\n    delete this._listeners[type];\n  } else {\n    this._listeners = {};\n  }\n};\nEventEmitter.prototype.once = function (type, listener) {\n  var self = this,\n    fired = false;\n  function g() {\n    self.removeListener(type, g);\n    if (!fired) {\n      fired = true;\n      listener.apply(this, arguments);\n    }\n  }\n  this.on(type, g);\n};\nEventEmitter.prototype.emit = function () {\n  var type = arguments[0];\n  var listeners = this._listeners[type];\n  if (!listeners) {\n    return;\n  }\n  // equivalent of Array.prototype.slice.call(arguments, 1);\n  var l = arguments.length;\n  var args = new Array(l - 1);\n  for (var ai = 1; ai < l; ai++) {\n    args[ai - 1] = arguments[ai];\n  }\n  for (var i = 0; i < listeners.length; i++) {\n    listeners[i].apply(this, args);\n  }\n};\nEventEmitter.prototype.on = EventEmitter.prototype.addListener = EventTarget.prototype.addEventListener;\nEventEmitter.prototype.removeListener = EventTarget.prototype.removeEventListener;\nmodule.exports.EventEmitter = EventEmitter;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}