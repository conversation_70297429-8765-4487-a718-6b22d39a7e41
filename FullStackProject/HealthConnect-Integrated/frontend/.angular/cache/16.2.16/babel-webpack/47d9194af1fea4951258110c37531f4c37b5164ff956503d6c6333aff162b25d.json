{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction LoginComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵelement(1, \"i\", 24);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.errorMessage, \" \");\n  }\n}\nfunction LoginComponent_form_30_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getFieldError(\"email\"), \" \");\n  }\n}\nfunction LoginComponent_form_30_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getFieldError(\"password\"), \" \");\n  }\n}\nfunction LoginComponent_form_30_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 43);\n  }\n}\nfunction LoginComponent_form_30_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Signing In...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_30_span_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 25);\n    i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_form_30_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 26)(2, \"label\", 27);\n    i0.ɵɵtext(3, \"Email Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 28)(5, \"span\", 29);\n    i0.ɵɵelement(6, \"i\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, LoginComponent_form_30_div_8_Template, 2, 1, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 26)(10, \"label\", 33);\n    i0.ɵɵtext(11, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 28)(13, \"span\", 29);\n    i0.ɵɵelement(14, \"i\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, LoginComponent_form_30_div_16_Template, 2, 1, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 36);\n    i0.ɵɵelement(18, \"input\", 37);\n    i0.ɵɵelementStart(19, \"label\", 38);\n    i0.ɵɵtext(20, \" Remember me \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"button\", 39);\n    i0.ɵɵtemplate(22, LoginComponent_form_30_span_22_Template, 1, 0, \"span\", 40);\n    i0.ɵɵtemplate(23, LoginComponent_form_30_span_23_Template, 2, 0, \"span\", 41);\n    i0.ɵɵtemplate(24, LoginComponent_form_30_span_24_Template, 2, 0, \"span\", 41);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.loginForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"email\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"email\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"password\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"password\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading);\n  }\n}\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    constructor(formBuilder, authService, router, route) {\n      this.formBuilder = formBuilder;\n      this.authService = authService;\n      this.router = router;\n      this.route = route;\n      this.isLoading = false;\n      this.errorMessage = '';\n      this.returnUrl = '';\n    }\n    ngOnInit() {\n      // Get return url from route parameters or default to '/'\n      this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';\n      // Redirect if already logged in\n      if (this.authService.isAuthenticated()) {\n        this.redirectToDashboard();\n        return;\n      }\n      this.initializeForm();\n    }\n    initializeForm() {\n      this.loginForm = this.formBuilder.group({\n        email: ['', [Validators.required, Validators.email]],\n        password: ['', [Validators.required, Validators.minLength(6)]],\n        rememberMe: [false]\n      });\n    }\n    onSubmit() {\n      if (this.loginForm.invalid) {\n        this.markFormGroupTouched();\n        return;\n      }\n      this.isLoading = true;\n      this.errorMessage = '';\n      const loginRequest = this.loginForm.value;\n      this.authService.login(loginRequest).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.redirectToDashboard();\n        },\n        error: error => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Login failed. Please try again.';\n        }\n      });\n    }\n    redirectToDashboard() {\n      const user = this.authService.getCurrentUser();\n      if (user) {\n        if (user.role === 'DOCTOR') {\n          this.router.navigate(['/doctor/dashboard']);\n        } else if (user.role === 'PATIENT') {\n          this.router.navigate(['/patient/dashboard']);\n        } else {\n          this.router.navigate(['/']);\n        }\n      }\n    }\n    markFormGroupTouched() {\n      Object.keys(this.loginForm.controls).forEach(key => {\n        const control = this.loginForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    // Getter methods for form validation\n    get email() {\n      return this.loginForm.get('email');\n    }\n    get password() {\n      return this.loginForm.get('password');\n    }\n    isFieldInvalid(fieldName) {\n      const field = this.loginForm.get(fieldName);\n      return !!(field && field.invalid && (field.dirty || field.touched));\n    }\n    getFieldError(fieldName) {\n      const field = this.loginForm.get(fieldName);\n      if (field?.errors) {\n        if (field.errors['required']) {\n          return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;\n        }\n        if (field.errors['email']) {\n          return 'Please enter a valid email address';\n        }\n        if (field.errors['minlength']) {\n          return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${field.errors['minlength'].requiredLength} characters`;\n        }\n      }\n      return '';\n    }\n    static {\n      this.ɵfac = function LoginComponent_Factory(t) {\n        return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LoginComponent,\n        selectors: [[\"app-login\"]],\n        decls: 43,\n        vars: 2,\n        consts: [[1, \"container-fluid\", \"vh-100\"], [1, \"row\", \"h-100\"], [1, \"col-md-6\", \"d-none\", \"d-md-flex\", \"align-items-center\", \"justify-content-center\", \"bg-primary\", \"text-white\"], [1, \"text-center\"], [1, \"bi\", \"bi-heart-pulse\", \"display-1\", \"mb-4\"], [1, \"display-4\", \"fw-bold\", \"mb-3\"], [1, \"lead\"], [1, \"mt-4\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\", \"mb-2\"], [1, \"bi\", \"bi-check-circle\", \"me-2\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\"], [1, \"col-md-6\", \"d-flex\", \"align-items-center\", \"justify-content-center\"], [1, \"w-100\", 2, \"max-width\", \"400px\"], [1, \"text-center\", \"mb-4\"], [1, \"fw-bold\", \"text-dark\"], [1, \"text-muted\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [\"novalidate\", \"\", 3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [1, \"text-center\", \"mt-4\"], [\"routerLink\", \"/auth/register\", 1, \"text-primary\", \"text-decoration-none\", \"fw-semibold\"], [1, \"mt-4\", \"p-3\", \"bg-light\", \"rounded\"], [1, \"fw-semibold\", \"mb-2\"], [1, \"text-muted\", \"d-block\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"bi\", \"bi-exclamation-triangle\", \"me-2\"], [\"novalidate\", \"\", 3, \"formGroup\", \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"email\", 1, \"form-label\"], [1, \"input-group\"], [1, \"input-group-text\"], [1, \"bi\", \"bi-envelope\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", 1, \"form-control\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"for\", \"password\", 1, \"form-label\"], [1, \"bi\", \"bi-lock\"], [\"type\", \"password\", \"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", 1, \"form-control\"], [1, \"mb-3\", \"form-check\"], [\"type\", \"checkbox\", \"id\", \"rememberMe\", \"formControlName\", \"rememberMe\", 1, \"form-check-input\"], [\"for\", \"rememberMe\", 1, \"form-check-label\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"w-100\", \"py-2\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"invalid-feedback\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n        template: function LoginComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelement(4, \"i\", 4);\n            i0.ɵɵelementStart(5, \"h1\", 5);\n            i0.ɵɵtext(6, \"HealthConnect\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"p\", 6);\n            i0.ɵɵtext(8, \"Your trusted medical platform for seamless healthcare management\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8);\n            i0.ɵɵelement(11, \"i\", 9);\n            i0.ɵɵelementStart(12, \"span\");\n            i0.ɵɵtext(13, \"Secure Patient-Doctor Communication\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"div\", 8);\n            i0.ɵɵelement(15, \"i\", 9);\n            i0.ɵɵelementStart(16, \"span\");\n            i0.ɵɵtext(17, \"Easy Appointment Scheduling\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(18, \"div\", 10);\n            i0.ɵɵelement(19, \"i\", 9);\n            i0.ɵɵelementStart(20, \"span\");\n            i0.ɵɵtext(21, \"AI-Powered Health Assistance\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(22, \"div\", 11)(23, \"div\", 12)(24, \"div\", 13)(25, \"h2\", 14);\n            i0.ɵɵtext(26, \"Welcome Back\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"p\", 15);\n            i0.ɵɵtext(28, \"Sign in to your HealthConnect account\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(29, LoginComponent_div_29_Template, 3, 1, \"div\", 16);\n            i0.ɵɵtemplate(30, LoginComponent_form_30_Template, 25, 11, \"form\", 17);\n            i0.ɵɵelementStart(31, \"div\", 18)(32, \"p\", 15);\n            i0.ɵɵtext(33, \" Don't have an account? \");\n            i0.ɵɵelementStart(34, \"a\", 19);\n            i0.ɵɵtext(35, \" Sign up here \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(36, \"div\", 20)(37, \"h6\", 21);\n            i0.ɵɵtext(38, \"Demo Credentials:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"small\", 22);\n            i0.ɵɵtext(40, \"Doctor: <EMAIL> / password123\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"small\", 22);\n            i0.ɵɵtext(42, \"Patient: <EMAIL> / password123\");\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(29);\n            i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loginForm);\n          }\n        },\n        dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink],\n        styles: [\".vh-100[_ngcontent-%COMP%]{min-height:100vh}.bg-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#0d6efd 0%,#0b5ed7 100%)}.input-group-text[_ngcontent-%COMP%]{background-color:#f8f9fa;border-right:none}.form-control[_ngcontent-%COMP%]{border-left:none}.form-control[_ngcontent-%COMP%]:focus{box-shadow:none;border-color:#86b7fe}.form-control[_ngcontent-%COMP%]:focus + .input-group-text[_ngcontent-%COMP%]{border-color:#86b7fe}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#0d6efd 0%,#0b5ed7 100%);border:none;font-weight:500}.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#0b5ed7 0%,#0a58ca 100%);transform:translateY(-1px);box-shadow:0 4px 8px #0d6efd4d}.btn-primary[_ngcontent-%COMP%]:disabled{background:#6c757d;transform:none;box-shadow:none}.alert[_ngcontent-%COMP%]{border:none;border-radius:8px}.form-check-input[_ngcontent-%COMP%]:checked{background-color:#0d6efd;border-color:#0d6efd}@media (max-width: 768px){.container-fluid[_ngcontent-%COMP%]{padding:1rem}.display-1[_ngcontent-%COMP%]{font-size:3rem}.display-4[_ngcontent-%COMP%]{font-size:2rem}}\"]\n      });\n    }\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}