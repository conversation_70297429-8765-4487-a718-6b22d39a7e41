{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SharedModule } from '../shared/shared.module';\nimport { LoginComponent } from './login/login.component';\nimport { RegisterComponent } from './register/register.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'register',\n  component: RegisterComponent\n}];\nexport class AuthModule {\n  static {\n    this.ɵfac = function AuthModule_Factory(t) {\n      return new (t || AuthModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AuthModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, RouterModule.forChild(routes)]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthModule, {\n    declarations: [LoginComponent, RegisterComponent],\n    imports: [SharedModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SharedModule", "LoginComponent", "RegisterComponent", "routes", "path", "redirectTo", "pathMatch", "component", "AuthModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/auth/auth.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SharedModule } from '../shared/shared.module';\n\nimport { LoginComponent } from './login/login.component';\nimport { RegisterComponent } from './register/register.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    redirectTo: 'login',\n    pathMatch: 'full'\n  },\n  {\n    path: 'login',\n    component: LoginComponent\n  },\n  {\n    path: 'register',\n    component: RegisterComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    LoginComponent,\n    RegisterComponent\n  ],\n  imports: [\n    SharedModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class AuthModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,yBAAyB;AAEtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,+BAA+B;;;AAEjE,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,OAAO;EACbG,SAAS,EAAEN;CACZ,EACD;EACEG,IAAI,EAAE,UAAU;EAChBG,SAAS,EAAEL;CACZ,CACF;AAYD,OAAM,MAAOM,UAAU;;;uBAAVA,UAAU;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBAJnBR,YAAY,EACZD,YAAY,CAACU,QAAQ,CAACN,MAAM,CAAC;IAAA;EAAA;;;2EAGpBK,UAAU;IAAAE,YAAA,GARnBT,cAAc,EACdC,iBAAiB;IAAAS,OAAA,GAGjBX,YAAY,EAAAY,EAAA,CAAAb,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}