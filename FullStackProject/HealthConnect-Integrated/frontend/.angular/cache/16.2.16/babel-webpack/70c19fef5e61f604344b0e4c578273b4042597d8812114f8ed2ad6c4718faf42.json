{"ast": null, "code": "'use strict';\n\nvar required = require('requires-port'),\n  qs = require('querystringify'),\n  controlOrWhitespace = /^[\\x00-\\x20\\u00a0\\u1680\\u2000-\\u200a\\u2028\\u2029\\u202f\\u205f\\u3000\\ufeff]+/,\n  CRHTLF = /[\\n\\r\\t]/g,\n  slashes = /^[A-Za-z][A-Za-z0-9+-.]*:\\/\\//,\n  port = /:\\d+$/,\n  protocolre = /^([a-z][a-z0-9.+-]*:)?(\\/\\/)?([\\\\/]+)?([\\S\\s]*)/i,\n  windowsDriveLetter = /^[a-zA-Z]:/;\n\n/**\n * Remove control characters and whitespace from the beginning of a string.\n *\n * @param {Object|String} str String to trim.\n * @returns {String} A new string representing `str` stripped of control\n *     characters and whitespace from its beginning.\n * @public\n */\nfunction trimLeft(str) {\n  return (str ? str : '').toString().replace(controlOrWhitespace, '');\n}\n\n/**\n * These are the parse rules for the URL parser, it informs the parser\n * about:\n *\n * 0. The char it Needs to parse, if it's a string it should be done using\n *    indexOf, RegExp using exec and NaN means set as current value.\n * 1. The property we should set when parsing this value.\n * 2. Indication if it's backwards or forward parsing, when set as number it's\n *    the value of extra chars that should be split off.\n * 3. Inherit from location if non existing in the parser.\n * 4. `toLowerCase` the resulting value.\n */\nvar rules = [['#', 'hash'],\n// Extract from the back.\n['?', 'query'],\n// Extract from the back.\nfunction sanitize(address, url) {\n  // Sanitize what is left of the address\n  return isSpecial(url.protocol) ? address.replace(/\\\\/g, '/') : address;\n}, ['/', 'pathname'],\n// Extract from the back.\n['@', 'auth', 1],\n// Extract from the front.\n[NaN, 'host', undefined, 1, 1],\n// Set left over value.\n[/:(\\d*)$/, 'port', undefined, 1],\n// RegExp the back.\n[NaN, 'hostname', undefined, 1, 1] // Set left over.\n];\n\n/**\n * These properties should not be copied or inherited from. This is only needed\n * for all non blob URL's as a blob URL does not include a hash, only the\n * origin.\n *\n * @type {Object}\n * @private\n */\nvar ignore = {\n  hash: 1,\n  query: 1\n};\n\n/**\n * The location object differs when your code is loaded through a normal page,\n * Worker or through a worker using a blob. And with the blobble begins the\n * trouble as the location object will contain the URL of the blob, not the\n * location of the page where our code is loaded in. The actual origin is\n * encoded in the `pathname` so we can thankfully generate a good \"default\"\n * location from it so we can generate proper relative URL's again.\n *\n * @param {Object|String} loc Optional default location object.\n * @returns {Object} lolcation object.\n * @public\n */\nfunction lolcation(loc) {\n  var globalVar;\n  if (typeof window !== 'undefined') globalVar = window;else if (typeof global !== 'undefined') globalVar = global;else if (typeof self !== 'undefined') globalVar = self;else globalVar = {};\n  var location = globalVar.location || {};\n  loc = loc || location;\n  var finaldestination = {},\n    type = typeof loc,\n    key;\n  if ('blob:' === loc.protocol) {\n    finaldestination = new Url(unescape(loc.pathname), {});\n  } else if ('string' === type) {\n    finaldestination = new Url(loc, {});\n    for (key in ignore) delete finaldestination[key];\n  } else if ('object' === type) {\n    for (key in loc) {\n      if (key in ignore) continue;\n      finaldestination[key] = loc[key];\n    }\n    if (finaldestination.slashes === undefined) {\n      finaldestination.slashes = slashes.test(loc.href);\n    }\n  }\n  return finaldestination;\n}\n\n/**\n * Check whether a protocol scheme is special.\n *\n * @param {String} The protocol scheme of the URL\n * @return {Boolean} `true` if the protocol scheme is special, else `false`\n * @private\n */\nfunction isSpecial(scheme) {\n  return scheme === 'file:' || scheme === 'ftp:' || scheme === 'http:' || scheme === 'https:' || scheme === 'ws:' || scheme === 'wss:';\n}\n\n/**\n * @typedef ProtocolExtract\n * @type Object\n * @property {String} protocol Protocol matched in the URL, in lowercase.\n * @property {Boolean} slashes `true` if protocol is followed by \"//\", else `false`.\n * @property {String} rest Rest of the URL that is not part of the protocol.\n */\n\n/**\n * Extract protocol information from a URL with/without double slash (\"//\").\n *\n * @param {String} address URL we want to extract from.\n * @param {Object} location\n * @return {ProtocolExtract} Extracted information.\n * @private\n */\nfunction extractProtocol(address, location) {\n  address = trimLeft(address);\n  address = address.replace(CRHTLF, '');\n  location = location || {};\n  var match = protocolre.exec(address);\n  var protocol = match[1] ? match[1].toLowerCase() : '';\n  var forwardSlashes = !!match[2];\n  var otherSlashes = !!match[3];\n  var slashesCount = 0;\n  var rest;\n  if (forwardSlashes) {\n    if (otherSlashes) {\n      rest = match[2] + match[3] + match[4];\n      slashesCount = match[2].length + match[3].length;\n    } else {\n      rest = match[2] + match[4];\n      slashesCount = match[2].length;\n    }\n  } else {\n    if (otherSlashes) {\n      rest = match[3] + match[4];\n      slashesCount = match[3].length;\n    } else {\n      rest = match[4];\n    }\n  }\n  if (protocol === 'file:') {\n    if (slashesCount >= 2) {\n      rest = rest.slice(2);\n    }\n  } else if (isSpecial(protocol)) {\n    rest = match[4];\n  } else if (protocol) {\n    if (forwardSlashes) {\n      rest = rest.slice(2);\n    }\n  } else if (slashesCount >= 2 && isSpecial(location.protocol)) {\n    rest = match[4];\n  }\n  return {\n    protocol: protocol,\n    slashes: forwardSlashes || isSpecial(protocol),\n    slashesCount: slashesCount,\n    rest: rest\n  };\n}\n\n/**\n * Resolve a relative URL pathname against a base URL pathname.\n *\n * @param {String} relative Pathname of the relative URL.\n * @param {String} base Pathname of the base URL.\n * @return {String} Resolved pathname.\n * @private\n */\nfunction resolve(relative, base) {\n  if (relative === '') return base;\n  var path = (base || '/').split('/').slice(0, -1).concat(relative.split('/')),\n    i = path.length,\n    last = path[i - 1],\n    unshift = false,\n    up = 0;\n  while (i--) {\n    if (path[i] === '.') {\n      path.splice(i, 1);\n    } else if (path[i] === '..') {\n      path.splice(i, 1);\n      up++;\n    } else if (up) {\n      if (i === 0) unshift = true;\n      path.splice(i, 1);\n      up--;\n    }\n  }\n  if (unshift) path.unshift('');\n  if (last === '.' || last === '..') path.push('');\n  return path.join('/');\n}\n\n/**\n * The actual URL instance. Instead of returning an object we've opted-in to\n * create an actual constructor as it's much more memory efficient and\n * faster and it pleases my OCD.\n *\n * It is worth noting that we should not use `URL` as class name to prevent\n * clashes with the global URL instance that got introduced in browsers.\n *\n * @constructor\n * @param {String} address URL we want to parse.\n * @param {Object|String} [location] Location defaults for relative paths.\n * @param {Boolean|Function} [parser] Parser for the query string.\n * @private\n */\nfunction Url(address, location, parser) {\n  address = trimLeft(address);\n  address = address.replace(CRHTLF, '');\n  if (!(this instanceof Url)) {\n    return new Url(address, location, parser);\n  }\n  var relative,\n    extracted,\n    parse,\n    instruction,\n    index,\n    key,\n    instructions = rules.slice(),\n    type = typeof location,\n    url = this,\n    i = 0;\n\n  //\n  // The following if statements allows this module two have compatibility with\n  // 2 different API:\n  //\n  // 1. Node.js's `url.parse` api which accepts a URL, boolean as arguments\n  //    where the boolean indicates that the query string should also be parsed.\n  //\n  // 2. The `URL` interface of the browser which accepts a URL, object as\n  //    arguments. The supplied object will be used as default values / fall-back\n  //    for relative paths.\n  //\n  if ('object' !== type && 'string' !== type) {\n    parser = location;\n    location = null;\n  }\n  if (parser && 'function' !== typeof parser) parser = qs.parse;\n  location = lolcation(location);\n\n  //\n  // Extract protocol information before running the instructions.\n  //\n  extracted = extractProtocol(address || '', location);\n  relative = !extracted.protocol && !extracted.slashes;\n  url.slashes = extracted.slashes || relative && location.slashes;\n  url.protocol = extracted.protocol || location.protocol || '';\n  address = extracted.rest;\n\n  //\n  // When the authority component is absent the URL starts with a path\n  // component.\n  //\n  if (extracted.protocol === 'file:' && (extracted.slashesCount !== 2 || windowsDriveLetter.test(address)) || !extracted.slashes && (extracted.protocol || extracted.slashesCount < 2 || !isSpecial(url.protocol))) {\n    instructions[3] = [/(.*)/, 'pathname'];\n  }\n  for (; i < instructions.length; i++) {\n    instruction = instructions[i];\n    if (typeof instruction === 'function') {\n      address = instruction(address, url);\n      continue;\n    }\n    parse = instruction[0];\n    key = instruction[1];\n    if (parse !== parse) {\n      url[key] = address;\n    } else if ('string' === typeof parse) {\n      index = parse === '@' ? address.lastIndexOf(parse) : address.indexOf(parse);\n      if (~index) {\n        if ('number' === typeof instruction[2]) {\n          url[key] = address.slice(0, index);\n          address = address.slice(index + instruction[2]);\n        } else {\n          url[key] = address.slice(index);\n          address = address.slice(0, index);\n        }\n      }\n    } else if (index = parse.exec(address)) {\n      url[key] = index[1];\n      address = address.slice(0, index.index);\n    }\n    url[key] = url[key] || (relative && instruction[3] ? location[key] || '' : '');\n\n    //\n    // Hostname, host and protocol should be lowercased so they can be used to\n    // create a proper `origin`.\n    //\n    if (instruction[4]) url[key] = url[key].toLowerCase();\n  }\n\n  //\n  // Also parse the supplied query string in to an object. If we're supplied\n  // with a custom parser as function use that instead of the default build-in\n  // parser.\n  //\n  if (parser) url.query = parser(url.query);\n\n  //\n  // If the URL is relative, resolve the pathname against the base URL.\n  //\n  if (relative && location.slashes && url.pathname.charAt(0) !== '/' && (url.pathname !== '' || location.pathname !== '')) {\n    url.pathname = resolve(url.pathname, location.pathname);\n  }\n\n  //\n  // Default to a / for pathname if none exists. This normalizes the URL\n  // to always have a /\n  //\n  if (url.pathname.charAt(0) !== '/' && isSpecial(url.protocol)) {\n    url.pathname = '/' + url.pathname;\n  }\n\n  //\n  // We should not add port numbers if they are already the default port number\n  // for a given protocol. As the host also contains the port number we're going\n  // override it with the hostname which contains no port number.\n  //\n  if (!required(url.port, url.protocol)) {\n    url.host = url.hostname;\n    url.port = '';\n  }\n\n  //\n  // Parse down the `auth` for the username and password.\n  //\n  url.username = url.password = '';\n  if (url.auth) {\n    index = url.auth.indexOf(':');\n    if (~index) {\n      url.username = url.auth.slice(0, index);\n      url.username = encodeURIComponent(decodeURIComponent(url.username));\n      url.password = url.auth.slice(index + 1);\n      url.password = encodeURIComponent(decodeURIComponent(url.password));\n    } else {\n      url.username = encodeURIComponent(decodeURIComponent(url.auth));\n    }\n    url.auth = url.password ? url.username + ':' + url.password : url.username;\n  }\n  url.origin = url.protocol !== 'file:' && isSpecial(url.protocol) && url.host ? url.protocol + '//' + url.host : 'null';\n\n  //\n  // The href is just the compiled result.\n  //\n  url.href = url.toString();\n}\n\n/**\n * This is convenience method for changing properties in the URL instance to\n * insure that they all propagate correctly.\n *\n * @param {String} part          Property we need to adjust.\n * @param {Mixed} value          The newly assigned value.\n * @param {Boolean|Function} fn  When setting the query, it will be the function\n *                               used to parse the query.\n *                               When setting the protocol, double slash will be\n *                               removed from the final url if it is true.\n * @returns {URL} URL instance for chaining.\n * @public\n */\nfunction set(part, value, fn) {\n  var url = this;\n  switch (part) {\n    case 'query':\n      if ('string' === typeof value && value.length) {\n        value = (fn || qs.parse)(value);\n      }\n      url[part] = value;\n      break;\n    case 'port':\n      url[part] = value;\n      if (!required(value, url.protocol)) {\n        url.host = url.hostname;\n        url[part] = '';\n      } else if (value) {\n        url.host = url.hostname + ':' + value;\n      }\n      break;\n    case 'hostname':\n      url[part] = value;\n      if (url.port) value += ':' + url.port;\n      url.host = value;\n      break;\n    case 'host':\n      url[part] = value;\n      if (port.test(value)) {\n        value = value.split(':');\n        url.port = value.pop();\n        url.hostname = value.join(':');\n      } else {\n        url.hostname = value;\n        url.port = '';\n      }\n      break;\n    case 'protocol':\n      url.protocol = value.toLowerCase();\n      url.slashes = !fn;\n      break;\n    case 'pathname':\n    case 'hash':\n      if (value) {\n        var char = part === 'pathname' ? '/' : '#';\n        url[part] = value.charAt(0) !== char ? char + value : value;\n      } else {\n        url[part] = value;\n      }\n      break;\n    case 'username':\n    case 'password':\n      url[part] = encodeURIComponent(value);\n      break;\n    case 'auth':\n      var index = value.indexOf(':');\n      if (~index) {\n        url.username = value.slice(0, index);\n        url.username = encodeURIComponent(decodeURIComponent(url.username));\n        url.password = value.slice(index + 1);\n        url.password = encodeURIComponent(decodeURIComponent(url.password));\n      } else {\n        url.username = encodeURIComponent(decodeURIComponent(value));\n      }\n  }\n  for (var i = 0; i < rules.length; i++) {\n    var ins = rules[i];\n    if (ins[4]) url[ins[1]] = url[ins[1]].toLowerCase();\n  }\n  url.auth = url.password ? url.username + ':' + url.password : url.username;\n  url.origin = url.protocol !== 'file:' && isSpecial(url.protocol) && url.host ? url.protocol + '//' + url.host : 'null';\n  url.href = url.toString();\n  return url;\n}\n\n/**\n * Transform the properties back in to a valid and full URL string.\n *\n * @param {Function} stringify Optional query stringify function.\n * @returns {String} Compiled version of the URL.\n * @public\n */\nfunction toString(stringify) {\n  if (!stringify || 'function' !== typeof stringify) stringify = qs.stringify;\n  var query,\n    url = this,\n    host = url.host,\n    protocol = url.protocol;\n  if (protocol && protocol.charAt(protocol.length - 1) !== ':') protocol += ':';\n  var result = protocol + (url.protocol && url.slashes || isSpecial(url.protocol) ? '//' : '');\n  if (url.username) {\n    result += url.username;\n    if (url.password) result += ':' + url.password;\n    result += '@';\n  } else if (url.password) {\n    result += ':' + url.password;\n    result += '@';\n  } else if (url.protocol !== 'file:' && isSpecial(url.protocol) && !host && url.pathname !== '/') {\n    //\n    // Add back the empty userinfo, otherwise the original invalid URL\n    // might be transformed into a valid one with `url.pathname` as host.\n    //\n    result += '@';\n  }\n\n  //\n  // Trailing colon is removed from `url.host` when it is parsed. If it still\n  // ends with a colon, then add back the trailing colon that was removed. This\n  // prevents an invalid URL from being transformed into a valid one.\n  //\n  if (host[host.length - 1] === ':' || port.test(url.hostname) && !url.port) {\n    host += ':';\n  }\n  result += host + url.pathname;\n  query = 'object' === typeof url.query ? stringify(url.query) : url.query;\n  if (query) result += '?' !== query.charAt(0) ? '?' + query : query;\n  if (url.hash) result += url.hash;\n  return result;\n}\nUrl.prototype = {\n  set: set,\n  toString: toString\n};\n\n//\n// Expose the URL parser and some additional properties that might be useful for\n// others or testing.\n//\nUrl.extractProtocol = extractProtocol;\nUrl.location = lolcation;\nUrl.trimLeft = trimLeft;\nUrl.qs = qs;\nmodule.exports = Url;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}