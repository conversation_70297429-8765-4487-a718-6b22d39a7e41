{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  Event = require('./event');\nfunction CloseEvent() {\n  Event.call(this);\n  this.initEvent('close', false, false);\n  this.wasClean = false;\n  this.code = 0;\n  this.reason = '';\n}\ninherits(CloseEvent, Event);\nmodule.exports = CloseEvent;", "map": {"version": 3, "names": ["inherits", "require", "Event", "CloseEvent", "call", "initEvent", "<PERSON><PERSON><PERSON>", "code", "reason", "module", "exports"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/sockjs-client/lib/event/close.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , Event = require('./event')\n  ;\n\nfunction CloseEvent() {\n  Event.call(this);\n  this.initEvent('close', false, false);\n  this.wasClean = false;\n  this.code = 0;\n  this.reason = '';\n}\n\ninherits(CloseEvent, Event);\n\nmodule.exports = CloseEvent;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,KAAK,GAAGD,OAAO,CAAC,SAAS,CAAC;AAG9B,SAASE,UAAUA,CAAA,EAAG;EACpBD,KAAK,CAACE,IAAI,CAAC,IAAI,CAAC;EAChB,IAAI,CAACC,SAAS,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC;EACrC,IAAI,CAACC,QAAQ,GAAG,KAAK;EACrB,IAAI,CAACC,IAAI,GAAG,CAAC;EACb,IAAI,CAACC,MAAM,GAAG,EAAE;AAClB;AAEAR,QAAQ,CAACG,UAAU,EAAED,KAAK,CAAC;AAE3BO,MAAM,CAACC,OAAO,GAAGP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}