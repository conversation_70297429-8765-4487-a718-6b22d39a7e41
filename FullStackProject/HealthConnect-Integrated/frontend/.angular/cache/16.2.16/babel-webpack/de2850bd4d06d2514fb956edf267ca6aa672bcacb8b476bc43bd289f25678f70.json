{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./core/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"./shared/components/notification-bell/notification-bell.component\";\nfunction AppComponent_nav_0_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 11)(2, \"a\", 34);\n    i0.ɵɵelement(3, \"i\", 35);\n    i0.ɵɵtext(4, \"Appointments \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"li\", 11)(6, \"a\", 36);\n    i0.ɵɵelement(7, \"i\", 37);\n    i0.ɵɵtext(8, \"Find Doctors \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"li\", 11)(10, \"a\", 38);\n    i0.ɵɵelement(11, \"i\", 39);\n    i0.ɵɵtext(12, \"Health Assistant \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AppComponent_nav_0_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 11)(2, \"a\", 40);\n    i0.ɵɵelement(3, \"i\", 41);\n    i0.ɵɵtext(4, \"Patients \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"li\", 11)(6, \"a\", 34);\n    i0.ɵɵelement(7, \"i\", 35);\n    i0.ɵɵtext(8, \"Schedule \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AppComponent_nav_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nav\", 3)(1, \"div\", 4)(2, \"a\", 5);\n    i0.ɵɵlistener(\"click\", function AppComponent_nav_0_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.navigateToDashboard());\n    });\n    i0.ɵɵelement(3, \"i\", 6);\n    i0.ɵɵtext(4, \"HealthConnect \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 7);\n    i0.ɵɵelement(6, \"span\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 9)(8, \"ul\", 10)(9, \"li\", 11)(10, \"a\", 12);\n    i0.ɵɵlistener(\"click\", function AppComponent_nav_0_Template_a_click_10_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.navigateToDashboard());\n    });\n    i0.ɵɵelement(11, \"i\", 13);\n    i0.ɵɵtext(12, \"Dashboard \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, AppComponent_nav_0_ng_container_13_Template, 13, 0, \"ng-container\", 14);\n    i0.ɵɵtemplate(14, AppComponent_nav_0_ng_container_14_Template, 9, 0, \"ng-container\", 14);\n    i0.ɵɵelementStart(15, \"li\", 11)(16, \"a\", 15);\n    i0.ɵɵelement(17, \"i\", 16);\n    i0.ɵɵtext(18, \"Messages \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"ul\", 17)(20, \"li\", 11);\n    i0.ɵɵelement(21, \"app-notification-bell\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"li\", 18)(23, \"a\", 19)(24, \"div\", 20);\n    i0.ɵɵelement(25, \"i\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 22);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"ul\", 23)(29, \"li\")(30, \"h6\", 24);\n    i0.ɵɵtext(31);\n    i0.ɵɵelement(32, \"br\");\n    i0.ɵɵelementStart(33, \"small\", 25);\n    i0.ɵɵtext(34);\n    i0.ɵɵpipe(35, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(36, \"li\");\n    i0.ɵɵelement(37, \"hr\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"li\")(39, \"a\", 27);\n    i0.ɵɵlistener(\"click\", function AppComponent_nav_0_Template_a_click_39_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.navigateToProfile());\n    });\n    i0.ɵɵelement(40, \"i\", 28);\n    i0.ɵɵtext(41, \"Profile Settings \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"li\")(43, \"a\", 29);\n    i0.ɵɵelement(44, \"i\", 30);\n    i0.ɵɵtext(45, \"Notifications \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"li\")(47, \"a\", 29);\n    i0.ɵɵelement(48, \"i\", 31);\n    i0.ɵɵtext(49, \"Help & Support \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"li\");\n    i0.ɵɵelement(51, \"hr\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"li\")(53, \"a\", 32);\n    i0.ɵɵlistener(\"click\", function AppComponent_nav_0_Template_a_click_53_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.logout());\n    });\n    i0.ɵɵelement(54, \"i\", 33);\n    i0.ɵɵtext(55, \"Sign Out \");\n    i0.ɵɵelementEnd()()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentUser.role === \"PATIENT\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentUser.role === \"DOCTOR\");\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(ctx_r0.currentUser.fullName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.currentUser.fullName, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(35, 5, ctx_r0.currentUser.role));\n  }\n}\nfunction AppComponent_footer_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"footer\", 42)(1, \"div\", 43)(2, \"small\", 25);\n    i0.ɵɵtext(3, \" \\u00A9 2024 HealthConnect. All rights reserved. | \");\n    i0.ɵɵelementStart(4, \"a\", 44);\n    i0.ɵɵtext(5, \"Privacy Policy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" | \");\n    i0.ɵɵelementStart(7, \"a\", 44);\n    i0.ɵɵtext(8, \"Terms of Service\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nexport class AppComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.title = 'HealthConnect';\n    this.currentUser = null;\n    this.showNavigation = false;\n  }\n  ngOnInit() {\n    // Subscribe to authentication state\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      this.updateNavigationVisibility();\n    });\n    // Subscribe to route changes to determine if navigation should be shown\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n      this.updateNavigationVisibility();\n    });\n  }\n  updateNavigationVisibility() {\n    const isAuthRoute = this.router.url.includes('/auth');\n    const isAuthenticated = this.authService.isAuthenticated();\n    this.showNavigation = !isAuthRoute && isAuthenticated && !!this.currentUser;\n  }\n  logout() {\n    this.authService.logout();\n  }\n  navigateToProfile() {\n    this.router.navigate(['/profile']);\n  }\n  navigateToDashboard() {\n    if (this.currentUser?.role === 'DOCTOR') {\n      this.router.navigate(['/doctor/dashboard']);\n    } else if (this.currentUser?.role === 'PATIENT') {\n      this.router.navigate(['/patient/dashboard']);\n    }\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 4,\n      vars: 4,\n      consts: [[\"class\", \"navbar navbar-expand-lg navbar-dark bg-primary\", 4, \"ngIf\"], [1, \"main-content\"], [\"class\", \"bg-light text-center py-3 mt-auto\", 4, \"ngIf\"], [1, \"navbar\", \"navbar-expand-lg\", \"navbar-dark\", \"bg-primary\"], [1, \"container-fluid\"], [1, \"navbar-brand\", \"fw-bold\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"bi\", \"bi-heart-pulse\", \"me-2\"], [\"type\", \"button\", \"data-bs-toggle\", \"collapse\", \"data-bs-target\", \"#navbarNav\", 1, \"navbar-toggler\"], [1, \"navbar-toggler-icon\"], [\"id\", \"navbarNav\", 1, \"collapse\", \"navbar-collapse\"], [1, \"navbar-nav\", \"me-auto\"], [1, \"nav-item\"], [1, \"nav-link\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"bi\", \"bi-house\", \"me-1\"], [4, \"ngIf\"], [\"routerLink\", \"/chat\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"bi\", \"bi-chat-dots\", \"me-1\"], [1, \"navbar-nav\"], [1, \"nav-item\", \"dropdown\"], [\"href\", \"#\", \"id\", \"navbarDropdown\", \"role\", \"button\", \"data-bs-toggle\", \"dropdown\", 1, \"nav-link\", \"dropdown-toggle\", \"d-flex\", \"align-items-center\"], [1, \"rounded-circle\", \"bg-light\", \"text-primary\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"me-2\", 2, \"width\", \"32px\", \"height\", \"32px\"], [1, \"bi\", \"bi-person\"], [1, \"d-none\", \"d-md-inline\"], [1, \"dropdown-menu\", \"dropdown-menu-end\"], [1, \"dropdown-header\"], [1, \"text-muted\"], [1, \"dropdown-divider\"], [1, \"dropdown-item\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"bi\", \"bi-person-gear\", \"me-2\"], [\"href\", \"#\", 1, \"dropdown-item\", 2, \"cursor\", \"pointer\"], [1, \"bi\", \"bi-bell\", \"me-2\"], [1, \"bi\", \"bi-question-circle\", \"me-2\"], [1, \"dropdown-item\", \"text-danger\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"bi\", \"bi-box-arrow-right\", \"me-2\"], [\"routerLink\", \"/appointments\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"bi\", \"bi-calendar\", \"me-1\"], [\"routerLink\", \"/doctors\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"bi\", \"bi-search\", \"me-1\"], [\"routerLink\", \"/health-bot\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"bi\", \"bi-robot\", \"me-1\"], [\"routerLink\", \"/patients\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"bi\", \"bi-people\", \"me-1\"], [1, \"bg-light\", \"text-center\", \"py-3\", \"mt-auto\"], [1, \"container\"], [\"href\", \"#\", 1, \"text-decoration-none\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AppComponent_nav_0_Template, 56, 7, \"nav\", 0);\n          i0.ɵɵelementStart(1, \"main\", 1);\n          i0.ɵɵelement(2, \"router-outlet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, AppComponent_footer_3_Template, 9, 0, \"footer\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.showNavigation && ctx.currentUser);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"with-navbar\", ctx.showNavigation && ctx.currentUser);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showNavigation);\n        }\n      },\n      dependencies: [i3.NgIf, i2.RouterOutlet, i2.RouterLink, i2.RouterLinkActive, i4.NotificationBellComponent, i3.TitleCasePipe],\n      styles: [\".navbar[_ngcontent-%COMP%] {\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  z-index: 1030;\\n}\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n}\\n\\n.nav-link[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n\\n.nav-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  border-radius: 0.375rem;\\n}\\n\\n.nav-link.active[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n  border-radius: 0.375rem;\\n}\\n\\n.dropdown-menu[_ngcontent-%COMP%] {\\n  border: none;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  border-radius: 0.5rem;\\n  min-width: 200px;\\n}\\n\\n.dropdown-item[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  transition: all 0.3s ease;\\n}\\n\\n.dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\n.dropdown-header[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #495057;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  min-height: calc(100vh - 60px);\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.main-content.with-navbar[_ngcontent-%COMP%] {\\n  min-height: calc(100vh - 116px);\\n}\\n\\nfooter[_ngcontent-%COMP%] {\\n  margin-top: auto;\\n  border-top: 1px solid #e9ecef;\\n}\\n\\n@media (max-width: 991px) {\\n  .navbar-nav[_ngcontent-%COMP%] {\\n    padding-top: 1rem;\\n  }\\n  .navbar-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    padding: 0.5rem 1rem;\\n  }\\n  .dropdown-menu[_ngcontent-%COMP%] {\\n    position: static !important;\\n    transform: none !important;\\n    border: none;\\n    box-shadow: none;\\n    background-color: rgba(255, 255, 255, 0.1);\\n    margin-top: 0.5rem;\\n  }\\n  .dropdown-item[_ngcontent-%COMP%] {\\n    color: rgba(255, 255, 255, 0.8);\\n  }\\n  .dropdown-item[_ngcontent-%COMP%]:hover {\\n    background-color: rgba(255, 255, 255, 0.1);\\n    color: white;\\n  }\\n  .dropdown-header[_ngcontent-%COMP%] {\\n    color: rgba(255, 255, 255, 0.9);\\n  }\\n  .dropdown-divider[_ngcontent-%COMP%] {\\n    border-color: rgba(255, 255, 255, 0.2);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUVBO0VBQ0Usd0NBQUE7RUFDQSxhQUFBO0FBREY7O0FBSUE7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0FBREY7O0FBSUE7RUFDRSxnQkFBQTtFQUNBLHlCQUFBO0FBREY7O0FBSUE7RUFDRSwwQ0FBQTtFQUNBLHVCQUFBO0FBREY7O0FBSUE7RUFDRSwwQ0FBQTtFQUNBLHVCQUFBO0FBREY7O0FBSUE7RUFDRSxZQUFBO0VBQ0Esd0NBQUE7RUFDQSxxQkFBQTtFQUNBLGdCQUFBO0FBREY7O0FBSUE7RUFDRSxvQkFBQTtFQUNBLHlCQUFBO0FBREY7O0FBSUE7RUFDRSx5QkFBQTtBQURGOztBQUlBO0VBQ0UsZ0JBQUE7RUFDQSxjQUFBO0FBREY7O0FBSUE7RUFDRSw4QkFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtBQURGOztBQUlBO0VBQ0UsK0JBQUE7QUFERjs7QUFJQTtFQUNFLGdCQUFBO0VBQ0EsNkJBQUE7QUFERjs7QUFLQTtFQUNFO0lBQ0UsaUJBQUE7RUFGRjtFQUtBO0lBQ0Usb0JBQUE7RUFIRjtFQU1BO0lBQ0UsMkJBQUE7SUFDQSwwQkFBQTtJQUNBLFlBQUE7SUFDQSxnQkFBQTtJQUNBLDBDQUFBO0lBQ0Esa0JBQUE7RUFKRjtFQU9BO0lBQ0UsK0JBQUE7RUFMRjtFQVFBO0lBQ0UsMENBQUE7SUFDQSxZQUFBO0VBTkY7RUFTQTtJQUNFLCtCQUFBO0VBUEY7RUFVQTtJQUNFLHNDQUFBO0VBUkY7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi8vIEFwcCBjb21wb25lbnQgc3R5bGVzXG5cbi5uYXZiYXIge1xuICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICB6LWluZGV4OiAxMDMwO1xufVxuXG4ubmF2YmFyLWJyYW5kIHtcbiAgZm9udC1zaXplOiAxLjVyZW07XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG59XG5cbi5uYXYtbGluayB7XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG59XG5cbi5uYXYtbGluazpob3ZlciB7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcbiAgYm9yZGVyLXJhZGl1czogMC4zNzVyZW07XG59XG5cbi5uYXYtbGluay5hY3RpdmUge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7XG4gIGJvcmRlci1yYWRpdXM6IDAuMzc1cmVtO1xufVxuXG4uZHJvcGRvd24tbWVudSB7XG4gIGJvcmRlcjogbm9uZTtcbiAgYm94LXNoYWRvdzogMCA0cHggNnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgYm9yZGVyLXJhZGl1czogMC41cmVtO1xuICBtaW4td2lkdGg6IDIwMHB4O1xufVxuXG4uZHJvcGRvd24taXRlbSB7XG4gIHBhZGRpbmc6IDAuNXJlbSAxcmVtO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xufVxuXG4uZHJvcGRvd24taXRlbTpob3ZlciB7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7XG59XG5cbi5kcm9wZG93bi1oZWFkZXIge1xuICBmb250LXdlaWdodDogNjAwO1xuICBjb2xvcjogIzQ5NTA1Nztcbn1cblxuLm1haW4tY29udGVudCB7XG4gIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA2MHB4KTsgLy8gQWRqdXN0IGJhc2VkIG9uIGZvb3RlciBoZWlnaHRcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbn1cblxuLm1haW4tY29udGVudC53aXRoLW5hdmJhciB7XG4gIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSAxMTZweCk7IC8vIEFkanVzdCBmb3IgbmF2YmFyICsgZm9vdGVyXG59XG5cbmZvb3RlciB7XG4gIG1hcmdpbi10b3A6IGF1dG87XG4gIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTllY2VmO1xufVxuXG4vLyBSZXNwb25zaXZlIGFkanVzdG1lbnRzXG5AbWVkaWEgKG1heC13aWR0aDogOTkxcHgpIHtcbiAgLm5hdmJhci1uYXYge1xuICAgIHBhZGRpbmctdG9wOiAxcmVtO1xuICB9XG4gIFxuICAubmF2YmFyLW5hdiAubmF2LWxpbmsge1xuICAgIHBhZGRpbmc6IDAuNXJlbSAxcmVtO1xuICB9XG4gIFxuICAuZHJvcGRvd24tbWVudSB7XG4gICAgcG9zaXRpb246IHN0YXRpYyAhaW1wb3J0YW50O1xuICAgIHRyYW5zZm9ybTogbm9uZSAhaW1wb3J0YW50O1xuICAgIGJvcmRlcjogbm9uZTtcbiAgICBib3gtc2hhZG93OiBub25lO1xuICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcbiAgICBtYXJnaW4tdG9wOiAwLjVyZW07XG4gIH1cbiAgXG4gIC5kcm9wZG93bi1pdGVtIHtcbiAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpO1xuICB9XG4gIFxuICAuZHJvcGRvd24taXRlbTpob3ZlciB7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xuICAgIGNvbG9yOiB3aGl0ZTtcbiAgfVxuICBcbiAgLmRyb3Bkb3duLWhlYWRlciB7XG4gICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KTtcbiAgfVxuICBcbiAgLmRyb3Bkb3duLWRpdmlkZXIge1xuICAgIGJvcmRlci1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NavigationEnd", "filter", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerEnd", "ɵɵlistener", "AppComponent_nav_0_Template_a_click_2_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "navigateToDashboard", "AppComponent_nav_0_Template_a_click_10_listener", "ctx_r6", "ɵɵtemplate", "AppComponent_nav_0_ng_container_13_Template", "AppComponent_nav_0_ng_container_14_Template", "AppComponent_nav_0_Template_a_click_39_listener", "ctx_r7", "navigateToProfile", "AppComponent_nav_0_Template_a_click_53_listener", "ctx_r8", "logout", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "currentUser", "role", "ɵɵtextInterpolate", "fullName", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "AppComponent", "constructor", "authService", "router", "title", "showNavigation", "ngOnInit", "currentUser$", "subscribe", "user", "updateNavigationVisibility", "events", "pipe", "event", "isAuthRoute", "url", "includes", "isAuthenticated", "navigate", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "AppComponent_nav_0_Template", "AppComponent_footer_3_Template", "ɵɵclassProp"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/app.component.ts", "/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/app.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router, NavigationEnd, Event } from '@angular/router';\nimport { AuthService } from './core/services/auth.service';\nimport { User } from './core/models/user.model';\nimport { filter } from 'rxjs/operators';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent implements OnInit {\n  title = 'HealthConnect';\n  currentUser: User | null = null;\n  showNavigation = false;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Subscribe to authentication state\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      this.updateNavigationVisibility();\n    });\n\n    // Subscribe to route changes to determine if navigation should be shown\n    this.router.events\n      .pipe(filter((event: Event): event is NavigationEnd => event instanceof NavigationEnd))\n      .subscribe((event: NavigationEnd) => {\n        this.updateNavigationVisibility();\n      });\n  }\n\n  private updateNavigationVisibility(): void {\n    const isAuthRoute = this.router.url.includes('/auth');\n    const isAuthenticated = this.authService.isAuthenticated();\n    this.showNavigation = !isAuthRoute && isAuthenticated && !!this.currentUser;\n  }\n\n  logout(): void {\n    this.authService.logout();\n  }\n\n  navigateToProfile(): void {\n    this.router.navigate(['/profile']);\n  }\n\n  navigateToDashboard(): void {\n    if (this.currentUser?.role === 'DOCTOR') {\n      this.router.navigate(['/doctor/dashboard']);\n    } else if (this.currentUser?.role === 'PATIENT') {\n      this.router.navigate(['/patient/dashboard']);\n    }\n  }\n}\n", "<!-- Navigation Bar -->\n<nav *ngIf=\"showNavigation && currentUser\" class=\"navbar navbar-expand-lg navbar-dark bg-primary\">\n  <div class=\"container-fluid\">\n    <!-- Brand -->\n    <a class=\"navbar-brand fw-bold\" (click)=\"navigateToDashboard()\" style=\"cursor: pointer;\">\n      <i class=\"bi bi-heart-pulse me-2\"></i>HealthConnect\n    </a>\n\n    <!-- Mobile toggle button -->\n    <button class=\"navbar-toggler\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#navbarNav\">\n      <span class=\"navbar-toggler-icon\"></span>\n    </button>\n\n    <!-- Navigation items -->\n    <div class=\"collapse navbar-collapse\" id=\"navbarNav\">\n      <ul class=\"navbar-nav me-auto\">\n        <li class=\"nav-item\">\n          <a class=\"nav-link\" (click)=\"navigateToDashboard()\" style=\"cursor: pointer;\">\n            <i class=\"bi bi-house me-1\"></i>Dashboard\n          </a>\n        </li>\n        \n        <!-- Patient-specific navigation -->\n        <ng-container *ngIf=\"currentUser.role === 'PATIENT'\">\n          <li class=\"nav-item\">\n            <a class=\"nav-link\" routerLink=\"/appointments\" routerLinkActive=\"active\">\n              <i class=\"bi bi-calendar me-1\"></i>Appointments\n            </a>\n          </li>\n          <li class=\"nav-item\">\n            <a class=\"nav-link\" routerLink=\"/doctors\" routerLinkActive=\"active\">\n              <i class=\"bi bi-search me-1\"></i>Find Doctors\n            </a>\n          </li>\n          <li class=\"nav-item\">\n            <a class=\"nav-link\" routerLink=\"/health-bot\" routerLinkActive=\"active\">\n              <i class=\"bi bi-robot me-1\"></i>Health Assistant\n            </a>\n          </li>\n        </ng-container>\n\n        <!-- Doctor-specific navigation -->\n        <ng-container *ngIf=\"currentUser.role === 'DOCTOR'\">\n          <li class=\"nav-item\">\n            <a class=\"nav-link\" routerLink=\"/patients\" routerLinkActive=\"active\">\n              <i class=\"bi bi-people me-1\"></i>Patients\n            </a>\n          </li>\n          <li class=\"nav-item\">\n            <a class=\"nav-link\" routerLink=\"/appointments\" routerLinkActive=\"active\">\n              <i class=\"bi bi-calendar me-1\"></i>Schedule\n            </a>\n          </li>\n        </ng-container>\n\n        <!-- Common navigation -->\n        <li class=\"nav-item\">\n          <a class=\"nav-link\" routerLink=\"/chat\" routerLinkActive=\"active\">\n            <i class=\"bi bi-chat-dots me-1\"></i>Messages\n          </a>\n        </li>\n      </ul>\n\n      <!-- User menu -->\n      <ul class=\"navbar-nav\">\n        <!-- Notification Bell -->\n        <li class=\"nav-item\">\n          <app-notification-bell></app-notification-bell>\n        </li>\n\n        <li class=\"nav-item dropdown\">\n          <a class=\"nav-link dropdown-toggle d-flex align-items-center\" href=\"#\" id=\"navbarDropdown\" \n             role=\"button\" data-bs-toggle=\"dropdown\">\n            <div class=\"rounded-circle bg-light text-primary d-flex align-items-center justify-content-center me-2\" \n                 style=\"width: 32px; height: 32px;\">\n              <i class=\"bi bi-person\"></i>\n            </div>\n            <span class=\"d-none d-md-inline\">{{ currentUser.fullName }}</span>\n          </a>\n          <ul class=\"dropdown-menu dropdown-menu-end\">\n            <li>\n              <h6 class=\"dropdown-header\">\n                {{ currentUser.fullName }}\n                <br>\n                <small class=\"text-muted\">{{ currentUser.role | titlecase }}</small>\n              </h6>\n            </li>\n            <li><hr class=\"dropdown-divider\"></li>\n            <li>\n              <a class=\"dropdown-item\" (click)=\"navigateToProfile()\" style=\"cursor: pointer;\">\n                <i class=\"bi bi-person-gear me-2\"></i>Profile Settings\n              </a>\n            </li>\n            <li>\n              <a class=\"dropdown-item\" href=\"#\" style=\"cursor: pointer;\">\n                <i class=\"bi bi-bell me-2\"></i>Notifications\n              </a>\n            </li>\n            <li>\n              <a class=\"dropdown-item\" href=\"#\" style=\"cursor: pointer;\">\n                <i class=\"bi bi-question-circle me-2\"></i>Help & Support\n              </a>\n            </li>\n            <li><hr class=\"dropdown-divider\"></li>\n            <li>\n              <a class=\"dropdown-item text-danger\" (click)=\"logout()\" style=\"cursor: pointer;\">\n                <i class=\"bi bi-box-arrow-right me-2\"></i>Sign Out\n              </a>\n            </li>\n          </ul>\n        </li>\n      </ul>\n    </div>\n  </div>\n</nav>\n\n<!-- Main Content -->\n<main class=\"main-content\" [class.with-navbar]=\"showNavigation && currentUser\">\n  <router-outlet></router-outlet>\n</main>\n\n<!-- Footer (only show when not in auth pages) -->\n<footer *ngIf=\"showNavigation\" class=\"bg-light text-center py-3 mt-auto\">\n  <div class=\"container\">\n    <small class=\"text-muted\">\n      &copy; 2024 HealthConnect. All rights reserved. | \n      <a href=\"#\" class=\"text-decoration-none\">Privacy Policy</a> | \n      <a href=\"#\" class=\"text-decoration-none\">Terms of Service</a>\n    </small>\n  </div>\n</footer>\n"], "mappings": "AACA,SAAiBA,aAAa,QAAe,iBAAiB;AAG9D,SAASC,MAAM,QAAQ,gBAAgB;;;;;;;;ICmB/BC,EAAA,CAAAC,uBAAA,GAAqD;IACnDD,EAAA,CAAAE,cAAA,aAAqB;IAEjBF,EAAA,CAAAG,SAAA,YAAmC;IAAAH,EAAA,CAAAI,MAAA,oBACrC;IAAAJ,EAAA,CAAAK,YAAA,EAAI;IAENL,EAAA,CAAAE,cAAA,aAAqB;IAEjBF,EAAA,CAAAG,SAAA,YAAiC;IAAAH,EAAA,CAAAI,MAAA,oBACnC;IAAAJ,EAAA,CAAAK,YAAA,EAAI;IAENL,EAAA,CAAAE,cAAA,aAAqB;IAEjBF,EAAA,CAAAG,SAAA,aAAgC;IAAAH,EAAA,CAAAI,MAAA,yBAClC;IAAAJ,EAAA,CAAAK,YAAA,EAAI;IAERL,EAAA,CAAAM,qBAAA,EAAe;;;;;IAGfN,EAAA,CAAAC,uBAAA,GAAoD;IAClDD,EAAA,CAAAE,cAAA,aAAqB;IAEjBF,EAAA,CAAAG,SAAA,YAAiC;IAAAH,EAAA,CAAAI,MAAA,gBACnC;IAAAJ,EAAA,CAAAK,YAAA,EAAI;IAENL,EAAA,CAAAE,cAAA,aAAqB;IAEjBF,EAAA,CAAAG,SAAA,YAAmC;IAAAH,EAAA,CAAAI,MAAA,gBACrC;IAAAJ,EAAA,CAAAK,YAAA,EAAI;IAERL,EAAA,CAAAM,qBAAA,EAAe;;;;;;IApDvBN,EAAA,CAAAE,cAAA,aAAkG;IAG9DF,EAAA,CAAAO,UAAA,mBAAAC,+CAAA;MAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAF,MAAA,CAAAG,mBAAA,EAAqB;IAAA,EAAC;IAC7Dd,EAAA,CAAAG,SAAA,WAAsC;IAAAH,EAAA,CAAAI,MAAA,qBACxC;IAAAJ,EAAA,CAAAK,YAAA,EAAI;IAGJL,EAAA,CAAAE,cAAA,gBAAmG;IACjGF,EAAA,CAAAG,SAAA,cAAyC;IAC3CH,EAAA,CAAAK,YAAA,EAAS;IAGTL,EAAA,CAAAE,cAAA,aAAqD;IAG3BF,EAAA,CAAAO,UAAA,mBAAAQ,gDAAA;MAAAf,EAAA,CAAAS,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAhB,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAG,MAAA,CAAAF,mBAAA,EAAqB;IAAA,EAAC;IACjDd,EAAA,CAAAG,SAAA,aAAgC;IAAAH,EAAA,CAAAI,MAAA,kBAClC;IAAAJ,EAAA,CAAAK,YAAA,EAAI;IAINL,EAAA,CAAAiB,UAAA,KAAAC,2CAAA,4BAgBe;IAGflB,EAAA,CAAAiB,UAAA,KAAAE,2CAAA,2BAWe;IAGfnB,EAAA,CAAAE,cAAA,cAAqB;IAEjBF,EAAA,CAAAG,SAAA,aAAoC;IAAAH,EAAA,CAAAI,MAAA,iBACtC;IAAAJ,EAAA,CAAAK,YAAA,EAAI;IAKRL,EAAA,CAAAE,cAAA,cAAuB;IAGnBF,EAAA,CAAAG,SAAA,6BAA+C;IACjDH,EAAA,CAAAK,YAAA,EAAK;IAELL,EAAA,CAAAE,cAAA,cAA8B;IAKxBF,EAAA,CAAAG,SAAA,aAA4B;IAC9BH,EAAA,CAAAK,YAAA,EAAM;IACNL,EAAA,CAAAE,cAAA,gBAAiC;IAAAF,EAAA,CAAAI,MAAA,IAA0B;IAAAJ,EAAA,CAAAK,YAAA,EAAO;IAEpEL,EAAA,CAAAE,cAAA,cAA4C;IAGtCF,EAAA,CAAAI,MAAA,IACA;IAAAJ,EAAA,CAAAG,SAAA,UAAI;IACJH,EAAA,CAAAE,cAAA,iBAA0B;IAAAF,EAAA,CAAAI,MAAA,IAAkC;;IAAAJ,EAAA,CAAAK,YAAA,EAAQ;IAGxEL,EAAA,CAAAE,cAAA,UAAI;IAAAF,EAAA,CAAAG,SAAA,cAA6B;IAAAH,EAAA,CAAAK,YAAA,EAAK;IACtCL,EAAA,CAAAE,cAAA,UAAI;IACuBF,EAAA,CAAAO,UAAA,mBAAAa,gDAAA;MAAApB,EAAA,CAAAS,aAAA,CAAAC,GAAA;MAAA,MAAAW,MAAA,GAAArB,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAQ,MAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACpDtB,EAAA,CAAAG,SAAA,aAAsC;IAAAH,EAAA,CAAAI,MAAA,yBACxC;IAAAJ,EAAA,CAAAK,YAAA,EAAI;IAENL,EAAA,CAAAE,cAAA,UAAI;IAEAF,EAAA,CAAAG,SAAA,aAA+B;IAAAH,EAAA,CAAAI,MAAA,sBACjC;IAAAJ,EAAA,CAAAK,YAAA,EAAI;IAENL,EAAA,CAAAE,cAAA,UAAI;IAEAF,EAAA,CAAAG,SAAA,aAA0C;IAAAH,EAAA,CAAAI,MAAA,uBAC5C;IAAAJ,EAAA,CAAAK,YAAA,EAAI;IAENL,EAAA,CAAAE,cAAA,UAAI;IAAAF,EAAA,CAAAG,SAAA,cAA6B;IAAAH,EAAA,CAAAK,YAAA,EAAK;IACtCL,EAAA,CAAAE,cAAA,UAAI;IACmCF,EAAA,CAAAO,UAAA,mBAAAgB,gDAAA;MAAAvB,EAAA,CAAAS,aAAA,CAAAC,GAAA;MAAA,MAAAc,MAAA,GAAAxB,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAW,MAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IACrDzB,EAAA,CAAAG,SAAA,aAA0C;IAAAH,EAAA,CAAAI,MAAA,iBAC5C;IAAAJ,EAAA,CAAAK,YAAA,EAAI;;;;IApFKL,EAAA,CAAA0B,SAAA,IAAoC;IAApC1B,EAAA,CAAA2B,UAAA,SAAAC,MAAA,CAAAC,WAAA,CAAAC,IAAA,eAAoC;IAmBpC9B,EAAA,CAAA0B,SAAA,GAAmC;IAAnC1B,EAAA,CAAA2B,UAAA,SAAAC,MAAA,CAAAC,WAAA,CAAAC,IAAA,cAAmC;IAmCb9B,EAAA,CAAA0B,SAAA,IAA0B;IAA1B1B,EAAA,CAAA+B,iBAAA,CAAAH,MAAA,CAAAC,WAAA,CAAAG,QAAA,CAA0B;IAKvDhC,EAAA,CAAA0B,SAAA,GACA;IADA1B,EAAA,CAAAiC,kBAAA,MAAAL,MAAA,CAAAC,WAAA,CAAAG,QAAA,MACA;IAC0BhC,EAAA,CAAA0B,SAAA,GAAkC;IAAlC1B,EAAA,CAAA+B,iBAAA,CAAA/B,EAAA,CAAAkC,WAAA,QAAAN,MAAA,CAAAC,WAAA,CAAAC,IAAA,EAAkC;;;;;IAsC5E9B,EAAA,CAAAE,cAAA,iBAAyE;IAGnEF,EAAA,CAAAI,MAAA,0DACA;IAAAJ,EAAA,CAAAE,cAAA,YAAyC;IAAAF,EAAA,CAAAI,MAAA,qBAAc;IAAAJ,EAAA,CAAAK,YAAA,EAAI;IAACL,EAAA,CAAAI,MAAA,UAC5D;IAAAJ,EAAA,CAAAE,cAAA,YAAyC;IAAAF,EAAA,CAAAI,MAAA,uBAAgB;IAAAJ,EAAA,CAAAK,YAAA,EAAI;;;ADpHnE,OAAM,MAAO8B,YAAY;EAKvBC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IANhB,KAAAC,KAAK,GAAG,eAAe;IACvB,KAAAV,WAAW,GAAgB,IAAI;IAC/B,KAAAW,cAAc,GAAG,KAAK;EAKnB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACJ,WAAW,CAACK,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACf,WAAW,GAAGe,IAAI;MACvB,IAAI,CAACC,0BAA0B,EAAE;IACnC,CAAC,CAAC;IAEF;IACA,IAAI,CAACP,MAAM,CAACQ,MAAM,CACfC,IAAI,CAAChD,MAAM,CAAEiD,KAAY,IAA6BA,KAAK,YAAYlD,aAAa,CAAC,CAAC,CACtF6C,SAAS,CAAEK,KAAoB,IAAI;MAClC,IAAI,CAACH,0BAA0B,EAAE;IACnC,CAAC,CAAC;EACN;EAEQA,0BAA0BA,CAAA;IAChC,MAAMI,WAAW,GAAG,IAAI,CAACX,MAAM,CAACY,GAAG,CAACC,QAAQ,CAAC,OAAO,CAAC;IACrD,MAAMC,eAAe,GAAG,IAAI,CAACf,WAAW,CAACe,eAAe,EAAE;IAC1D,IAAI,CAACZ,cAAc,GAAG,CAACS,WAAW,IAAIG,eAAe,IAAI,CAAC,CAAC,IAAI,CAACvB,WAAW;EAC7E;EAEAJ,MAAMA,CAAA;IACJ,IAAI,CAACY,WAAW,CAACZ,MAAM,EAAE;EAC3B;EAEAH,iBAAiBA,CAAA;IACf,IAAI,CAACgB,MAAM,CAACe,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEAvC,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACe,WAAW,EAAEC,IAAI,KAAK,QAAQ,EAAE;MACvC,IAAI,CAACQ,MAAM,CAACe,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;KAC5C,MAAM,IAAI,IAAI,CAACxB,WAAW,EAAEC,IAAI,KAAK,SAAS,EAAE;MAC/C,IAAI,CAACQ,MAAM,CAACe,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;;EAEhD;;;uBA7CWlB,YAAY,EAAAnC,EAAA,CAAAsD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxD,EAAA,CAAAsD,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAZvB,YAAY;MAAAwB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVzBjE,EAAA,CAAAiB,UAAA,IAAAkD,2BAAA,kBAiHM;UAGNnE,EAAA,CAAAE,cAAA,cAA+E;UAC7EF,EAAA,CAAAG,SAAA,oBAA+B;UACjCH,EAAA,CAAAK,YAAA,EAAO;UAGPL,EAAA,CAAAiB,UAAA,IAAAmD,8BAAA,oBAQS;;;UAjIHpE,EAAA,CAAA2B,UAAA,SAAAuC,GAAA,CAAA1B,cAAA,IAAA0B,GAAA,CAAArC,WAAA,CAAmC;UAoHd7B,EAAA,CAAA0B,SAAA,GAAmD;UAAnD1B,EAAA,CAAAqE,WAAA,gBAAAH,GAAA,CAAA1B,cAAA,IAAA0B,GAAA,CAAArC,WAAA,CAAmD;UAKrE7B,EAAA,CAAA0B,SAAA,GAAoB;UAApB1B,EAAA,CAAA2B,UAAA,SAAAuC,GAAA,CAAA1B,cAAA,CAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}