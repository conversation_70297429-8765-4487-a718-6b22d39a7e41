{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/auth.service\";\nimport * as i2 from \"../../core/services/user.service\";\nimport * as i3 from \"../../core/services/appointment.service\";\nimport * as i4 from \"../../core/services/chat.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"../../chat/chat-list/chat-list.component\";\nimport * as i8 from \"../../chat/chat-window/chat-window.component\";\nconst _c0 = [\"chatWindow\"];\nfunction DoctorDashboardComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"span\", 19);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 20);\n    i0.ɵɵtext(5, \"Loading your dashboard...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DoctorDashboardComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"i\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction DoctorDashboardComponent_div_3_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \\u2022 \", ctx_r4.currentUser == null ? null : ctx_r4.currentUser.affiliation, \"\");\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 34)(2, \"div\", 39)(3, \"div\", 59)(4, \"div\", 60);\n    i0.ɵɵelement(5, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 61)(7, \"h6\", 62);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"h3\", 63);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"small\");\n    i0.ɵɵelement(12, \"i\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const stat_r11 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassMapInterpolate2(\"bi bi-\", stat_r11.icon, \" fs-2 \", stat_r11.color, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(stat_r11.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r11.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r5.getChangeClass(stat_r11.changeType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r5.getChangeIcon(stat_r11.changeType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", stat_r11.change, \" \");\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelement(1, \"i\", 65);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No appointments scheduled for today\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_27_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const appointment_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \\u2022 \", appointment_r12.reasonForVisit, \"\");\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 59)(2, \"div\", 60)(3, \"div\", 67);\n    i0.ɵɵelement(4, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h6\", 63);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 68);\n    i0.ɵɵelement(9, \"i\", 69);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementStart(11, \"span\", 70)(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"small\", 71);\n    i0.ɵɵtext(16);\n    i0.ɵɵtemplate(17, DoctorDashboardComponent_div_3_div_27_span_17_Template, 2, 1, \"span\", 3);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 72)(19, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_div_27_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r16);\n      const appointment_r12 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.navigateTo(\"/appointments/\" + appointment_r12.id));\n    });\n    i0.ɵɵelement(20, \"i\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const appointment_r12 = ctx.$implicit;\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMapInterpolate2(\"bi bi-\", ctx_r7.getAppointmentTypeIcon(appointment_r12.type), \" \", ctx_r7.getAppointmentTypeClass(appointment_r12.type), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(appointment_r12.patient == null ? null : appointment_r12.patient.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", appointment_r12.startTime, \" - \", appointment_r12.endTime, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r7.getStatusBadgeClass(appointment_r12.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 16, appointment_r12.status), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", appointment_r12.type === \"VIDEO_CALL\" ? \"Video Consultation\" : \"In-Person Visit\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", appointment_r12.reasonForVisit);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMapInterpolate1(\"bi bi-\", appointment_r12.type === \"VIDEO_CALL\" ? \"camera-video\" : \"person\", \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", appointment_r12.type === \"VIDEO_CALL\" ? \"Join Call\" : \"View Details\", \" \");\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_35_hr_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"hr\", 79);\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75)(2, \"div\", 60)(3, \"div\", 76);\n    i0.ɵɵelement(4, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 61)(6, \"h6\", 77);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 68);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"small\", 71);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(12, DoctorDashboardComponent_div_3_div_35_hr_12_Template, 1, 0, \"hr\", 78);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const activity_r17 = ctx.$implicit;\n    const isLast_r18 = ctx.last;\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMapInterpolate2(\"bi bi-\", activity_r17.icon, \" \", activity_r17.color, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r17.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r17.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r17.time);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !isLast_r18);\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelement(1, \"i\", 80);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No messages yet\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_48_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind3(2, 2, chat_r20.lastMessage.content, 0, 50), \"\", chat_r20.lastMessage.content.length > 50 ? \"...\" : \"\", \" \");\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_48_small_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r20 = i0.ɵɵnextContext().$implicit;\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r22.formatChatTime(chat_r20.lastMessage.createdAt), \" \");\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_48_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 87);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", chat_r20.unreadCount, \" \");\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"div\", 59)(2, \"div\", 60);\n    i0.ɵɵelement(3, \"img\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\")(5, \"h6\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, DoctorDashboardComponent_div_3_div_48_p_7_Template, 3, 6, \"p\", 83);\n    i0.ɵɵtemplate(8, DoctorDashboardComponent_div_3_div_48_small_8_Template, 2, 1, \"small\", 84);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 72);\n    i0.ɵɵtemplate(10, DoctorDashboardComponent_div_3_div_48_span_10_Template, 2, 1, \"span\", 85);\n    i0.ɵɵelementStart(11, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_div_48_Template_button_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r28);\n      const chat_r20 = restoredCtx.$implicit;\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.openChat(chat_r20));\n    });\n    i0.ɵɵelement(12, \"i\", 86);\n    i0.ɵɵtext(13, \"Reply \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const chat_r20 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", chat_r20.patient.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", chat_r20.patient.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(chat_r20.patient.fullName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", chat_r20.lastMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", chat_r20.lastMessage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", chat_r20.unreadCount > 0);\n  }\n}\nfunction DoctorDashboardComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 23)(2, \"div\", 24)(3, \"div\", 25)(4, \"div\")(5, \"h1\", 26);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 27);\n    i0.ɵɵelement(8, \"i\", 28);\n    i0.ɵɵtext(9);\n    i0.ɵɵtemplate(10, DoctorDashboardComponent_div_3_span_10_Template, 2, 1, \"span\", 3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.refreshData());\n    });\n    i0.ɵɵelement(12, \"i\", 30);\n    i0.ɵɵtext(13, \"Refresh \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"div\", 23);\n    i0.ɵɵtemplate(15, DoctorDashboardComponent_div_3_div_15_Template, 14, 11, \"div\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 32)(17, \"div\", 33)(18, \"div\", 34)(19, \"div\", 35)(20, \"h6\", 36);\n    i0.ɵɵelement(21, \"i\", 37);\n    i0.ɵɵtext(22, \"Today's Schedule \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.navigateTo(\"/appointments\"));\n    });\n    i0.ɵɵtext(24, \" View All \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 39);\n    i0.ɵɵtemplate(26, DoctorDashboardComponent_div_3_div_26_Template, 4, 0, \"div\", 40);\n    i0.ɵɵtemplate(27, DoctorDashboardComponent_div_3_div_27_Template, 22, 18, \"div\", 41);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 42)(29, \"div\", 34)(30, \"div\", 43)(31, \"h6\", 36);\n    i0.ɵɵelement(32, \"i\", 44);\n    i0.ɵɵtext(33, \"Recent Activities \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 39);\n    i0.ɵɵtemplate(35, DoctorDashboardComponent_div_3_div_35_Template, 13, 8, \"div\", 45);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(36, \"div\", 23)(37, \"div\", 24)(38, \"div\", 46)(39, \"div\", 35)(40, \"h6\", 36);\n    i0.ɵɵelement(41, \"i\", 9);\n    i0.ɵɵtext(42, \"Recent Messages \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_43_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.openChatModal());\n    });\n    i0.ɵɵelement(44, \"i\", 47);\n    i0.ɵɵtext(45, \"View All Messages \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 39);\n    i0.ɵɵtemplate(47, DoctorDashboardComponent_div_3_div_47_Template, 4, 0, \"div\", 40);\n    i0.ɵɵtemplate(48, DoctorDashboardComponent_div_3_div_48_Template, 14, 6, \"div\", 48);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(49, \"div\", 32)(50, \"div\", 24)(51, \"div\", 46)(52, \"div\", 43)(53, \"h6\", 36);\n    i0.ɵɵelement(54, \"i\", 49);\n    i0.ɵɵtext(55, \"Quick Actions \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"div\", 39)(57, \"div\", 32)(58, \"div\", 50)(59, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_59_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.navigateTo(\"/patients\"));\n    });\n    i0.ɵɵelement(60, \"i\", 52);\n    i0.ɵɵelementStart(61, \"span\");\n    i0.ɵɵtext(62, \"Manage Patients\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(63, \"div\", 50)(64, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_64_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.navigateTo(\"/appointments\"));\n    });\n    i0.ɵɵelement(65, \"i\", 54);\n    i0.ɵɵelementStart(66, \"span\");\n    i0.ɵɵtext(67, \"Schedule Appointment\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(68, \"div\", 50)(69, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_69_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.navigateTo(\"/chat\"));\n    });\n    i0.ɵɵelement(70, \"i\", 56);\n    i0.ɵɵelementStart(71, \"span\");\n    i0.ɵɵtext(72, \"Messages\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(73, \"div\", 50)(74, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_74_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.navigateTo(\"/reports\"));\n    });\n    i0.ɵɵelement(75, \"i\", 58);\n    i0.ɵɵelementStart(76, \"span\");\n    i0.ɵɵtext(77, \"View Reports\");\n    i0.ɵɵelementEnd()()()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.getGreeting(), \", Dr. \", ctx_r2.currentUser == null ? null : ctx_r2.currentUser.fullName, \"!\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r2.currentUser == null ? null : ctx_r2.currentUser.specialization) || \"General Practice\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentUser == null ? null : ctx_r2.currentUser.affiliation);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.dashboardStats);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.realTodayAppointments.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.realTodayAppointments);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.recentActivities);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.recentChats.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.recentChats);\n  }\n}\nexport class DoctorDashboardComponent {\n  constructor(authService, userService, appointmentService, chatService, router) {\n    this.authService = authService;\n    this.userService = userService;\n    this.appointmentService = appointmentService;\n    this.chatService = chatService;\n    this.router = router;\n    this.currentUser = null;\n    this.isLoading = true;\n    this.error = '';\n    this.realTodayAppointments = [];\n    this.recentChats = [];\n    this.dashboardStats = [{\n      title: 'Total Patients',\n      value: '156',\n      change: '+12%',\n      changeType: 'increase',\n      icon: 'people',\n      color: 'text-primary'\n    }, {\n      title: 'Today\\'s Appointments',\n      value: '8',\n      change: '+2',\n      changeType: 'increase',\n      icon: 'calendar-check',\n      color: 'text-success'\n    }, {\n      title: 'Pending Reviews',\n      value: '5',\n      change: '-3',\n      changeType: 'decrease',\n      icon: 'clipboard-check',\n      color: 'text-warning'\n    }, {\n      title: 'Messages',\n      value: '12',\n      change: '+4',\n      changeType: 'increase',\n      icon: 'chat-dots',\n      color: 'text-info'\n    }];\n    this.todayAppointments = [{\n      id: 1,\n      patientName: 'John Smith',\n      time: '09:00 AM',\n      type: 'VIDEO_CALL',\n      status: 'SCHEDULED'\n    }, {\n      id: 2,\n      patientName: 'Sarah Johnson',\n      time: '10:30 AM',\n      type: 'IN_PERSON',\n      status: 'SCHEDULED'\n    }, {\n      id: 3,\n      patientName: 'Michael Brown',\n      time: '02:00 PM',\n      type: 'VIDEO_CALL',\n      status: 'SCHEDULED'\n    }, {\n      id: 4,\n      patientName: 'Emily Davis',\n      time: '03:30 PM',\n      type: 'IN_PERSON',\n      status: 'SCHEDULED'\n    }];\n    this.recentActivities = [{\n      title: 'New Patient Registration',\n      description: 'Alice Wilson registered as a new patient',\n      time: '30 minutes ago',\n      icon: 'person-plus',\n      color: 'text-success'\n    }, {\n      title: 'Appointment Rescheduled',\n      description: 'Tom Anderson moved appointment to tomorrow',\n      time: '1 hour ago',\n      icon: 'calendar-event',\n      color: 'text-warning'\n    }, {\n      title: 'Message Received',\n      description: 'New message from Lisa Parker about medication',\n      time: '2 hours ago',\n      icon: 'envelope',\n      color: 'text-info'\n    }, {\n      title: 'Lab Results Available',\n      description: 'Blood test results for David Miller are ready',\n      time: '3 hours ago',\n      icon: 'file-medical',\n      color: 'text-primary'\n    }];\n  }\n  ngOnInit() {\n    this.loadUserData();\n    this.loadTodayAppointments();\n    this.loadRecentChats();\n  }\n  loadUserData() {\n    this.authService.currentUser$.subscribe({\n      next: user => {\n        this.currentUser = user;\n        this.isLoading = false;\n      },\n      error: error => {\n        this.error = 'Failed to load user data';\n        this.isLoading = false;\n      }\n    });\n  }\n  loadTodayAppointments() {\n    this.appointmentService.getTodayAppointments().subscribe({\n      next: appointments => {\n        this.realTodayAppointments = appointments;\n        this.updateDashboardStats(appointments.length);\n      },\n      error: error => {\n        console.error('Failed to load today appointments:', error);\n      }\n    });\n  }\n  updateDashboardStats(todayCount) {\n    // Update the \"Today's Appointments\" stat with real data\n    const todayStatIndex = this.dashboardStats.findIndex(stat => stat.title === \"Today's Appointments\");\n    if (todayStatIndex !== -1) {\n      this.dashboardStats[todayStatIndex].value = todayCount.toString();\n    }\n  }\n  getGreeting() {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Good morning';\n    if (hour < 18) return 'Good afternoon';\n    return 'Good evening';\n  }\n  getChangeClass(changeType) {\n    switch (changeType) {\n      case 'increase':\n        return 'text-success';\n      case 'decrease':\n        return 'text-danger';\n      default:\n        return 'text-muted';\n    }\n  }\n  getChangeIcon(changeType) {\n    switch (changeType) {\n      case 'increase':\n        return 'bi-arrow-up';\n      case 'decrease':\n        return 'bi-arrow-down';\n      default:\n        return 'bi-dash';\n    }\n  }\n  getAppointmentTypeIcon(type) {\n    return type === 'VIDEO_CALL' ? 'camera-video' : 'geo-alt';\n  }\n  getAppointmentTypeClass(type) {\n    return type === 'VIDEO_CALL' ? 'text-primary' : 'text-success';\n  }\n  getStatusBadgeClass(status) {\n    switch (status) {\n      case 'SCHEDULED':\n        return 'badge bg-primary';\n      case 'COMPLETED':\n        return 'badge bg-success';\n      case 'CANCELLED':\n        return 'badge bg-danger';\n      default:\n        return 'badge bg-secondary';\n    }\n  }\n  startAppointment(appointment) {\n    if (appointment.type === 'VIDEO_CALL') {\n      this.router.navigate(['/video-call'], {\n        queryParams: {\n          appointmentId: appointment.id\n        }\n      });\n    } else {\n      // For in-person appointments, navigate to patient details or appointment view\n      this.router.navigate(['/appointments', appointment.id]);\n    }\n  }\n  navigateTo(route) {\n    this.router.navigate([route]);\n  }\n  refreshData() {\n    this.isLoading = true;\n    this.loadTodayAppointments();\n    this.loadRecentChats();\n    setTimeout(() => {\n      this.isLoading = false;\n    }, 1000);\n  }\n  loadRecentChats() {\n    this.chatService.getUserChats().subscribe({\n      next: chats => {\n        this.recentChats = chats.slice(0, 3); // Show only recent 3 chats\n      },\n\n      error: error => {\n        console.error('Failed to load chats:', error);\n      }\n    });\n  }\n  openChatModal() {\n    const modalElement = document.getElementById('chatModal');\n    if (modalElement) {\n      const modal = new window.bootstrap.Modal(modalElement);\n      modal.show();\n    }\n  }\n  openChat(chat) {\n    this.openChatModal();\n    // Wait for modal to open, then load chat\n    setTimeout(() => {\n      this.onChatSelected(chat);\n    }, 300);\n  }\n  onChatSelected(chat) {\n    if (this.chatWindow) {\n      this.chatWindow.loadChat(chat);\n    }\n  }\n  formatChatTime(dateString) {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n    if (diffInHours < 1) {\n      return 'Just now';\n    } else if (diffInHours < 24) {\n      return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else {\n      return date.toLocaleDateString();\n    }\n  }\n  static {\n    this.ɵfac = function DoctorDashboardComponent_Factory(t) {\n      return new (t || DoctorDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.AppointmentService), i0.ɵɵdirectiveInject(i4.ChatService), i0.ɵɵdirectiveInject(i5.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DoctorDashboardComponent,\n      selectors: [[\"app-doctor-dashboard\"]],\n      viewQuery: function DoctorDashboardComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chatWindow = _t.first);\n        }\n      },\n      decls: 19,\n      vars: 3,\n      consts: [[1, \"container-fluid\", \"py-4\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [4, \"ngIf\"], [\"id\", \"chatModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"chatModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-xl\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"chatModalLabel\", 1, \"modal-title\"], [1, \"bi\", \"bi-chat-dots\", \"me-2\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"btn-close\"], [1, \"modal-body\", \"p-0\", 2, \"height\", \"600px\"], [1, \"row\", \"h-100\", \"g-0\"], [1, \"col-md-4\", \"border-end\"], [3, \"chatSelected\"], [1, \"col-md-8\"], [\"chatWindow\", \"\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-3\", \"text-muted\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"bi\", \"bi-exclamation-triangle\", \"me-2\"], [1, \"row\", \"mb-4\"], [1, \"col-12\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"h3\", \"mb-1\"], [1, \"text-muted\", \"mb-0\"], [1, \"bi\", \"bi-hospital\", \"me-2\"], [1, \"btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\", \"me-2\"], [\"class\", \"col-md-3 col-sm-6 mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\"], [1, \"col-md-8\", \"mb-4\"], [1, \"card\", \"h-100\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"bi\", \"bi-calendar-day\", \"me-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"card-body\"], [\"class\", \"text-center py-4 text-muted\", 4, \"ngIf\"], [\"class\", \"appointment-item d-flex align-items-center justify-content-between p-3 mb-2 rounded border\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\", \"mb-4\"], [1, \"card-header\"], [1, \"bi\", \"bi-activity\", \"me-2\"], [\"class\", \"activity-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"card\"], [1, \"bi\", \"bi-chat-plus\", \"me-1\"], [\"class\", \"chat-preview d-flex align-items-center justify-content-between p-3 mb-2 rounded border\", 4, \"ngFor\", \"ngForOf\"], [1, \"bi\", \"bi-lightning\", \"me-2\"], [1, \"col-md-3\", \"col-sm-6\", \"mb-3\"], [1, \"btn\", \"btn-outline-primary\", \"w-100\", \"py-3\", 3, \"click\"], [1, \"bi\", \"bi-people\", \"d-block\", \"fs-4\", \"mb-2\"], [1, \"btn\", \"btn-outline-success\", \"w-100\", \"py-3\", 3, \"click\"], [1, \"bi\", \"bi-calendar-plus\", \"d-block\", \"fs-4\", \"mb-2\"], [1, \"btn\", \"btn-outline-info\", \"w-100\", \"py-3\", 3, \"click\"], [1, \"bi\", \"bi-chat-dots\", \"d-block\", \"fs-4\", \"mb-2\"], [1, \"btn\", \"btn-outline-warning\", \"w-100\", \"py-3\", 3, \"click\"], [1, \"bi\", \"bi-graph-up\", \"d-block\", \"fs-4\", \"mb-2\"], [1, \"d-flex\", \"align-items-center\"], [1, \"flex-shrink-0\", \"me-3\"], [1, \"flex-grow-1\"], [1, \"card-title\", \"text-muted\", \"mb-1\"], [1, \"mb-1\"], [1, \"text-center\", \"py-4\", \"text-muted\"], [1, \"bi\", \"bi-calendar-x\", \"display-6\", \"mb-3\"], [1, \"appointment-item\", \"d-flex\", \"align-items-center\", \"justify-content-between\", \"p-3\", \"mb-2\", \"rounded\", \"border\"], [1, \"rounded-circle\", \"bg-light\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"45px\", \"height\", \"45px\"], [1, \"mb-1\", \"text-muted\", \"small\"], [1, \"bi\", \"bi-clock\", \"me-1\"], [1, \"ms-2\"], [1, \"text-muted\"], [1, \"flex-shrink-0\"], [1, \"btn\", \"btn-sm\", \"btn-primary\", 3, \"click\"], [1, \"activity-item\"], [1, \"d-flex\", \"align-items-start\"], [1, \"rounded-circle\", \"bg-light\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"35px\", \"height\", \"35px\"], [1, \"mb-1\", \"small\"], [\"class\", \"my-3\", 4, \"ngIf\"], [1, \"my-3\"], [1, \"bi\", \"bi-chat-square-text\", \"display-6\", \"mb-3\"], [1, \"chat-preview\", \"d-flex\", \"align-items-center\", \"justify-content-between\", \"p-3\", \"mb-2\", \"rounded\", \"border\"], [1, \"rounded-circle\", 2, \"width\", \"45px\", \"height\", \"45px\", \"object-fit\", \"cover\", 3, \"src\", \"alt\"], [\"class\", \"mb-1 text-muted small\", 4, \"ngIf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [\"class\", \"badge bg-primary rounded-pill me-2\", 4, \"ngIf\"], [1, \"bi\", \"bi-chat\", \"me-1\"], [1, \"badge\", \"bg-primary\", \"rounded-pill\", \"me-2\"]],\n      template: function DoctorDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, DoctorDashboardComponent_div_1_Template, 6, 0, \"div\", 1);\n          i0.ɵɵtemplate(2, DoctorDashboardComponent_div_2_Template, 3, 1, \"div\", 2);\n          i0.ɵɵtemplate(3, DoctorDashboardComponent_div_3_Template, 78, 10, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"h5\", 8);\n          i0.ɵɵelement(9, \"i\", 9);\n          i0.ɵɵtext(10, \"Messages \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(11, \"button\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 11)(13, \"div\", 12)(14, \"div\", 13)(15, \"app-chat-list\", 14);\n          i0.ɵɵlistener(\"chatSelected\", function DoctorDashboardComponent_Template_app_chat_list_chatSelected_15_listener($event) {\n            return ctx.onChatSelected($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 15);\n          i0.ɵɵelement(17, \"app-chat-window\", null, 16);\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i7.ChatListComponent, i8.ChatWindowComponent, i6.SlicePipe, i6.TitleCasePipe],\n      styles: [\".appointment-item[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  transition: all 0.3s ease;\\n}\\n\\n.appointment-item[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n  border-color: #dee2e6;\\n}\\n\\n.activity-item[_ngcontent-%COMP%] {\\n  padding-bottom: 1rem;\\n}\\n\\n.activity-item[_ngcontent-%COMP%]:last-child {\\n  padding-bottom: 0;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  border-bottom: 1px solid #e9ecef;\\n  font-weight: 600;\\n}\\n\\n.btn-outline-primary[_ngcontent-%COMP%], .btn-outline-success[_ngcontent-%COMP%], .btn-outline-info[_ngcontent-%COMP%], .btn-outline-warning[_ngcontent-%COMP%] {\\n  border-width: 2px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-outline-primary[_ngcontent-%COMP%]:hover, .btn-outline-success[_ngcontent-%COMP%]:hover, .btn-outline-info[_ngcontent-%COMP%]:hover, .btn-outline-warning[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n\\n.text-primary[_ngcontent-%COMP%] {\\n  color: #0d6efd !important;\\n}\\n\\n.text-success[_ngcontent-%COMP%] {\\n  color: #198754 !important;\\n}\\n\\n.text-warning[_ngcontent-%COMP%] {\\n  color: #ffc107 !important;\\n}\\n\\n.text-info[_ngcontent-%COMP%] {\\n  color: #0dcaf0 !important;\\n}\\n\\n.text-danger[_ngcontent-%COMP%] {\\n  color: #dc3545 !important;\\n}\\n\\n.spinner-border[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .container-fluid[_ngcontent-%COMP%] {\\n    padding-left: 1rem;\\n    padding-right: 1rem;\\n  }\\n  .card-body[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .appointment-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start !important;\\n  }\\n  .appointment-item[_ngcontent-%COMP%]   .flex-shrink-0[_ngcontent-%COMP%]:last-child {\\n    margin-top: 1rem;\\n    align-self: stretch;\\n  }\\n  .appointment-item[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ctx_r4", "currentUser", "affiliation", "ɵɵclassMapInterpolate2", "stat_r11", "icon", "color", "ɵɵtextInterpolate", "title", "value", "ɵɵclassMap", "ctx_r5", "getChangeClass", "changeType", "getChangeIcon", "change", "appointment_r12", "reasonForVisit", "ɵɵtemplate", "DoctorDashboardComponent_div_3_div_27_span_17_Template", "ɵɵlistener", "DoctorDashboardComponent_div_3_div_27_Template_button_click_19_listener", "restoredCtx", "ɵɵrestoreView", "_r16", "$implicit", "ctx_r15", "ɵɵnextContext", "ɵɵresetView", "navigateTo", "id", "ctx_r7", "getAppointmentTypeIcon", "type", "getAppointmentTypeClass", "patient", "fullName", "ɵɵtextInterpolate2", "startTime", "endTime", "getStatusBadgeClass", "status", "ɵɵpipeBind1", "ɵɵproperty", "ɵɵclassMapInterpolate1", "DoctorDashboardComponent_div_3_div_35_hr_12_Template", "activity_r17", "description", "time", "isLast_r18", "ɵɵpipeBind3", "chat_r20", "lastMessage", "content", "length", "ctx_r22", "formatChatTime", "createdAt", "unreadCount", "DoctorDashboardComponent_div_3_div_48_p_7_Template", "DoctorDashboardComponent_div_3_div_48_small_8_Template", "DoctorDashboardComponent_div_3_div_48_span_10_Template", "DoctorDashboardComponent_div_3_div_48_Template_button_click_11_listener", "_r28", "ctx_r27", "openChat", "avatar", "ɵɵsanitizeUrl", "DoctorDashboardComponent_div_3_span_10_Template", "DoctorDashboardComponent_div_3_Template_button_click_11_listener", "_r30", "ctx_r29", "refreshData", "DoctorDashboardComponent_div_3_div_15_Template", "DoctorDashboardComponent_div_3_Template_button_click_23_listener", "ctx_r31", "DoctorDashboardComponent_div_3_div_26_Template", "DoctorDashboardComponent_div_3_div_27_Template", "DoctorDashboardComponent_div_3_div_35_Template", "DoctorDashboardComponent_div_3_Template_button_click_43_listener", "ctx_r32", "openChatModal", "DoctorDashboardComponent_div_3_div_47_Template", "DoctorDashboardComponent_div_3_div_48_Template", "DoctorDashboardComponent_div_3_Template_button_click_59_listener", "ctx_r33", "DoctorDashboardComponent_div_3_Template_button_click_64_listener", "ctx_r34", "DoctorDashboardComponent_div_3_Template_button_click_69_listener", "ctx_r35", "DoctorDashboardComponent_div_3_Template_button_click_74_listener", "ctx_r36", "ctx_r2", "getGreeting", "specialization", "dashboardStats", "realTodayAppointments", "recentActivities", "recentChats", "DoctorDashboardComponent", "constructor", "authService", "userService", "appointmentService", "chatService", "router", "isLoading", "todayAppointments", "patientName", "ngOnInit", "loadUserData", "loadTodayAppointments", "loadRecentChats", "currentUser$", "subscribe", "next", "user", "getTodayAppointments", "appointments", "updateDashboardStats", "console", "todayCount", "todayStatIndex", "findIndex", "stat", "toString", "hour", "Date", "getHours", "startAppointment", "appointment", "navigate", "queryParams", "appointmentId", "route", "setTimeout", "getUserChats", "chats", "slice", "modalElement", "document", "getElementById", "modal", "window", "bootstrap", "Modal", "show", "chat", "onChatSelected", "chatWindow", "loadChat", "dateString", "date", "now", "diffInHours", "getTime", "toLocaleTimeString", "minute", "toLocaleDateString", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "UserService", "i3", "AppointmentService", "i4", "ChatService", "i5", "Router", "selectors", "viewQuery", "DoctorDashboardComponent_Query", "rf", "ctx", "DoctorDashboardComponent_div_1_Template", "DoctorDashboardComponent_div_2_Template", "DoctorDashboardComponent_div_3_Template", "DoctorDashboardComponent_Template_app_chat_list_chatSelected_15_listener", "$event"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/doctor/dashboard/dashboard.component.ts", "/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/doctor/dashboard/dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../core/services/auth.service';\nimport { UserService } from '../../core/services/user.service';\nimport { AppointmentService } from '../../core/services/appointment.service';\nimport { ChatService } from '../../core/services/chat.service';\nimport { User } from '../../core/models/user.model';\nimport { Appointment } from '../../core/models/appointment.model';\nimport { Chat } from '../../core/models/chat.model';\nimport { ChatWindowComponent } from '../../chat/chat-window/chat-window.component';\n\ninterface DashboardStats {\n  title: string;\n  value: string;\n  change: string;\n  changeType: 'increase' | 'decrease' | 'neutral';\n  icon: string;\n  color: string;\n}\n\ninterface TodayAppointment {\n  id: number;\n  patientName: string;\n  time: string;\n  type: 'VIDEO_CALL' | 'IN_PERSON';\n  status: 'SCHEDULED' | 'COMPLETED' | 'CANCELLED';\n}\n\ninterface RecentActivity {\n  title: string;\n  description: string;\n  time: string;\n  icon: string;\n  color: string;\n}\n\n@Component({\n  selector: 'app-doctor-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class DoctorDashboardComponent implements OnInit {\n  @ViewChild('chatWindow') chatWindow!: ChatWindowComponent;\n\n  currentUser: User | null = null;\n  isLoading = true;\n  error = '';\n  realTodayAppointments: Appointment[] = [];\n  recentChats: Chat[] = [];\n\n  dashboardStats: DashboardStats[] = [\n    {\n      title: 'Total Patients',\n      value: '156',\n      change: '+12%',\n      changeType: 'increase',\n      icon: 'people',\n      color: 'text-primary'\n    },\n    {\n      title: 'Today\\'s Appointments',\n      value: '8',\n      change: '+2',\n      changeType: 'increase',\n      icon: 'calendar-check',\n      color: 'text-success'\n    },\n    {\n      title: 'Pending Reviews',\n      value: '5',\n      change: '-3',\n      changeType: 'decrease',\n      icon: 'clipboard-check',\n      color: 'text-warning'\n    },\n    {\n      title: 'Messages',\n      value: '12',\n      change: '+4',\n      changeType: 'increase',\n      icon: 'chat-dots',\n      color: 'text-info'\n    }\n  ];\n\n  todayAppointments: TodayAppointment[] = [\n    {\n      id: 1,\n      patientName: 'John Smith',\n      time: '09:00 AM',\n      type: 'VIDEO_CALL',\n      status: 'SCHEDULED'\n    },\n    {\n      id: 2,\n      patientName: 'Sarah Johnson',\n      time: '10:30 AM',\n      type: 'IN_PERSON',\n      status: 'SCHEDULED'\n    },\n    {\n      id: 3,\n      patientName: 'Michael Brown',\n      time: '02:00 PM',\n      type: 'VIDEO_CALL',\n      status: 'SCHEDULED'\n    },\n    {\n      id: 4,\n      patientName: 'Emily Davis',\n      time: '03:30 PM',\n      type: 'IN_PERSON',\n      status: 'SCHEDULED'\n    }\n  ];\n\n  recentActivities: RecentActivity[] = [\n    {\n      title: 'New Patient Registration',\n      description: 'Alice Wilson registered as a new patient',\n      time: '30 minutes ago',\n      icon: 'person-plus',\n      color: 'text-success'\n    },\n    {\n      title: 'Appointment Rescheduled',\n      description: 'Tom Anderson moved appointment to tomorrow',\n      time: '1 hour ago',\n      icon: 'calendar-event',\n      color: 'text-warning'\n    },\n    {\n      title: 'Message Received',\n      description: 'New message from Lisa Parker about medication',\n      time: '2 hours ago',\n      icon: 'envelope',\n      color: 'text-info'\n    },\n    {\n      title: 'Lab Results Available',\n      description: 'Blood test results for David Miller are ready',\n      time: '3 hours ago',\n      icon: 'file-medical',\n      color: 'text-primary'\n    }\n  ];\n\n  constructor(\n    private authService: AuthService,\n    private userService: UserService,\n    private appointmentService: AppointmentService,\n    private chatService: ChatService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.loadUserData();\n    this.loadTodayAppointments();\n    this.loadRecentChats();\n  }\n\n  private loadUserData(): void {\n    this.authService.currentUser$.subscribe({\n      next: (user) => {\n        this.currentUser = user;\n        this.isLoading = false;\n      },\n      error: (error) => {\n        this.error = 'Failed to load user data';\n        this.isLoading = false;\n      }\n    });\n  }\n\n  private loadTodayAppointments(): void {\n    this.appointmentService.getTodayAppointments().subscribe({\n      next: (appointments) => {\n        this.realTodayAppointments = appointments;\n        this.updateDashboardStats(appointments.length);\n      },\n      error: (error) => {\n        console.error('Failed to load today appointments:', error);\n      }\n    });\n  }\n\n  private updateDashboardStats(todayCount: number): void {\n    // Update the \"Today's Appointments\" stat with real data\n    const todayStatIndex = this.dashboardStats.findIndex(stat => stat.title === \"Today's Appointments\");\n    if (todayStatIndex !== -1) {\n      this.dashboardStats[todayStatIndex].value = todayCount.toString();\n    }\n  }\n\n  getGreeting(): string {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Good morning';\n    if (hour < 18) return 'Good afternoon';\n    return 'Good evening';\n  }\n\n  getChangeClass(changeType: string): string {\n    switch (changeType) {\n      case 'increase': return 'text-success';\n      case 'decrease': return 'text-danger';\n      default: return 'text-muted';\n    }\n  }\n\n  getChangeIcon(changeType: string): string {\n    switch (changeType) {\n      case 'increase': return 'bi-arrow-up';\n      case 'decrease': return 'bi-arrow-down';\n      default: return 'bi-dash';\n    }\n  }\n\n  getAppointmentTypeIcon(type: string): string {\n    return type === 'VIDEO_CALL' ? 'camera-video' : 'geo-alt';\n  }\n\n  getAppointmentTypeClass(type: string): string {\n    return type === 'VIDEO_CALL' ? 'text-primary' : 'text-success';\n  }\n\n  getStatusBadgeClass(status: string): string {\n    switch (status) {\n      case 'SCHEDULED': return 'badge bg-primary';\n      case 'COMPLETED': return 'badge bg-success';\n      case 'CANCELLED': return 'badge bg-danger';\n      default: return 'badge bg-secondary';\n    }\n  }\n\n  startAppointment(appointment: TodayAppointment): void {\n    if (appointment.type === 'VIDEO_CALL') {\n      this.router.navigate(['/video-call'], { \n        queryParams: { appointmentId: appointment.id } \n      });\n    } else {\n      // For in-person appointments, navigate to patient details or appointment view\n      this.router.navigate(['/appointments', appointment.id]);\n    }\n  }\n\n  navigateTo(route: string): void {\n    this.router.navigate([route]);\n  }\n\n  refreshData(): void {\n    this.isLoading = true;\n    this.loadTodayAppointments();\n    this.loadRecentChats();\n    setTimeout(() => {\n      this.isLoading = false;\n    }, 1000);\n  }\n\n  private loadRecentChats(): void {\n    this.chatService.getUserChats().subscribe({\n      next: (chats) => {\n        this.recentChats = chats.slice(0, 3); // Show only recent 3 chats\n      },\n      error: (error) => {\n        console.error('Failed to load chats:', error);\n      }\n    });\n  }\n\n  openChatModal(): void {\n    const modalElement = document.getElementById('chatModal');\n    if (modalElement) {\n      const modal = new (window as any).bootstrap.Modal(modalElement);\n      modal.show();\n    }\n  }\n\n  openChat(chat: Chat): void {\n    this.openChatModal();\n    // Wait for modal to open, then load chat\n    setTimeout(() => {\n      this.onChatSelected(chat);\n    }, 300);\n  }\n\n  onChatSelected(chat: Chat): void {\n    if (this.chatWindow) {\n      this.chatWindow.loadChat(chat);\n    }\n  }\n\n  formatChatTime(dateString: string): string {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n\n    if (diffInHours < 1) {\n      return 'Just now';\n    } else if (diffInHours < 24) {\n      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n    } else {\n      return date.toLocaleDateString();\n    }\n  }\n}\n", "<div class=\"container-fluid py-4\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"text-center py-5\">\n    <div class=\"spinner-border text-primary\" role=\"status\">\n      <span class=\"visually-hidden\">Loading...</span>\n    </div>\n    <p class=\"mt-3 text-muted\">Loading your dashboard...</p>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"alert alert-danger\" role=\"alert\">\n    <i class=\"bi bi-exclamation-triangle me-2\"></i>\n    {{ error }}\n  </div>\n\n  <!-- Dashboard Content -->\n  <div *ngIf=\"!isLoading && !error\">\n    <!-- Welcome Header -->\n    <div class=\"row mb-4\">\n      <div class=\"col-12\">\n        <div class=\"d-flex justify-content-between align-items-center\">\n          <div>\n            <h1 class=\"h3 mb-1\">{{ getGreeting() }}, Dr. {{ currentUser?.fullName }}!</h1>\n            <p class=\"text-muted mb-0\">\n              <i class=\"bi bi-hospital me-2\"></i>{{ currentUser?.specialization || 'General Practice' }}\n              <span *ngIf=\"currentUser?.affiliation\"> • {{ currentUser?.affiliation }}</span>\n            </p>\n          </div>\n          <button class=\"btn btn-outline-primary\" (click)=\"refreshData()\">\n            <i class=\"bi bi-arrow-clockwise me-2\"></i>Refresh\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Dashboard Statistics -->\n    <div class=\"row mb-4\">\n      <div class=\"col-md-3 col-sm-6 mb-3\" *ngFor=\"let stat of dashboardStats\">\n        <div class=\"card h-100\">\n          <div class=\"card-body\">\n            <div class=\"d-flex align-items-center\">\n              <div class=\"flex-shrink-0 me-3\">\n                <i class=\"bi bi-{{ stat.icon }} fs-2 {{ stat.color }}\"></i>\n              </div>\n              <div class=\"flex-grow-1\">\n                <h6 class=\"card-title text-muted mb-1\">{{ stat.title }}</h6>\n                <h3 class=\"mb-1\">{{ stat.value }}</h3>\n                <small [class]=\"getChangeClass(stat.changeType)\">\n                  <i [class]=\"getChangeIcon(stat.changeType)\"></i>\n                  {{ stat.change }}\n                </small>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Today's Schedule & Recent Activities -->\n    <div class=\"row\">\n      <!-- Today's Appointments -->\n      <div class=\"col-md-8 mb-4\">\n        <div class=\"card h-100\">\n          <div class=\"card-header d-flex justify-content-between align-items-center\">\n            <h6 class=\"mb-0\">\n              <i class=\"bi bi-calendar-day me-2\"></i>Today's Schedule\n            </h6>\n            <button class=\"btn btn-sm btn-outline-primary\" (click)=\"navigateTo('/appointments')\">\n              View All\n            </button>\n          </div>\n          <div class=\"card-body\">\n            <div *ngIf=\"realTodayAppointments.length === 0\" class=\"text-center py-4 text-muted\">\n              <i class=\"bi bi-calendar-x display-6 mb-3\"></i>\n              <p>No appointments scheduled for today</p>\n            </div>\n\n            <div *ngFor=\"let appointment of realTodayAppointments\" class=\"appointment-item d-flex align-items-center justify-content-between p-3 mb-2 rounded border\">\n              <div class=\"d-flex align-items-center\">\n                <div class=\"flex-shrink-0 me-3\">\n                  <div class=\"rounded-circle bg-light d-flex align-items-center justify-content-center\" \n                       style=\"width: 45px; height: 45px;\">\n                    <i class=\"bi bi-{{ getAppointmentTypeIcon(appointment.type) }} {{ getAppointmentTypeClass(appointment.type) }}\"></i>\n                  </div>\n                </div>\n                <div>\n                  <h6 class=\"mb-1\">{{ appointment.patient?.fullName }}</h6>\n                  <p class=\"mb-1 text-muted small\">\n                    <i class=\"bi bi-clock me-1\"></i>{{ appointment.startTime }} - {{ appointment.endTime }}\n                    <span class=\"ms-2\">\n                      <span [class]=\"getStatusBadgeClass(appointment.status)\">\n                        {{ appointment.status | titlecase }}\n                      </span>\n                    </span>\n                  </p>\n                  <small class=\"text-muted\">\n                    {{ appointment.type === 'VIDEO_CALL' ? 'Video Consultation' : 'In-Person Visit' }}\n                    <span *ngIf=\"appointment.reasonForVisit\"> • {{ appointment.reasonForVisit }}</span>\n                  </small>\n                </div>\n              </div>\n              <div class=\"flex-shrink-0\">\n                <button\n                  class=\"btn btn-sm btn-primary\"\n                  (click)=\"navigateTo('/appointments/' + appointment.id)\"\n                >\n                  <i class=\"bi bi-{{ appointment.type === 'VIDEO_CALL' ? 'camera-video' : 'person' }} me-1\"></i>\n                  {{ appointment.type === 'VIDEO_CALL' ? 'Join Call' : 'View Details' }}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Recent Activities -->\n      <div class=\"col-md-4 mb-4\">\n        <div class=\"card h-100\">\n          <div class=\"card-header\">\n            <h6 class=\"mb-0\">\n              <i class=\"bi bi-activity me-2\"></i>Recent Activities\n            </h6>\n          </div>\n          <div class=\"card-body\">\n            <div *ngFor=\"let activity of recentActivities; last as isLast\" class=\"activity-item\">\n              <div class=\"d-flex align-items-start\">\n                <div class=\"flex-shrink-0 me-3\">\n                  <div class=\"rounded-circle bg-light d-flex align-items-center justify-content-center\" \n                       style=\"width: 35px; height: 35px;\">\n                    <i class=\"bi bi-{{ activity.icon }} {{ activity.color }}\"></i>\n                  </div>\n                </div>\n                <div class=\"flex-grow-1\">\n                  <h6 class=\"mb-1 small\">{{ activity.title }}</h6>\n                  <p class=\"mb-1 text-muted small\">{{ activity.description }}</p>\n                  <small class=\"text-muted\">{{ activity.time }}</small>\n                </div>\n              </div>\n              <hr *ngIf=\"!isLast\" class=\"my-3\">\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Messages Section -->\n    <div class=\"row mb-4\">\n      <div class=\"col-12\">\n        <div class=\"card\">\n          <div class=\"card-header d-flex justify-content-between align-items-center\">\n            <h6 class=\"mb-0\">\n              <i class=\"bi bi-chat-dots me-2\"></i>Recent Messages\n            </h6>\n            <button class=\"btn btn-sm btn-outline-primary\" (click)=\"openChatModal()\">\n              <i class=\"bi bi-chat-plus me-1\"></i>View All Messages\n            </button>\n          </div>\n          <div class=\"card-body\">\n            <div *ngIf=\"recentChats.length === 0\" class=\"text-center py-4 text-muted\">\n              <i class=\"bi bi-chat-square-text display-6 mb-3\"></i>\n              <p>No messages yet</p>\n            </div>\n\n            <div *ngFor=\"let chat of recentChats\" class=\"chat-preview d-flex align-items-center justify-content-between p-3 mb-2 rounded border\">\n              <div class=\"d-flex align-items-center\">\n                <div class=\"flex-shrink-0 me-3\">\n                  <img\n                    [src]=\"chat.patient.avatar || '/assets/images/default-avatar.png'\"\n                    [alt]=\"chat.patient.fullName\"\n                    class=\"rounded-circle\"\n                    style=\"width: 45px; height: 45px; object-fit: cover;\">\n                </div>\n                <div>\n                  <h6 class=\"mb-1\">{{ chat.patient.fullName }}</h6>\n                  <p class=\"mb-1 text-muted small\" *ngIf=\"chat.lastMessage\">\n                    {{ chat.lastMessage.content | slice:0:50 }}{{ chat.lastMessage.content.length > 50 ? '...' : '' }}\n                  </p>\n                  <small class=\"text-muted\" *ngIf=\"chat.lastMessage\">\n                    {{ formatChatTime(chat.lastMessage.createdAt) }}\n                  </small>\n                </div>\n              </div>\n              <div class=\"flex-shrink-0\">\n                <span *ngIf=\"chat.unreadCount > 0\" class=\"badge bg-primary rounded-pill me-2\">\n                  {{ chat.unreadCount }}\n                </span>\n                <button\n                  class=\"btn btn-sm btn-outline-primary\"\n                  (click)=\"openChat(chat)\"\n                >\n                  <i class=\"bi bi-chat me-1\"></i>Reply\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Quick Actions -->\n    <div class=\"row\">\n      <div class=\"col-12\">\n        <div class=\"card\">\n          <div class=\"card-header\">\n            <h6 class=\"mb-0\">\n              <i class=\"bi bi-lightning me-2\"></i>Quick Actions\n            </h6>\n          </div>\n          <div class=\"card-body\">\n            <div class=\"row\">\n              <div class=\"col-md-3 col-sm-6 mb-3\">\n                <button class=\"btn btn-outline-primary w-100 py-3\" (click)=\"navigateTo('/patients')\">\n                  <i class=\"bi bi-people d-block fs-4 mb-2\"></i>\n                  <span>Manage Patients</span>\n                </button>\n              </div>\n              <div class=\"col-md-3 col-sm-6 mb-3\">\n                <button class=\"btn btn-outline-success w-100 py-3\" (click)=\"navigateTo('/appointments')\">\n                  <i class=\"bi bi-calendar-plus d-block fs-4 mb-2\"></i>\n                  <span>Schedule Appointment</span>\n                </button>\n              </div>\n              <div class=\"col-md-3 col-sm-6 mb-3\">\n                <button class=\"btn btn-outline-info w-100 py-3\" (click)=\"navigateTo('/chat')\">\n                  <i class=\"bi bi-chat-dots d-block fs-4 mb-2\"></i>\n                  <span>Messages</span>\n                </button>\n              </div>\n              <div class=\"col-md-3 col-sm-6 mb-3\">\n                <button class=\"btn btn-outline-warning w-100 py-3\" (click)=\"navigateTo('/reports')\">\n                  <i class=\"bi bi-graph-up d-block fs-4 mb-2\"></i>\n                  <span>View Reports</span>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Chat Modal -->\n<div class=\"modal fade\" id=\"chatModal\" tabindex=\"-1\" aria-labelledby=\"chatModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog modal-xl\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\" id=\"chatModalLabel\">\n          <i class=\"bi bi-chat-dots me-2\"></i>Messages\n        </h5>\n        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\n      </div>\n      <div class=\"modal-body p-0\" style=\"height: 600px;\">\n        <div class=\"row h-100 g-0\">\n          <div class=\"col-md-4 border-end\">\n            <app-chat-list (chatSelected)=\"onChatSelected($event)\"></app-chat-list>\n          </div>\n          <div class=\"col-md-8\">\n            <app-chat-window #chatWindow></app-chat-window>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;;;;;ICEEA,EAAA,CAAAC,cAAA,cAAgD;IAEdD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEjDH,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAI1DH,EAAA,CAAAC,cAAA,cAAyE;IACvED,EAAA,CAAAI,SAAA,YAA+C;IAC/CJ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAYYR,EAAA,CAAAC,cAAA,WAAuC;IAACD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAvCH,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,kBAAA,aAAAG,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,WAAA,KAAgC;;;;;IAYhFX,EAAA,CAAAC,cAAA,cAAwE;IAK9DD,EAAA,CAAAI,SAAA,QAA2D;IAC7DJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyB;IACgBD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5DH,EAAA,CAAAC,cAAA,aAAiB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAI,SAAA,SAAgD;IAChDJ,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IARLH,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAY,sBAAA,WAAAC,QAAA,CAAAC,IAAA,YAAAD,QAAA,CAAAE,KAAA,KAAmD;IAGff,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAgB,iBAAA,CAAAH,QAAA,CAAAI,KAAA,CAAgB;IACtCjB,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAgB,iBAAA,CAAAH,QAAA,CAAAK,KAAA,CAAgB;IAC1BlB,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAmB,UAAA,CAAAC,MAAA,CAAAC,cAAA,CAAAR,QAAA,CAAAS,UAAA,EAAyC;IAC3CtB,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAmB,UAAA,CAAAC,MAAA,CAAAG,aAAA,CAAAV,QAAA,CAAAS,UAAA,EAAwC;IAC3CtB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAO,QAAA,CAAAW,MAAA,MACF;;;;;IAsBJxB,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAI,SAAA,YAA+C;IAC/CJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,0CAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAuBpCH,EAAA,CAAAC,cAAA,WAAyC;IAACD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzCH,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,kBAAA,aAAAmB,eAAA,CAAAC,cAAA,KAAkC;;;;;;IApBpF1B,EAAA,CAAAC,cAAA,cAA0J;IAKlJD,EAAA,CAAAI,SAAA,QAAoH;IACtHJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,UAAK;IACcD,EAAA,CAAAE,MAAA,GAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzDH,EAAA,CAAAC,cAAA,YAAiC;IAC/BD,EAAA,CAAAI,SAAA,YAAgC;IAAAJ,EAAA,CAAAE,MAAA,IAChC;IAAAF,EAAA,CAAAC,cAAA,gBAAmB;IAEfD,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGXH,EAAA,CAAAC,cAAA,iBAA0B;IACxBD,EAAA,CAAAE,MAAA,IACA;IAAAF,EAAA,CAAA2B,UAAA,KAAAC,sDAAA,kBAAmF;IACrF5B,EAAA,CAAAG,YAAA,EAAQ;IAGZH,EAAA,CAAAC,cAAA,eAA2B;IAGvBD,EAAA,CAAA6B,UAAA,mBAAAC,wEAAA;MAAA,MAAAC,WAAA,GAAA/B,EAAA,CAAAgC,aAAA,CAAAC,IAAA;MAAA,MAAAR,eAAA,GAAAM,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAnC,EAAA,CAAAoC,aAAA;MAAA,OAASpC,EAAA,CAAAqC,WAAA,CAAAF,OAAA,CAAAG,UAAA,CAAW,gBAAgB,GAAAb,eAAA,CAAAc,EAAA,CAAkB;IAAA,EAAC;IAEvDvC,EAAA,CAAAI,SAAA,SAA8F;IAC9FJ,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IA1BFH,EAAA,CAAAK,SAAA,GAA4G;IAA5GL,EAAA,CAAAY,sBAAA,WAAA4B,MAAA,CAAAC,sBAAA,CAAAhB,eAAA,CAAAiB,IAAA,QAAAF,MAAA,CAAAG,uBAAA,CAAAlB,eAAA,CAAAiB,IAAA,MAA4G;IAIhG1C,EAAA,CAAAK,SAAA,GAAmC;IAAnCL,EAAA,CAAAgB,iBAAA,CAAAS,eAAA,CAAAmB,OAAA,kBAAAnB,eAAA,CAAAmB,OAAA,CAAAC,QAAA,CAAmC;IAElB7C,EAAA,CAAAK,SAAA,GAChC;IADgCL,EAAA,CAAA8C,kBAAA,KAAArB,eAAA,CAAAsB,SAAA,SAAAtB,eAAA,CAAAuB,OAAA,MAChC;IACQhD,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAmB,UAAA,CAAAqB,MAAA,CAAAS,mBAAA,CAAAxB,eAAA,CAAAyB,MAAA,EAAiD;IACrDlD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAmD,WAAA,SAAA1B,eAAA,CAAAyB,MAAA,OACF;IAIFlD,EAAA,CAAAK,SAAA,GACA;IADAL,EAAA,CAAAM,kBAAA,MAAAmB,eAAA,CAAAiB,IAAA,kEACA;IAAO1C,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAoD,UAAA,SAAA3B,eAAA,CAAAC,cAAA,CAAgC;IAStC1B,EAAA,CAAAK,SAAA,GAAsF;IAAtFL,EAAA,CAAAqD,sBAAA,WAAA5B,eAAA,CAAAiB,IAAA,uDAAsF;IACzF1C,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAmB,eAAA,CAAAiB,IAAA,sDACF;;;;;IA8BF1C,EAAA,CAAAI,SAAA,aAAiC;;;;;IAdnCJ,EAAA,CAAAC,cAAA,cAAqF;IAK7ED,EAAA,CAAAI,SAAA,QAA8D;IAChEJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAAyB;IACAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/DH,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAGzDH,EAAA,CAAA2B,UAAA,KAAA2B,oDAAA,iBAAiC;IACnCtD,EAAA,CAAAG,YAAA,EAAM;;;;;IAVKH,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAAY,sBAAA,WAAA2C,YAAA,CAAAzC,IAAA,OAAAyC,YAAA,CAAAxC,KAAA,KAAsD;IAIpCf,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAgB,iBAAA,CAAAuC,YAAA,CAAAtC,KAAA,CAAoB;IACVjB,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAgB,iBAAA,CAAAuC,YAAA,CAAAC,WAAA,CAA0B;IACjCxD,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAgB,iBAAA,CAAAuC,YAAA,CAAAE,IAAA,CAAmB;IAG5CzD,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAoD,UAAA,UAAAM,UAAA,CAAa;;;;;IAoBpB1D,EAAA,CAAAC,cAAA,cAA0E;IACxED,EAAA,CAAAI,SAAA,YAAqD;IACrDJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAclBH,EAAA,CAAAC,cAAA,YAA0D;IACxDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAA8C,kBAAA,MAAA9C,EAAA,CAAA2D,WAAA,OAAAC,QAAA,CAAAC,WAAA,CAAAC,OAAA,cAAAF,QAAA,CAAAC,WAAA,CAAAC,OAAA,CAAAC,MAAA,wBACF;;;;;IACA/D,EAAA,CAAAC,cAAA,gBAAmD;IACjDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IADNH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA0D,OAAA,CAAAC,cAAA,CAAAL,QAAA,CAAAC,WAAA,CAAAK,SAAA,OACF;;;;;IAIFlE,EAAA,CAAAC,cAAA,eAA8E;IAC5ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAsD,QAAA,CAAAO,WAAA,MACF;;;;;;IAtBJnE,EAAA,CAAAC,cAAA,cAAqI;IAG/HD,EAAA,CAAAI,SAAA,cAIwD;IAC1DJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,UAAK;IACcD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAA2B,UAAA,IAAAyC,kDAAA,gBAEI;IACJpE,EAAA,CAAA2B,UAAA,IAAA0C,sDAAA,oBAEQ;IACVrE,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAA2B,UAAA,KAAA2C,sDAAA,mBAEO;IACPtE,EAAA,CAAAC,cAAA,kBAGC;IADCD,EAAA,CAAA6B,UAAA,mBAAA0C,wEAAA;MAAA,MAAAxC,WAAA,GAAA/B,EAAA,CAAAgC,aAAA,CAAAwC,IAAA;MAAA,MAAAZ,QAAA,GAAA7B,WAAA,CAAAG,SAAA;MAAA,MAAAuC,OAAA,GAAAzE,EAAA,CAAAoC,aAAA;MAAA,OAASpC,EAAA,CAAAqC,WAAA,CAAAoC,OAAA,CAAAC,QAAA,CAAAd,QAAA,CAAc;IAAA,EAAC;IAExB5D,EAAA,CAAAI,SAAA,aAA+B;IAAAJ,EAAA,CAAAE,MAAA,cACjC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAxBLH,EAAA,CAAAK,SAAA,GAAkE;IAAlEL,EAAA,CAAAoD,UAAA,QAAAQ,QAAA,CAAAhB,OAAA,CAAA+B,MAAA,yCAAA3E,EAAA,CAAA4E,aAAA,CAAkE,QAAAhB,QAAA,CAAAhB,OAAA,CAAAC,QAAA;IAMnD7C,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAgB,iBAAA,CAAA4C,QAAA,CAAAhB,OAAA,CAAAC,QAAA,CAA2B;IACV7C,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAoD,UAAA,SAAAQ,QAAA,CAAAC,WAAA,CAAsB;IAG7B7D,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAoD,UAAA,SAAAQ,QAAA,CAAAC,WAAA,CAAsB;IAM5C7D,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAoD,UAAA,SAAAQ,QAAA,CAAAO,WAAA,KAA0B;;;;;;IAvK/CnE,EAAA,CAAAC,cAAA,UAAkC;IAMJD,EAAA,CAAAE,MAAA,GAAqD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9EH,EAAA,CAAAC,cAAA,YAA2B;IACzBD,EAAA,CAAAI,SAAA,YAAmC;IAAAJ,EAAA,CAAAE,MAAA,GACnC;IAAAF,EAAA,CAAA2B,UAAA,KAAAkD,+CAAA,kBAA+E;IACjF7E,EAAA,CAAAG,YAAA,EAAI;IAENH,EAAA,CAAAC,cAAA,kBAAgE;IAAxBD,EAAA,CAAA6B,UAAA,mBAAAiD,iEAAA;MAAA9E,EAAA,CAAAgC,aAAA,CAAA+C,IAAA;MAAA,MAAAC,OAAA,GAAAhF,EAAA,CAAAoC,aAAA;MAAA,OAASpC,EAAA,CAAAqC,WAAA,CAAA2C,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAC7DjF,EAAA,CAAAI,SAAA,aAA0C;IAAAJ,EAAA,CAAAE,MAAA,gBAC5C;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAMfH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAA2B,UAAA,KAAAuD,8CAAA,oBAkBM;IACRlF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAiB;IAMPD,EAAA,CAAAI,SAAA,aAAuC;IAAAJ,EAAA,CAAAE,MAAA,yBACzC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,kBAAqF;IAAtCD,EAAA,CAAA6B,UAAA,mBAAAsD,iEAAA;MAAAnF,EAAA,CAAAgC,aAAA,CAAA+C,IAAA;MAAA,MAAAK,OAAA,GAAApF,EAAA,CAAAoC,aAAA;MAAA,OAASpC,EAAA,CAAAqC,WAAA,CAAA+C,OAAA,CAAA9C,UAAA,CAAW,eAAe,CAAC;IAAA,EAAC;IAClFtC,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAA2B,UAAA,KAAA0D,8CAAA,kBAGM;IAENrF,EAAA,CAAA2B,UAAA,KAAA2D,8CAAA,oBAiCM;IACRtF,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,eAA2B;IAInBD,EAAA,CAAAI,SAAA,aAAmC;IAAAJ,EAAA,CAAAE,MAAA,0BACrC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAA2B,UAAA,KAAA4D,8CAAA,mBAeM;IACRvF,EAAA,CAAAG,YAAA,EAAM;IAMZH,EAAA,CAAAC,cAAA,eAAsB;IAKZD,EAAA,CAAAI,SAAA,YAAoC;IAAAJ,EAAA,CAAAE,MAAA,wBACtC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,kBAAyE;IAA1BD,EAAA,CAAA6B,UAAA,mBAAA2D,iEAAA;MAAAxF,EAAA,CAAAgC,aAAA,CAAA+C,IAAA;MAAA,MAAAU,OAAA,GAAAzF,EAAA,CAAAoC,aAAA;MAAA,OAASpC,EAAA,CAAAqC,WAAA,CAAAoD,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IACtE1F,EAAA,CAAAI,SAAA,aAAoC;IAAAJ,EAAA,CAAAE,MAAA,0BACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAA2B,UAAA,KAAAgE,8CAAA,kBAGM;IAEN3F,EAAA,CAAA2B,UAAA,KAAAiE,8CAAA,mBA8BM;IACR5F,EAAA,CAAAG,YAAA,EAAM;IAMZH,EAAA,CAAAC,cAAA,eAAiB;IAKPD,EAAA,CAAAI,SAAA,aAAoC;IAAAJ,EAAA,CAAAE,MAAA,sBACtC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAC,cAAA,eAAuB;IAGkCD,EAAA,CAAA6B,UAAA,mBAAAgE,iEAAA;MAAA7F,EAAA,CAAAgC,aAAA,CAAA+C,IAAA;MAAA,MAAAe,OAAA,GAAA9F,EAAA,CAAAoC,aAAA;MAAA,OAASpC,EAAA,CAAAqC,WAAA,CAAAyD,OAAA,CAAAxD,UAAA,CAAW,WAAW,CAAC;IAAA,EAAC;IAClFtC,EAAA,CAAAI,SAAA,aAA8C;IAC9CJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGhCH,EAAA,CAAAC,cAAA,eAAoC;IACiBD,EAAA,CAAA6B,UAAA,mBAAAkE,iEAAA;MAAA/F,EAAA,CAAAgC,aAAA,CAAA+C,IAAA;MAAA,MAAAiB,OAAA,GAAAhG,EAAA,CAAAoC,aAAA;MAAA,OAASpC,EAAA,CAAAqC,WAAA,CAAA2D,OAAA,CAAA1D,UAAA,CAAW,eAAe,CAAC;IAAA,EAAC;IACtFtC,EAAA,CAAAI,SAAA,aAAqD;IACrDJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGrCH,EAAA,CAAAC,cAAA,eAAoC;IACcD,EAAA,CAAA6B,UAAA,mBAAAoE,iEAAA;MAAAjG,EAAA,CAAAgC,aAAA,CAAA+C,IAAA;MAAA,MAAAmB,OAAA,GAAAlG,EAAA,CAAAoC,aAAA;MAAA,OAASpC,EAAA,CAAAqC,WAAA,CAAA6D,OAAA,CAAA5D,UAAA,CAAW,OAAO,CAAC;IAAA,EAAC;IAC3EtC,EAAA,CAAAI,SAAA,aAAiD;IACjDJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGzBH,EAAA,CAAAC,cAAA,eAAoC;IACiBD,EAAA,CAAA6B,UAAA,mBAAAsE,iEAAA;MAAAnG,EAAA,CAAAgC,aAAA,CAAA+C,IAAA;MAAA,MAAAqB,OAAA,GAAApG,EAAA,CAAAoC,aAAA;MAAA,OAASpC,EAAA,CAAAqC,WAAA,CAAA+D,OAAA,CAAA9D,UAAA,CAAW,UAAU,CAAC;IAAA,EAAC;IACjFtC,EAAA,CAAAI,SAAA,aAAgD;IAChDJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAjNXH,EAAA,CAAAK,SAAA,GAAqD;IAArDL,EAAA,CAAA8C,kBAAA,KAAAuD,MAAA,CAAAC,WAAA,cAAAD,MAAA,CAAA3F,WAAA,kBAAA2F,MAAA,CAAA3F,WAAA,CAAAmC,QAAA,MAAqD;IAEpC7C,EAAA,CAAAK,SAAA,GACnC;IADmCL,EAAA,CAAAM,kBAAA,MAAA+F,MAAA,CAAA3F,WAAA,kBAAA2F,MAAA,CAAA3F,WAAA,CAAA6F,cAAA,6BACnC;IAAOvG,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAoD,UAAA,SAAAiD,MAAA,CAAA3F,WAAA,kBAAA2F,MAAA,CAAA3F,WAAA,CAAAC,WAAA,CAA8B;IAYQX,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAoD,UAAA,YAAAiD,MAAA,CAAAG,cAAA,CAAiB;IAmC1DxG,EAAA,CAAAK,SAAA,IAAwC;IAAxCL,EAAA,CAAAoD,UAAA,SAAAiD,MAAA,CAAAI,qBAAA,CAAA1C,MAAA,OAAwC;IAKjB/D,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAoD,UAAA,YAAAiD,MAAA,CAAAI,qBAAA,CAAwB;IA+C3BzG,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAoD,UAAA,YAAAiD,MAAA,CAAAK,gBAAA,CAAqB;IAkCzC1G,EAAA,CAAAK,SAAA,IAA8B;IAA9BL,EAAA,CAAAoD,UAAA,SAAAiD,MAAA,CAAAM,WAAA,CAAA5C,MAAA,OAA8B;IAKd/D,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAAoD,UAAA,YAAAiD,MAAA,CAAAM,WAAA,CAAc;;;AD1HhD,OAAM,MAAOC,wBAAwB;EA0GnCC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,kBAAsC,EACtCC,WAAwB,EACxBC,MAAc;IAJd,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IA5GhB,KAAAxG,WAAW,GAAgB,IAAI;IAC/B,KAAAyG,SAAS,GAAG,IAAI;IAChB,KAAA3G,KAAK,GAAG,EAAE;IACV,KAAAiG,qBAAqB,GAAkB,EAAE;IACzC,KAAAE,WAAW,GAAW,EAAE;IAExB,KAAAH,cAAc,GAAqB,CACjC;MACEvF,KAAK,EAAE,gBAAgB;MACvBC,KAAK,EAAE,KAAK;MACZM,MAAM,EAAE,MAAM;MACdF,UAAU,EAAE,UAAU;MACtBR,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,uBAAuB;MAC9BC,KAAK,EAAE,GAAG;MACVM,MAAM,EAAE,IAAI;MACZF,UAAU,EAAE,UAAU;MACtBR,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,iBAAiB;MACxBC,KAAK,EAAE,GAAG;MACVM,MAAM,EAAE,IAAI;MACZF,UAAU,EAAE,UAAU;MACtBR,IAAI,EAAE,iBAAiB;MACvBC,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,UAAU;MACjBC,KAAK,EAAE,IAAI;MACXM,MAAM,EAAE,IAAI;MACZF,UAAU,EAAE,UAAU;MACtBR,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE;KACR,CACF;IAED,KAAAqG,iBAAiB,GAAuB,CACtC;MACE7E,EAAE,EAAE,CAAC;MACL8E,WAAW,EAAE,YAAY;MACzB5D,IAAI,EAAE,UAAU;MAChBf,IAAI,EAAE,YAAY;MAClBQ,MAAM,EAAE;KACT,EACD;MACEX,EAAE,EAAE,CAAC;MACL8E,WAAW,EAAE,eAAe;MAC5B5D,IAAI,EAAE,UAAU;MAChBf,IAAI,EAAE,WAAW;MACjBQ,MAAM,EAAE;KACT,EACD;MACEX,EAAE,EAAE,CAAC;MACL8E,WAAW,EAAE,eAAe;MAC5B5D,IAAI,EAAE,UAAU;MAChBf,IAAI,EAAE,YAAY;MAClBQ,MAAM,EAAE;KACT,EACD;MACEX,EAAE,EAAE,CAAC;MACL8E,WAAW,EAAE,aAAa;MAC1B5D,IAAI,EAAE,UAAU;MAChBf,IAAI,EAAE,WAAW;MACjBQ,MAAM,EAAE;KACT,CACF;IAED,KAAAwD,gBAAgB,GAAqB,CACnC;MACEzF,KAAK,EAAE,0BAA0B;MACjCuC,WAAW,EAAE,0CAA0C;MACvDC,IAAI,EAAE,gBAAgB;MACtB3C,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,yBAAyB;MAChCuC,WAAW,EAAE,4CAA4C;MACzDC,IAAI,EAAE,YAAY;MAClB3C,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,kBAAkB;MACzBuC,WAAW,EAAE,+CAA+C;MAC5DC,IAAI,EAAE,aAAa;MACnB3C,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,uBAAuB;MAC9BuC,WAAW,EAAE,+CAA+C;MAC5DC,IAAI,EAAE,aAAa;MACnB3C,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE;KACR,CACF;EAQE;EAEHuG,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,qBAAqB,EAAE;IAC5B,IAAI,CAACC,eAAe,EAAE;EACxB;EAEQF,YAAYA,CAAA;IAClB,IAAI,CAACT,WAAW,CAACY,YAAY,CAACC,SAAS,CAAC;MACtCC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACnH,WAAW,GAAGmH,IAAI;QACvB,IAAI,CAACV,SAAS,GAAG,KAAK;MACxB,CAAC;MACD3G,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,0BAA0B;QACvC,IAAI,CAAC2G,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEQK,qBAAqBA,CAAA;IAC3B,IAAI,CAACR,kBAAkB,CAACc,oBAAoB,EAAE,CAACH,SAAS,CAAC;MACvDC,IAAI,EAAGG,YAAY,IAAI;QACrB,IAAI,CAACtB,qBAAqB,GAAGsB,YAAY;QACzC,IAAI,CAACC,oBAAoB,CAACD,YAAY,CAAChE,MAAM,CAAC;MAChD,CAAC;MACDvD,KAAK,EAAGA,KAAK,IAAI;QACfyH,OAAO,CAACzH,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC5D;KACD,CAAC;EACJ;EAEQwH,oBAAoBA,CAACE,UAAkB;IAC7C;IACA,MAAMC,cAAc,GAAG,IAAI,CAAC3B,cAAc,CAAC4B,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACpH,KAAK,KAAK,sBAAsB,CAAC;IACnG,IAAIkH,cAAc,KAAK,CAAC,CAAC,EAAE;MACzB,IAAI,CAAC3B,cAAc,CAAC2B,cAAc,CAAC,CAACjH,KAAK,GAAGgH,UAAU,CAACI,QAAQ,EAAE;;EAErE;EAEAhC,WAAWA,CAAA;IACT,MAAMiC,IAAI,GAAG,IAAIC,IAAI,EAAE,CAACC,QAAQ,EAAE;IAClC,IAAIF,IAAI,GAAG,EAAE,EAAE,OAAO,cAAc;IACpC,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,cAAc;EACvB;EAEAlH,cAAcA,CAACC,UAAkB;IAC/B,QAAQA,UAAU;MAChB,KAAK,UAAU;QAAE,OAAO,cAAc;MACtC,KAAK,UAAU;QAAE,OAAO,aAAa;MACrC;QAAS,OAAO,YAAY;;EAEhC;EAEAC,aAAaA,CAACD,UAAkB;IAC9B,QAAQA,UAAU;MAChB,KAAK,UAAU;QAAE,OAAO,aAAa;MACrC,KAAK,UAAU;QAAE,OAAO,eAAe;MACvC;QAAS,OAAO,SAAS;;EAE7B;EAEAmB,sBAAsBA,CAACC,IAAY;IACjC,OAAOA,IAAI,KAAK,YAAY,GAAG,cAAc,GAAG,SAAS;EAC3D;EAEAC,uBAAuBA,CAACD,IAAY;IAClC,OAAOA,IAAI,KAAK,YAAY,GAAG,cAAc,GAAG,cAAc;EAChE;EAEAO,mBAAmBA,CAACC,MAAc;IAChC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,kBAAkB;MAC3C,KAAK,WAAW;QAAE,OAAO,kBAAkB;MAC3C,KAAK,WAAW;QAAE,OAAO,iBAAiB;MAC1C;QAAS,OAAO,oBAAoB;;EAExC;EAEAwF,gBAAgBA,CAACC,WAA6B;IAC5C,IAAIA,WAAW,CAACjG,IAAI,KAAK,YAAY,EAAE;MACrC,IAAI,CAACwE,MAAM,CAAC0B,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;QACpCC,WAAW,EAAE;UAAEC,aAAa,EAAEH,WAAW,CAACpG;QAAE;OAC7C,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAAC2E,MAAM,CAAC0B,QAAQ,CAAC,CAAC,eAAe,EAAED,WAAW,CAACpG,EAAE,CAAC,CAAC;;EAE3D;EAEAD,UAAUA,CAACyG,KAAa;IACtB,IAAI,CAAC7B,MAAM,CAAC0B,QAAQ,CAAC,CAACG,KAAK,CAAC,CAAC;EAC/B;EAEA9D,WAAWA,CAAA;IACT,IAAI,CAACkC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACK,qBAAqB,EAAE;IAC5B,IAAI,CAACC,eAAe,EAAE;IACtBuB,UAAU,CAAC,MAAK;MACd,IAAI,CAAC7B,SAAS,GAAG,KAAK;IACxB,CAAC,EAAE,IAAI,CAAC;EACV;EAEQM,eAAeA,CAAA;IACrB,IAAI,CAACR,WAAW,CAACgC,YAAY,EAAE,CAACtB,SAAS,CAAC;MACxCC,IAAI,EAAGsB,KAAK,IAAI;QACd,IAAI,CAACvC,WAAW,GAAGuC,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC;;MACD3I,KAAK,EAAGA,KAAK,IAAI;QACfyH,OAAO,CAACzH,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC/C;KACD,CAAC;EACJ;EAEAkF,aAAaA,CAAA;IACX,MAAM0D,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,WAAW,CAAC;IACzD,IAAIF,YAAY,EAAE;MAChB,MAAMG,KAAK,GAAG,IAAKC,MAAc,CAACC,SAAS,CAACC,KAAK,CAACN,YAAY,CAAC;MAC/DG,KAAK,CAACI,IAAI,EAAE;;EAEhB;EAEAjF,QAAQA,CAACkF,IAAU;IACjB,IAAI,CAAClE,aAAa,EAAE;IACpB;IACAsD,UAAU,CAAC,MAAK;MACd,IAAI,CAACa,cAAc,CAACD,IAAI,CAAC;IAC3B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,cAAcA,CAACD,IAAU;IACvB,IAAI,IAAI,CAACE,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,CAACC,QAAQ,CAACH,IAAI,CAAC;;EAElC;EAEA3F,cAAcA,CAAC+F,UAAkB;IAC/B,MAAMC,IAAI,GAAG,IAAIzB,IAAI,CAACwB,UAAU,CAAC;IACjC,MAAME,GAAG,GAAG,IAAI1B,IAAI,EAAE;IACtB,MAAM2B,WAAW,GAAG,CAACD,GAAG,CAACE,OAAO,EAAE,GAAGH,IAAI,CAACG,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEvE,IAAID,WAAW,GAAG,CAAC,EAAE;MACnB,OAAO,UAAU;KAClB,MAAM,IAAIA,WAAW,GAAG,EAAE,EAAE;MAC3B,OAAOF,IAAI,CAACI,kBAAkB,CAAC,EAAE,EAAE;QAAE9B,IAAI,EAAE,SAAS;QAAE+B,MAAM,EAAE;MAAS,CAAE,CAAC;KAC3E,MAAM;MACL,OAAOL,IAAI,CAACM,kBAAkB,EAAE;;EAEpC;;;uBAtQW3D,wBAAwB,EAAA5G,EAAA,CAAAwK,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1K,EAAA,CAAAwK,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA5K,EAAA,CAAAwK,iBAAA,CAAAK,EAAA,CAAAC,kBAAA,GAAA9K,EAAA,CAAAwK,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAhL,EAAA,CAAAwK,iBAAA,CAAAS,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAxBtE,wBAAwB;MAAAuE,SAAA;MAAAC,SAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;UCzCrCtL,EAAA,CAAAC,cAAA,aAAkC;UAEhCD,EAAA,CAAA2B,UAAA,IAAA6J,uCAAA,iBAKM;UAGNxL,EAAA,CAAA2B,UAAA,IAAA8J,uCAAA,iBAGM;UAGNzL,EAAA,CAAA2B,UAAA,IAAA+J,uCAAA,mBA+NM;UACR1L,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,aAAyG;UAK/FD,EAAA,CAAAI,SAAA,WAAoC;UAAAJ,EAAA,CAAAE,MAAA,iBACtC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAI,SAAA,kBAA4F;UAC9FJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAmD;UAG9BD,EAAA,CAAA6B,UAAA,0BAAA8J,yEAAAC,MAAA;YAAA,OAAgBL,GAAA,CAAA1B,cAAA,CAAA+B,MAAA,CAAsB;UAAA,EAAC;UAAC5L,EAAA,CAAAG,YAAA,EAAgB;UAEzEH,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAI,SAAA,iCAA+C;UACjDJ,EAAA,CAAAG,YAAA,EAAM;;;UAjQRH,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAAoD,UAAA,SAAAmI,GAAA,CAAApE,SAAA,CAAe;UAQfnH,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAoD,UAAA,SAAAmI,GAAA,CAAA/K,KAAA,KAAA+K,GAAA,CAAApE,SAAA,CAAyB;UAMzBnH,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAAoD,UAAA,UAAAmI,GAAA,CAAApE,SAAA,KAAAoE,GAAA,CAAA/K,KAAA,CAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}