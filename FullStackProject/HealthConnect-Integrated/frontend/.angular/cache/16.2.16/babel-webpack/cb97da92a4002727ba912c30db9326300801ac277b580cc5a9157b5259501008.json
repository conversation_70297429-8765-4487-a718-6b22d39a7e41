{"ast": null, "code": "import { BYTE } from './byte.js';\n/**\n * Frame class represents a STOMP frame.\n *\n * @internal\n */\nexport class FrameImpl {\n  /**\n   * body of the frame\n   */\n  get body() {\n    if (!this._body && this.isBinaryBody) {\n      this._body = new TextDecoder().decode(this._binaryBody);\n    }\n    return this._body || '';\n  }\n  /**\n   * body as Uint8Array\n   */\n  get binaryBody() {\n    if (!this._binaryBody && !this.isBinaryBody) {\n      this._binaryBody = new TextEncoder().encode(this._body);\n    }\n    // At this stage it will definitely have a valid value\n    return this._binaryBody;\n  }\n  /**\n   * Frame constructor. `command`, `headers` and `body` are available as properties.\n   *\n   * @internal\n   */\n  constructor(params) {\n    const {\n      command,\n      headers,\n      body,\n      binaryBody,\n      escapeHeaderValues,\n      skipContentLengthHeader\n    } = params;\n    this.command = command;\n    this.headers = Object.assign({}, headers || {});\n    if (binaryBody) {\n      this._binaryBody = binaryBody;\n      this.isBinaryBody = true;\n    } else {\n      this._body = body || '';\n      this.isBinaryBody = false;\n    }\n    this.escapeHeaderValues = escapeHeaderValues || false;\n    this.skipContentLengthHeader = skipContentLengthHeader || false;\n  }\n  /**\n   * deserialize a STOMP Frame from raw data.\n   *\n   * @internal\n   */\n  static fromRawFrame(rawFrame, escapeHeaderValues) {\n    const headers = {};\n    const trim = str => str.replace(/^\\s+|\\s+$/g, '');\n    // In case of repeated headers, as per standards, first value need to be used\n    for (const header of rawFrame.headers.reverse()) {\n      const idx = header.indexOf(':');\n      const key = trim(header[0]);\n      let value = trim(header[1]);\n      if (escapeHeaderValues && rawFrame.command !== 'CONNECT' && rawFrame.command !== 'CONNECTED') {\n        value = FrameImpl.hdrValueUnEscape(value);\n      }\n      headers[key] = value;\n    }\n    return new FrameImpl({\n      command: rawFrame.command,\n      headers,\n      binaryBody: rawFrame.binaryBody,\n      escapeHeaderValues\n    });\n  }\n  /**\n   * @internal\n   */\n  toString() {\n    return this.serializeCmdAndHeaders();\n  }\n  /**\n   * serialize this Frame in a format suitable to be passed to WebSocket.\n   * If the body is string the output will be string.\n   * If the body is binary (i.e. of type Unit8Array) it will be serialized to ArrayBuffer.\n   *\n   * @internal\n   */\n  serialize() {\n    const cmdAndHeaders = this.serializeCmdAndHeaders();\n    if (this.isBinaryBody) {\n      return FrameImpl.toUnit8Array(cmdAndHeaders, this._binaryBody).buffer;\n    } else {\n      return cmdAndHeaders + this._body + BYTE.NULL;\n    }\n  }\n  serializeCmdAndHeaders() {\n    const lines = [this.command];\n    if (this.skipContentLengthHeader) {\n      delete this.headers['content-length'];\n    }\n    for (const name of Object.keys(this.headers || {})) {\n      const value = this.headers[name];\n      if (this.escapeHeaderValues && this.command !== 'CONNECT' && this.command !== 'CONNECTED') {\n        lines.push(`${name}:${FrameImpl.hdrValueEscape(`${value}`)}`);\n      } else {\n        lines.push(`${name}:${value}`);\n      }\n    }\n    if (this.isBinaryBody || !this.isBodyEmpty() && !this.skipContentLengthHeader) {\n      lines.push(`content-length:${this.bodyLength()}`);\n    }\n    return lines.join(BYTE.LF) + BYTE.LF + BYTE.LF;\n  }\n  isBodyEmpty() {\n    return this.bodyLength() === 0;\n  }\n  bodyLength() {\n    const binaryBody = this.binaryBody;\n    return binaryBody ? binaryBody.length : 0;\n  }\n  /**\n   * Compute the size of a UTF-8 string by counting its number of bytes\n   * (and not the number of characters composing the string)\n   */\n  static sizeOfUTF8(s) {\n    return s ? new TextEncoder().encode(s).length : 0;\n  }\n  static toUnit8Array(cmdAndHeaders, binaryBody) {\n    const uint8CmdAndHeaders = new TextEncoder().encode(cmdAndHeaders);\n    const nullTerminator = new Uint8Array([0]);\n    const uint8Frame = new Uint8Array(uint8CmdAndHeaders.length + binaryBody.length + nullTerminator.length);\n    uint8Frame.set(uint8CmdAndHeaders);\n    uint8Frame.set(binaryBody, uint8CmdAndHeaders.length);\n    uint8Frame.set(nullTerminator, uint8CmdAndHeaders.length + binaryBody.length);\n    return uint8Frame;\n  }\n  /**\n   * Serialize a STOMP frame as per STOMP standards, suitable to be sent to the STOMP broker.\n   *\n   * @internal\n   */\n  static marshall(params) {\n    const frame = new FrameImpl(params);\n    return frame.serialize();\n  }\n  /**\n   *  Escape header values\n   */\n  static hdrValueEscape(str) {\n    return str.replace(/\\\\/g, '\\\\\\\\').replace(/\\r/g, '\\\\r').replace(/\\n/g, '\\\\n').replace(/:/g, '\\\\c');\n  }\n  /**\n   * UnEscape header values\n   */\n  static hdrValueUnEscape(str) {\n    return str.replace(/\\\\r/g, '\\r').replace(/\\\\n/g, '\\n').replace(/\\\\c/g, ':').replace(/\\\\\\\\/g, '\\\\');\n  }\n}", "map": {"version": 3, "names": ["BYTE", "FrameImpl", "body", "_body", "isBinaryBody", "TextDecoder", "decode", "_binaryBody", "binaryBody", "TextEncoder", "encode", "constructor", "params", "command", "headers", "escapeHeader<PERSON><PERSON>ues", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "assign", "fromRawFrame", "rawFrame", "trim", "str", "replace", "header", "reverse", "idx", "indexOf", "key", "value", "hdrValueUnEscape", "toString", "serializeCmdAndHeaders", "serialize", "cmdAndHeaders", "toUnit8Array", "buffer", "NULL", "lines", "name", "keys", "push", "hdrValueEscape", "isBodyEmpty", "<PERSON><PERSON><PERSON><PERSON>", "join", "LF", "length", "sizeOfUTF8", "s", "uint8CmdAndHeaders", "nullTerminator", "Uint8Array", "uint8Frame", "set", "marshall", "frame"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/@stomp/stompjs/esm6/frame-impl.js"], "sourcesContent": ["import { BYTE } from './byte.js';\n/**\n * Frame class represents a STOMP frame.\n *\n * @internal\n */\nexport class FrameImpl {\n    /**\n     * body of the frame\n     */\n    get body() {\n        if (!this._body && this.isBinaryBody) {\n            this._body = new TextDecoder().decode(this._binaryBody);\n        }\n        return this._body || '';\n    }\n    /**\n     * body as Uint8Array\n     */\n    get binaryBody() {\n        if (!this._binaryBody && !this.isBinaryBody) {\n            this._binaryBody = new TextEncoder().encode(this._body);\n        }\n        // At this stage it will definitely have a valid value\n        return this._binaryBody;\n    }\n    /**\n     * Frame constructor. `command`, `headers` and `body` are available as properties.\n     *\n     * @internal\n     */\n    constructor(params) {\n        const { command, headers, body, binaryBody, escapeHeaderValues, skipContentLengthHeader, } = params;\n        this.command = command;\n        this.headers = Object.assign({}, headers || {});\n        if (binaryBody) {\n            this._binaryBody = binaryBody;\n            this.isBinaryBody = true;\n        }\n        else {\n            this._body = body || '';\n            this.isBinaryBody = false;\n        }\n        this.escapeHeaderValues = escapeHeaderValues || false;\n        this.skipContentLengthHeader = skipContentLengthHeader || false;\n    }\n    /**\n     * deserialize a STOMP Frame from raw data.\n     *\n     * @internal\n     */\n    static fromRawFrame(rawFrame, escapeHeaderValues) {\n        const headers = {};\n        const trim = (str) => str.replace(/^\\s+|\\s+$/g, '');\n        // In case of repeated headers, as per standards, first value need to be used\n        for (const header of rawFrame.headers.reverse()) {\n            const idx = header.indexOf(':');\n            const key = trim(header[0]);\n            let value = trim(header[1]);\n            if (escapeHeaderValues &&\n                rawFrame.command !== 'CONNECT' &&\n                rawFrame.command !== 'CONNECTED') {\n                value = FrameImpl.hdrValueUnEscape(value);\n            }\n            headers[key] = value;\n        }\n        return new FrameImpl({\n            command: rawFrame.command,\n            headers,\n            binaryBody: rawFrame.binaryBody,\n            escapeHeaderValues,\n        });\n    }\n    /**\n     * @internal\n     */\n    toString() {\n        return this.serializeCmdAndHeaders();\n    }\n    /**\n     * serialize this Frame in a format suitable to be passed to WebSocket.\n     * If the body is string the output will be string.\n     * If the body is binary (i.e. of type Unit8Array) it will be serialized to ArrayBuffer.\n     *\n     * @internal\n     */\n    serialize() {\n        const cmdAndHeaders = this.serializeCmdAndHeaders();\n        if (this.isBinaryBody) {\n            return FrameImpl.toUnit8Array(cmdAndHeaders, this._binaryBody).buffer;\n        }\n        else {\n            return cmdAndHeaders + this._body + BYTE.NULL;\n        }\n    }\n    serializeCmdAndHeaders() {\n        const lines = [this.command];\n        if (this.skipContentLengthHeader) {\n            delete this.headers['content-length'];\n        }\n        for (const name of Object.keys(this.headers || {})) {\n            const value = this.headers[name];\n            if (this.escapeHeaderValues &&\n                this.command !== 'CONNECT' &&\n                this.command !== 'CONNECTED') {\n                lines.push(`${name}:${FrameImpl.hdrValueEscape(`${value}`)}`);\n            }\n            else {\n                lines.push(`${name}:${value}`);\n            }\n        }\n        if (this.isBinaryBody ||\n            (!this.isBodyEmpty() && !this.skipContentLengthHeader)) {\n            lines.push(`content-length:${this.bodyLength()}`);\n        }\n        return lines.join(BYTE.LF) + BYTE.LF + BYTE.LF;\n    }\n    isBodyEmpty() {\n        return this.bodyLength() === 0;\n    }\n    bodyLength() {\n        const binaryBody = this.binaryBody;\n        return binaryBody ? binaryBody.length : 0;\n    }\n    /**\n     * Compute the size of a UTF-8 string by counting its number of bytes\n     * (and not the number of characters composing the string)\n     */\n    static sizeOfUTF8(s) {\n        return s ? new TextEncoder().encode(s).length : 0;\n    }\n    static toUnit8Array(cmdAndHeaders, binaryBody) {\n        const uint8CmdAndHeaders = new TextEncoder().encode(cmdAndHeaders);\n        const nullTerminator = new Uint8Array([0]);\n        const uint8Frame = new Uint8Array(uint8CmdAndHeaders.length + binaryBody.length + nullTerminator.length);\n        uint8Frame.set(uint8CmdAndHeaders);\n        uint8Frame.set(binaryBody, uint8CmdAndHeaders.length);\n        uint8Frame.set(nullTerminator, uint8CmdAndHeaders.length + binaryBody.length);\n        return uint8Frame;\n    }\n    /**\n     * Serialize a STOMP frame as per STOMP standards, suitable to be sent to the STOMP broker.\n     *\n     * @internal\n     */\n    static marshall(params) {\n        const frame = new FrameImpl(params);\n        return frame.serialize();\n    }\n    /**\n     *  Escape header values\n     */\n    static hdrValueEscape(str) {\n        return str\n            .replace(/\\\\/g, '\\\\\\\\')\n            .replace(/\\r/g, '\\\\r')\n            .replace(/\\n/g, '\\\\n')\n            .replace(/:/g, '\\\\c');\n    }\n    /**\n     * UnEscape header values\n     */\n    static hdrValueUnEscape(str) {\n        return str\n            .replace(/\\\\r/g, '\\r')\n            .replace(/\\\\n/g, '\\n')\n            .replace(/\\\\c/g, ':')\n            .replace(/\\\\\\\\/g, '\\\\');\n    }\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,WAAW;AAChC;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,SAAS,CAAC;EACnB;AACJ;AACA;EACI,IAAIC,IAAIA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACC,KAAK,IAAI,IAAI,CAACC,YAAY,EAAE;MAClC,IAAI,CAACD,KAAK,GAAG,IAAIE,WAAW,CAAC,CAAC,CAACC,MAAM,CAAC,IAAI,CAACC,WAAW,CAAC;IAC3D;IACA,OAAO,IAAI,CAACJ,KAAK,IAAI,EAAE;EAC3B;EACA;AACJ;AACA;EACI,IAAIK,UAAUA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAACD,WAAW,IAAI,CAAC,IAAI,CAACH,YAAY,EAAE;MACzC,IAAI,CAACG,WAAW,GAAG,IAAIE,WAAW,CAAC,CAAC,CAACC,MAAM,CAAC,IAAI,CAACP,KAAK,CAAC;IAC3D;IACA;IACA,OAAO,IAAI,CAACI,WAAW;EAC3B;EACA;AACJ;AACA;AACA;AACA;EACII,WAAWA,CAACC,MAAM,EAAE;IAChB,MAAM;MAAEC,OAAO;MAAEC,OAAO;MAAEZ,IAAI;MAAEM,UAAU;MAAEO,kBAAkB;MAAEC;IAAyB,CAAC,GAAGJ,MAAM;IACnG,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,OAAO,IAAI,CAAC,CAAC,CAAC;IAC/C,IAAIN,UAAU,EAAE;MACZ,IAAI,CAACD,WAAW,GAAGC,UAAU;MAC7B,IAAI,CAACJ,YAAY,GAAG,IAAI;IAC5B,CAAC,MACI;MACD,IAAI,CAACD,KAAK,GAAGD,IAAI,IAAI,EAAE;MACvB,IAAI,CAACE,YAAY,GAAG,KAAK;IAC7B;IACA,IAAI,CAACW,kBAAkB,GAAGA,kBAAkB,IAAI,KAAK;IACrD,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB,IAAI,KAAK;EACnE;EACA;AACJ;AACA;AACA;AACA;EACI,OAAOG,YAAYA,CAACC,QAAQ,EAAEL,kBAAkB,EAAE;IAC9C,MAAMD,OAAO,GAAG,CAAC,CAAC;IAClB,MAAMO,IAAI,GAAIC,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;IACnD;IACA,KAAK,MAAMC,MAAM,IAAIJ,QAAQ,CAACN,OAAO,CAACW,OAAO,CAAC,CAAC,EAAE;MAC7C,MAAMC,GAAG,GAAGF,MAAM,CAACG,OAAO,CAAC,GAAG,CAAC;MAC/B,MAAMC,GAAG,GAAGP,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,CAAC;MAC3B,IAAIK,KAAK,GAAGR,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,CAAC;MAC3B,IAAIT,kBAAkB,IAClBK,QAAQ,CAACP,OAAO,KAAK,SAAS,IAC9BO,QAAQ,CAACP,OAAO,KAAK,WAAW,EAAE;QAClCgB,KAAK,GAAG5B,SAAS,CAAC6B,gBAAgB,CAACD,KAAK,CAAC;MAC7C;MACAf,OAAO,CAACc,GAAG,CAAC,GAAGC,KAAK;IACxB;IACA,OAAO,IAAI5B,SAAS,CAAC;MACjBY,OAAO,EAAEO,QAAQ,CAACP,OAAO;MACzBC,OAAO;MACPN,UAAU,EAAEY,QAAQ,CAACZ,UAAU;MAC/BO;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACIgB,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,sBAAsB,CAAC,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAAA,EAAG;IACR,MAAMC,aAAa,GAAG,IAAI,CAACF,sBAAsB,CAAC,CAAC;IACnD,IAAI,IAAI,CAAC5B,YAAY,EAAE;MACnB,OAAOH,SAAS,CAACkC,YAAY,CAACD,aAAa,EAAE,IAAI,CAAC3B,WAAW,CAAC,CAAC6B,MAAM;IACzE,CAAC,MACI;MACD,OAAOF,aAAa,GAAG,IAAI,CAAC/B,KAAK,GAAGH,IAAI,CAACqC,IAAI;IACjD;EACJ;EACAL,sBAAsBA,CAAA,EAAG;IACrB,MAAMM,KAAK,GAAG,CAAC,IAAI,CAACzB,OAAO,CAAC;IAC5B,IAAI,IAAI,CAACG,uBAAuB,EAAE;MAC9B,OAAO,IAAI,CAACF,OAAO,CAAC,gBAAgB,CAAC;IACzC;IACA,KAAK,MAAMyB,IAAI,IAAItB,MAAM,CAACuB,IAAI,CAAC,IAAI,CAAC1B,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE;MAChD,MAAMe,KAAK,GAAG,IAAI,CAACf,OAAO,CAACyB,IAAI,CAAC;MAChC,IAAI,IAAI,CAACxB,kBAAkB,IACvB,IAAI,CAACF,OAAO,KAAK,SAAS,IAC1B,IAAI,CAACA,OAAO,KAAK,WAAW,EAAE;QAC9ByB,KAAK,CAACG,IAAI,CAAE,GAAEF,IAAK,IAAGtC,SAAS,CAACyC,cAAc,CAAE,GAAEb,KAAM,EAAC,CAAE,EAAC,CAAC;MACjE,CAAC,MACI;QACDS,KAAK,CAACG,IAAI,CAAE,GAAEF,IAAK,IAAGV,KAAM,EAAC,CAAC;MAClC;IACJ;IACA,IAAI,IAAI,CAACzB,YAAY,IAChB,CAAC,IAAI,CAACuC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC3B,uBAAwB,EAAE;MACxDsB,KAAK,CAACG,IAAI,CAAE,kBAAiB,IAAI,CAACG,UAAU,CAAC,CAAE,EAAC,CAAC;IACrD;IACA,OAAON,KAAK,CAACO,IAAI,CAAC7C,IAAI,CAAC8C,EAAE,CAAC,GAAG9C,IAAI,CAAC8C,EAAE,GAAG9C,IAAI,CAAC8C,EAAE;EAClD;EACAH,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,UAAU,CAAC,CAAC,KAAK,CAAC;EAClC;EACAA,UAAUA,CAAA,EAAG;IACT,MAAMpC,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,OAAOA,UAAU,GAAGA,UAAU,CAACuC,MAAM,GAAG,CAAC;EAC7C;EACA;AACJ;AACA;AACA;EACI,OAAOC,UAAUA,CAACC,CAAC,EAAE;IACjB,OAAOA,CAAC,GAAG,IAAIxC,WAAW,CAAC,CAAC,CAACC,MAAM,CAACuC,CAAC,CAAC,CAACF,MAAM,GAAG,CAAC;EACrD;EACA,OAAOZ,YAAYA,CAACD,aAAa,EAAE1B,UAAU,EAAE;IAC3C,MAAM0C,kBAAkB,GAAG,IAAIzC,WAAW,CAAC,CAAC,CAACC,MAAM,CAACwB,aAAa,CAAC;IAClE,MAAMiB,cAAc,GAAG,IAAIC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,MAAMC,UAAU,GAAG,IAAID,UAAU,CAACF,kBAAkB,CAACH,MAAM,GAAGvC,UAAU,CAACuC,MAAM,GAAGI,cAAc,CAACJ,MAAM,CAAC;IACxGM,UAAU,CAACC,GAAG,CAACJ,kBAAkB,CAAC;IAClCG,UAAU,CAACC,GAAG,CAAC9C,UAAU,EAAE0C,kBAAkB,CAACH,MAAM,CAAC;IACrDM,UAAU,CAACC,GAAG,CAACH,cAAc,EAAED,kBAAkB,CAACH,MAAM,GAAGvC,UAAU,CAACuC,MAAM,CAAC;IAC7E,OAAOM,UAAU;EACrB;EACA;AACJ;AACA;AACA;AACA;EACI,OAAOE,QAAQA,CAAC3C,MAAM,EAAE;IACpB,MAAM4C,KAAK,GAAG,IAAIvD,SAAS,CAACW,MAAM,CAAC;IACnC,OAAO4C,KAAK,CAACvB,SAAS,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;EACI,OAAOS,cAAcA,CAACpB,GAAG,EAAE;IACvB,OAAOA,GAAG,CACLC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CACtBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;EAC7B;EACA;AACJ;AACA;EACI,OAAOO,gBAAgBA,CAACR,GAAG,EAAE;IACzB,OAAOA,GAAG,CACLC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CACrBA,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CACrBA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;EAC/B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}