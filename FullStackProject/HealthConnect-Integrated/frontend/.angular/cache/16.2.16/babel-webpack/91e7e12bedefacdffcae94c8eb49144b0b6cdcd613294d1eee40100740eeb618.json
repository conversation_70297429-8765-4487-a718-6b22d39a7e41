{"ast": null, "code": "'use strict';\n\nvar EventEmitter = require('events').EventEmitter,\n  inherits = require('inherits');\nfunction XHRFake( /* method, url, payload, opts */\n) {\n  var self = this;\n  EventEmitter.call(this);\n  this.to = setTimeout(function () {\n    self.emit('finish', 200, '{}');\n  }, XHRFake.timeout);\n}\ninherits(XHRFake, EventEmitter);\nXHRFake.prototype.close = function () {\n  clearTimeout(this.to);\n};\nXHRFake.timeout = 2000;\nmodule.exports = XHRFake;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}