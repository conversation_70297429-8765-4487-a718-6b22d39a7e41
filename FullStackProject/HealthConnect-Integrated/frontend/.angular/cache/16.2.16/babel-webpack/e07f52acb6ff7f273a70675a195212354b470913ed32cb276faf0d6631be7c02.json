{"ast": null, "code": "import { interval } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction DoctorAvailabilityComponent_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.getStatusText());\n  }\n}\nfunction DoctorAvailabilityComponent_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"i\", 10);\n    i0.ɵɵelementStart(2, \"small\", 11);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.availability.expectedResponseTime);\n  }\n}\nfunction DoctorAvailabilityComponent_div_4_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"i\", 12);\n    i0.ɵɵelementStart(2, \"small\", 11);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" Chat hours: \", ctx_r3.availability.chatStartTime, \" - \", ctx_r3.availability.chatEndTime, \" \");\n  }\n}\nfunction DoctorAvailabilityComponent_div_4_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"i\", 13);\n    i0.ɵɵelementStart(2, \"small\", 11);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.availability.customMessage);\n  }\n}\nfunction DoctorAvailabilityComponent_div_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"i\", 14);\n    i0.ɵɵelementStart(2, \"small\", 11);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Last seen \", ctx_r5.getLastSeenText(), \"\");\n  }\n}\nfunction DoctorAvailabilityComponent_div_4_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16);\n    i0.ɵɵelement(2, \"i\", 17);\n    i0.ɵɵelementStart(3, \"small\");\n    i0.ɵɵtext(4, \"Outside chat hours\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DoctorAvailabilityComponent_div_4_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵelement(2, \"i\", 20);\n    i0.ɵɵelementStart(3, \"small\");\n    i0.ɵɵtext(4, \"Currently in consultation\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DoctorAvailabilityComponent_div_4_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 21);\n    i0.ɵɵelement(2, \"i\", 22);\n    i0.ɵɵelementStart(3, \"small\");\n    i0.ɵɵtext(4, \"Emergency contacts only\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DoctorAvailabilityComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵtemplate(1, DoctorAvailabilityComponent_div_4_div_1_Template, 4, 1, \"div\", 6);\n    i0.ɵɵtemplate(2, DoctorAvailabilityComponent_div_4_div_2_Template, 4, 2, \"div\", 6);\n    i0.ɵɵtemplate(3, DoctorAvailabilityComponent_div_4_div_3_Template, 4, 1, \"div\", 6);\n    i0.ɵɵtemplate(4, DoctorAvailabilityComponent_div_4_div_4_Template, 4, 1, \"div\", 6);\n    i0.ɵɵtemplate(5, DoctorAvailabilityComponent_div_4_div_5_Template, 5, 0, \"div\", 7);\n    i0.ɵɵtemplate(6, DoctorAvailabilityComponent_div_4_div_6_Template, 5, 0, \"div\", 8);\n    i0.ɵɵtemplate(7, DoctorAvailabilityComponent_div_4_div_7_Template, 5, 0, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availability.expectedResponseTime);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availability.chatStartTime && ctx_r1.availability.chatEndTime);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availability.customMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availability.status === \"OFFLINE\" && ctx_r1.availability.lastSeen);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isWithinChatHours());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availability.status === \"BUSY\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availability.status === \"DO_NOT_DISTURB\");\n  }\n}\nexport class DoctorAvailabilityComponent {\n  constructor() {\n    this.showDetails = true;\n    this.size = 'md';\n    this.availability = null;\n    this.loading = false;\n  }\n  ngOnInit() {\n    this.loadAvailability();\n    this.startPeriodicRefresh();\n  }\n  ngOnDestroy() {\n    if (this.refreshSubscription) {\n      this.refreshSubscription.unsubscribe();\n    }\n  }\n  loadAvailability() {\n    // Mock data for now - replace with actual service call\n    this.availability = {\n      status: 'ONLINE',\n      expectedResponseTime: 'Within 2 hours',\n      customMessage: 'Available for consultations',\n      chatStartTime: '09:00',\n      chatEndTime: '17:00'\n    };\n  }\n  startPeriodicRefresh() {\n    // Refresh availability every 5 minutes\n    this.refreshSubscription = interval(5 * 60 * 1000).subscribe(() => {\n      this.loadAvailability();\n    });\n  }\n  getStatusIcon() {\n    if (!this.availability) return 'fas fa-circle text-secondary';\n    switch (this.availability.status) {\n      case 'ONLINE':\n        return 'fas fa-circle text-success';\n      case 'BUSY':\n        return 'fas fa-circle text-warning';\n      case 'AWAY':\n        return 'fas fa-circle text-info';\n      case 'DO_NOT_DISTURB':\n        return 'fas fa-minus-circle text-danger';\n      case 'OFFLINE':\n      default:\n        return 'fas fa-circle text-secondary';\n    }\n  }\n  getStatusText() {\n    if (!this.availability) return 'Unknown';\n    switch (this.availability.status) {\n      case 'ONLINE':\n        return 'Online';\n      case 'BUSY':\n        return 'Busy';\n      case 'AWAY':\n        return 'Away';\n      case 'DO_NOT_DISTURB':\n        return 'Do Not Disturb';\n      case 'OFFLINE':\n      default:\n        return 'Offline';\n    }\n  }\n  getStatusClass() {\n    if (!this.availability) return 'status-offline';\n    switch (this.availability.status) {\n      case 'ONLINE':\n        return 'status-online';\n      case 'BUSY':\n        return 'status-busy';\n      case 'AWAY':\n        return 'status-away';\n      case 'DO_NOT_DISTURB':\n        return 'status-dnd';\n      case 'OFFLINE':\n      default:\n        return 'status-offline';\n    }\n  }\n  isAvailable() {\n    return this.availability?.status === 'ONLINE' || this.availability?.status === 'AWAY';\n  }\n  getLastSeenText() {\n    if (!this.availability?.lastSeen) return '';\n    const lastSeen = new Date(this.availability.lastSeen);\n    const now = new Date();\n    const diffMs = now.getTime() - lastSeen.getTime();\n    const diffMinutes = Math.floor(diffMs / (1000 * 60));\n    const diffHours = Math.floor(diffMinutes / 60);\n    const diffDays = Math.floor(diffHours / 24);\n    if (diffMinutes < 1) {\n      return 'Just now';\n    } else if (diffMinutes < 60) {\n      return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;\n    } else if (diffHours < 24) {\n      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;\n    } else {\n      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;\n    }\n  }\n  isWithinChatHours() {\n    if (!this.availability?.chatStartTime || !this.availability?.chatEndTime) {\n      return true; // Assume available if no hours set\n    }\n\n    const now = new Date();\n    const currentTime = now.toTimeString().slice(0, 5); // HH:MM format\n    return currentTime >= this.availability.chatStartTime && currentTime <= this.availability.chatEndTime;\n  }\n  static {\n    this.ɵfac = function DoctorAvailabilityComponent_Factory(t) {\n      return new (t || DoctorAvailabilityComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DoctorAvailabilityComponent,\n      selectors: [[\"app-doctor-availability\"]],\n      inputs: {\n        doctorId: \"doctorId\",\n        showDetails: \"showDetails\",\n        size: \"size\"\n      },\n      decls: 5,\n      vars: 8,\n      consts: [[1, \"doctor-availability\"], [1, \"status-indicator\"], [\"class\", \"status-text ms-2\", 4, \"ngIf\"], [\"class\", \"availability-details mt-2\", 4, \"ngIf\"], [1, \"status-text\", \"ms-2\"], [1, \"availability-details\", \"mt-2\"], [\"class\", \"detail-item\", 4, \"ngIf\"], [\"class\", \"availability-warning mt-2\", 4, \"ngIf\"], [\"class\", \"status-message mt-2\", 4, \"ngIf\"], [1, \"detail-item\"], [1, \"fas\", \"fa-clock\", \"text-muted\", \"me-2\"], [1, \"text-muted\"], [1, \"fas\", \"fa-calendar-clock\", \"text-muted\", \"me-2\"], [1, \"fas\", \"fa-comment\", \"text-muted\", \"me-2\"], [1, \"fas\", \"fa-eye\", \"text-muted\", \"me-2\"], [1, \"availability-warning\", \"mt-2\"], [1, \"alert\", \"alert-warning\", \"py-1\", \"px-2\", \"mb-0\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-1\"], [1, \"status-message\", \"mt-2\"], [1, \"alert\", \"alert-info\", \"py-1\", \"px-2\", \"mb-0\"], [1, \"fas\", \"fa-user-clock\", \"me-1\"], [1, \"alert\", \"alert-danger\", \"py-1\", \"px-2\", \"mb-0\"], [1, \"fas\", \"fa-ban\", \"me-1\"]],\n      template: function DoctorAvailabilityComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"i\");\n          i0.ɵɵtemplate(3, DoctorAvailabilityComponent_span_3_Template, 2, 1, \"span\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, DoctorAvailabilityComponent_div_4_Template, 8, 7, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(\"size-\" + ctx.size);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.getStatusClass());\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.getStatusIcon());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.size !== \"sm\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showDetails && ctx.availability && ctx.size !== \"sm\");\n        }\n      },\n      dependencies: [i1.NgIf],\n      styles: [\".doctor-availability[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-online[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-busy[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-away[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  color: #17a2b8;\\n}\\n.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-dnd[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-offline[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 0.25rem;\\n}\\n.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  width: 14px;\\n  font-size: 0.75rem;\\n}\\n.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  border-radius: 4px;\\n}\\n.doctor-availability.size-sm[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.doctor-availability.size-sm[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.doctor-availability.size-lg[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n}\\n.doctor-availability.size-lg[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.doctor-availability.size-lg[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n.doctor-availability.size-lg[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%] {\\n  margin-top: 0.75rem;\\n}\\n.doctor-availability.size-lg[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n}\\n.doctor-availability.size-lg[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  width: 16px;\\n  font-size: 0.875rem;\\n}\\n.doctor-availability.size-lg[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n.status-online[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.7;\\n  }\\n  100% {\\n    opacity: 1;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n  }\\n  .doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    margin-bottom: 0.25rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["interval", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "getStatusText", "ɵɵelement", "ctx_r2", "availability", "expectedResponseTime", "ɵɵtextInterpolate2", "ctx_r3", "chatStartTime", "chatEndTime", "ctx_r4", "customMessage", "ɵɵtextInterpolate1", "ctx_r5", "getLastSeenText", "ɵɵtemplate", "DoctorAvailabilityComponent_div_4_div_1_Template", "DoctorAvailabilityComponent_div_4_div_2_Template", "DoctorAvailabilityComponent_div_4_div_3_Template", "DoctorAvailabilityComponent_div_4_div_4_Template", "DoctorAvailabilityComponent_div_4_div_5_Template", "DoctorAvailabilityComponent_div_4_div_6_Template", "DoctorAvailabilityComponent_div_4_div_7_Template", "ɵɵproperty", "ctx_r1", "status", "lastSeen", "isWithinChatHours", "DoctorAvailabilityComponent", "constructor", "showDetails", "size", "loading", "ngOnInit", "loadAvailability", "startPeriodicRefresh", "ngOnDestroy", "refreshSubscription", "unsubscribe", "subscribe", "getStatusIcon", "getStatusClass", "isAvailable", "Date", "now", "diffMs", "getTime", "diffMinutes", "Math", "floor", "diffHours", "diffDays", "currentTime", "toTimeString", "slice", "selectors", "inputs", "doctorId", "decls", "vars", "consts", "template", "DoctorAvailabilityComponent_Template", "rf", "ctx", "DoctorAvailabilityComponent_span_3_Template", "DoctorAvailabilityComponent_div_4_Template", "ɵɵclassMap"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/shared/components/doctor-availability/doctor-availability.component.ts", "/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/shared/components/doctor-availability/doctor-availability.component.html"], "sourcesContent": ["import { Component, Input, OnInit, OnDestroy } from '@angular/core';\nimport { interval, Subscription } from 'rxjs';\n\nexport interface DoctorAvailability {\n  status: 'ONLINE' | 'BUSY' | 'AWAY' | 'OFFLINE' | 'DO_NOT_DISTURB';\n  lastSeen?: string;\n  expectedResponseTime?: string;\n  customMessage?: string;\n  chatStartTime?: string;\n  chatEndTime?: string;\n}\n\n@Component({\n  selector: 'app-doctor-availability',\n  templateUrl: './doctor-availability.component.html',\n  styleUrls: ['./doctor-availability.component.scss']\n})\nexport class DoctorAvailabilityComponent implements OnInit, OnDestroy {\n  @Input() doctorId!: number;\n  @Input() showDetails = true;\n  @Input() size: 'sm' | 'md' | 'lg' = 'md';\n  \n  availability: DoctorAvailability | null = null;\n  loading = false;\n  private refreshSubscription?: Subscription;\n\n  constructor() {}\n\n  ngOnInit(): void {\n    this.loadAvailability();\n    this.startPeriodicRefresh();\n  }\n\n  ngOnDestroy(): void {\n    if (this.refreshSubscription) {\n      this.refreshSubscription.unsubscribe();\n    }\n  }\n\n  private loadAvailability(): void {\n    // Mock data for now - replace with actual service call\n    this.availability = {\n      status: 'ONLINE',\n      expectedResponseTime: 'Within 2 hours',\n      customMessage: 'Available for consultations',\n      chatStartTime: '09:00',\n      chatEndTime: '17:00'\n    };\n  }\n\n  private startPeriodicRefresh(): void {\n    // Refresh availability every 5 minutes\n    this.refreshSubscription = interval(5 * 60 * 1000).subscribe(() => {\n      this.loadAvailability();\n    });\n  }\n\n  getStatusIcon(): string {\n    if (!this.availability) return 'fas fa-circle text-secondary';\n    \n    switch (this.availability.status) {\n      case 'ONLINE':\n        return 'fas fa-circle text-success';\n      case 'BUSY':\n        return 'fas fa-circle text-warning';\n      case 'AWAY':\n        return 'fas fa-circle text-info';\n      case 'DO_NOT_DISTURB':\n        return 'fas fa-minus-circle text-danger';\n      case 'OFFLINE':\n      default:\n        return 'fas fa-circle text-secondary';\n    }\n  }\n\n  getStatusText(): string {\n    if (!this.availability) return 'Unknown';\n    \n    switch (this.availability.status) {\n      case 'ONLINE':\n        return 'Online';\n      case 'BUSY':\n        return 'Busy';\n      case 'AWAY':\n        return 'Away';\n      case 'DO_NOT_DISTURB':\n        return 'Do Not Disturb';\n      case 'OFFLINE':\n      default:\n        return 'Offline';\n    }\n  }\n\n  getStatusClass(): string {\n    if (!this.availability) return 'status-offline';\n    \n    switch (this.availability.status) {\n      case 'ONLINE':\n        return 'status-online';\n      case 'BUSY':\n        return 'status-busy';\n      case 'AWAY':\n        return 'status-away';\n      case 'DO_NOT_DISTURB':\n        return 'status-dnd';\n      case 'OFFLINE':\n      default:\n        return 'status-offline';\n    }\n  }\n\n  isAvailable(): boolean {\n    return this.availability?.status === 'ONLINE' || this.availability?.status === 'AWAY';\n  }\n\n  getLastSeenText(): string {\n    if (!this.availability?.lastSeen) return '';\n    \n    const lastSeen = new Date(this.availability.lastSeen);\n    const now = new Date();\n    const diffMs = now.getTime() - lastSeen.getTime();\n    const diffMinutes = Math.floor(diffMs / (1000 * 60));\n    const diffHours = Math.floor(diffMinutes / 60);\n    const diffDays = Math.floor(diffHours / 24);\n    \n    if (diffMinutes < 1) {\n      return 'Just now';\n    } else if (diffMinutes < 60) {\n      return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;\n    } else if (diffHours < 24) {\n      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;\n    } else {\n      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;\n    }\n  }\n\n  isWithinChatHours(): boolean {\n    if (!this.availability?.chatStartTime || !this.availability?.chatEndTime) {\n      return true; // Assume available if no hours set\n    }\n    \n    const now = new Date();\n    const currentTime = now.toTimeString().slice(0, 5); // HH:MM format\n    \n    return currentTime >= this.availability.chatStartTime && \n           currentTime <= this.availability.chatEndTime;\n  }\n}\n", "<div class=\"doctor-availability\" [class]=\"'size-' + size\">\n  <!-- Basic Status Indicator -->\n  <div class=\"status-indicator\" [class]=\"getStatusClass()\">\n    <i [class]=\"getStatusIcon()\"></i>\n    <span *ngIf=\"size !== 'sm'\" class=\"status-text ms-2\">{{ getStatusText() }}</span>\n  </div>\n\n  <!-- Detailed Information -->\n  <div *ngIf=\"showDetails && availability && size !== 'sm'\" class=\"availability-details mt-2\">\n    \n    <!-- Expected Response Time -->\n    <div *ngIf=\"availability.expectedResponseTime\" class=\"detail-item\">\n      <i class=\"fas fa-clock text-muted me-2\"></i>\n      <small class=\"text-muted\">{{ availability.expectedResponseTime }}</small>\n    </div>\n\n    <!-- Chat Hours -->\n    <div *ngIf=\"availability.chatStartTime && availability.chatEndTime\" class=\"detail-item\">\n      <i class=\"fas fa-calendar-clock text-muted me-2\"></i>\n      <small class=\"text-muted\">\n        Chat hours: {{ availability.chatStartTime }} - {{ availability.chatEndTime }}\n      </small>\n    </div>\n\n    <!-- Custom Message -->\n    <div *ngIf=\"availability.customMessage\" class=\"detail-item\">\n      <i class=\"fas fa-comment text-muted me-2\"></i>\n      <small class=\"text-muted\">{{ availability.customMessage }}</small>\n    </div>\n\n    <!-- Last Seen (for offline status) -->\n    <div *ngIf=\"availability.status === 'OFFLINE' && availability.lastSeen\" class=\"detail-item\">\n      <i class=\"fas fa-eye text-muted me-2\"></i>\n      <small class=\"text-muted\">Last seen {{ getLastSeenText() }}</small>\n    </div>\n\n    <!-- Availability Warning -->\n    <div *ngIf=\"!isWithinChatHours()\" class=\"availability-warning mt-2\">\n      <div class=\"alert alert-warning py-1 px-2 mb-0\">\n        <i class=\"fas fa-exclamation-triangle me-1\"></i>\n        <small>Outside chat hours</small>\n      </div>\n    </div>\n\n    <!-- Status-specific Messages -->\n    <div *ngIf=\"availability.status === 'BUSY'\" class=\"status-message mt-2\">\n      <div class=\"alert alert-info py-1 px-2 mb-0\">\n        <i class=\"fas fa-user-clock me-1\"></i>\n        <small>Currently in consultation</small>\n      </div>\n    </div>\n\n    <div *ngIf=\"availability.status === 'DO_NOT_DISTURB'\" class=\"status-message mt-2\">\n      <div class=\"alert alert-danger py-1 px-2 mb-0\">\n        <i class=\"fas fa-ban me-1\"></i>\n        <small>Emergency contacts only</small>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,QAAQ,QAAsB,MAAM;;;;;ICGzCC,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA5BH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,aAAA,GAAqB;;;;;IAO1EP,EAAA,CAAAC,cAAA,aAAmE;IACjED,EAAA,CAAAQ,SAAA,YAA4C;IAC5CR,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA/CH,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAK,iBAAA,CAAAI,MAAA,CAAAC,YAAA,CAAAC,oBAAA,CAAuC;;;;;IAInEX,EAAA,CAAAC,cAAA,aAAwF;IACtFD,EAAA,CAAAQ,SAAA,YAAqD;IACrDR,EAAA,CAAAC,cAAA,gBAA0B;IACxBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IADNH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAY,kBAAA,kBAAAC,MAAA,CAAAH,YAAA,CAAAI,aAAA,SAAAD,MAAA,CAAAH,YAAA,CAAAK,WAAA,MACF;;;;;IAIFf,EAAA,CAAAC,cAAA,aAA4D;IAC1DD,EAAA,CAAAQ,SAAA,YAA8C;IAC9CR,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAxCH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,iBAAA,CAAAW,MAAA,CAAAN,YAAA,CAAAO,aAAA,CAAgC;;;;;IAI5DjB,EAAA,CAAAC,cAAA,aAA4F;IAC1FD,EAAA,CAAAQ,SAAA,YAA0C;IAC1CR,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAzCH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAkB,kBAAA,eAAAC,MAAA,CAAAC,eAAA,OAAiC;;;;;IAI7DpB,EAAA,CAAAC,cAAA,cAAoE;IAEhED,EAAA,CAAAQ,SAAA,YAAgD;IAChDR,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAKrCH,EAAA,CAAAC,cAAA,cAAwE;IAEpED,EAAA,CAAAQ,SAAA,YAAsC;IACtCR,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAI5CH,EAAA,CAAAC,cAAA,cAAkF;IAE9ED,EAAA,CAAAQ,SAAA,YAA+B;IAC/BR,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IA/C5CH,EAAA,CAAAC,cAAA,aAA4F;IAG1FD,EAAA,CAAAqB,UAAA,IAAAC,gDAAA,iBAGM;IAGNtB,EAAA,CAAAqB,UAAA,IAAAE,gDAAA,iBAKM;IAGNvB,EAAA,CAAAqB,UAAA,IAAAG,gDAAA,iBAGM;IAGNxB,EAAA,CAAAqB,UAAA,IAAAI,gDAAA,iBAGM;IAGNzB,EAAA,CAAAqB,UAAA,IAAAK,gDAAA,iBAKM;IAGN1B,EAAA,CAAAqB,UAAA,IAAAM,gDAAA,iBAKM;IAEN3B,EAAA,CAAAqB,UAAA,IAAAO,gDAAA,iBAKM;IACR5B,EAAA,CAAAG,YAAA,EAAM;;;;IA/CEH,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAA6B,UAAA,SAAAC,MAAA,CAAApB,YAAA,CAAAC,oBAAA,CAAuC;IAMvCX,EAAA,CAAAI,SAAA,GAA4D;IAA5DJ,EAAA,CAAA6B,UAAA,SAAAC,MAAA,CAAApB,YAAA,CAAAI,aAAA,IAAAgB,MAAA,CAAApB,YAAA,CAAAK,WAAA,CAA4D;IAQ5Df,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAA6B,UAAA,SAAAC,MAAA,CAAApB,YAAA,CAAAO,aAAA,CAAgC;IAMhCjB,EAAA,CAAAI,SAAA,GAAgE;IAAhEJ,EAAA,CAAA6B,UAAA,SAAAC,MAAA,CAAApB,YAAA,CAAAqB,MAAA,kBAAAD,MAAA,CAAApB,YAAA,CAAAsB,QAAA,CAAgE;IAMhEhC,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAA6B,UAAA,UAAAC,MAAA,CAAAG,iBAAA,GAA0B;IAQ1BjC,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAA6B,UAAA,SAAAC,MAAA,CAAApB,YAAA,CAAAqB,MAAA,YAAoC;IAOpC/B,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAA6B,UAAA,SAAAC,MAAA,CAAApB,YAAA,CAAAqB,MAAA,sBAA8C;;;ADnCxD,OAAM,MAAOG,2BAA2B;EAStCC,YAAA;IAPS,KAAAC,WAAW,GAAG,IAAI;IAClB,KAAAC,IAAI,GAAuB,IAAI;IAExC,KAAA3B,YAAY,GAA8B,IAAI;IAC9C,KAAA4B,OAAO,GAAG,KAAK;EAGA;EAEfC,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACC,WAAW,EAAE;;EAE1C;EAEQJ,gBAAgBA,CAAA;IACtB;IACA,IAAI,CAAC9B,YAAY,GAAG;MAClBqB,MAAM,EAAE,QAAQ;MAChBpB,oBAAoB,EAAE,gBAAgB;MACtCM,aAAa,EAAE,6BAA6B;MAC5CH,aAAa,EAAE,OAAO;MACtBC,WAAW,EAAE;KACd;EACH;EAEQ0B,oBAAoBA,CAAA;IAC1B;IACA,IAAI,CAACE,mBAAmB,GAAG5C,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC8C,SAAS,CAAC,MAAK;MAChE,IAAI,CAACL,gBAAgB,EAAE;IACzB,CAAC,CAAC;EACJ;EAEAM,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACpC,YAAY,EAAE,OAAO,8BAA8B;IAE7D,QAAQ,IAAI,CAACA,YAAY,CAACqB,MAAM;MAC9B,KAAK,QAAQ;QACX,OAAO,4BAA4B;MACrC,KAAK,MAAM;QACT,OAAO,4BAA4B;MACrC,KAAK,MAAM;QACT,OAAO,yBAAyB;MAClC,KAAK,gBAAgB;QACnB,OAAO,iCAAiC;MAC1C,KAAK,SAAS;MACd;QACE,OAAO,8BAA8B;;EAE3C;EAEAxB,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACG,YAAY,EAAE,OAAO,SAAS;IAExC,QAAQ,IAAI,CAACA,YAAY,CAACqB,MAAM;MAC9B,KAAK,QAAQ;QACX,OAAO,QAAQ;MACjB,KAAK,MAAM;QACT,OAAO,MAAM;MACf,KAAK,MAAM;QACT,OAAO,MAAM;MACf,KAAK,gBAAgB;QACnB,OAAO,gBAAgB;MACzB,KAAK,SAAS;MACd;QACE,OAAO,SAAS;;EAEtB;EAEAgB,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACrC,YAAY,EAAE,OAAO,gBAAgB;IAE/C,QAAQ,IAAI,CAACA,YAAY,CAACqB,MAAM;MAC9B,KAAK,QAAQ;QACX,OAAO,eAAe;MACxB,KAAK,MAAM;QACT,OAAO,aAAa;MACtB,KAAK,MAAM;QACT,OAAO,aAAa;MACtB,KAAK,gBAAgB;QACnB,OAAO,YAAY;MACrB,KAAK,SAAS;MACd;QACE,OAAO,gBAAgB;;EAE7B;EAEAiB,WAAWA,CAAA;IACT,OAAO,IAAI,CAACtC,YAAY,EAAEqB,MAAM,KAAK,QAAQ,IAAI,IAAI,CAACrB,YAAY,EAAEqB,MAAM,KAAK,MAAM;EACvF;EAEAX,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACV,YAAY,EAAEsB,QAAQ,EAAE,OAAO,EAAE;IAE3C,MAAMA,QAAQ,GAAG,IAAIiB,IAAI,CAAC,IAAI,CAACvC,YAAY,CAACsB,QAAQ,CAAC;IACrD,MAAMkB,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAME,MAAM,GAAGD,GAAG,CAACE,OAAO,EAAE,GAAGpB,QAAQ,CAACoB,OAAO,EAAE;IACjD,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IACpD,MAAMK,SAAS,GAAGF,IAAI,CAACC,KAAK,CAACF,WAAW,GAAG,EAAE,CAAC;IAC9C,MAAMI,QAAQ,GAAGH,IAAI,CAACC,KAAK,CAACC,SAAS,GAAG,EAAE,CAAC;IAE3C,IAAIH,WAAW,GAAG,CAAC,EAAE;MACnB,OAAO,UAAU;KAClB,MAAM,IAAIA,WAAW,GAAG,EAAE,EAAE;MAC3B,OAAO,GAAGA,WAAW,UAAUA,WAAW,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;KAChE,MAAM,IAAIG,SAAS,GAAG,EAAE,EAAE;MACzB,OAAO,GAAGA,SAAS,QAAQA,SAAS,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;KAC1D,MAAM;MACL,OAAO,GAAGC,QAAQ,OAAOA,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;;EAE1D;EAEAxB,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACvB,YAAY,EAAEI,aAAa,IAAI,CAAC,IAAI,CAACJ,YAAY,EAAEK,WAAW,EAAE;MACxE,OAAO,IAAI,CAAC,CAAC;;;IAGf,MAAMmC,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAMS,WAAW,GAAGR,GAAG,CAACS,YAAY,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAEpD,OAAOF,WAAW,IAAI,IAAI,CAAChD,YAAY,CAACI,aAAa,IAC9C4C,WAAW,IAAI,IAAI,CAAChD,YAAY,CAACK,WAAW;EACrD;;;uBAjIWmB,2BAA2B;IAAA;EAAA;;;YAA3BA,2BAA2B;MAAA2B,SAAA;MAAAC,MAAA;QAAAC,QAAA;QAAA3B,WAAA;QAAAC,IAAA;MAAA;MAAA2B,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBxCrE,EAAA,CAAAC,cAAA,aAA0D;UAGtDD,EAAA,CAAAQ,SAAA,QAAiC;UACjCR,EAAA,CAAAqB,UAAA,IAAAkD,2CAAA,kBAAiF;UACnFvE,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAqB,UAAA,IAAAmD,0CAAA,iBAkDM;UACRxE,EAAA,CAAAG,YAAA,EAAM;;;UA3D2BH,EAAA,CAAAyE,UAAA,WAAAH,GAAA,CAAAjC,IAAA,CAAwB;UAEzBrC,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAyE,UAAA,CAAAH,GAAA,CAAAvB,cAAA,GAA0B;UACnD/C,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAyE,UAAA,CAAAH,GAAA,CAAAxB,aAAA,GAAyB;UACrB9C,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAA6B,UAAA,SAAAyC,GAAA,CAAAjC,IAAA,UAAmB;UAItBrC,EAAA,CAAAI,SAAA,GAAkD;UAAlDJ,EAAA,CAAA6B,UAAA,SAAAyC,GAAA,CAAAlC,WAAA,IAAAkC,GAAA,CAAA5D,YAAA,IAAA4D,GAAA,CAAAjC,IAAA,UAAkD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}