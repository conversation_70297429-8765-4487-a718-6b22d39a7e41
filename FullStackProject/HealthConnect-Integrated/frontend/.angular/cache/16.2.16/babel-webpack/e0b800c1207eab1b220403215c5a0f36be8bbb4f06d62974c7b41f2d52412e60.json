{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction MessageItemComponent_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 7);\n    i0.ɵɵelement(1, \"i\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.getStatusClass());\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.getStatusIcon());\n  }\n}\nexport let MessageItemComponent = /*#__PURE__*/(() => {\n  class MessageItemComponent {\n    constructor() {\n      this.isOwn = false;\n    }\n    formatTime(dateString) {\n      const date = new Date(dateString);\n      return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n    getStatusIcon() {\n      switch (this.message.status) {\n        case 'SENT':\n          return 'bi-check';\n        case 'DELIVERED':\n          return 'bi-check2';\n        case 'READ':\n          return 'bi-check2-all';\n        default:\n          return 'bi-clock';\n      }\n    }\n    getStatusClass() {\n      return this.message.status === 'READ' ? 'text-primary' : 'text-muted';\n    }\n    static {\n      this.ɵfac = function MessageItemComponent_Factory(t) {\n        return new (t || MessageItemComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: MessageItemComponent,\n        selectors: [[\"app-message-item\"]],\n        inputs: {\n          message: \"message\",\n          isOwn: \"isOwn\"\n        },\n        decls: 9,\n        vars: 7,\n        consts: [[1, \"message-item\"], [1, \"message-content\"], [1, \"message-bubble\"], [1, \"message-text\", \"mb-1\"], [1, \"message-meta\"], [1, \"message-time\"], [\"class\", \"message-status ms-1\", 3, \"class\", 4, \"ngIf\"], [1, \"message-status\", \"ms-1\"], [1, \"bi\"]],\n        template: function MessageItemComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"p\", 3);\n            i0.ɵɵtext(4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 4)(6, \"small\", 5);\n            i0.ɵɵtext(7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(8, MessageItemComponent_span_8_Template, 2, 4, \"span\", 6);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"own-message\", ctx.isOwn)(\"other-message\", !ctx.isOwn);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate(ctx.message.content);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(ctx.formatTime(ctx.message.createdAt));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isOwn);\n          }\n        },\n        dependencies: [i1.NgIf],\n        styles: [\".message-item[_ngcontent-%COMP%]{margin-bottom:.75rem;display:flex}.message-item.own-message[_ngcontent-%COMP%]{justify-content:flex-end}.message-item.own-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]{background-color:#007bff;color:#fff;border-bottom-right-radius:.25rem}.message-item.own-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%]{color:#fffc}.message-item.other-message[_ngcontent-%COMP%]{justify-content:flex-start}.message-item.other-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]{background-color:#fff;color:#333;border:1px solid #e9ecef;border-bottom-left-radius:.25rem}.message-item.other-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%]{color:#6c757d}.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{max-width:70%}.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]{padding:.75rem 1rem;border-radius:1rem;word-wrap:break-word;box-shadow:0 1px 2px #0000001a}.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]{line-height:1.4;margin:0}.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end;margin-top:.25rem}.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%], .message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%]   .message-status[_ngcontent-%COMP%]{font-size:.75rem}@media (max-width: 768px){.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{max-width:85%}.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]{padding:.5rem .75rem}}\"]\n      });\n    }\n  }\n  return MessageItemComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}