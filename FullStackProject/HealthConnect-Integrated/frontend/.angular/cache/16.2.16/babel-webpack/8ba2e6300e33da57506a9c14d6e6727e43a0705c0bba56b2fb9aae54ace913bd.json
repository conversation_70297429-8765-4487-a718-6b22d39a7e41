{"ast": null, "code": "import _asyncToGenerator from \"/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { <PERSON>ompHandler } from './stomp-handler.js';\nimport { ActivationState, ReconnectionTimeMode, StompSocketState, TickerStrategy } from './types.js';\nimport { Versions } from './versions.js';\n/**\n * STOMP Client Class.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class Client {\n  /**\n   * Underlying WebSocket instance, READONLY.\n   */\n  get webSocket() {\n    return this._stompHandler?._webSocket;\n  }\n  /**\n   * Disconnection headers.\n   */\n  get disconnectHeaders() {\n    return this._disconnectHeaders;\n  }\n  set disconnectHeaders(value) {\n    this._disconnectHeaders = value;\n    if (this._stompHandler) {\n      this._stompHandler.disconnectHeaders = this._disconnectHeaders;\n    }\n  }\n  /**\n   * `true` if there is an active connection to STOMP Broker\n   */\n  get connected() {\n    return !!this._stompHandler && this._stompHandler.connected;\n  }\n  /**\n   * version of STOMP protocol negotiated with the server, READONLY\n   */\n  get connectedVersion() {\n    return this._stompHandler ? this._stompHandler.connectedVersion : undefined;\n  }\n  /**\n   * if the client is active (connected or going to reconnect)\n   */\n  get active() {\n    return this.state === ActivationState.ACTIVE;\n  }\n  _changeState(state) {\n    this.state = state;\n    this.onChangeState(state);\n  }\n  /**\n   * Create an instance.\n   */\n  constructor(conf = {}) {\n    /**\n     * STOMP versions to attempt during STOMP handshake. By default, versions `1.2`, `1.1`, and `1.0` are attempted.\n     *\n     * Example:\n     * ```javascript\n     *        // Try only versions 1.1 and 1.0\n     *        client.stompVersions = new Versions(['1.1', '1.0'])\n     * ```\n     */\n    this.stompVersions = Versions.default;\n    /**\n     * Will retry if Stomp connection is not established in specified milliseconds.\n     * Default 0, which switches off automatic reconnection.\n     */\n    this.connectionTimeout = 0;\n    /**\n     *  automatically reconnect with delay in milliseconds, set to 0 to disable.\n     */\n    this.reconnectDelay = 5000;\n    /**\n     * tracking the time to the next reconnection. Initialized to [Client#reconnectDelay]{@link Client#reconnectDelay}'s value and it may\n     * change depending on the [Client#reconnectTimeMode]{@link Client#reconnectTimeMode} setting\n     */\n    this._nextReconnectDelay = 0;\n    /**\n     * Maximum time to wait between reconnects, in milliseconds. Defaults to 15 minutes.\n     * Only relevant when [Client#reconnectTimeMode]{@link Client#reconnectTimeMode} not LINEAR (e.g., EXPONENTIAL).\n     * Set to 0 for no limit on wait time.\n     */\n    this.maxReconnectDelay = 15 * 60 * 1000; // 15 minutes in ms\n    /**\n     * Reconnection wait time mode, either linear (default) or exponential.\n     * Note: See [Client#maxReconnectDelay]{@link Client#maxReconnectDelay} for setting the maximum delay when exponential\n     *\n     * ```javascript\n     * client.configure({\n     *   reconnectTimeMode: ReconnectionTimeMode.EXPONENTIAL,\n     *   reconnectDelay: 200, // It will wait 200, 400, 800 ms...\n     *   maxReconnectDelay: 10000, // Optional, when provided, it will not wait more that these ms\n     * })\n     * ```\n     */\n    this.reconnectTimeMode = ReconnectionTimeMode.LINEAR;\n    /**\n     * Incoming heartbeat interval in milliseconds. Set to 0 to disable.\n     */\n    this.heartbeatIncoming = 10000;\n    /**\n     * Outgoing heartbeat interval in milliseconds. Set to 0 to disable.\n     */\n    this.heartbeatOutgoing = 10000;\n    /**\n     * Outgoing heartbeat strategy.\n     * See https://github.com/stomp-js/stompjs/pull/579\n     *\n     * Can be worker or interval strategy, but will always use `interval`\n     * if web workers are unavailable, for example, in a non-browser environment.\n     *\n     * Using Web Workers may work better on long-running pages\n     * and mobile apps, as the browser may suspend Timers in the main page.\n     * Try the `Worker` mode if you discover disconnects when the browser tab is in the background.\n     *\n     * When used in a JS environment, use 'worker' or 'interval' as valid values.\n     *\n     * Defaults to `interval` strategy.\n     */\n    this.heartbeatStrategy = TickerStrategy.Interval;\n    /**\n     * This switches on a non-standard behavior while sending WebSocket packets.\n     * It splits larger (text) packets into chunks of [maxWebSocketChunkSize]{@link Client#maxWebSocketChunkSize}.\n     * Only Java Spring brokers seem to support this mode.\n     *\n     * WebSockets, by itself, split large (text) packets,\n     * so it is not needed with a truly compliant STOMP/WebSocket broker.\n     * Setting it for such a broker will cause large messages to fail.\n     *\n     * `false` by default.\n     *\n     * Binary frames are never split.\n     */\n    this.splitLargeFrames = false;\n    /**\n     * See [splitLargeFrames]{@link Client#splitLargeFrames}.\n     * This has no effect if [splitLargeFrames]{@link Client#splitLargeFrames} is `false`.\n     */\n    this.maxWebSocketChunkSize = 8 * 1024;\n    /**\n     * Usually the\n     * [type of WebSocket frame]{@link https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send#Parameters}\n     * is automatically decided by type of the payload.\n     * Default is `false`, which should work with all compliant brokers.\n     *\n     * Set this flag to force binary frames.\n     */\n    this.forceBinaryWSFrames = false;\n    /**\n     * A bug in ReactNative chops a string on occurrence of a NULL.\n     * See issue [https://github.com/stomp-js/stompjs/issues/89]{@link https://github.com/stomp-js/stompjs/issues/89}.\n     * This makes incoming WebSocket messages invalid STOMP packets.\n     * Setting this flag attempts to reverse the damage by appending a NULL.\n     * If the broker splits a large message into multiple WebSocket messages,\n     * this flag will cause data loss and abnormal termination of connection.\n     *\n     * This is not an ideal solution, but a stop gap until the underlying issue is fixed at ReactNative library.\n     */\n    this.appendMissingNULLonIncoming = false;\n    /**\n     * Browsers do not immediately close WebSockets when `.close` is issued.\n     * This may cause reconnection to take a significantly long time in case\n     *  of some types of failures.\n     * In case of incoming heartbeat failure, this experimental flag instructs\n     * the library to discard the socket immediately\n     * (even before it is actually closed).\n     */\n    this.discardWebsocketOnCommFailure = false;\n    /**\n     * Activation state.\n     *\n     * It will usually be ACTIVE or INACTIVE.\n     * When deactivating, it may go from ACTIVE to INACTIVE without entering DEACTIVATING.\n     */\n    this.state = ActivationState.INACTIVE;\n    // No op callbacks\n    const noOp = () => {};\n    this.debug = noOp;\n    this.beforeConnect = noOp;\n    this.onConnect = noOp;\n    this.onDisconnect = noOp;\n    this.onUnhandledMessage = noOp;\n    this.onUnhandledReceipt = noOp;\n    this.onUnhandledFrame = noOp;\n    this.onStompError = noOp;\n    this.onWebSocketClose = noOp;\n    this.onWebSocketError = noOp;\n    this.logRawCommunication = false;\n    this.onChangeState = noOp;\n    // These parameters would typically get proper values before connect is called\n    this.connectHeaders = {};\n    this._disconnectHeaders = {};\n    // Apply configuration\n    this.configure(conf);\n  }\n  /**\n   * Update configuration.\n   */\n  configure(conf) {\n    // bulk assign all properties to this\n    Object.assign(this, conf);\n    // Warn on incorrect maxReconnectDelay settings\n    if (this.maxReconnectDelay > 0 && this.maxReconnectDelay < this.reconnectDelay) {\n      this.debug(`Warning: maxReconnectDelay (${this.maxReconnectDelay}ms) is less than reconnectDelay (${this.reconnectDelay}ms). Using reconnectDelay as the maxReconnectDelay delay.`);\n      this.maxReconnectDelay = this.reconnectDelay;\n    }\n  }\n  /**\n   * Initiate the connection with the broker.\n   * If the connection breaks, as per [Client#reconnectDelay]{@link Client#reconnectDelay},\n   * it will keep trying to reconnect. If the [Client#reconnectTimeMode]{@link Client#reconnectTimeMode}\n   * is set to EXPONENTIAL it will increase the wait time exponentially\n   *\n   * Call [Client#deactivate]{@link Client#deactivate} to disconnect and stop reconnection attempts.\n   */\n  activate() {\n    const _activate = () => {\n      if (this.active) {\n        this.debug('Already ACTIVE, ignoring request to activate');\n        return;\n      }\n      this._changeState(ActivationState.ACTIVE);\n      this._nextReconnectDelay = this.reconnectDelay;\n      this._connect();\n    };\n    // if it is deactivating, wait for it to complete before activating.\n    if (this.state === ActivationState.DEACTIVATING) {\n      this.debug('Waiting for deactivation to finish before activating');\n      this.deactivate().then(() => {\n        _activate();\n      });\n    } else {\n      _activate();\n    }\n  }\n  _connect() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.beforeConnect(_this);\n      if (_this._stompHandler) {\n        _this.debug('There is already a stompHandler, skipping the call to connect');\n        return;\n      }\n      if (!_this.active) {\n        _this.debug('Client has been marked inactive, will not attempt to connect');\n        return;\n      }\n      // setup connection watcher\n      if (_this.connectionTimeout > 0) {\n        // clear first\n        if (_this._connectionWatcher) {\n          clearTimeout(_this._connectionWatcher);\n        }\n        _this._connectionWatcher = setTimeout(() => {\n          if (_this.connected) {\n            return;\n          }\n          // Connection not established, close the underlying socket\n          // a reconnection will be attempted\n          _this.debug(`Connection not established in ${_this.connectionTimeout}ms, closing socket`);\n          _this.forceDisconnect();\n        }, _this.connectionTimeout);\n      }\n      _this.debug('Opening Web Socket...');\n      // Get the actual WebSocket (or a similar object)\n      const webSocket = _this._createWebSocket();\n      _this._stompHandler = new StompHandler(_this, webSocket, {\n        debug: _this.debug,\n        stompVersions: _this.stompVersions,\n        connectHeaders: _this.connectHeaders,\n        disconnectHeaders: _this._disconnectHeaders,\n        heartbeatIncoming: _this.heartbeatIncoming,\n        heartbeatOutgoing: _this.heartbeatOutgoing,\n        heartbeatStrategy: _this.heartbeatStrategy,\n        splitLargeFrames: _this.splitLargeFrames,\n        maxWebSocketChunkSize: _this.maxWebSocketChunkSize,\n        forceBinaryWSFrames: _this.forceBinaryWSFrames,\n        logRawCommunication: _this.logRawCommunication,\n        appendMissingNULLonIncoming: _this.appendMissingNULLonIncoming,\n        discardWebsocketOnCommFailure: _this.discardWebsocketOnCommFailure,\n        onConnect: frame => {\n          // Successfully connected, stop the connection watcher\n          if (_this._connectionWatcher) {\n            clearTimeout(_this._connectionWatcher);\n            _this._connectionWatcher = undefined;\n          }\n          if (!_this.active) {\n            _this.debug('STOMP got connected while deactivate was issued, will disconnect now');\n            _this._disposeStompHandler();\n            return;\n          }\n          _this.onConnect(frame);\n        },\n        onDisconnect: frame => {\n          _this.onDisconnect(frame);\n        },\n        onStompError: frame => {\n          _this.onStompError(frame);\n        },\n        onWebSocketClose: evt => {\n          _this._stompHandler = undefined; // a new one will be created in case of a reconnect\n          if (_this.state === ActivationState.DEACTIVATING) {\n            // Mark deactivation complete\n            _this._changeState(ActivationState.INACTIVE);\n          }\n          // The callback is called before attempting to reconnect, this would allow the client\n          // to be `deactivated` in the callback.\n          _this.onWebSocketClose(evt);\n          if (_this.active) {\n            _this._schedule_reconnect();\n          }\n        },\n        onWebSocketError: evt => {\n          _this.onWebSocketError(evt);\n        },\n        onUnhandledMessage: message => {\n          _this.onUnhandledMessage(message);\n        },\n        onUnhandledReceipt: frame => {\n          _this.onUnhandledReceipt(frame);\n        },\n        onUnhandledFrame: frame => {\n          _this.onUnhandledFrame(frame);\n        }\n      });\n      _this._stompHandler.start();\n    })();\n  }\n  _createWebSocket() {\n    let webSocket;\n    if (this.webSocketFactory) {\n      webSocket = this.webSocketFactory();\n    } else if (this.brokerURL) {\n      webSocket = new WebSocket(this.brokerURL, this.stompVersions.protocolVersions());\n    } else {\n      throw new Error('Either brokerURL or webSocketFactory must be provided');\n    }\n    webSocket.binaryType = 'arraybuffer';\n    return webSocket;\n  }\n  _schedule_reconnect() {\n    if (this._nextReconnectDelay > 0) {\n      this.debug(`STOMP: scheduling reconnection in ${this._nextReconnectDelay}ms`);\n      this._reconnector = setTimeout(() => {\n        if (this.reconnectTimeMode === ReconnectionTimeMode.EXPONENTIAL) {\n          this._nextReconnectDelay = this._nextReconnectDelay * 2;\n          // Truncated exponential backoff with a set limit unless disabled\n          if (this.maxReconnectDelay !== 0) {\n            this._nextReconnectDelay = Math.min(this._nextReconnectDelay, this.maxReconnectDelay);\n          }\n        }\n        this._connect();\n      }, this._nextReconnectDelay);\n    }\n  }\n  /**\n   * Disconnect if connected and stop auto reconnect loop.\n   * Appropriate callbacks will be invoked if there is an underlying STOMP connection.\n   *\n   * This call is async. It will resolve immediately if there is no underlying active websocket,\n   * otherwise, it will resolve after the underlying websocket is properly disposed of.\n   *\n   * It is not an error to invoke this method more than once.\n   * Each of those would resolve on completion of deactivation.\n   *\n   * To reactivate, you can call [Client#activate]{@link Client#activate}.\n   *\n   * Experimental: pass `force: true` to immediately discard the underlying connection.\n   * This mode will skip both the STOMP and the Websocket shutdown sequences.\n   * In some cases, browsers take a long time in the Websocket shutdown\n   * if the underlying connection had gone stale.\n   * Using this mode can speed up.\n   * When this mode is used, the actual Websocket may linger for a while\n   * and the broker may not realize that the connection is no longer in use.\n   *\n   * It is possible to invoke this method initially without the `force` option\n   * and subsequently, say after a wait, with the `force` option.\n   */\n  deactivate() {\n    var _this2 = this;\n    return _asyncToGenerator(function* (options = {}) {\n      const force = options.force || false;\n      const needToDispose = _this2.active;\n      let retPromise;\n      if (_this2.state === ActivationState.INACTIVE) {\n        _this2.debug(`Already INACTIVE, nothing more to do`);\n        return Promise.resolve();\n      }\n      _this2._changeState(ActivationState.DEACTIVATING);\n      // Reset reconnection timer just to be safe\n      _this2._nextReconnectDelay = 0;\n      // Clear if a reconnection was scheduled\n      if (_this2._reconnector) {\n        clearTimeout(_this2._reconnector);\n        _this2._reconnector = undefined;\n      }\n      if (_this2._stompHandler &&\n      // @ts-ignore - if there is a _stompHandler, there is the webSocket\n      _this2.webSocket.readyState !== StompSocketState.CLOSED) {\n        const origOnWebSocketClose = _this2._stompHandler.onWebSocketClose;\n        // we need to wait for the underlying websocket to close\n        retPromise = new Promise((resolve, reject) => {\n          // @ts-ignore - there is a _stompHandler\n          _this2._stompHandler.onWebSocketClose = evt => {\n            origOnWebSocketClose(evt);\n            resolve();\n          };\n        });\n      } else {\n        // indicate that auto reconnect loop should terminate\n        _this2._changeState(ActivationState.INACTIVE);\n        return Promise.resolve();\n      }\n      if (force) {\n        _this2._stompHandler?.discardWebsocket();\n      } else if (needToDispose) {\n        _this2._disposeStompHandler();\n      }\n      return retPromise;\n    }).apply(this, arguments);\n  }\n  /**\n   * Force disconnect if there is an active connection by directly closing the underlying WebSocket.\n   * This is different from a normal disconnect where a DISCONNECT sequence is carried out with the broker.\n   * After forcing disconnect, automatic reconnect will be attempted.\n   * To stop further reconnects call [Client#deactivate]{@link Client#deactivate} as well.\n   */\n  forceDisconnect() {\n    if (this._stompHandler) {\n      this._stompHandler.forceDisconnect();\n    }\n  }\n  _disposeStompHandler() {\n    // Dispose STOMP Handler\n    if (this._stompHandler) {\n      this._stompHandler.dispose();\n    }\n  }\n  /**\n   * Send a message to a named destination. Refer to your STOMP broker documentation for types\n   * and naming of destinations.\n   *\n   * STOMP protocol specifies and suggests some headers and also allows broker-specific headers.\n   *\n   * `body` must be String.\n   * You will need to covert the payload to string in case it is not string (e.g. JSON).\n   *\n   * To send a binary message body, use `binaryBody` parameter. It should be a\n   * [Uint8Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint8Array).\n   * Sometimes brokers may not support binary frames out of the box.\n   * Please check your broker documentation.\n   *\n   * `content-length` header is automatically added to the STOMP Frame sent to the broker.\n   * Set `skipContentLengthHeader` to indicate that `content-length` header should not be added.\n   * For binary messages, `content-length` header is always added.\n   *\n   * Caution: The broker will, most likely, report an error and disconnect\n   * if the message body has NULL octet(s) and `content-length` header is missing.\n   *\n   * ```javascript\n   *        client.publish({destination: \"/queue/test\", headers: {priority: 9}, body: \"Hello, STOMP\"});\n   *\n   *        // Only destination is mandatory parameter\n   *        client.publish({destination: \"/queue/test\", body: \"Hello, STOMP\"});\n   *\n   *        // Skip content-length header in the frame to the broker\n   *        client.publish({\"/queue/test\", body: \"Hello, STOMP\", skipContentLengthHeader: true});\n   *\n   *        var binaryData = generateBinaryData(); // This need to be of type Uint8Array\n   *        // setting content-type header is not mandatory, however a good practice\n   *        client.publish({destination: '/topic/special', binaryBody: binaryData,\n   *                         headers: {'content-type': 'application/octet-stream'}});\n   * ```\n   */\n  publish(params) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.publish(params);\n  }\n  _checkConnection() {\n    if (!this.connected) {\n      throw new TypeError('There is no underlying STOMP connection');\n    }\n  }\n  /**\n   * STOMP brokers may carry out operation asynchronously and allow requesting for acknowledgement.\n   * To request an acknowledgement, a `receipt` header needs to be sent with the actual request.\n   * The value (say receipt-id) for this header needs to be unique for each use.\n   * Typically, a sequence, a UUID, a random number or a combination may be used.\n   *\n   * A complaint broker will send a RECEIPT frame when an operation has actually been completed.\n   * The operation needs to be matched based on the value of the receipt-id.\n   *\n   * This method allows watching for a receipt and invoking the callback\n   *  when the corresponding receipt has been received.\n   *\n   * The actual {@link IFrame} will be passed as parameter to the callback.\n   *\n   * Example:\n   * ```javascript\n   *        // Subscribing with acknowledgement\n   *        let receiptId = randomText();\n   *\n   *        client.watchForReceipt(receiptId, function() {\n   *          // Will be called after server acknowledges\n   *        });\n   *\n   *        client.subscribe(TEST.destination, onMessage, {receipt: receiptId});\n   *\n   *\n   *        // Publishing with acknowledgement\n   *        receiptId = randomText();\n   *\n   *        client.watchForReceipt(receiptId, function() {\n   *          // Will be called after server acknowledges\n   *        });\n   *        client.publish({destination: TEST.destination, headers: {receipt: receiptId}, body: msg});\n   * ```\n   */\n  watchForReceipt(receiptId, callback) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.watchForReceipt(receiptId, callback);\n  }\n  /**\n   * Subscribe to a STOMP Broker location. The callback will be invoked for each\n   * received message with the {@link IMessage} as argument.\n   *\n   * Note: The library will generate a unique ID if there is none provided in the headers.\n   *       To use your own ID, pass it using the `headers` argument.\n   *\n   * ```javascript\n   *        callback = function(message) {\n   *        // called when the client receives a STOMP message from the server\n   *          if (message.body) {\n   *            alert(\"got message with body \" + message.body)\n   *          } else {\n   *            alert(\"got empty message\");\n   *          }\n   *        });\n   *\n   *        var subscription = client.subscribe(\"/queue/test\", callback);\n   *\n   *        // Explicit subscription id\n   *        var mySubId = 'my-subscription-id-001';\n   *        var subscription = client.subscribe(destination, callback, { id: mySubId });\n   * ```\n   */\n  subscribe(destination, callback, headers = {}) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    return this._stompHandler.subscribe(destination, callback, headers);\n  }\n  /**\n   * It is preferable to unsubscribe from a subscription by calling\n   * `unsubscribe()` directly on {@link StompSubscription} returned by `client.subscribe()`:\n   *\n   * ```javascript\n   *        var subscription = client.subscribe(destination, onmessage);\n   *        // ...\n   *        subscription.unsubscribe();\n   * ```\n   *\n   * See: https://stomp.github.com/stomp-specification-1.2.html#UNSUBSCRIBE UNSUBSCRIBE Frame\n   */\n  unsubscribe(id, headers = {}) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.unsubscribe(id, headers);\n  }\n  /**\n   * Start a transaction, the returned {@link ITransaction} has methods - [commit]{@link ITransaction#commit}\n   * and [abort]{@link ITransaction#abort}.\n   *\n   * `transactionId` is optional, if not passed the library will generate it internally.\n   */\n  begin(transactionId) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    return this._stompHandler.begin(transactionId);\n  }\n  /**\n   * Commit a transaction.\n   *\n   * It is preferable to commit a transaction by calling [commit]{@link ITransaction#commit} directly on\n   * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n   *\n   * ```javascript\n   *        var tx = client.begin(txId);\n   *        //...\n   *        tx.commit();\n   * ```\n   */\n  commit(transactionId) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.commit(transactionId);\n  }\n  /**\n   * Abort a transaction.\n   * It is preferable to abort a transaction by calling [abort]{@link ITransaction#abort} directly on\n   * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n   *\n   * ```javascript\n   *        var tx = client.begin(txId);\n   *        //...\n   *        tx.abort();\n   * ```\n   */\n  abort(transactionId) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.abort(transactionId);\n  }\n  /**\n   * ACK a message. It is preferable to acknowledge a message by calling [ack]{@link IMessage#ack} directly\n   * on the {@link IMessage} handled by a subscription callback:\n   *\n   * ```javascript\n   *        var callback = function (message) {\n   *          // process the message\n   *          // acknowledge it\n   *          message.ack();\n   *        };\n   *        client.subscribe(destination, callback, {'ack': 'client'});\n   * ```\n   */\n  ack(messageId, subscriptionId, headers = {}) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.ack(messageId, subscriptionId, headers);\n  }\n  /**\n   * NACK a message. It is preferable to acknowledge a message by calling [nack]{@link IMessage#nack} directly\n   * on the {@link IMessage} handled by a subscription callback:\n   *\n   * ```javascript\n   *        var callback = function (message) {\n   *          // process the message\n   *          // an error occurs, nack it\n   *          message.nack();\n   *        };\n   *        client.subscribe(destination, callback, {'ack': 'client'});\n   * ```\n   */\n  nack(messageId, subscriptionId, headers = {}) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.nack(messageId, subscriptionId, headers);\n  }\n}\n//# sourceMappingURL=client.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}