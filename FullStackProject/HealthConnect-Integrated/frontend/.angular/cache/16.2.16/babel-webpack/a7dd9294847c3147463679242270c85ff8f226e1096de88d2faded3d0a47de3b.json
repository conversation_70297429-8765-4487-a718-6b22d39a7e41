{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { ChatAccessComponent } from './components/chat-access/chat-access.component';\nimport { DoctorAvailabilityComponent } from './components/doctor-availability/doctor-availability.component';\nimport { NotificationBellComponent } from './components/notification-bell/notification-bell.component';\nimport * as i0 from \"@angular/core\";\nexport class SharedModule {\n  static {\n    this.ɵfac = function SharedModule_Factory(t) {\n      return new (t || SharedModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SharedModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule, CommonModule, ReactiveFormsModule, FormsModule, RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SharedModule, {\n    declarations: [ChatAccessComponent, DoctorAvailabilityComponent, NotificationBellComponent],\n    imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule],\n    exports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule, ChatAccessComponent, DoctorAvailabilityComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "FormsModule", "RouterModule", "ChatAccessComponent", "DoctorAvailabilityComponent", "NotificationBellComponent", "SharedModule", "declarations", "imports", "exports"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/shared/shared.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { ChatAccessComponent } from './components/chat-access/chat-access.component';\nimport { DoctorAvailabilityComponent } from './components/doctor-availability/doctor-availability.component';\nimport { NotificationBellComponent } from './components/notification-bell/notification-bell.component';\n\n@NgModule({\n  declarations: [\n    ChatAccessComponent,\n    DoctorAvailabilityComponent,\n    NotificationBellComponent\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    FormsModule,\n    RouterModule\n  ],\n  exports: [\n    CommonModule,\n    ReactiveFormsModule,\n    FormsModule,\n    RouterModule,\n    ChatAccessComponent,\n    DoctorAvailabilityComponent\n  ]\n})\nexport class SharedModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,2BAA2B,QAAQ,gEAAgE;AAC5G,SAASC,yBAAyB,QAAQ,4DAA4D;;AAuBtG,OAAM,MAAOC,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBAdrBP,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY,EAGZH,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY;IAAA;EAAA;;;2EAKHI,YAAY;IAAAC,YAAA,GAnBrBJ,mBAAmB,EACnBC,2BAA2B,EAC3BC,yBAAyB;IAAAG,OAAA,GAGzBT,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY;IAAAO,OAAA,GAGZV,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY,EACZC,mBAAmB,EACnBC,2BAA2B;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}