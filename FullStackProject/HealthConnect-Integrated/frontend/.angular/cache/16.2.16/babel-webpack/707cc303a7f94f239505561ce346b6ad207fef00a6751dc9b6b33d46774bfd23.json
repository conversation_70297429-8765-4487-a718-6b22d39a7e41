{"ast": null, "code": "'use strict';\n\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:utils:transport');\n}\nmodule.exports = function (availableTransports) {\n  return {\n    filterToEnabled: function (transportsWhitelist, info) {\n      var transports = {\n        main: [],\n        facade: []\n      };\n      if (!transportsWhitelist) {\n        transportsWhitelist = [];\n      } else if (typeof transportsWhitelist === 'string') {\n        transportsWhitelist = [transportsWhitelist];\n      }\n      availableTransports.forEach(function (trans) {\n        if (!trans) {\n          return;\n        }\n        if (trans.transportName === 'websocket' && info.websocket === false) {\n          debug('disabled from server', 'websocket');\n          return;\n        }\n        if (transportsWhitelist.length && transportsWhitelist.indexOf(trans.transportName) === -1) {\n          debug('not in whitelist', trans.transportName);\n          return;\n        }\n        if (trans.enabled(info)) {\n          debug('enabled', trans.transportName);\n          transports.main.push(trans);\n          if (trans.facadeTransport) {\n            transports.facade.push(trans.facadeTransport);\n          }\n        } else {\n          debug('disabled', trans.transportName);\n        }\n      });\n      return transports;\n    }\n  };\n};", "map": {"version": 3, "names": ["debug", "process", "env", "NODE_ENV", "require", "module", "exports", "availableTransports", "filterToEnabled", "transports<PERSON><PERSON><PERSON><PERSON>", "info", "transports", "main", "facade", "for<PERSON>ach", "trans", "transportName", "websocket", "length", "indexOf", "enabled", "push", "facadeTransport"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/sockjs-client/lib/utils/transport.js"], "sourcesContent": ["'use strict';\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:utils:transport');\n}\n\nmodule.exports = function(availableTransports) {\n  return {\n    filterToEnabled: function(transportsWhitelist, info) {\n      var transports = {\n        main: []\n      , facade: []\n      };\n      if (!transportsWhitelist) {\n        transportsWhitelist = [];\n      } else if (typeof transportsWhitelist === 'string') {\n        transportsWhitelist = [transportsWhitelist];\n      }\n\n      availableTransports.forEach(function(trans) {\n        if (!trans) {\n          return;\n        }\n\n        if (trans.transportName === 'websocket' && info.websocket === false) {\n          debug('disabled from server', 'websocket');\n          return;\n        }\n\n        if (transportsWhitelist.length &&\n            transportsWhitelist.indexOf(trans.transportName) === -1) {\n          debug('not in whitelist', trans.transportName);\n          return;\n        }\n\n        if (trans.enabled(info)) {\n          debug('enabled', trans.transportName);\n          transports.main.push(trans);\n          if (trans.facadeTransport) {\n            transports.facade.push(trans.facadeTransport);\n          }\n        } else {\n          debug('disabled', trans.transportName);\n        }\n      });\n      return transports;\n    }\n  };\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGI,OAAO,CAAC,OAAO,CAAC,CAAC,+BAA+B,CAAC;AAC3D;AAEAC,MAAM,CAACC,OAAO,GAAG,UAASC,mBAAmB,EAAE;EAC7C,OAAO;IACLC,eAAe,EAAE,SAAAA,CAASC,mBAAmB,EAAEC,IAAI,EAAE;MACnD,IAAIC,UAAU,GAAG;QACfC,IAAI,EAAE,EAAE;QACRC,MAAM,EAAE;MACV,CAAC;MACD,IAAI,CAACJ,mBAAmB,EAAE;QACxBA,mBAAmB,GAAG,EAAE;MAC1B,CAAC,MAAM,IAAI,OAAOA,mBAAmB,KAAK,QAAQ,EAAE;QAClDA,mBAAmB,GAAG,CAACA,mBAAmB,CAAC;MAC7C;MAEAF,mBAAmB,CAACO,OAAO,CAAC,UAASC,KAAK,EAAE;QAC1C,IAAI,CAACA,KAAK,EAAE;UACV;QACF;QAEA,IAAIA,KAAK,CAACC,aAAa,KAAK,WAAW,IAAIN,IAAI,CAACO,SAAS,KAAK,KAAK,EAAE;UACnEjB,KAAK,CAAC,sBAAsB,EAAE,WAAW,CAAC;UAC1C;QACF;QAEA,IAAIS,mBAAmB,CAACS,MAAM,IAC1BT,mBAAmB,CAACU,OAAO,CAACJ,KAAK,CAACC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;UAC3DhB,KAAK,CAAC,kBAAkB,EAAEe,KAAK,CAACC,aAAa,CAAC;UAC9C;QACF;QAEA,IAAID,KAAK,CAACK,OAAO,CAACV,IAAI,CAAC,EAAE;UACvBV,KAAK,CAAC,SAAS,EAAEe,KAAK,CAACC,aAAa,CAAC;UACrCL,UAAU,CAACC,IAAI,CAACS,IAAI,CAACN,KAAK,CAAC;UAC3B,IAAIA,KAAK,CAACO,eAAe,EAAE;YACzBX,UAAU,CAACE,MAAM,CAACQ,IAAI,CAACN,KAAK,CAACO,eAAe,CAAC;UAC/C;QACF,CAAC,MAAM;UACLtB,KAAK,CAAC,UAAU,EAAEe,KAAK,CAACC,aAAa,CAAC;QACxC;MACF,CAAC,CAAC;MACF,OAAOL,UAAU;IACnB;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}