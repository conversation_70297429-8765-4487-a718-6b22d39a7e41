{"ast": null, "code": "/**\n * Configuration options for STOMP Client, each key corresponds to\n * field by the same name in {@link Client}. This can be passed to\n * the constructor of {@link Client} or to [Client#configure]{@link Client#configure}.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class StompConfig {}\n//# sourceMappingURL=stomp-config.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}