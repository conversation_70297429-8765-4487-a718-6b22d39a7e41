{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  EventEmitter = require('events').EventEmitter;\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:polling');\n}\nfunction Polling(Receiver, receiveUrl, AjaxObject) {\n  debug(receiveUrl);\n  EventEmitter.call(this);\n  this.Receiver = Receiver;\n  this.receiveUrl = receiveUrl;\n  this.AjaxObject = AjaxObject;\n  this._scheduleReceiver();\n}\ninherits(Polling, EventEmitter);\nPolling.prototype._scheduleReceiver = function () {\n  debug('_scheduleReceiver');\n  var self = this;\n  var poll = this.poll = new this.Receiver(this.receiveUrl, this.AjaxObject);\n  poll.on('message', function (msg) {\n    debug('message', msg);\n    self.emit('message', msg);\n  });\n  poll.once('close', function (code, reason) {\n    debug('close', code, reason, self.pollIsClosing);\n    self.poll = poll = null;\n    if (!self.pollIsClosing) {\n      if (reason === 'network') {\n        self._scheduleReceiver();\n      } else {\n        self.emit('close', code || 1006, reason);\n        self.removeAllListeners();\n      }\n    }\n  });\n};\nPolling.prototype.abort = function () {\n  debug('abort');\n  this.removeAllListeners();\n  this.pollIsClosing = true;\n  if (this.poll) {\n    this.poll.abort();\n  }\n};\nmodule.exports = Polling;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}