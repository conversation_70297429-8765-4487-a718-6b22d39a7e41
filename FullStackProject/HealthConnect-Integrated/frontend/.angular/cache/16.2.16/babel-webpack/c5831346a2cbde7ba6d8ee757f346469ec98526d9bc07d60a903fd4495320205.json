{"ast": null, "code": "'use strict';\n\nmodule.exports = [\n// streaming transports\nrequire('./transport/websocket'), require('./transport/xhr-streaming'), require('./transport/xdr-streaming'), require('./transport/eventsource'), require('./transport/lib/iframe-wrap')(require('./transport/eventsource'))\n\n// polling transports\n, require('./transport/htmlfile'), require('./transport/lib/iframe-wrap')(require('./transport/htmlfile')), require('./transport/xhr-polling'), require('./transport/xdr-polling'), require('./transport/lib/iframe-wrap')(require('./transport/xhr-polling')), require('./transport/jsonp-polling')];", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}