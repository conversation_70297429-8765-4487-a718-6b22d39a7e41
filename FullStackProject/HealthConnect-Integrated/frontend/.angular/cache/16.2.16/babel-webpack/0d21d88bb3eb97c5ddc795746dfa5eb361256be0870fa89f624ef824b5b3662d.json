{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/notification.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction NotificationBellComponent_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.unreadCount > 99 ? \"99+\" : ctx_r0.unreadCount, \" \");\n  }\n}\nfunction NotificationBellComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function NotificationBellComponent_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.markAllAsRead());\n    });\n    i0.ɵɵtext(1, \" Mark all read \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationBellComponent_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function NotificationBellComponent_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.clearAll());\n    });\n    i0.ɵɵtext(1, \" Clear all \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationBellComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20)(2, \"span\", 21);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction NotificationBellComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵelement(2, \"i\", 24);\n    i0.ɵɵelementStart(3, \"p\", 25);\n    i0.ɵɵtext(4, \"No notifications\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction NotificationBellComponent_div_15_div_9_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 41);\n  }\n  if (rf & 2) {\n    const notification_r11 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"src\", notification_r11.fromUser.avatar, i0.ɵɵsanitizeUrl)(\"alt\", notification_r11.fromUser.name);\n  }\n}\nfunction NotificationBellComponent_div_15_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, NotificationBellComponent_div_15_div_9_img_1_Template, 1, 2, \"img\", 39);\n    i0.ɵɵelementStart(2, \"span\", 40);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const notification_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r11.fromUser.avatar);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(notification_r11.fromUser.name);\n  }\n}\nfunction NotificationBellComponent_div_15_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 42);\n  }\n}\nfunction NotificationBellComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function NotificationBellComponent_div_15_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const notification_r11 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.handleNotificationClick(notification_r11));\n    });\n    i0.ɵɵelementStart(1, \"div\", 27)(2, \"div\", 28);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 29)(5, \"div\", 30);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 31);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, NotificationBellComponent_div_15_div_9_Template, 4, 2, \"div\", 32);\n    i0.ɵɵelementStart(10, \"div\", 33);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 34)(13, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function NotificationBellComponent_div_15_Template_button_click_13_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const notification_r11 = restoredCtx.$implicit;\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.removeNotification(notification_r11, $event));\n    });\n    i0.ɵɵelement(14, \"i\", 36);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(15, NotificationBellComponent_div_15_div_15_Template, 1, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notification_r11 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r5.getNotificationClass(notification_r11));\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r5.getNotificationIcon(notification_r11.type));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(notification_r11.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(notification_r11.message);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r11.fromUser);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.formatTime(notification_r11.timestamp));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !notification_r11.read);\n  }\n}\nfunction NotificationBellComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"a\", 44);\n    i0.ɵɵtext(2, \" View All Notifications \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let NotificationBellComponent = /*#__PURE__*/(() => {\n  class NotificationBellComponent {\n    constructor(notificationService, router) {\n      this.notificationService = notificationService;\n      this.router = router;\n      this.notifications = [];\n      this.unreadCount = 0;\n      this.showDropdown = false;\n      this.loading = false;\n      this.subscriptions = [];\n    }\n    ngOnInit() {\n      // Subscribe to notifications\n      const notificationsSub = this.notificationService.getNotifications().subscribe(notifications => {\n        this.notifications = notifications.slice(0, 10); // Show only latest 10\n      });\n\n      this.subscriptions.push(notificationsSub);\n      // Subscribe to unread count\n      const unreadSub = this.notificationService.getUnreadCount().subscribe(count => this.unreadCount = count);\n      this.subscriptions.push(unreadSub);\n      // Request notification permission\n      this.notificationService.requestNotificationPermission();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    toggleDropdown() {\n      this.showDropdown = !this.showDropdown;\n    }\n    closeDropdown() {\n      this.showDropdown = false;\n    }\n    markAsRead(notification) {\n      if (!notification.read) {\n        this.notificationService.markAsRead(notification.id);\n      }\n    }\n    markAllAsRead() {\n      this.notificationService.markAllAsRead();\n    }\n    handleNotificationClick(notification) {\n      this.markAsRead(notification);\n      if (notification.actionUrl) {\n        this.router.navigate([notification.actionUrl]);\n      }\n      this.closeDropdown();\n    }\n    removeNotification(notification, event) {\n      event.stopPropagation();\n      this.notificationService.removeNotification(notification.id);\n    }\n    clearAll() {\n      this.notificationService.clearAll();\n    }\n    getNotificationIcon(type) {\n      switch (type) {\n        case 'message':\n          return 'fas fa-comment';\n        case 'appointment':\n          return 'fas fa-calendar';\n        case 'urgent':\n          return 'fas fa-exclamation-triangle';\n        case 'system':\n          return 'fas fa-cog';\n        default:\n          return 'fas fa-bell';\n      }\n    }\n    getNotificationClass(notification) {\n      const classes = ['notification-item'];\n      if (!notification.read) {\n        classes.push('unread');\n      }\n      classes.push(`priority-${notification.priority}`);\n      return classes.join(' ');\n    }\n    formatTime(timestamp) {\n      const now = new Date();\n      const diffMs = now.getTime() - timestamp.getTime();\n      const diffMinutes = Math.floor(diffMs / (1000 * 60));\n      const diffHours = Math.floor(diffMinutes / 60);\n      const diffDays = Math.floor(diffHours / 24);\n      if (diffMinutes < 1) {\n        return 'Just now';\n      } else if (diffMinutes < 60) {\n        return `${diffMinutes}m ago`;\n      } else if (diffHours < 24) {\n        return `${diffHours}h ago`;\n      } else if (diffDays < 7) {\n        return `${diffDays}d ago`;\n      } else {\n        return timestamp.toLocaleDateString();\n      }\n    }\n    trackByNotificationId(index, notification) {\n      return notification.id;\n    }\n    static {\n      this.ɵfac = function NotificationBellComponent_Factory(t) {\n        return new (t || NotificationBellComponent)(i0.ɵɵdirectiveInject(i1.NotificationService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: NotificationBellComponent,\n        selectors: [[\"app-notification-bell\"]],\n        decls: 17,\n        vars: 12,\n        consts: [[1, \"notification-bell\", 3, \"clickOutside\"], [\"type\", \"button\", 1, \"btn\", \"btn-link\", \"notification-trigger\", 3, \"click\"], [1, \"fas\", \"fa-bell\"], [\"class\", \"badge bg-danger notification-badge\", 4, \"ngIf\"], [1, \"notification-dropdown\", 3, \"click\"], [1, \"dropdown-header\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"dropdown-actions\"], [\"type\", \"button\", \"class\", \"btn btn-sm btn-link text-primary\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-sm btn-link text-danger\", 3, \"click\", 4, \"ngIf\"], [1, \"notifications-list\"], [\"class\", \"text-center py-3\", 4, \"ngIf\"], [\"class\", \"no-notifications\", 4, \"ngIf\"], [3, \"class\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"dropdown-footer\", 4, \"ngIf\"], [1, \"badge\", \"bg-danger\", \"notification-badge\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-link\", \"text-primary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-link\", \"text-danger\", 3, \"click\"], [1, \"text-center\", \"py-3\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\"], [1, \"visually-hidden\"], [1, \"no-notifications\"], [1, \"text-center\", \"py-4\"], [1, \"fas\", \"fa-bell-slash\", \"fa-2x\", \"text-muted\", \"mb-2\"], [1, \"text-muted\", \"mb-0\"], [3, \"click\"], [1, \"notification-content\"], [1, \"notification-icon\"], [1, \"notification-body\"], [1, \"notification-title\"], [1, \"notification-message\"], [\"class\", \"notification-from\", 4, \"ngIf\"], [1, \"notification-time\"], [1, \"notification-actions\"], [\"type\", \"button\", \"title\", \"Remove notification\", 1, \"btn\", \"btn-sm\", \"btn-link\", \"text-muted\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"class\", \"unread-indicator\", 4, \"ngIf\"], [1, \"notification-from\"], [\"class\", \"from-avatar\", 3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"from-name\"], [1, \"from-avatar\", 3, \"src\", \"alt\"], [1, \"unread-indicator\"], [1, \"dropdown-footer\"], [\"routerLink\", \"/notifications\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", \"w-100\"]],\n        template: function NotificationBellComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵlistener(\"clickOutside\", function NotificationBellComponent_Template_div_clickOutside_0_listener() {\n              return ctx.closeDropdown();\n            });\n            i0.ɵɵelementStart(1, \"button\", 1);\n            i0.ɵɵlistener(\"click\", function NotificationBellComponent_Template_button_click_1_listener() {\n              return ctx.toggleDropdown();\n            });\n            i0.ɵɵelement(2, \"i\", 2);\n            i0.ɵɵtemplate(3, NotificationBellComponent_span_3_Template, 2, 1, \"span\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 4);\n            i0.ɵɵlistener(\"click\", function NotificationBellComponent_Template_div_click_4_listener($event) {\n              return $event.stopPropagation();\n            });\n            i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"h6\", 7);\n            i0.ɵɵtext(8, \"Notifications\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"div\", 8);\n            i0.ɵɵtemplate(10, NotificationBellComponent_button_10_Template, 2, 0, \"button\", 9);\n            i0.ɵɵtemplate(11, NotificationBellComponent_button_11_Template, 2, 0, \"button\", 10);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(12, \"div\", 11);\n            i0.ɵɵtemplate(13, NotificationBellComponent_div_13_Template, 4, 0, \"div\", 12);\n            i0.ɵɵtemplate(14, NotificationBellComponent_div_14_Template, 5, 0, \"div\", 13);\n            i0.ɵɵtemplate(15, NotificationBellComponent_div_15_Template, 16, 9, \"div\", 14);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(16, NotificationBellComponent_div_16_Template, 3, 0, \"div\", 15);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"has-notifications\", ctx.unreadCount > 0);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.unreadCount > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"show\", ctx.showDropdown);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.unreadCount > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.notifications.length > 0);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.notifications.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngForOf\", ctx.notifications)(\"ngForTrackBy\", ctx.trackByNotificationId);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.notifications.length > 0);\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i2.RouterLink],\n        styles: [\".notification-bell[_ngcontent-%COMP%]{position:relative;display:inline-block}.notification-bell[_ngcontent-%COMP%]   .notification-trigger[_ngcontent-%COMP%]{position:relative;padding:.5rem;color:#6c757d;border:none;background:none;font-size:1.25rem;transition:color .2s ease}.notification-bell[_ngcontent-%COMP%]   .notification-trigger[_ngcontent-%COMP%]:hover{color:#495057}.notification-bell[_ngcontent-%COMP%]   .notification-trigger.has-notifications[_ngcontent-%COMP%]{color:#007bff;animation:_ngcontent-%COMP%_bellShake 2s infinite}.notification-bell[_ngcontent-%COMP%]   .notification-trigger[_ngcontent-%COMP%]   .notification-badge[_ngcontent-%COMP%]{position:absolute;top:0;right:0;font-size:.75rem;min-width:18px;height:18px;border-radius:9px;display:flex;align-items:center;justify-content:center;transform:translate(25%,-25%)}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]{position:absolute;top:100%;right:0;width:380px;max-height:500px;background:white;border:1px solid #dee2e6;border-radius:8px;box-shadow:0 4px 12px #00000026;z-index:1050;opacity:0;visibility:hidden;transform:translateY(-10px);transition:all .2s ease}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown.show[_ngcontent-%COMP%]{opacity:1;visibility:visible;transform:translateY(0)}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%]{padding:1rem;border-bottom:1px solid #dee2e6;background:#f8f9fa;border-radius:8px 8px 0 0}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#495057;font-weight:600}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%]   .dropdown-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:.25rem .5rem;font-size:.875rem;text-decoration:none}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%]   .dropdown-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{text-decoration:underline}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]{max-height:350px;overflow-y:auto}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]{position:relative;padding:1rem;border-bottom:1px solid #f1f3f4;cursor:pointer;transition:background-color .2s ease}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.unread[_ngcontent-%COMP%]{background-color:#f0f8ff;border-left:3px solid #007bff}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.unread[_ngcontent-%COMP%]   .notification-title[_ngcontent-%COMP%]{font-weight:600}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.priority-urgent[_ngcontent-%COMP%]{border-left-color:#dc3545}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.priority-urgent[_ngcontent-%COMP%]   .notification-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#dc3545;animation:_ngcontent-%COMP%_pulse 2s infinite}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.priority-high[_ngcontent-%COMP%]{border-left-color:#fd7e14}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-content[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:.75rem}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-icon[_ngcontent-%COMP%]{flex-shrink:0;width:32px;height:32px;border-radius:50%;background:#e9ecef;display:flex;align-items:center;justify-content:center}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.875rem;color:#6c757d}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]{flex:1;min-width:0}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-title[_ngcontent-%COMP%]{font-size:.875rem;color:#212529;margin-bottom:.25rem;line-height:1.4}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-message[_ngcontent-%COMP%]{font-size:.8125rem;color:#6c757d;line-height:1.4;margin-bottom:.5rem;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-from[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-bottom:.25rem}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-from[_ngcontent-%COMP%]   .from-avatar[_ngcontent-%COMP%]{width:20px;height:20px;border-radius:50%;object-fit:cover}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-from[_ngcontent-%COMP%]   .from-name[_ngcontent-%COMP%]{font-size:.75rem;color:#495057;font-weight:500}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-time[_ngcontent-%COMP%]{font-size:.75rem;color:#adb5bd}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-actions[_ngcontent-%COMP%]{flex-shrink:0}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:.25rem;border:none;background:none}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{background:#e9ecef;border-radius:4px}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .unread-indicator[_ngcontent-%COMP%]{position:absolute;top:50%;right:.5rem;width:8px;height:8px;background:#007bff;border-radius:50%;transform:translateY(-50%)}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .no-notifications[_ngcontent-%COMP%]{padding:2rem 1rem}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]{padding:.75rem 1rem;border-top:1px solid #dee2e6;background:#f8f9fa;border-radius:0 0 8px 8px}@keyframes _ngcontent-%COMP%_bellShake{0%,50%,to{transform:rotate(0)}10%,30%{transform:rotate(-10deg)}20%,40%{transform:rotate(10deg)}}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.5}to{opacity:1}}@media (max-width: 768px){.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]{width:320px;right:-50px}}@media (max-width: 576px){.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]{width:280px;right:-100px}}\"]\n      });\n    }\n  }\n  return NotificationBellComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}