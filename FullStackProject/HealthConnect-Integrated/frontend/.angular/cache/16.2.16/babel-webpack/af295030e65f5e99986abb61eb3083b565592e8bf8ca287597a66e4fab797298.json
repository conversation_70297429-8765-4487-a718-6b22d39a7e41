{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  urlUtils = require('../../utils/url'),\n  SenderReceiver = require('./sender-receiver');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:ajax-based');\n}\nfunction createAjaxSender(AjaxObject) {\n  return function (url, payload, callback) {\n    debug('create ajax sender', url, payload);\n    var opt = {};\n    if (typeof payload === 'string') {\n      opt.headers = {\n        'Content-type': 'text/plain'\n      };\n    }\n    var ajaxUrl = urlUtils.addPath(url, '/xhr_send');\n    var xo = new AjaxObject('POST', ajaxUrl, payload, opt);\n    xo.once('finish', function (status) {\n      debug('finish', status);\n      xo = null;\n      if (status !== 200 && status !== 204) {\n        return callback(new Error('http status ' + status));\n      }\n      callback();\n    });\n    return function () {\n      debug('abort');\n      xo.close();\n      xo = null;\n      var err = new Error('Aborted');\n      err.code = 1000;\n      callback(err);\n    };\n  };\n}\nfunction AjaxBasedTransport(transUrl, urlSuffix, Receiver, AjaxObject) {\n  SenderReceiver.call(this, transUrl, urlSuffix, createAjaxSender(AjaxObject), Receiver, AjaxObject);\n}\ninherits(AjaxBasedTransport, SenderReceiver);\nmodule.exports = AjaxBasedTransport;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}