{"ast": null, "code": "import _asyncToGenerator from \"/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/chat.service\";\nimport * as i2 from \"../../../core/services/appointment.service\";\nimport * as i3 from \"../../../core/services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nfunction ChatAccessComponent_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 3)(1, \"span\", 4);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatAccessComponent_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\");\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.iconClass + \" me-2\");\n  }\n}\nexport let ChatAccessComponent = /*#__PURE__*/(() => {\n  class ChatAccessComponent {\n    constructor(chatService, appointmentService, authService, router) {\n      this.chatService = chatService;\n      this.appointmentService = appointmentService;\n      this.authService = authService;\n      this.router = router;\n      this.config = {};\n      this.loading = false;\n    }\n    ngOnInit() {\n      this.currentUser = this.authService.getCurrentUser();\n      this.setDefaults();\n    }\n    setDefaults() {\n      if (!this.config.buttonText) {\n        this.config.buttonText = this.getDefaultButtonText();\n      }\n      if (!this.config.buttonClass) {\n        this.config.buttonClass = 'btn-primary';\n      }\n      if (this.config.showIcon === undefined) {\n        this.config.showIcon = true;\n      }\n      if (!this.config.size) {\n        this.config.size = 'md';\n      }\n    }\n    getDefaultButtonText() {\n      switch (this.config.chatType) {\n        case 'PRE_APPOINTMENT':\n          return 'Chat Before Appointment';\n        case 'POST_APPOINTMENT':\n          return 'Follow-up Chat';\n        case 'URGENT':\n          return 'Urgent Chat';\n        case 'PRESCRIPTION_INQUIRY':\n          return 'Ask About Prescription';\n        case 'FOLLOW_UP':\n          return 'Follow-up Questions';\n        default:\n          return 'Start Chat';\n      }\n    }\n    startChat() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        if (_this.loading) return;\n        _this.loading = true;\n        try {\n          let participantId;\n          // Determine participant ID based on current user role\n          if (_this.currentUser.role === 'PATIENT') {\n            participantId = _this.config.doctorId;\n          } else {\n            participantId = _this.config.patientId;\n          }\n          let chatResponse;\n          if (_this.config.appointmentId) {\n            // Create appointment-specific chat\n            chatResponse = yield _this.appointmentService.createAppointmentChat(_this.config.appointmentId, participantId, _this.config.chatType || 'GENERAL', _this.config.subject).toPromise();\n          } else {\n            // Create general chat\n            chatResponse = yield _this.chatService.createOrGetChat(participantId).toPromise();\n          }\n          // Navigate to chat\n          _this.router.navigate(['/chat'], {\n            queryParams: {\n              chatId: chatResponse.id,\n              appointmentId: _this.config.appointmentId\n            }\n          });\n        } catch (error) {\n          console.error('Error starting chat:', error);\n          // Handle error (show toast, etc.)\n        } finally {\n          _this.loading = false;\n        }\n      })();\n    }\n    get buttonSizeClass() {\n      switch (this.config.size) {\n        case 'sm':\n          return 'btn-sm';\n        case 'lg':\n          return 'btn-lg';\n        default:\n          return '';\n      }\n    }\n    get iconClass() {\n      switch (this.config.chatType) {\n        case 'URGENT':\n          return 'fas fa-exclamation-triangle text-warning';\n        case 'PRESCRIPTION_INQUIRY':\n          return 'fas fa-pills';\n        case 'FOLLOW_UP':\n          return 'fas fa-stethoscope';\n        default:\n          return 'fas fa-comments';\n      }\n    }\n    static {\n      this.ɵfac = function ChatAccessComponent_Factory(t) {\n        return new (t || ChatAccessComponent)(i0.ɵɵdirectiveInject(i1.ChatService), i0.ɵɵdirectiveInject(i2.AppointmentService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ChatAccessComponent,\n        selectors: [[\"app-chat-access\"]],\n        inputs: {\n          config: \"config\"\n        },\n        decls: 4,\n        vars: 6,\n        consts: [[\"type\", \"button\", 3, \"disabled\", \"click\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [1, \"visually-hidden\"]],\n        template: function ChatAccessComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"button\", 0);\n            i0.ɵɵlistener(\"click\", function ChatAccessComponent_Template_button_click_0_listener() {\n              return ctx.startChat();\n            });\n            i0.ɵɵtemplate(1, ChatAccessComponent_span_1_Template, 3, 0, \"span\", 1);\n            i0.ɵɵtemplate(2, ChatAccessComponent_i_2_Template, 1, 2, \"i\", 2);\n            i0.ɵɵtext(3);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassMap(\"btn \" + ctx.config.buttonClass + \" \" + ctx.buttonSizeClass);\n            i0.ɵɵproperty(\"disabled\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.config.showIcon);\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.config.buttonText, \"\\n\");\n          }\n        },\n        dependencies: [i5.NgIf],\n        styles: [\".btn[_ngcontent-%COMP%]{transition:all .2s ease-in-out}.btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 8px #0000001a}.btn[_ngcontent-%COMP%]:disabled{transform:none;box-shadow:none}.text-warning[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.7}to{opacity:1}}\"]\n      });\n    }\n  }\n  return ChatAccessComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}