{"ast": null, "code": "'use strict';\n\nvar logObject = {};\n['log', 'debug', 'warn'].forEach(function (level) {\n  var levelExists;\n  try {\n    levelExists = global.console && global.console[level] && global.console[level].apply;\n  } catch (e) {\n    // do nothing\n  }\n  logObject[level] = levelExists ? function () {\n    return global.console[level].apply(global.console, arguments);\n  } : level === 'log' ? function () {} : logObject.log;\n});\nmodule.exports = logObject;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}