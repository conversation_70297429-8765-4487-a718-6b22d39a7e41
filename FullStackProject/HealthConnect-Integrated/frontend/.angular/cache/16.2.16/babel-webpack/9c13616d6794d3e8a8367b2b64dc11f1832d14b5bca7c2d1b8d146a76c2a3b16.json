{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { catchError, throwError, tap } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nexport class UserService {\n  constructor(http, authService) {\n    this.http = http;\n    this.authService = authService;\n    this.API_URL = environment.apiUrl + '/users';\n  }\n  getHeaders() {\n    const token = this.authService.getToken();\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${token}`\n    });\n  }\n  getCurrentUserProfile() {\n    return this.http.get(`${this.API_URL}/me`, {\n      headers: this.getHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  updateProfile(request) {\n    return this.http.put(`${this.API_URL}/me`, request, {\n      headers: this.getHeaders()\n    }).pipe(tap(updatedUser => {\n      // Update the current user in auth service\n      const currentUser = this.authService.getCurrentUser();\n      if (currentUser) {\n        const updatedCurrentUser = {\n          ...currentUser,\n          ...updatedUser\n        };\n        localStorage.setItem('currentUser', JSON.stringify(updatedCurrentUser));\n      }\n    }), catchError(this.handleError));\n  }\n  getUserById(id) {\n    return this.http.get(`${this.API_URL}/${id}`, {\n      headers: this.getHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  handleError(error) {\n    let errorMessage = 'An error occurred';\n    if (error.error) {\n      if (typeof error.error === 'string') {\n        errorMessage = error.error;\n      } else if (error.error.message) {\n        errorMessage = error.error.message;\n      }\n    } else if (error.message) {\n      errorMessage = error.message;\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  static {\n    this.ɵfac = function UserService_Factory(t) {\n      return new (t || UserService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UserService,\n      factory: UserService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "catchError", "throwError", "tap", "environment", "UserService", "constructor", "http", "authService", "API_URL", "apiUrl", "getHeaders", "token", "getToken", "getCurrentUserProfile", "get", "headers", "pipe", "handleError", "updateProfile", "request", "put", "updatedUser", "currentUser", "getCurrentUser", "updatedCurrentUser", "localStorage", "setItem", "JSON", "stringify", "getUserById", "id", "error", "errorMessage", "message", "Error", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/core/services/user.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, catchError, throwError, tap } from 'rxjs';\nimport { User, UpdateProfileRequest } from '../models/user.model';\nimport { AuthService } from './auth.service';\nimport { environment } from '../../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class UserService {\n  private readonly API_URL = environment.apiUrl + '/users';\n\n  constructor(\n    private http: HttpClient,\n    private authService: AuthService\n  ) {}\n\n  private getHeaders(): HttpHeaders {\n    const token = this.authService.getToken();\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${token}`\n    });\n  }\n\n  getCurrentUserProfile(): Observable<User> {\n    return this.http.get<User>(`${this.API_URL}/me`, {\n      headers: this.getHeaders()\n    }).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  updateProfile(request: UpdateProfileRequest): Observable<User> {\n    return this.http.put<User>(`${this.API_URL}/me`, request, {\n      headers: this.getHeaders()\n    }).pipe(\n      tap(updatedUser => {\n        // Update the current user in auth service\n        const currentUser = this.authService.getCurrentUser();\n        if (currentUser) {\n          const updatedCurrentUser = { ...currentUser, ...updatedUser };\n          localStorage.setItem('currentUser', JSON.stringify(updatedCurrentUser));\n        }\n      }),\n      catchError(this.handleError)\n    );\n  }\n\n  getUserById(id: number): Observable<User> {\n    return this.http.get<User>(`${this.API_URL}/${id}`, {\n      headers: this.getHeaders()\n    }).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  private handleError(error: any): Observable<never> {\n    let errorMessage = 'An error occurred';\n    \n    if (error.error) {\n      if (typeof error.error === 'string') {\n        errorMessage = error.error;\n      } else if (error.error.message) {\n        errorMessage = error.error.message;\n      }\n    } else if (error.message) {\n      errorMessage = error.message;\n    }\n    \n    return throwError(() => new Error(errorMessage));\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAAqBC,UAAU,EAAEC,UAAU,EAAEC,GAAG,QAAQ,MAAM;AAG9D,SAASC,WAAW,QAAQ,mCAAmC;;;;AAK/D,OAAM,MAAOC,WAAW;EAGtBC,YACUC,IAAgB,EAChBC,WAAwB;IADxB,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IAJJ,KAAAC,OAAO,GAAGL,WAAW,CAACM,MAAM,GAAG,QAAQ;EAKrD;EAEKC,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAG,IAAI,CAACJ,WAAW,CAACK,QAAQ,EAAE;IACzC,OAAO,IAAIb,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,eAAe,EAAE,UAAUY,KAAK;KACjC,CAAC;EACJ;EAEAE,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACP,IAAI,CAACQ,GAAG,CAAO,GAAG,IAAI,CAACN,OAAO,KAAK,EAAE;MAC/CO,OAAO,EAAE,IAAI,CAACL,UAAU;KACzB,CAAC,CAACM,IAAI,CACLhB,UAAU,CAAC,IAAI,CAACiB,WAAW,CAAC,CAC7B;EACH;EAEAC,aAAaA,CAACC,OAA6B;IACzC,OAAO,IAAI,CAACb,IAAI,CAACc,GAAG,CAAO,GAAG,IAAI,CAACZ,OAAO,KAAK,EAAEW,OAAO,EAAE;MACxDJ,OAAO,EAAE,IAAI,CAACL,UAAU;KACzB,CAAC,CAACM,IAAI,CACLd,GAAG,CAACmB,WAAW,IAAG;MAChB;MACA,MAAMC,WAAW,GAAG,IAAI,CAACf,WAAW,CAACgB,cAAc,EAAE;MACrD,IAAID,WAAW,EAAE;QACf,MAAME,kBAAkB,GAAG;UAAE,GAAGF,WAAW;UAAE,GAAGD;QAAW,CAAE;QAC7DI,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAACJ,kBAAkB,CAAC,CAAC;;IAE3E,CAAC,CAAC,EACFxB,UAAU,CAAC,IAAI,CAACiB,WAAW,CAAC,CAC7B;EACH;EAEAY,WAAWA,CAACC,EAAU;IACpB,OAAO,IAAI,CAACxB,IAAI,CAACQ,GAAG,CAAO,GAAG,IAAI,CAACN,OAAO,IAAIsB,EAAE,EAAE,EAAE;MAClDf,OAAO,EAAE,IAAI,CAACL,UAAU;KACzB,CAAC,CAACM,IAAI,CACLhB,UAAU,CAAC,IAAI,CAACiB,WAAW,CAAC,CAC7B;EACH;EAEQA,WAAWA,CAACc,KAAU;IAC5B,IAAIC,YAAY,GAAG,mBAAmB;IAEtC,IAAID,KAAK,CAACA,KAAK,EAAE;MACf,IAAI,OAAOA,KAAK,CAACA,KAAK,KAAK,QAAQ,EAAE;QACnCC,YAAY,GAAGD,KAAK,CAACA,KAAK;OAC3B,MAAM,IAAIA,KAAK,CAACA,KAAK,CAACE,OAAO,EAAE;QAC9BD,YAAY,GAAGD,KAAK,CAACA,KAAK,CAACE,OAAO;;KAErC,MAAM,IAAIF,KAAK,CAACE,OAAO,EAAE;MACxBD,YAAY,GAAGD,KAAK,CAACE,OAAO;;IAG9B,OAAOhC,UAAU,CAAC,MAAM,IAAIiC,KAAK,CAACF,YAAY,CAAC,CAAC;EAClD;;;uBA9DW5B,WAAW,EAAA+B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAXpC,WAAW;MAAAqC,OAAA,EAAXrC,WAAW,CAAAsC,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}