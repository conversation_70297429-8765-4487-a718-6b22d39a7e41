{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SharedModule } from '../shared/shared.module';\nimport { ChatModule } from '../chat/chat.module';\nimport { PatientDashboardComponent } from './dashboard/dashboard.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'dashboard',\n  pathMatch: 'full'\n}, {\n  path: 'dashboard',\n  component: PatientDashboardComponent\n}];\nexport class PatientModule {\n  static {\n    this.ɵfac = function PatientModule_Factory(t) {\n      return new (t || PatientModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PatientModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, ChatModule, RouterModule.forChild(routes)]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PatientModule, {\n    declarations: [PatientDashboardComponent],\n    imports: [SharedModule, ChatModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SharedModule", "ChatModule", "PatientDashboardComponent", "routes", "path", "redirectTo", "pathMatch", "component", "PatientModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/patient/patient.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SharedModule } from '../shared/shared.module';\nimport { ChatModule } from '../chat/chat.module';\n\nimport { PatientDashboardComponent } from './dashboard/dashboard.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    redirectTo: 'dashboard',\n    pathMatch: 'full'\n  },\n  {\n    path: 'dashboard',\n    component: PatientDashboardComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    PatientDashboardComponent\n  ],\n  imports: [\n    SharedModule,\n    ChatModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class PatientModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,UAAU,QAAQ,qBAAqB;AAEhD,SAASC,yBAAyB,QAAQ,iCAAiC;;;AAE3E,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,WAAW;EACvBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,WAAW;EACjBG,SAAS,EAAEL;CACZ,CACF;AAYD,OAAM,MAAOM,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBALtBR,YAAY,EACZC,UAAU,EACVF,YAAY,CAACU,QAAQ,CAACN,MAAM,CAAC;IAAA;EAAA;;;2EAGpBK,aAAa;IAAAE,YAAA,GARtBR,yBAAyB;IAAAS,OAAA,GAGzBX,YAAY,EACZC,UAAU,EAAAW,EAAA,CAAAb,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}