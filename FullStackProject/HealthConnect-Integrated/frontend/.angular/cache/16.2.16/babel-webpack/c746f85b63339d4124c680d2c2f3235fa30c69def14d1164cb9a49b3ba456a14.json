{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { catchError, throwError, tap } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nexport let UserService = /*#__PURE__*/(() => {\n  class UserService {\n    constructor(http, authService) {\n      this.http = http;\n      this.authService = authService;\n      this.API_URL = environment.apiUrl + '/users';\n    }\n    getHeaders() {\n      const token = this.authService.getToken();\n      return new HttpHeaders({\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${token}`\n      });\n    }\n    getCurrentUserProfile() {\n      return this.http.get(`${this.API_URL}/me`, {\n        headers: this.getHeaders()\n      }).pipe(catchError(this.handleError));\n    }\n    updateProfile(request) {\n      return this.http.put(`${this.API_URL}/me`, request, {\n        headers: this.getHeaders()\n      }).pipe(tap(updatedUser => {\n        // Update the current user in auth service\n        const currentUser = this.authService.getCurrentUser();\n        if (currentUser) {\n          const updatedCurrentUser = {\n            ...currentUser,\n            ...updatedUser\n          };\n          localStorage.setItem('currentUser', JSON.stringify(updatedCurrentUser));\n        }\n      }), catchError(this.handleError));\n    }\n    getUserById(id) {\n      return this.http.get(`${this.API_URL}/${id}`, {\n        headers: this.getHeaders()\n      }).pipe(catchError(this.handleError));\n    }\n    handleError(error) {\n      let errorMessage = 'An error occurred';\n      if (error.error) {\n        if (typeof error.error === 'string') {\n          errorMessage = error.error;\n        } else if (error.error.message) {\n          errorMessage = error.error.message;\n        }\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      return throwError(() => new Error(errorMessage));\n    }\n    static {\n      this.ɵfac = function UserService_Factory(t) {\n        return new (t || UserService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: UserService,\n        factory: UserService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return UserService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}