{"ast": null, "code": "import { BehaviorSubject, tap, catchError, throwError } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport let AuthService = /*#__PURE__*/(() => {\n  class AuthService {\n    constructor(http, router) {\n      this.http = http;\n      this.router = router;\n      this.API_URL = environment.apiUrl + '/auth';\n      this.currentUserSubject = new BehaviorSubject(null);\n      this.currentUser$ = this.currentUserSubject.asObservable();\n      // Check for existing token on service initialization\n      this.loadUserFromStorage();\n    }\n    loadUserFromStorage() {\n      const token = this.getToken();\n      const userData = localStorage.getItem('currentUser');\n      if (token && userData && !this.isTokenExpired(token)) {\n        try {\n          const user = JSON.parse(userData);\n          this.currentUserSubject.next(user);\n        } catch (error) {\n          console.error('Error parsing stored user data:', error);\n          this.clearAuthData();\n        }\n      } else {\n        // Clear invalid/expired data without navigation\n        this.clearAuthData();\n      }\n    }\n    clearAuthData() {\n      localStorage.removeItem('token');\n      localStorage.removeItem('currentUser');\n      this.currentUserSubject.next(null);\n    }\n    register(request) {\n      return this.http.post(`${this.API_URL}/register`, request).pipe(tap(response => {\n        if (response.token) {\n          this.setAuthData(response);\n        }\n      }), catchError(this.handleError));\n    }\n    login(request) {\n      return this.http.post(`${this.API_URL}/login`, request).pipe(tap(response => {\n        if (response.token) {\n          this.setAuthData(response);\n        }\n      }), catchError(this.handleError));\n    }\n    logout() {\n      // Clear authentication data\n      this.clearAuthData();\n      // Navigate to login only if not already on auth page\n      if (!this.router.url.includes('/auth')) {\n        this.router.navigate(['/auth/login']);\n      }\n    }\n    setAuthData(response) {\n      // Store token\n      localStorage.setItem('token', response.token);\n      // Store user data (without token for security)\n      const userData = {\n        id: response.id,\n        fullName: response.fullName,\n        email: response.email,\n        role: response.role,\n        avatar: response.avatar,\n        specialization: response.specialization,\n        licenseNumber: response.licenseNumber,\n        affiliation: response.affiliation,\n        yearsOfExperience: response.yearsOfExperience,\n        phoneNumber: response.phoneNumber,\n        address: response.address,\n        createdAt: response.createdAt,\n        updatedAt: response.updatedAt\n      };\n      localStorage.setItem('currentUser', JSON.stringify(userData));\n      this.currentUserSubject.next(userData);\n    }\n    getToken() {\n      return localStorage.getItem('token');\n    }\n    getCurrentUser() {\n      return this.currentUserSubject.value;\n    }\n    isAuthenticated() {\n      const token = this.getToken();\n      return !!token && !this.isTokenExpired(token);\n    }\n    hasRole(roles) {\n      const user = this.getCurrentUser();\n      return user ? roles.includes(user.role) : false;\n    }\n    isTokenExpired(token) {\n      try {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        const currentTime = Math.floor(Date.now() / 1000);\n        return payload.exp < currentTime;\n      } catch (error) {\n        return true;\n      }\n    }\n    handleError(error) {\n      let errorMessage = 'An error occurred';\n      if (error.error) {\n        if (typeof error.error === 'string') {\n          errorMessage = error.error;\n        } else if (error.error.message) {\n          errorMessage = error.error.message;\n        }\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      return throwError(() => new Error(errorMessage));\n    }\n    static {\n      this.ɵfac = function AuthService_Factory(t) {\n        return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthService,\n        factory: AuthService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}