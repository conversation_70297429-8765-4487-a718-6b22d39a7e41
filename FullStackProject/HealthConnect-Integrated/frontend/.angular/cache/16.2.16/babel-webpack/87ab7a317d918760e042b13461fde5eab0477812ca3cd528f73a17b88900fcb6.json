{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  EventEmitter = require('events').EventEmitter;\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:buffered-sender');\n}\nfunction BufferedSender(url, sender) {\n  debug(url);\n  EventEmitter.call(this);\n  this.sendBuffer = [];\n  this.sender = sender;\n  this.url = url;\n}\ninherits(BufferedSender, EventEmitter);\nBufferedSender.prototype.send = function (message) {\n  debug('send', message);\n  this.sendBuffer.push(message);\n  if (!this.sendStop) {\n    this.sendSchedule();\n  }\n};\n\n// For polling transports in a situation when in the message callback,\n// new message is being send. If the sending connection was started\n// before receiving one, it is possible to saturate the network and\n// timeout due to the lack of receiving socket. To avoid that we delay\n// sending messages by some small time, in order to let receiving\n// connection be started beforehand. This is only a halfmeasure and\n// does not fix the big problem, but it does make the tests go more\n// stable on slow networks.\nBufferedSender.prototype.sendScheduleWait = function () {\n  debug('sendScheduleWait');\n  var self = this;\n  var tref;\n  this.sendStop = function () {\n    debug('sendStop');\n    self.sendStop = null;\n    clearTimeout(tref);\n  };\n  tref = setTimeout(function () {\n    debug('timeout');\n    self.sendStop = null;\n    self.sendSchedule();\n  }, 25);\n};\nBufferedSender.prototype.sendSchedule = function () {\n  debug('sendSchedule', this.sendBuffer.length);\n  var self = this;\n  if (this.sendBuffer.length > 0) {\n    var payload = '[' + this.sendBuffer.join(',') + ']';\n    this.sendStop = this.sender(this.url, payload, function (err) {\n      self.sendStop = null;\n      if (err) {\n        debug('error', err);\n        self.emit('close', err.code || 1006, 'Sending error: ' + err);\n        self.close();\n      } else {\n        self.sendScheduleWait();\n      }\n    });\n    this.sendBuffer = [];\n  }\n};\nBufferedSender.prototype._cleanup = function () {\n  debug('_cleanup');\n  this.removeAllListeners();\n};\nBufferedSender.prototype.close = function () {\n  debug('close');\n  this._cleanup();\n  if (this.sendStop) {\n    this.sendStop();\n    this.sendStop = null;\n  }\n};\nmodule.exports = BufferedSender;", "map": {"version": 3, "names": ["inherits", "require", "EventEmitter", "debug", "process", "env", "NODE_ENV", "BufferedSender", "url", "sender", "call", "send<PERSON><PERSON><PERSON>", "prototype", "send", "message", "push", "sendStop", "sendSchedule", "sendScheduleWait", "self", "tref", "clearTimeout", "setTimeout", "length", "payload", "join", "err", "emit", "code", "close", "_cleanup", "removeAllListeners", "module", "exports"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/sockjs-client/lib/transport/lib/buffered-sender.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:buffered-sender');\n}\n\nfunction BufferedSender(url, sender) {\n  debug(url);\n  EventEmitter.call(this);\n  this.sendBuffer = [];\n  this.sender = sender;\n  this.url = url;\n}\n\ninherits(BufferedSender, EventEmitter);\n\nBufferedSender.prototype.send = function(message) {\n  debug('send', message);\n  this.sendBuffer.push(message);\n  if (!this.sendStop) {\n    this.sendSchedule();\n  }\n};\n\n// For polling transports in a situation when in the message callback,\n// new message is being send. If the sending connection was started\n// before receiving one, it is possible to saturate the network and\n// timeout due to the lack of receiving socket. To avoid that we delay\n// sending messages by some small time, in order to let receiving\n// connection be started beforehand. This is only a halfmeasure and\n// does not fix the big problem, but it does make the tests go more\n// stable on slow networks.\nBufferedSender.prototype.sendScheduleWait = function() {\n  debug('sendScheduleWait');\n  var self = this;\n  var tref;\n  this.sendStop = function() {\n    debug('sendStop');\n    self.sendStop = null;\n    clearTimeout(tref);\n  };\n  tref = setTimeout(function() {\n    debug('timeout');\n    self.sendStop = null;\n    self.sendSchedule();\n  }, 25);\n};\n\nBufferedSender.prototype.sendSchedule = function() {\n  debug('sendSchedule', this.sendBuffer.length);\n  var self = this;\n  if (this.sendBuffer.length > 0) {\n    var payload = '[' + this.sendBuffer.join(',') + ']';\n    this.sendStop = this.sender(this.url, payload, function(err) {\n      self.sendStop = null;\n      if (err) {\n        debug('error', err);\n        self.emit('close', err.code || 1006, 'Sending error: ' + err);\n        self.close();\n      } else {\n        self.sendScheduleWait();\n      }\n    });\n    this.sendBuffer = [];\n  }\n};\n\nBufferedSender.prototype._cleanup = function() {\n  debug('_cleanup');\n  this.removeAllListeners();\n};\n\nBufferedSender.prototype.close = function() {\n  debug('close');\n  this._cleanup();\n  if (this.sendStop) {\n    this.sendStop();\n    this.sendStop = null;\n  }\n};\n\nmodule.exports = BufferedSender;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,YAAY,GAAGD,OAAO,CAAC,QAAQ,CAAC,CAACC,YAAY;AAGjD,IAAIC,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGF,OAAO,CAAC,OAAO,CAAC,CAAC,+BAA+B,CAAC;AAC3D;AAEA,SAASM,cAAcA,CAACC,GAAG,EAAEC,MAAM,EAAE;EACnCN,KAAK,CAACK,GAAG,CAAC;EACVN,YAAY,CAACQ,IAAI,CAAC,IAAI,CAAC;EACvB,IAAI,CAACC,UAAU,GAAG,EAAE;EACpB,IAAI,CAACF,MAAM,GAAGA,MAAM;EACpB,IAAI,CAACD,GAAG,GAAGA,GAAG;AAChB;AAEAR,QAAQ,CAACO,cAAc,EAAEL,YAAY,CAAC;AAEtCK,cAAc,CAACK,SAAS,CAACC,IAAI,GAAG,UAASC,OAAO,EAAE;EAChDX,KAAK,CAAC,MAAM,EAAEW,OAAO,CAAC;EACtB,IAAI,CAACH,UAAU,CAACI,IAAI,CAACD,OAAO,CAAC;EAC7B,IAAI,CAAC,IAAI,CAACE,QAAQ,EAAE;IAClB,IAAI,CAACC,YAAY,CAAC,CAAC;EACrB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAV,cAAc,CAACK,SAAS,CAACM,gBAAgB,GAAG,YAAW;EACrDf,KAAK,CAAC,kBAAkB,CAAC;EACzB,IAAIgB,IAAI,GAAG,IAAI;EACf,IAAIC,IAAI;EACR,IAAI,CAACJ,QAAQ,GAAG,YAAW;IACzBb,KAAK,CAAC,UAAU,CAAC;IACjBgB,IAAI,CAACH,QAAQ,GAAG,IAAI;IACpBK,YAAY,CAACD,IAAI,CAAC;EACpB,CAAC;EACDA,IAAI,GAAGE,UAAU,CAAC,YAAW;IAC3BnB,KAAK,CAAC,SAAS,CAAC;IAChBgB,IAAI,CAACH,QAAQ,GAAG,IAAI;IACpBG,IAAI,CAACF,YAAY,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;AACR,CAAC;AAEDV,cAAc,CAACK,SAAS,CAACK,YAAY,GAAG,YAAW;EACjDd,KAAK,CAAC,cAAc,EAAE,IAAI,CAACQ,UAAU,CAACY,MAAM,CAAC;EAC7C,IAAIJ,IAAI,GAAG,IAAI;EACf,IAAI,IAAI,CAACR,UAAU,CAACY,MAAM,GAAG,CAAC,EAAE;IAC9B,IAAIC,OAAO,GAAG,GAAG,GAAG,IAAI,CAACb,UAAU,CAACc,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;IACnD,IAAI,CAACT,QAAQ,GAAG,IAAI,CAACP,MAAM,CAAC,IAAI,CAACD,GAAG,EAAEgB,OAAO,EAAE,UAASE,GAAG,EAAE;MAC3DP,IAAI,CAACH,QAAQ,GAAG,IAAI;MACpB,IAAIU,GAAG,EAAE;QACPvB,KAAK,CAAC,OAAO,EAAEuB,GAAG,CAAC;QACnBP,IAAI,CAACQ,IAAI,CAAC,OAAO,EAAED,GAAG,CAACE,IAAI,IAAI,IAAI,EAAE,iBAAiB,GAAGF,GAAG,CAAC;QAC7DP,IAAI,CAACU,KAAK,CAAC,CAAC;MACd,CAAC,MAAM;QACLV,IAAI,CAACD,gBAAgB,CAAC,CAAC;MACzB;IACF,CAAC,CAAC;IACF,IAAI,CAACP,UAAU,GAAG,EAAE;EACtB;AACF,CAAC;AAEDJ,cAAc,CAACK,SAAS,CAACkB,QAAQ,GAAG,YAAW;EAC7C3B,KAAK,CAAC,UAAU,CAAC;EACjB,IAAI,CAAC4B,kBAAkB,CAAC,CAAC;AAC3B,CAAC;AAEDxB,cAAc,CAACK,SAAS,CAACiB,KAAK,GAAG,YAAW;EAC1C1B,KAAK,CAAC,OAAO,CAAC;EACd,IAAI,CAAC2B,QAAQ,CAAC,CAAC;EACf,IAAI,IAAI,CAACd,QAAQ,EAAE;IACjB,IAAI,CAACA,QAAQ,CAAC,CAAC;IACf,IAAI,CAACA,QAAQ,GAAG,IAAI;EACtB;AACF,CAAC;AAEDgB,MAAM,CAACC,OAAO,GAAG1B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}