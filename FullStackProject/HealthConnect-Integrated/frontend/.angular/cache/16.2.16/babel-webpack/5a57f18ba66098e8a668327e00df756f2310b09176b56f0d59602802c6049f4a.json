{"ast": null, "code": "'use strict';\n\nvar Driver = global.WebSocket || global.MozWebSocket;\nif (Driver) {\n  module.exports = function WebSocketBrowserDriver(url) {\n    return new Driver(url);\n  };\n} else {\n  module.exports = undefined;\n}", "map": {"version": 3, "names": ["Driver", "global", "WebSocket", "MozWebSocket", "module", "exports", "WebSocketBrowserDriver", "url", "undefined"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/sockjs-client/lib/transport/browser/websocket.js"], "sourcesContent": ["'use strict';\n\nvar Driver = global.WebSocket || global.MozWebSocket;\nif (Driver) {\n\tmodule.exports = function WebSocketBrowserDriver(url) {\n\t\treturn new Driver(url);\n\t};\n} else {\n\tmodule.exports = undefined;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,MAAM,CAACC,SAAS,IAAID,MAAM,CAACE,YAAY;AACpD,IAAIH,MAAM,EAAE;EACXI,MAAM,CAACC,OAAO,GAAG,SAASC,sBAAsBA,CAACC,GAAG,EAAE;IACrD,OAAO,IAAIP,MAAM,CAACO,GAAG,CAAC;EACvB,CAAC;AACF,CAAC,MAAM;EACNH,MAAM,CAACC,OAAO,GAAGG,SAAS;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}