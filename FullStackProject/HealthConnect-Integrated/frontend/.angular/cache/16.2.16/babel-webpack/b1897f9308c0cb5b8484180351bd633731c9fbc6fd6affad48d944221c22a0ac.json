{"ast": null, "code": "/**\n * @internal\n */\nconst NULL = 0;\n/**\n * @internal\n */\nconst LF = 10;\n/**\n * @internal\n */\nconst CR = 13;\n/**\n * @internal\n */\nconst COLON = 58;\n/**\n * This is an evented, rec descent parser.\n * A stream of Octets can be passed and whenever it recognizes\n * a complete Frame or an incoming ping it will invoke the registered callbacks.\n *\n * All incoming Octets are fed into _onByte function.\n * Depending on current state the _onByte function keeps changing.\n * Depending on the state it keeps accumulating into _token and _results.\n * State is indicated by current value of _onByte, all states are named as _collect.\n *\n * STOMP standards https://stomp.github.io/stomp-specification-1.2.html\n * imply that all lengths are considered in bytes (instead of string lengths).\n * So, before actual parsing, if the incoming data is String it is converted to Octets.\n * This allows faithful implementation of the protocol and allows NULL Octets to be present in the body.\n *\n * There is no peek function on the incoming data.\n * When a state change occurs based on an Octet without consuming the Octet,\n * the Octet, after state change, is fed again (_reinjectByte).\n * This became possible as the state change can be determined by inspecting just one Octet.\n *\n * There are two modes to collect the body, if content-length header is there then it by counting Octets\n * otherwise it is determined by NULL terminator.\n *\n * Following the standards, the command and headers are converted to Strings\n * and the body is returned as Octets.\n * Headers are returned as an array and not as Hash - to allow multiple occurrence of an header.\n *\n * This parser does not use Regular Expressions as that can only operate on Strings.\n *\n * It handles if multiple STOMP frames are given as one chunk, a frame is split into multiple chunks, or\n * any combination there of. The parser remembers its state (any partial frame) and continues when a new chunk\n * is pushed.\n *\n * Typically the higher level function will convert headers to Hash, handle unescaping of header values\n * (which is protocol version specific), and convert body to text.\n *\n * Check the parser.spec.js to understand cases that this parser is supposed to handle.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class Parser {\n  constructor(onFrame, onIncomingPing) {\n    this.onFrame = onFrame;\n    this.onIncomingPing = onIncomingPing;\n    this._encoder = new TextEncoder();\n    this._decoder = new TextDecoder();\n    this._token = [];\n    this._initState();\n  }\n  parseChunk(segment, appendMissingNULLonIncoming = false) {\n    let chunk;\n    if (typeof segment === 'string') {\n      chunk = this._encoder.encode(segment);\n    } else {\n      chunk = new Uint8Array(segment);\n    }\n    // See https://github.com/stomp-js/stompjs/issues/89\n    // Remove when underlying issue is fixed.\n    //\n    // Send a NULL byte, if the last byte of a Text frame was not NULL.F\n    if (appendMissingNULLonIncoming && chunk[chunk.length - 1] !== 0) {\n      const chunkWithNull = new Uint8Array(chunk.length + 1);\n      chunkWithNull.set(chunk, 0);\n      chunkWithNull[chunk.length] = 0;\n      chunk = chunkWithNull;\n    }\n    // tslint:disable-next-line:prefer-for-of\n    for (let i = 0; i < chunk.length; i++) {\n      const byte = chunk[i];\n      this._onByte(byte);\n    }\n  }\n  // The following implements a simple Rec Descent Parser.\n  // The grammar is simple and just one byte tells what should be the next state\n  _collectFrame(byte) {\n    if (byte === NULL) {\n      // Ignore\n      return;\n    }\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      // Incoming Ping\n      this.onIncomingPing();\n      return;\n    }\n    this._onByte = this._collectCommand;\n    this._reinjectByte(byte);\n  }\n  _collectCommand(byte) {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      this._results.command = this._consumeTokenAsUTF8();\n      this._onByte = this._collectHeaders;\n      return;\n    }\n    this._consumeByte(byte);\n  }\n  _collectHeaders(byte) {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      this._setupCollectBody();\n      return;\n    }\n    this._onByte = this._collectHeaderKey;\n    this._reinjectByte(byte);\n  }\n  _reinjectByte(byte) {\n    this._onByte(byte);\n  }\n  _collectHeaderKey(byte) {\n    if (byte === COLON) {\n      this._headerKey = this._consumeTokenAsUTF8();\n      this._onByte = this._collectHeaderValue;\n      return;\n    }\n    this._consumeByte(byte);\n  }\n  _collectHeaderValue(byte) {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      this._results.headers.push([this._headerKey, this._consumeTokenAsUTF8()]);\n      this._headerKey = undefined;\n      this._onByte = this._collectHeaders;\n      return;\n    }\n    this._consumeByte(byte);\n  }\n  _setupCollectBody() {\n    const contentLengthHeader = this._results.headers.filter(header => {\n      return header[0] === 'content-length';\n    })[0];\n    if (contentLengthHeader) {\n      this._bodyBytesRemaining = parseInt(contentLengthHeader[1], 10);\n      this._onByte = this._collectBodyFixedSize;\n    } else {\n      this._onByte = this._collectBodyNullTerminated;\n    }\n  }\n  _collectBodyNullTerminated(byte) {\n    if (byte === NULL) {\n      this._retrievedBody();\n      return;\n    }\n    this._consumeByte(byte);\n  }\n  _collectBodyFixedSize(byte) {\n    // It is post decrement, so that we discard the trailing NULL octet\n    if (this._bodyBytesRemaining-- === 0) {\n      this._retrievedBody();\n      return;\n    }\n    this._consumeByte(byte);\n  }\n  _retrievedBody() {\n    this._results.binaryBody = this._consumeTokenAsRaw();\n    try {\n      this.onFrame(this._results);\n    } catch (e) {\n      console.log(`Ignoring an exception thrown by a frame handler. Original exception: `, e);\n    }\n    this._initState();\n  }\n  // Rec Descent Parser helpers\n  _consumeByte(byte) {\n    this._token.push(byte);\n  }\n  _consumeTokenAsUTF8() {\n    return this._decoder.decode(this._consumeTokenAsRaw());\n  }\n  _consumeTokenAsRaw() {\n    const rawResult = new Uint8Array(this._token);\n    this._token = [];\n    return rawResult;\n  }\n  _initState() {\n    this._results = {\n      command: undefined,\n      headers: [],\n      binaryBody: undefined\n    };\n    this._token = [];\n    this._headerKey = undefined;\n    this._onByte = this._collectFrame;\n  }\n}", "map": {"version": 3, "names": ["NULL", "LF", "CR", "COLON", "<PERSON><PERSON><PERSON>", "constructor", "onFrame", "onIncomingPing", "_encoder", "TextEncoder", "_decoder", "TextDecoder", "_token", "_initState", "parseChunk", "segment", "appendMissingNULLonIncoming", "chunk", "encode", "Uint8Array", "length", "chunkWithNull", "set", "i", "byte", "_onByte", "_collectFrame", "_collectCommand", "_reinjectByte", "_results", "command", "_consumeTokenAsUTF8", "_collectHeaders", "_consumeByte", "_setupCollectBody", "_collect<PERSON><PERSON><PERSON><PERSON>ey", "_<PERSON><PERSON><PERSON>", "_collectHeaderValue", "headers", "push", "undefined", "contentLengthHeader", "filter", "header", "_bodyBytesRemaining", "parseInt", "_collectBodyFixedSize", "_collectBodyNullTerminated", "_retrievedBody", "binaryBody", "_consumeTokenAsRaw", "e", "console", "log", "decode", "rawResult"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/@stomp/stompjs/esm6/parser.js"], "sourcesContent": ["/**\n * @internal\n */\nconst NULL = 0;\n/**\n * @internal\n */\nconst LF = 10;\n/**\n * @internal\n */\nconst CR = 13;\n/**\n * @internal\n */\nconst COLON = 58;\n/**\n * This is an evented, rec descent parser.\n * A stream of Octets can be passed and whenever it recognizes\n * a complete Frame or an incoming ping it will invoke the registered callbacks.\n *\n * All incoming Octets are fed into _onByte function.\n * Depending on current state the _onByte function keeps changing.\n * Depending on the state it keeps accumulating into _token and _results.\n * State is indicated by current value of _onByte, all states are named as _collect.\n *\n * STOMP standards https://stomp.github.io/stomp-specification-1.2.html\n * imply that all lengths are considered in bytes (instead of string lengths).\n * So, before actual parsing, if the incoming data is String it is converted to Octets.\n * This allows faithful implementation of the protocol and allows NULL Octets to be present in the body.\n *\n * There is no peek function on the incoming data.\n * When a state change occurs based on an Octet without consuming the Octet,\n * the Octet, after state change, is fed again (_reinjectByte).\n * This became possible as the state change can be determined by inspecting just one Octet.\n *\n * There are two modes to collect the body, if content-length header is there then it by counting Octets\n * otherwise it is determined by NULL terminator.\n *\n * Following the standards, the command and headers are converted to Strings\n * and the body is returned as Octets.\n * Headers are returned as an array and not as Hash - to allow multiple occurrence of an header.\n *\n * This parser does not use Regular Expressions as that can only operate on Strings.\n *\n * It handles if multiple STOMP frames are given as one chunk, a frame is split into multiple chunks, or\n * any combination there of. The parser remembers its state (any partial frame) and continues when a new chunk\n * is pushed.\n *\n * Typically the higher level function will convert headers to Hash, handle unescaping of header values\n * (which is protocol version specific), and convert body to text.\n *\n * Check the parser.spec.js to understand cases that this parser is supposed to handle.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class Parser {\n    constructor(onFrame, onIncomingPing) {\n        this.onFrame = onFrame;\n        this.onIncomingPing = onIncomingPing;\n        this._encoder = new TextEncoder();\n        this._decoder = new TextDecoder();\n        this._token = [];\n        this._initState();\n    }\n    parseChunk(segment, appendMissingNULLonIncoming = false) {\n        let chunk;\n        if (typeof segment === 'string') {\n            chunk = this._encoder.encode(segment);\n        }\n        else {\n            chunk = new Uint8Array(segment);\n        }\n        // See https://github.com/stomp-js/stompjs/issues/89\n        // Remove when underlying issue is fixed.\n        //\n        // Send a NULL byte, if the last byte of a Text frame was not NULL.F\n        if (appendMissingNULLonIncoming && chunk[chunk.length - 1] !== 0) {\n            const chunkWithNull = new Uint8Array(chunk.length + 1);\n            chunkWithNull.set(chunk, 0);\n            chunkWithNull[chunk.length] = 0;\n            chunk = chunkWithNull;\n        }\n        // tslint:disable-next-line:prefer-for-of\n        for (let i = 0; i < chunk.length; i++) {\n            const byte = chunk[i];\n            this._onByte(byte);\n        }\n    }\n    // The following implements a simple Rec Descent Parser.\n    // The grammar is simple and just one byte tells what should be the next state\n    _collectFrame(byte) {\n        if (byte === NULL) {\n            // Ignore\n            return;\n        }\n        if (byte === CR) {\n            // Ignore CR\n            return;\n        }\n        if (byte === LF) {\n            // Incoming Ping\n            this.onIncomingPing();\n            return;\n        }\n        this._onByte = this._collectCommand;\n        this._reinjectByte(byte);\n    }\n    _collectCommand(byte) {\n        if (byte === CR) {\n            // Ignore CR\n            return;\n        }\n        if (byte === LF) {\n            this._results.command = this._consumeTokenAsUTF8();\n            this._onByte = this._collectHeaders;\n            return;\n        }\n        this._consumeByte(byte);\n    }\n    _collectHeaders(byte) {\n        if (byte === CR) {\n            // Ignore CR\n            return;\n        }\n        if (byte === LF) {\n            this._setupCollectBody();\n            return;\n        }\n        this._onByte = this._collectHeaderKey;\n        this._reinjectByte(byte);\n    }\n    _reinjectByte(byte) {\n        this._onByte(byte);\n    }\n    _collectHeaderKey(byte) {\n        if (byte === COLON) {\n            this._headerKey = this._consumeTokenAsUTF8();\n            this._onByte = this._collectHeaderValue;\n            return;\n        }\n        this._consumeByte(byte);\n    }\n    _collectHeaderValue(byte) {\n        if (byte === CR) {\n            // Ignore CR\n            return;\n        }\n        if (byte === LF) {\n            this._results.headers.push([\n                this._headerKey,\n                this._consumeTokenAsUTF8(),\n            ]);\n            this._headerKey = undefined;\n            this._onByte = this._collectHeaders;\n            return;\n        }\n        this._consumeByte(byte);\n    }\n    _setupCollectBody() {\n        const contentLengthHeader = this._results.headers.filter((header) => {\n            return header[0] === 'content-length';\n        })[0];\n        if (contentLengthHeader) {\n            this._bodyBytesRemaining = parseInt(contentLengthHeader[1], 10);\n            this._onByte = this._collectBodyFixedSize;\n        }\n        else {\n            this._onByte = this._collectBodyNullTerminated;\n        }\n    }\n    _collectBodyNullTerminated(byte) {\n        if (byte === NULL) {\n            this._retrievedBody();\n            return;\n        }\n        this._consumeByte(byte);\n    }\n    _collectBodyFixedSize(byte) {\n        // It is post decrement, so that we discard the trailing NULL octet\n        if (this._bodyBytesRemaining-- === 0) {\n            this._retrievedBody();\n            return;\n        }\n        this._consumeByte(byte);\n    }\n    _retrievedBody() {\n        this._results.binaryBody = this._consumeTokenAsRaw();\n        try {\n            this.onFrame(this._results);\n        }\n        catch (e) {\n            console.log(`Ignoring an exception thrown by a frame handler. Original exception: `, e);\n        }\n        this._initState();\n    }\n    // Rec Descent Parser helpers\n    _consumeByte(byte) {\n        this._token.push(byte);\n    }\n    _consumeTokenAsUTF8() {\n        return this._decoder.decode(this._consumeTokenAsRaw());\n    }\n    _consumeTokenAsRaw() {\n        const rawResult = new Uint8Array(this._token);\n        this._token = [];\n        return rawResult;\n    }\n    _initState() {\n        this._results = {\n            command: undefined,\n            headers: [],\n            binaryBody: undefined,\n        };\n        this._token = [];\n        this._headerKey = undefined;\n        this._onByte = this._collectFrame;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,IAAI,GAAG,CAAC;AACd;AACA;AACA;AACA,MAAMC,EAAE,GAAG,EAAE;AACb;AACA;AACA;AACA,MAAMC,EAAE,GAAG,EAAE;AACb;AACA;AACA;AACA,MAAMC,KAAK,GAAG,EAAE;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,MAAM,CAAC;EAChBC,WAAWA,CAACC,OAAO,EAAEC,cAAc,EAAE;IACjC,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,QAAQ,GAAG,IAAIC,WAAW,CAAC,CAAC;IACjC,IAAI,CAACC,QAAQ,GAAG,IAAIC,WAAW,CAAC,CAAC;IACjC,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACAC,UAAUA,CAACC,OAAO,EAAEC,2BAA2B,GAAG,KAAK,EAAE;IACrD,IAAIC,KAAK;IACT,IAAI,OAAOF,OAAO,KAAK,QAAQ,EAAE;MAC7BE,KAAK,GAAG,IAAI,CAACT,QAAQ,CAACU,MAAM,CAACH,OAAO,CAAC;IACzC,CAAC,MACI;MACDE,KAAK,GAAG,IAAIE,UAAU,CAACJ,OAAO,CAAC;IACnC;IACA;IACA;IACA;IACA;IACA,IAAIC,2BAA2B,IAAIC,KAAK,CAACA,KAAK,CAACG,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;MAC9D,MAAMC,aAAa,GAAG,IAAIF,UAAU,CAACF,KAAK,CAACG,MAAM,GAAG,CAAC,CAAC;MACtDC,aAAa,CAACC,GAAG,CAACL,KAAK,EAAE,CAAC,CAAC;MAC3BI,aAAa,CAACJ,KAAK,CAACG,MAAM,CAAC,GAAG,CAAC;MAC/BH,KAAK,GAAGI,aAAa;IACzB;IACA;IACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,CAACG,MAAM,EAAEG,CAAC,EAAE,EAAE;MACnC,MAAMC,IAAI,GAAGP,KAAK,CAACM,CAAC,CAAC;MACrB,IAAI,CAACE,OAAO,CAACD,IAAI,CAAC;IACtB;EACJ;EACA;EACA;EACAE,aAAaA,CAACF,IAAI,EAAE;IAChB,IAAIA,IAAI,KAAKxB,IAAI,EAAE;MACf;MACA;IACJ;IACA,IAAIwB,IAAI,KAAKtB,EAAE,EAAE;MACb;MACA;IACJ;IACA,IAAIsB,IAAI,KAAKvB,EAAE,EAAE;MACb;MACA,IAAI,CAACM,cAAc,CAAC,CAAC;MACrB;IACJ;IACA,IAAI,CAACkB,OAAO,GAAG,IAAI,CAACE,eAAe;IACnC,IAAI,CAACC,aAAa,CAACJ,IAAI,CAAC;EAC5B;EACAG,eAAeA,CAACH,IAAI,EAAE;IAClB,IAAIA,IAAI,KAAKtB,EAAE,EAAE;MACb;MACA;IACJ;IACA,IAAIsB,IAAI,KAAKvB,EAAE,EAAE;MACb,IAAI,CAAC4B,QAAQ,CAACC,OAAO,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;MAClD,IAAI,CAACN,OAAO,GAAG,IAAI,CAACO,eAAe;MACnC;IACJ;IACA,IAAI,CAACC,YAAY,CAACT,IAAI,CAAC;EAC3B;EACAQ,eAAeA,CAACR,IAAI,EAAE;IAClB,IAAIA,IAAI,KAAKtB,EAAE,EAAE;MACb;MACA;IACJ;IACA,IAAIsB,IAAI,KAAKvB,EAAE,EAAE;MACb,IAAI,CAACiC,iBAAiB,CAAC,CAAC;MACxB;IACJ;IACA,IAAI,CAACT,OAAO,GAAG,IAAI,CAACU,iBAAiB;IACrC,IAAI,CAACP,aAAa,CAACJ,IAAI,CAAC;EAC5B;EACAI,aAAaA,CAACJ,IAAI,EAAE;IAChB,IAAI,CAACC,OAAO,CAACD,IAAI,CAAC;EACtB;EACAW,iBAAiBA,CAACX,IAAI,EAAE;IACpB,IAAIA,IAAI,KAAKrB,KAAK,EAAE;MAChB,IAAI,CAACiC,UAAU,GAAG,IAAI,CAACL,mBAAmB,CAAC,CAAC;MAC5C,IAAI,CAACN,OAAO,GAAG,IAAI,CAACY,mBAAmB;MACvC;IACJ;IACA,IAAI,CAACJ,YAAY,CAACT,IAAI,CAAC;EAC3B;EACAa,mBAAmBA,CAACb,IAAI,EAAE;IACtB,IAAIA,IAAI,KAAKtB,EAAE,EAAE;MACb;MACA;IACJ;IACA,IAAIsB,IAAI,KAAKvB,EAAE,EAAE;MACb,IAAI,CAAC4B,QAAQ,CAACS,OAAO,CAACC,IAAI,CAAC,CACvB,IAAI,CAACH,UAAU,EACf,IAAI,CAACL,mBAAmB,CAAC,CAAC,CAC7B,CAAC;MACF,IAAI,CAACK,UAAU,GAAGI,SAAS;MAC3B,IAAI,CAACf,OAAO,GAAG,IAAI,CAACO,eAAe;MACnC;IACJ;IACA,IAAI,CAACC,YAAY,CAACT,IAAI,CAAC;EAC3B;EACAU,iBAAiBA,CAAA,EAAG;IAChB,MAAMO,mBAAmB,GAAG,IAAI,CAACZ,QAAQ,CAACS,OAAO,CAACI,MAAM,CAAEC,MAAM,IAAK;MACjE,OAAOA,MAAM,CAAC,CAAC,CAAC,KAAK,gBAAgB;IACzC,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,IAAIF,mBAAmB,EAAE;MACrB,IAAI,CAACG,mBAAmB,GAAGC,QAAQ,CAACJ,mBAAmB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC/D,IAAI,CAAChB,OAAO,GAAG,IAAI,CAACqB,qBAAqB;IAC7C,CAAC,MACI;MACD,IAAI,CAACrB,OAAO,GAAG,IAAI,CAACsB,0BAA0B;IAClD;EACJ;EACAA,0BAA0BA,CAACvB,IAAI,EAAE;IAC7B,IAAIA,IAAI,KAAKxB,IAAI,EAAE;MACf,IAAI,CAACgD,cAAc,CAAC,CAAC;MACrB;IACJ;IACA,IAAI,CAACf,YAAY,CAACT,IAAI,CAAC;EAC3B;EACAsB,qBAAqBA,CAACtB,IAAI,EAAE;IACxB;IACA,IAAI,IAAI,CAACoB,mBAAmB,EAAE,KAAK,CAAC,EAAE;MAClC,IAAI,CAACI,cAAc,CAAC,CAAC;MACrB;IACJ;IACA,IAAI,CAACf,YAAY,CAACT,IAAI,CAAC;EAC3B;EACAwB,cAAcA,CAAA,EAAG;IACb,IAAI,CAACnB,QAAQ,CAACoB,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACpD,IAAI;MACA,IAAI,CAAC5C,OAAO,CAAC,IAAI,CAACuB,QAAQ,CAAC;IAC/B,CAAC,CACD,OAAOsB,CAAC,EAAE;MACNC,OAAO,CAACC,GAAG,CAAE,uEAAsE,EAAEF,CAAC,CAAC;IAC3F;IACA,IAAI,CAACtC,UAAU,CAAC,CAAC;EACrB;EACA;EACAoB,YAAYA,CAACT,IAAI,EAAE;IACf,IAAI,CAACZ,MAAM,CAAC2B,IAAI,CAACf,IAAI,CAAC;EAC1B;EACAO,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACrB,QAAQ,CAAC4C,MAAM,CAAC,IAAI,CAACJ,kBAAkB,CAAC,CAAC,CAAC;EAC1D;EACAA,kBAAkBA,CAAA,EAAG;IACjB,MAAMK,SAAS,GAAG,IAAIpC,UAAU,CAAC,IAAI,CAACP,MAAM,CAAC;IAC7C,IAAI,CAACA,MAAM,GAAG,EAAE;IAChB,OAAO2C,SAAS;EACpB;EACA1C,UAAUA,CAAA,EAAG;IACT,IAAI,CAACgB,QAAQ,GAAG;MACZC,OAAO,EAAEU,SAAS;MAClBF,OAAO,EAAE,EAAE;MACXW,UAAU,EAAET;IAChB,CAAC;IACD,IAAI,CAAC5B,MAAM,GAAG,EAAE;IAChB,IAAI,CAACwB,UAAU,GAAGI,SAAS;IAC3B,IAAI,CAACf,OAAO,GAAG,IAAI,CAACC,aAAa;EACrC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}