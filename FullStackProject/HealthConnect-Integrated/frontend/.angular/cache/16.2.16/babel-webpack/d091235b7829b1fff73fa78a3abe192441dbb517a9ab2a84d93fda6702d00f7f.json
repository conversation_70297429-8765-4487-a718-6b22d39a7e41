{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { HttpClientModule } from '@angular/common/http';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\n// Core modules\nimport { CoreModule } from './core/core.module';\nimport { SharedModule } from './shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [BrowserModule, BrowserAnimationsModule, HttpClientModule, ReactiveFormsModule, FormsModule, AppRoutingModule, CoreModule, SharedModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent],\n    imports: [BrowserModule, BrowserAnimationsModule, HttpClientModule, ReactiveFormsModule, FormsModule, AppRoutingModule, CoreModule, SharedModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "BrowserAnimationsModule", "HttpClientModule", "ReactiveFormsModule", "FormsModule", "AppRoutingModule", "AppComponent", "CoreModule", "SharedModule", "AppModule", "bootstrap", "declarations", "imports"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { HttpClientModule } from '@angular/common/http';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\n\n// Core modules\nimport { CoreModule } from './core/core.module';\nimport { SharedModule } from './shared/shared.module';\n\n@NgModule({\n  declarations: [\n    AppComponent\n  ],\n  imports: [\n    BrowserModule,\n    BrowserAnimationsModule,\n    HttpClientModule,\n    ReactiveFormsModule,\n    FormsModule,\n    AppRoutingModule,\n    CoreModule,\n    SharedModule\n  ],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAEjE,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,YAAY,QAAQ,wBAAwB;;AAmBrD,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRJ,YAAY;IAAA;EAAA;;;gBAVtBN,aAAa,EACbC,uBAAuB,EACvBC,gBAAgB,EAChBC,mBAAmB,EACnBC,WAAW,EACXC,gBAAgB,EAChBE,UAAU,EACVC,YAAY;IAAA;EAAA;;;2EAKHC,SAAS;IAAAE,YAAA,GAflBL,YAAY;IAAAM,OAAA,GAGZZ,aAAa,EACbC,uBAAuB,EACvBC,gBAAgB,EAChBC,mBAAmB,EACnBC,WAAW,EACXC,gBAAgB,EAChBE,UAAU,EACVC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}