{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./core/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"./shared/components/notification-bell/notification-bell.component\";\nfunction AppComponent_nav_0_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 11)(2, \"a\", 34);\n    i0.ɵɵelement(3, \"i\", 35);\n    i0.ɵɵtext(4, \"Appointments \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"li\", 11)(6, \"a\", 36);\n    i0.ɵɵelement(7, \"i\", 37);\n    i0.ɵɵtext(8, \"Find Doctors \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"li\", 11)(10, \"a\", 38);\n    i0.ɵɵelement(11, \"i\", 39);\n    i0.ɵɵtext(12, \"Health Assistant \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AppComponent_nav_0_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 11)(2, \"a\", 40);\n    i0.ɵɵelement(3, \"i\", 41);\n    i0.ɵɵtext(4, \"Patients \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"li\", 11)(6, \"a\", 34);\n    i0.ɵɵelement(7, \"i\", 35);\n    i0.ɵɵtext(8, \"Schedule \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AppComponent_nav_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nav\", 3)(1, \"div\", 4)(2, \"a\", 5);\n    i0.ɵɵlistener(\"click\", function AppComponent_nav_0_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.navigateToDashboard());\n    });\n    i0.ɵɵelement(3, \"i\", 6);\n    i0.ɵɵtext(4, \"HealthConnect \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 7);\n    i0.ɵɵelement(6, \"span\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 9)(8, \"ul\", 10)(9, \"li\", 11)(10, \"a\", 12);\n    i0.ɵɵlistener(\"click\", function AppComponent_nav_0_Template_a_click_10_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.navigateToDashboard());\n    });\n    i0.ɵɵelement(11, \"i\", 13);\n    i0.ɵɵtext(12, \"Dashboard \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, AppComponent_nav_0_ng_container_13_Template, 13, 0, \"ng-container\", 14);\n    i0.ɵɵtemplate(14, AppComponent_nav_0_ng_container_14_Template, 9, 0, \"ng-container\", 14);\n    i0.ɵɵelementStart(15, \"li\", 11)(16, \"a\", 15);\n    i0.ɵɵelement(17, \"i\", 16);\n    i0.ɵɵtext(18, \"Messages \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"ul\", 17)(20, \"li\", 11);\n    i0.ɵɵelement(21, \"app-notification-bell\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"li\", 18)(23, \"a\", 19)(24, \"div\", 20);\n    i0.ɵɵelement(25, \"i\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 22);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"ul\", 23)(29, \"li\")(30, \"h6\", 24);\n    i0.ɵɵtext(31);\n    i0.ɵɵelement(32, \"br\");\n    i0.ɵɵelementStart(33, \"small\", 25);\n    i0.ɵɵtext(34);\n    i0.ɵɵpipe(35, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(36, \"li\");\n    i0.ɵɵelement(37, \"hr\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"li\")(39, \"a\", 27);\n    i0.ɵɵlistener(\"click\", function AppComponent_nav_0_Template_a_click_39_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.navigateToProfile());\n    });\n    i0.ɵɵelement(40, \"i\", 28);\n    i0.ɵɵtext(41, \"Profile Settings \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"li\")(43, \"a\", 29);\n    i0.ɵɵelement(44, \"i\", 30);\n    i0.ɵɵtext(45, \"Notifications \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"li\")(47, \"a\", 29);\n    i0.ɵɵelement(48, \"i\", 31);\n    i0.ɵɵtext(49, \"Help & Support \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"li\");\n    i0.ɵɵelement(51, \"hr\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"li\")(53, \"a\", 32);\n    i0.ɵɵlistener(\"click\", function AppComponent_nav_0_Template_a_click_53_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.logout());\n    });\n    i0.ɵɵelement(54, \"i\", 33);\n    i0.ɵɵtext(55, \"Sign Out \");\n    i0.ɵɵelementEnd()()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentUser.role === \"PATIENT\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentUser.role === \"DOCTOR\");\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(ctx_r0.currentUser.fullName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.currentUser.fullName, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(35, 5, ctx_r0.currentUser.role));\n  }\n}\nfunction AppComponent_footer_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"footer\", 42)(1, \"div\", 43)(2, \"small\", 25);\n    i0.ɵɵtext(3, \" \\u00A9 2024 HealthConnect. All rights reserved. | \");\n    i0.ɵɵelementStart(4, \"a\", 44);\n    i0.ɵɵtext(5, \"Privacy Policy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" | \");\n    i0.ɵɵelementStart(7, \"a\", 44);\n    i0.ɵɵtext(8, \"Terms of Service\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n      this.title = 'HealthConnect';\n      this.currentUser = null;\n      this.showNavigation = false;\n    }\n    ngOnInit() {\n      // Subscribe to authentication state\n      this.authService.currentUser$.subscribe(user => {\n        this.currentUser = user;\n        this.updateNavigationVisibility();\n      });\n      // Subscribe to route changes to determine if navigation should be shown\n      this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n        this.updateNavigationVisibility();\n      });\n    }\n    updateNavigationVisibility() {\n      const isAuthRoute = this.router.url.includes('/auth');\n      const isAuthenticated = this.authService.isAuthenticated();\n      this.showNavigation = !isAuthRoute && isAuthenticated && !!this.currentUser;\n    }\n    logout() {\n      this.authService.logout();\n    }\n    navigateToProfile() {\n      this.router.navigate(['/profile']);\n    }\n    navigateToDashboard() {\n      if (this.currentUser?.role === 'DOCTOR') {\n        this.router.navigate(['/doctor/dashboard']);\n      } else if (this.currentUser?.role === 'PATIENT') {\n        this.router.navigate(['/patient/dashboard']);\n      }\n    }\n    static {\n      this.ɵfac = function AppComponent_Factory(t) {\n        return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppComponent,\n        selectors: [[\"app-root\"]],\n        decls: 4,\n        vars: 4,\n        consts: [[\"class\", \"navbar navbar-expand-lg navbar-dark bg-primary\", 4, \"ngIf\"], [1, \"main-content\"], [\"class\", \"bg-light text-center py-3 mt-auto\", 4, \"ngIf\"], [1, \"navbar\", \"navbar-expand-lg\", \"navbar-dark\", \"bg-primary\"], [1, \"container-fluid\"], [1, \"navbar-brand\", \"fw-bold\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"bi\", \"bi-heart-pulse\", \"me-2\"], [\"type\", \"button\", \"data-bs-toggle\", \"collapse\", \"data-bs-target\", \"#navbarNav\", 1, \"navbar-toggler\"], [1, \"navbar-toggler-icon\"], [\"id\", \"navbarNav\", 1, \"collapse\", \"navbar-collapse\"], [1, \"navbar-nav\", \"me-auto\"], [1, \"nav-item\"], [1, \"nav-link\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"bi\", \"bi-house\", \"me-1\"], [4, \"ngIf\"], [\"routerLink\", \"/chat\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"bi\", \"bi-chat-dots\", \"me-1\"], [1, \"navbar-nav\"], [1, \"nav-item\", \"dropdown\"], [\"href\", \"#\", \"id\", \"navbarDropdown\", \"role\", \"button\", \"data-bs-toggle\", \"dropdown\", 1, \"nav-link\", \"dropdown-toggle\", \"d-flex\", \"align-items-center\"], [1, \"rounded-circle\", \"bg-light\", \"text-primary\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"me-2\", 2, \"width\", \"32px\", \"height\", \"32px\"], [1, \"bi\", \"bi-person\"], [1, \"d-none\", \"d-md-inline\"], [1, \"dropdown-menu\", \"dropdown-menu-end\"], [1, \"dropdown-header\"], [1, \"text-muted\"], [1, \"dropdown-divider\"], [1, \"dropdown-item\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"bi\", \"bi-person-gear\", \"me-2\"], [\"href\", \"#\", 1, \"dropdown-item\", 2, \"cursor\", \"pointer\"], [1, \"bi\", \"bi-bell\", \"me-2\"], [1, \"bi\", \"bi-question-circle\", \"me-2\"], [1, \"dropdown-item\", \"text-danger\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"bi\", \"bi-box-arrow-right\", \"me-2\"], [\"routerLink\", \"/appointments\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"bi\", \"bi-calendar\", \"me-1\"], [\"routerLink\", \"/doctors\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"bi\", \"bi-search\", \"me-1\"], [\"routerLink\", \"/health-bot\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"bi\", \"bi-robot\", \"me-1\"], [\"routerLink\", \"/patients\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"bi\", \"bi-people\", \"me-1\"], [1, \"bg-light\", \"text-center\", \"py-3\", \"mt-auto\"], [1, \"container\"], [\"href\", \"#\", 1, \"text-decoration-none\"]],\n        template: function AppComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, AppComponent_nav_0_Template, 56, 7, \"nav\", 0);\n            i0.ɵɵelementStart(1, \"main\", 1);\n            i0.ɵɵelement(2, \"router-outlet\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(3, AppComponent_footer_3_Template, 9, 0, \"footer\", 2);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.showNavigation && ctx.currentUser);\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"with-navbar\", ctx.showNavigation && ctx.currentUser);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.showNavigation);\n          }\n        },\n        dependencies: [i3.NgIf, i2.RouterOutlet, i2.RouterLink, i2.RouterLinkActive, i4.NotificationBellComponent, i3.TitleCasePipe],\n        styles: [\".navbar[_ngcontent-%COMP%]{box-shadow:0 2px 4px #0000001a;z-index:1030}.navbar-brand[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:700}.nav-link[_ngcontent-%COMP%]{font-weight:500;transition:all .3s ease}.nav-link[_ngcontent-%COMP%]:hover{background-color:#ffffff1a;border-radius:.375rem}.nav-link.active[_ngcontent-%COMP%]{background-color:#fff3;border-radius:.375rem}.dropdown-menu[_ngcontent-%COMP%]{border:none;box-shadow:0 4px 6px #0000001a;border-radius:.5rem;min-width:200px}.dropdown-item[_ngcontent-%COMP%]{padding:.5rem 1rem;transition:all .3s ease}.dropdown-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.dropdown-header[_ngcontent-%COMP%]{font-weight:600;color:#495057}.main-content[_ngcontent-%COMP%]{min-height:calc(100vh - 60px);display:flex;flex-direction:column}.main-content.with-navbar[_ngcontent-%COMP%]{min-height:calc(100vh - 116px)}footer[_ngcontent-%COMP%]{margin-top:auto;border-top:1px solid #e9ecef}@media (max-width: 991px){.navbar-nav[_ngcontent-%COMP%]{padding-top:1rem}.navbar-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]{padding:.5rem 1rem}.dropdown-menu[_ngcontent-%COMP%]{position:static!important;transform:none!important;border:none;box-shadow:none;background-color:#ffffff1a;margin-top:.5rem}.dropdown-item[_ngcontent-%COMP%]{color:#fffc}.dropdown-item[_ngcontent-%COMP%]:hover{background-color:#ffffff1a;color:#fff}.dropdown-header[_ngcontent-%COMP%]{color:#ffffffe6}.dropdown-divider[_ngcontent-%COMP%]{border-color:#fff3}}\"]\n      });\n    }\n  }\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}