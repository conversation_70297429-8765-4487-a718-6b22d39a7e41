{"ast": null, "code": "/**\n * Some byte values, used as per STOMP specifications.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport const BYTE = {\n  // LINEFEED byte (octet 10)\n  LF: '\\x0A',\n  // NULL byte (octet 0)\n  NULL: '\\x00'\n};", "map": {"version": 3, "names": ["BYTE", "LF", "NULL"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/@stomp/stompjs/esm6/byte.js"], "sourcesContent": ["/**\n * Some byte values, used as per STOMP specifications.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport const BYTE = {\n    // LINEFEED byte (octet 10)\n    LF: '\\x0A',\n    // NULL byte (octet 0)\n    NULL: '\\x00',\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,IAAI,GAAG;EAChB;EACAC,EAAE,EAAE,MAAM;EACV;EACAC,IAAI,EAAE;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}