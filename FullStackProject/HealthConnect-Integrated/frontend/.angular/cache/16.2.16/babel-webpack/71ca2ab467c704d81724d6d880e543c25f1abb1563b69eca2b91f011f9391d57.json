{"ast": null, "code": "'use strict';\n\nmodule.exports = global.location || {\n  origin: 'http://localhost:80',\n  protocol: 'http:',\n  host: 'localhost',\n  port: 80,\n  href: 'http://localhost/',\n  hash: ''\n};", "map": {"version": 3, "names": ["module", "exports", "global", "location", "origin", "protocol", "host", "port", "href", "hash"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/sockjs-client/lib/location.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = global.location || {\n  origin: 'http://localhost:80'\n, protocol: 'http:'\n, host: 'localhost'\n, port: 80\n, href: 'http://localhost/'\n, hash: ''\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM,CAACC,QAAQ,IAAI;EAClCC,MAAM,EAAE,qBAAqB;EAC7BC,QAAQ,EAAE,OAAO;EACjBC,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,EAAE;EACRC,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE;AACR,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}