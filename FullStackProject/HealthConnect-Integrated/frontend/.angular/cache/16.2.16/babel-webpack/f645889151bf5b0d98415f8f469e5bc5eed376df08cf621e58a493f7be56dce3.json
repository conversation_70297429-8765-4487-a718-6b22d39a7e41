{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { AppointmentsRoutingModule } from './appointments-routing.module';\nimport { SharedModule } from '../shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport let AppointmentsModule = /*#__PURE__*/(() => {\n  class AppointmentsModule {\n    static {\n      this.ɵfac = function AppointmentsModule_Factory(t) {\n        return new (t || AppointmentsModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AppointmentsModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule, AppointmentsRoutingModule, SharedModule]\n      });\n    }\n  }\n  return AppointmentsModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}