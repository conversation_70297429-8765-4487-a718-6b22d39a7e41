{"ast": null, "code": "/**\n * STOMP headers. Many functions calls will accept headers as parameters.\n * The headers sent by <PERSON><PERSON><PERSON> will be available as [IFrame#headers]{@link IFrame#headers}.\n *\n * `key` and `value` must be valid strings.\n * In addition, `key` must not contain `CR`, `LF`, or `:`.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class StompHeaders {}", "map": {"version": 3, "names": ["StompHeaders"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/@stomp/stompjs/esm6/stomp-headers.js"], "sourcesContent": ["/**\n * STOMP headers. Many functions calls will accept headers as parameters.\n * The headers sent by <PERSON><PERSON><PERSON> will be available as [IFrame#headers]{@link IFrame#headers}.\n *\n * `key` and `value` must be valid strings.\n * In addition, `key` must not contain `CR`, `LF`, or `:`.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class StompHeaders {\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}