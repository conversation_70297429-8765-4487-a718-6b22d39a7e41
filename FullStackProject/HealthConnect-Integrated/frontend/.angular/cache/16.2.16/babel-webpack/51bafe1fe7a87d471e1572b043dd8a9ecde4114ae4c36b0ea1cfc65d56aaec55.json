{"ast": null, "code": "import { CommonModule } from '@angular/common';\n// Services\nimport { AuthService } from './services/auth.service';\nimport { UserService } from './services/user.service';\n// Guards\nimport { AuthGuard } from './guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nexport class CoreModule {\n  constructor(parentModule) {\n    if (parentModule) {\n      throw new Error('CoreModule is already loaded. Import it in the AppModule only');\n    }\n  }\n  static {\n    this.ɵfac = function CoreModule_Factory(t) {\n      return new (t || CoreModule)(i0.ɵɵinject(CoreModule, 12));\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CoreModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [AuthService, UserService, AuthGuard],\n      imports: [CommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CoreModule, {\n    imports: [CommonModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "AuthService", "UserService", "<PERSON><PERSON><PERSON><PERSON>", "CoreModule", "constructor", "parentModule", "Error", "i0", "ɵɵinject", "imports"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/core/core.module.ts"], "sourcesContent": ["import { NgModule, Optional, SkipSelf } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { HTTP_INTERCEPTORS } from '@angular/common/http';\n\n// Services\nimport { AuthService } from './services/auth.service';\nimport { UserService } from './services/user.service';\n\n// Guards\nimport { AuthGuard } from './guards/auth.guard';\n\n@NgModule({\n  declarations: [],\n  imports: [\n    CommonModule\n  ],\n  providers: [\n    AuthService,\n    UserService,\n    AuthGuard\n  ]\n})\nexport class CoreModule {\n  constructor(@Optional() @SkipSelf() parentModule: CoreModule) {\n    if (parentModule) {\n      throw new Error('CoreModule is already loaded. Import it in the AppModule only');\n    }\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAG9C;AACA,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,WAAW,QAAQ,yBAAyB;AAErD;AACA,SAASC,SAAS,QAAQ,qBAAqB;;AAa/C,OAAM,MAAOC,UAAU;EACrBC,YAAoCC,YAAwB;IAC1D,IAAIA,YAAY,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,+DAA+D,CAAC;;EAEpF;;;uBALWH,UAAU,EAAAI,EAAA,CAAAC,QAAA,CAAAL,UAAA;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;iBANV,CACTH,WAAW,EACXC,WAAW,EACXC,SAAS,CACV;MAAAO,OAAA,GANCV,YAAY;IAAA;EAAA;;;2EAQHI,UAAU;IAAAM,OAAA,GARnBV,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}