{"ast": null, "code": "import { Versions } from '../versions.js';\nimport { CompatClient } from './compat-client.js';\n/**\n * STOMP Class, acts like a factory to create {@link Client}.\n *\n * Part of `@stomp/stompjs`.\n *\n * **Deprecated**\n *\n * It will be removed in next major version. Please switch to {@link Client}.\n */\nexport let Stomp = /*#__PURE__*/(() => {\n  class Stomp {\n    /**\n     * This method creates a WebSocket client that is connected to\n     * the STOMP server located at the url.\n     *\n     * ```javascript\n     *        var url = \"ws://localhost:61614/stomp\";\n     *        var client = Stomp.client(url);\n     * ```\n     *\n     * **Deprecated**\n     *\n     * It will be removed in next major version. Please switch to {@link Client}\n     * using [Client#brokerURL]{@link Client#brokerURL}.\n     */\n    static client(url, protocols) {\n      // This is a hack to allow another implementation than the standard\n      // HTML5 WebSocket class.\n      //\n      // It is possible to use another class by calling\n      //\n      //     Stomp.WebSocketClass = MozWebSocket\n      //\n      // *prior* to call `Stomp.client()`.\n      //\n      // This hack is deprecated and `Stomp.over()` method should be used\n      // instead.\n      // See remarks on the function Stomp.over\n      if (protocols == null) {\n        protocols = Versions.default.protocolVersions();\n      }\n      const wsFn = () => {\n        const klass = Stomp.WebSocketClass || WebSocket;\n        return new klass(url, protocols);\n      };\n      return new CompatClient(wsFn);\n    }\n    /**\n     * This method is an alternative to [Stomp#client]{@link Stomp#client} to let the user\n     * specify the WebSocket to use (either a standard HTML5 WebSocket or\n     * a similar object).\n     *\n     * In order to support reconnection, the function Client._connect should be callable more than once.\n     * While reconnecting\n     * a new instance of underlying transport (TCP Socket, WebSocket or SockJS) will be needed. So, this function\n     * alternatively allows passing a function that should return a new instance of the underlying socket.\n     *\n     * ```javascript\n     *        var client = Stomp.over(function(){\n     *          return new WebSocket('ws://localhost:15674/ws')\n     *        });\n     * ```\n     *\n     * **Deprecated**\n     *\n     * It will be removed in next major version. Please switch to {@link Client}\n     * using [Client#webSocketFactory]{@link Client#webSocketFactory}.\n     */\n    static over(ws) {\n      let wsFn;\n      if (typeof ws === 'function') {\n        wsFn = ws;\n      } else {\n        console.warn('Stomp.over did not receive a factory, auto reconnect will not work. ' + 'Please see https://stomp-js.github.io/api-docs/latest/classes/Stomp.html#over');\n        wsFn = () => ws;\n      }\n      return new CompatClient(wsFn);\n    }\n  }\n  /**\n   * In case you need to use a non standard class for WebSocket.\n   *\n   * For example when using within NodeJS environment:\n   *\n   * ```javascript\n   *        StompJs = require('../../esm5/');\n   *        Stomp = StompJs.Stomp;\n   *        Stomp.WebSocketClass = require('websocket').w3cwebsocket;\n   * ```\n   *\n   * **Deprecated**\n   *\n   *\n   * It will be removed in next major version. Please switch to {@link Client}\n   * using [Client#webSocketFactory]{@link Client#webSocketFactory}.\n   */\n  // tslint:disable-next-line:variable-name\n\n  //# sourceMappingURL=stomp.js.map\n  Stomp.WebSocketClass = null;\n  return Stomp;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}