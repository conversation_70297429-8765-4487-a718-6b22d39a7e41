{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthGuard } from '../core/guards/auth.guard';\nimport { AppointmentListComponent } from './appointment-list/appointment-list.component';\nimport { AppointmentBookingComponent } from './appointment-booking/appointment-booking.component';\nimport { DoctorSearchComponent } from './doctor-search/doctor-search.component';\nimport { AppointmentDetailsComponent } from './appointment-details/appointment-details.component';\nimport { AppointmentCalendarComponent } from './appointment-calendar/appointment-calendar.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  canActivate: [AuthGuard],\n  children: [{\n    path: '',\n    redirectTo: 'list',\n    pathMatch: 'full'\n  }, {\n    path: 'list',\n    component: AppointmentListComponent\n  }, {\n    path: 'calendar',\n    component: AppointmentCalendarComponent\n  }, {\n    path: 'book',\n    component: AppointmentBookingComponent\n  }, {\n    path: 'doctors',\n    component: DoctorSearchComponent\n  }, {\n    path: ':id',\n    component: AppointmentDetailsComponent\n  }]\n}];\nexport let AppointmentsRoutingModule = /*#__PURE__*/(() => {\n  class AppointmentsRoutingModule {\n    static {\n      this.ɵfac = function AppointmentsRoutingModule_Factory(t) {\n        return new (t || AppointmentsRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AppointmentsRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return AppointmentsRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}