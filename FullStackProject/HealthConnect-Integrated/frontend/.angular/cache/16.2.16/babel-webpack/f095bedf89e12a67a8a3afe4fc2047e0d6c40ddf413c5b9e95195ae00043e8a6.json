{"ast": null, "code": "import { TickerStrategy } from './types.js';\nexport class Ticker {\n  constructor(_interval, _strategy = TickerStrategy.Interval, _debug) {\n    this._interval = _interval;\n    this._strategy = _strategy;\n    this._debug = _debug;\n    this._workerScript = `\n    var startTime = Date.now();\n    setInterval(function() {\n        self.postMessage(Date.now() - startTime);\n    }, ${this._interval});\n  `;\n  }\n  start(tick) {\n    this.stop();\n    if (this.shouldUseWorker()) {\n      this.runWorker(tick);\n    } else {\n      this.runInterval(tick);\n    }\n  }\n  stop() {\n    this.disposeWorker();\n    this.disposeInterval();\n  }\n  shouldUseWorker() {\n    return typeof Worker !== 'undefined' && this._strategy === TickerStrategy.Worker;\n  }\n  runWorker(tick) {\n    this._debug('Using runWorker for outgoing pings');\n    if (!this._worker) {\n      this._worker = new Worker(URL.createObjectURL(new Blob([this._workerScript], {\n        type: 'text/javascript'\n      })));\n      this._worker.onmessage = message => tick(message.data);\n    }\n  }\n  runInterval(tick) {\n    this._debug('Using runInterval for outgoing pings');\n    if (!this._timer) {\n      const startTime = Date.now();\n      this._timer = setInterval(() => {\n        tick(Date.now() - startTime);\n      }, this._interval);\n    }\n  }\n  disposeWorker() {\n    if (this._worker) {\n      this._worker.terminate();\n      delete this._worker;\n      this._debug('Outgoing ping disposeWorker');\n    }\n  }\n  disposeInterval() {\n    if (this._timer) {\n      clearInterval(this._timer);\n      delete this._timer;\n      this._debug('Outgoing ping disposeInterval');\n    }\n  }\n}", "map": {"version": 3, "names": ["TickerStrategy", "Ticker", "constructor", "_interval", "_strategy", "Interval", "_debug", "_workerScript", "start", "tick", "stop", "shouldUseWorker", "runWorker", "runInterval", "disposeWorker", "disposeInterval", "Worker", "_worker", "URL", "createObjectURL", "Blob", "type", "onmessage", "message", "data", "_timer", "startTime", "Date", "now", "setInterval", "terminate", "clearInterval"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/@stomp/stompjs/esm6/ticker.js"], "sourcesContent": ["import { TickerStrategy } from './types.js';\nexport class Ticker {\n    constructor(_interval, _strategy = TickerStrategy.Interval, _debug) {\n        this._interval = _interval;\n        this._strategy = _strategy;\n        this._debug = _debug;\n        this._workerScript = `\n    var startTime = Date.now();\n    setInterval(function() {\n        self.postMessage(Date.now() - startTime);\n    }, ${this._interval});\n  `;\n    }\n    start(tick) {\n        this.stop();\n        if (this.shouldUseWorker()) {\n            this.runWorker(tick);\n        }\n        else {\n            this.runInterval(tick);\n        }\n    }\n    stop() {\n        this.disposeWorker();\n        this.disposeInterval();\n    }\n    shouldUseWorker() {\n        return typeof (Worker) !== 'undefined' && this._strategy === TickerStrategy.Worker;\n    }\n    runWorker(tick) {\n        this._debug('Using runWorker for outgoing pings');\n        if (!this._worker) {\n            this._worker = new Worker(URL.createObjectURL(new Blob([this._workerScript], { type: 'text/javascript' })));\n            this._worker.onmessage = (message) => tick(message.data);\n        }\n    }\n    runInterval(tick) {\n        this._debug('Using runInterval for outgoing pings');\n        if (!this._timer) {\n            const startTime = Date.now();\n            this._timer = setInterval(() => {\n                tick(Date.now() - startTime);\n            }, this._interval);\n        }\n    }\n    disposeWorker() {\n        if (this._worker) {\n            this._worker.terminate();\n            delete this._worker;\n            this._debug('Outgoing ping disposeWorker');\n        }\n    }\n    disposeInterval() {\n        if (this._timer) {\n            clearInterval(this._timer);\n            delete this._timer;\n            this._debug('Outgoing ping disposeInterval');\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,YAAY;AAC3C,OAAO,MAAMC,MAAM,CAAC;EAChBC,WAAWA,CAACC,SAAS,EAAEC,SAAS,GAAGJ,cAAc,CAACK,QAAQ,EAAEC,MAAM,EAAE;IAChE,IAAI,CAACH,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACE,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,aAAa,GAAI;AAC9B;AACA;AACA;AACA,SAAS,IAAI,CAACJ,SAAU;AACxB,GAAG;EACC;EACAK,KAAKA,CAACC,IAAI,EAAE;IACR,IAAI,CAACC,IAAI,CAAC,CAAC;IACX,IAAI,IAAI,CAACC,eAAe,CAAC,CAAC,EAAE;MACxB,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACI,WAAW,CAACJ,IAAI,CAAC;IAC1B;EACJ;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACI,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,eAAe,CAAC,CAAC;EAC1B;EACAJ,eAAeA,CAAA,EAAG;IACd,OAAO,OAAQK,MAAO,KAAK,WAAW,IAAI,IAAI,CAACZ,SAAS,KAAKJ,cAAc,CAACgB,MAAM;EACtF;EACAJ,SAASA,CAACH,IAAI,EAAE;IACZ,IAAI,CAACH,MAAM,CAAC,oCAAoC,CAAC;IACjD,IAAI,CAAC,IAAI,CAACW,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAID,MAAM,CAACE,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAAC,IAAI,CAACb,aAAa,CAAC,EAAE;QAAEc,IAAI,EAAE;MAAkB,CAAC,CAAC,CAAC,CAAC;MAC3G,IAAI,CAACJ,OAAO,CAACK,SAAS,GAAIC,OAAO,IAAKd,IAAI,CAACc,OAAO,CAACC,IAAI,CAAC;IAC5D;EACJ;EACAX,WAAWA,CAACJ,IAAI,EAAE;IACd,IAAI,CAACH,MAAM,CAAC,sCAAsC,CAAC;IACnD,IAAI,CAAC,IAAI,CAACmB,MAAM,EAAE;MACd,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B,IAAI,CAACH,MAAM,GAAGI,WAAW,CAAC,MAAM;QAC5BpB,IAAI,CAACkB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS,CAAC;MAChC,CAAC,EAAE,IAAI,CAACvB,SAAS,CAAC;IACtB;EACJ;EACAW,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACG,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACa,SAAS,CAAC,CAAC;MACxB,OAAO,IAAI,CAACb,OAAO;MACnB,IAAI,CAACX,MAAM,CAAC,6BAA6B,CAAC;IAC9C;EACJ;EACAS,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACU,MAAM,EAAE;MACbM,aAAa,CAAC,IAAI,CAACN,MAAM,CAAC;MAC1B,OAAO,IAAI,CAACA,MAAM;MAClB,IAAI,CAACnB,MAAM,CAAC,+BAA+B,CAAC;IAChD;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}