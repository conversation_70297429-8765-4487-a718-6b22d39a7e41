{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../core/services/chat.service\";\nimport * as i3 from \"../../core/services/auth.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../message-item/message-item.component\";\nconst _c0 = [\"messagesContainer\"];\nfunction ChatWindowComponent_div_0_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"span\", 26);\n    i0.ɵɵtext(3, \"Loading messages...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ChatWindowComponent_div_0_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"i\", 28);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No messages yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"small\");\n    i0.ɵɵtext(5, \"Start the conversation!\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatWindowComponent_div_0_div_16_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"span\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r11.formatMessageDate(message_r9.createdAt));\n  }\n}\nfunction ChatWindowComponent_div_0_div_16_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ChatWindowComponent_div_0_div_16_ng_container_1_div_1_Template, 3, 1, \"div\", 32);\n    i0.ɵɵelement(2, \"app-message-item\", 33);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r9 = ctx.$implicit;\n    const i_r10 = ctx.index;\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.shouldShowDateSeparator(i_r10));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"message\", message_r9)(\"isOwn\", message_r9.sender.id === (ctx_r7.currentUser == null ? null : ctx_r7.currentUser.id));\n  }\n}\nfunction ChatWindowComponent_div_0_div_16_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37);\n    i0.ɵɵelement(2, \"span\")(3, \"span\")(4, \"span\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"small\", 38);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    let tmp_0_0;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", (tmp_0_0 = ctx_r8.getOtherParticipant()) == null ? null : tmp_0_0.fullName, \" is typing...\");\n  }\n}\nfunction ChatWindowComponent_div_0_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, ChatWindowComponent_div_0_div_16_ng_container_1_Template, 3, 3, \"ng-container\", 30);\n    i0.ɵɵtemplate(2, ChatWindowComponent_div_0_div_16_div_2_Template, 7, 1, \"div\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.messages);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isTyping());\n  }\n}\nfunction ChatWindowComponent_div_0_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"small\", 40);\n    i0.ɵɵelement(2, \"i\", 41);\n    i0.ɵɵtext(3, \" Connection lost. Trying to reconnect... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatWindowComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n    i0.ɵɵelement(3, \"img\", 5);\n    i0.ɵɵelementStart(4, \"div\", 6)(5, \"h6\", 7);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"small\", 8);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 9)(10, \"span\", 10);\n    i0.ɵɵelement(11, \"i\", 11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 12, 13);\n    i0.ɵɵtemplate(14, ChatWindowComponent_div_0_div_14_Template, 4, 0, \"div\", 14);\n    i0.ɵɵtemplate(15, ChatWindowComponent_div_0_div_15_Template, 6, 0, \"div\", 15);\n    i0.ɵɵtemplate(16, ChatWindowComponent_div_0_div_16_Template, 3, 2, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 17)(18, \"form\", 18);\n    i0.ɵɵlistener(\"ngSubmit\", function ChatWindowComponent_div_0_Template_form_ngSubmit_18_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.sendMessage());\n    });\n    i0.ɵɵelementStart(19, \"div\", 19)(20, \"input\", 20);\n    i0.ɵɵlistener(\"input\", function ChatWindowComponent_div_0_Template_input_input_20_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onTyping());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 21);\n    i0.ɵɵelement(22, \"i\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(23, ChatWindowComponent_div_0_div_23_Template, 4, 0, \"div\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ((tmp_0_0 = ctx_r0.getOtherParticipant()) == null ? null : tmp_0_0.avatar) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", (tmp_1_0 = ctx_r0.getOtherParticipant()) == null ? null : tmp_1_0.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((tmp_2_0 = ctx_r0.getOtherParticipant()) == null ? null : tmp_2_0.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ((tmp_3_0 = ctx_r0.getOtherParticipant()) == null ? null : tmp_3_0.role) === \"DOCTOR\" ? \"Dr. \" + ((tmp_3_0 = ctx_r0.getOtherParticipant()) == null ? null : tmp_3_0.specialization) : \"Patient\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"connected\", ctx_r0.connectionStatus)(\"disconnected\", !ctx_r0.connectionStatus);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"bi-wifi\", ctx_r0.connectionStatus)(\"bi-wifi-off\", !ctx_r0.connectionStatus);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading && ctx_r0.messages.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading && ctx_r0.messages.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.messageForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.connectionStatus);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.messageForm.valid || !ctx_r0.connectionStatus);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.connectionStatus);\n  }\n}\nfunction ChatWindowComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 43);\n    i0.ɵɵelement(2, \"i\", 28);\n    i0.ɵɵelementStart(3, \"h5\");\n    i0.ɵɵtext(4, \"Select a conversation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Choose a conversation from the list to start messaging\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class ChatWindowComponent {\n  constructor(fb, chatService, authService) {\n    this.fb = fb;\n    this.chatService = chatService;\n    this.authService = authService;\n    this.chat = null;\n    this.messages = [];\n    this.loading = false;\n    this.typingUsers = new Set();\n    this.connectionStatus = false;\n    this.subscriptions = [];\n    this.shouldScrollToBottom = false;\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]]\n    });\n  }\n  ngOnInit() {\n    this.currentUser = this.authService.getCurrentUser();\n    this.subscribeToServices();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n  }\n  ngAfterViewChecked() {\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n  subscribeToServices() {\n    // Subscribe to connection status\n    const connectionSub = this.chatService.connectionStatus$.subscribe(status => {\n      this.connectionStatus = status;\n    });\n    this.subscriptions.push(connectionSub);\n    // Subscribe to new messages\n    const messagesSub = this.chatService.messages$.subscribe(message => {\n      if (this.chat && message.chatId === this.chat.id) {\n        this.messages.push(message);\n        this.shouldScrollToBottom = true;\n        // Mark as read if not from current user\n        if (message.sender.id !== this.currentUser?.id) {\n          this.chatService.markMessagesAsRead(this.chat.id).subscribe();\n        }\n      }\n    });\n    this.subscriptions.push(messagesSub);\n    // Subscribe to typing notifications\n    const typingSub = this.chatService.typing$.subscribe(notification => {\n      this.handleTypingNotification(notification);\n    });\n    this.subscriptions.push(typingSub);\n  }\n  loadChat(chat) {\n    this.chat = chat;\n    this.messages = [];\n    this.typingUsers.clear();\n    if (chat) {\n      this.loadMessages();\n      this.chatService.subscribeToChatMessages(chat.id);\n    }\n  }\n  loadMessages() {\n    if (!this.chat) return;\n    this.loading = true;\n    this.chatService.getChatMessages(this.chat.id).subscribe({\n      next: messages => {\n        this.messages = messages.reverse(); // Reverse to show oldest first\n        this.loading = false;\n        this.shouldScrollToBottom = true;\n      },\n      error: error => {\n        console.error('Failed to load messages:', error);\n        this.loading = false;\n      }\n    });\n  }\n  sendMessage() {\n    if (!this.messageForm.valid || !this.chat || !this.connectionStatus) {\n      return;\n    }\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) {\n      return;\n    }\n    try {\n      this.chatService.sendMessage(this.chat.id, content);\n      this.messageForm.reset();\n      this.stopTyping();\n    } catch (error) {\n      console.error('Failed to send message:', error);\n    }\n  }\n  onTyping() {\n    if (!this.chat) return;\n    this.chatService.sendTypingNotification(this.chat.id, true);\n    // Clear existing timeout\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    // Set timeout to stop typing after 3 seconds\n    this.typingTimeout = setTimeout(() => {\n      this.stopTyping();\n    }, 3000);\n  }\n  stopTyping() {\n    if (!this.chat) return;\n    this.chatService.sendTypingNotification(this.chat.id, false);\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n      this.typingTimeout = null;\n    }\n  }\n  handleTypingNotification(notification) {\n    if (notification.userId === this.currentUser?.id) {\n      return; // Ignore own typing notifications\n    }\n\n    if (notification.status === 'typing') {\n      this.typingUsers.add(notification.userId);\n    } else {\n      this.typingUsers.delete(notification.userId);\n    }\n  }\n  scrollToBottom() {\n    try {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    } catch (err) {\n      console.error('Error scrolling to bottom:', err);\n    }\n  }\n  getOtherParticipant() {\n    if (!this.chat) return null;\n    return this.currentUser?.role === 'PATIENT' ? this.chat.doctor : this.chat.patient;\n  }\n  isTyping() {\n    return this.typingUsers.size > 0;\n  }\n  formatMessageTime(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString([], {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  formatMessageDate(dateString) {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return 'Today';\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Yesterday';\n    } else {\n      return date.toLocaleDateString();\n    }\n  }\n  shouldShowDateSeparator(index) {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    const currentDate = new Date(currentMessage.createdAt).toDateString();\n    const previousDate = new Date(previousMessage.createdAt).toDateString();\n    return currentDate !== previousDate;\n  }\n  static {\n    this.ɵfac = function ChatWindowComponent_Factory(t) {\n      return new (t || ChatWindowComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ChatService), i0.ɵɵdirectiveInject(i3.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatWindowComponent,\n      selectors: [[\"app-chat-window\"]],\n      viewQuery: function ChatWindowComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n        }\n      },\n      inputs: {\n        chat: \"chat\"\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"chat-window\", 4, \"ngIf\"], [\"class\", \"chat-placeholder\", 4, \"ngIf\"], [1, \"chat-window\"], [1, \"chat-header\"], [1, \"participant-info\"], [1, \"participant-avatar\", 3, \"src\", \"alt\"], [1, \"participant-details\"], [1, \"mb-0\"], [1, \"text-muted\"], [1, \"connection-status\"], [1, \"status-indicator\"], [1, \"bi\"], [1, \"messages-container\"], [\"messagesContainer\", \"\"], [\"class\", \"text-center p-3\", 4, \"ngIf\"], [\"class\", \"text-center p-4 text-muted\", 4, \"ngIf\"], [\"class\", \"messages-list\", 4, \"ngIf\"], [1, \"message-input-container\"], [1, \"message-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"input-group\"], [\"type\", \"text\", \"formControlName\", \"content\", \"placeholder\", \"Type a message...\", \"autocomplete\", \"off\", 1, \"form-control\", 3, \"disabled\", \"input\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [1, \"bi\", \"bi-send\"], [\"class\", \"connection-warning\", 4, \"ngIf\"], [1, \"text-center\", \"p-3\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\"], [1, \"visually-hidden\"], [1, \"text-center\", \"p-4\", \"text-muted\"], [1, \"bi\", \"bi-chat-square-text\", \"fs-1\", \"mb-3\", \"d-block\"], [1, \"messages-list\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"typing-indicator\", 4, \"ngIf\"], [\"class\", \"date-separator\", 4, \"ngIf\"], [3, \"message\", \"isOwn\"], [1, \"date-separator\"], [1, \"date-label\"], [1, \"typing-indicator\"], [1, \"typing-dots\"], [1, \"text-muted\", \"ms-2\"], [1, \"connection-warning\"], [1, \"text-warning\"], [1, \"bi\", \"bi-exclamation-triangle\", \"me-1\"], [1, \"chat-placeholder\"], [1, \"text-center\", \"text-muted\"]],\n      template: function ChatWindowComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ChatWindowComponent_div_0_Template, 24, 19, \"div\", 0);\n          i0.ɵɵtemplate(1, ChatWindowComponent_div_1_Template, 7, 0, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.chat);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.chat);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.MessageItemComponent],\n      styles: [\".chat-window[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1rem;\\n  border-bottom: 1px solid #e9ecef;\\n  background-color: #f8f9fa;\\n}\\n.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .participant-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  margin-right: 0.75rem;\\n  border: 2px solid #e9ecef;\\n}\\n.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .participant-details[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n}\\n.chat-header[_ngcontent-%COMP%]   .connection-status[_ngcontent-%COMP%]   .status-indicator.connected[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.chat-header[_ngcontent-%COMP%]   .connection-status[_ngcontent-%COMP%]   .status-indicator.disconnected[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n\\n.messages-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 1rem;\\n  background-color: #f8f9fa;\\n}\\n\\n.messages-list[_ngcontent-%COMP%]   .date-separator[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 1rem 0;\\n}\\n.messages-list[_ngcontent-%COMP%]   .date-separator[_ngcontent-%COMP%]   .date-label[_ngcontent-%COMP%] {\\n  background-color: #e9ecef;\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 1rem;\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n}\\n.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin: 0.5rem 0;\\n}\\n.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #e9ecef;\\n  padding: 0.5rem 0.75rem;\\n  border-radius: 1rem;\\n}\\n.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  width: 6px;\\n  height: 6px;\\n  background-color: #6c757d;\\n  border-radius: 50%;\\n  margin: 0 2px;\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n}\\n.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: -0.32s;\\n}\\n.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: -0.16s;\\n}\\n\\n.message-input-container[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  border-top: 1px solid #e9ecef;\\n  background-color: white;\\n}\\n.message-input-container[_ngcontent-%COMP%]   .message-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border-right: none;\\n}\\n.message-input-container[_ngcontent-%COMP%]   .message-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  border-color: #80bdff;\\n}\\n.message-input-container[_ngcontent-%COMP%]   .message-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-left: none;\\n  padding: 0.5rem 1rem;\\n}\\n.message-input-container[_ngcontent-%COMP%]   .message-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n}\\n.message-input-container[_ngcontent-%COMP%]   .connection-warning[_ngcontent-%COMP%] {\\n  margin-top: 0.5rem;\\n  text-align: center;\\n}\\n\\n.chat-placeholder[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: #f8f9fa;\\n}\\n.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%] {\\n  max-width: 300px;\\n}\\n.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0%, 60%, 100% {\\n    transform: translateY(0);\\n  }\\n  30% {\\n    transform: translateY(-10px);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .chat-header[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .participant-avatar[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .messages-container[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .message-input-container[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r11", "formatMessageDate", "message_r9", "createdAt", "ɵɵelementContainerStart", "ɵɵtemplate", "ChatWindowComponent_div_0_div_16_ng_container_1_div_1_Template", "ɵɵelementContainerEnd", "ɵɵproperty", "ctx_r7", "shouldShowDateSeparator", "i_r10", "sender", "id", "currentUser", "ɵɵtextInterpolate1", "tmp_0_0", "ctx_r8", "getOtherParticipant", "fullName", "ChatWindowComponent_div_0_div_16_ng_container_1_Template", "ChatWindowComponent_div_0_div_16_div_2_Template", "ctx_r5", "messages", "isTyping", "ChatWindowComponent_div_0_div_14_Template", "ChatWindowComponent_div_0_div_15_Template", "ChatWindowComponent_div_0_div_16_Template", "ɵɵlistener", "ChatWindowComponent_div_0_Template_form_ngSubmit_18_listener", "ɵɵrestoreView", "_r14", "ctx_r13", "ɵɵnextContext", "ɵɵresetView", "sendMessage", "ChatWindowComponent_div_0_Template_input_input_20_listener", "ctx_r15", "onTyping", "ChatWindowComponent_div_0_div_23_Template", "ctx_r0", "avatar", "ɵɵsanitizeUrl", "tmp_1_0", "tmp_2_0", "tmp_3_0", "role", "specialization", "ɵɵclassProp", "connectionStatus", "loading", "length", "messageForm", "valid", "ChatWindowComponent", "constructor", "fb", "chatService", "authService", "chat", "typingUsers", "Set", "subscriptions", "shouldScrollToBottom", "group", "content", "required", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "getCurrentUser", "subscribeToServices", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "typingTimeout", "clearTimeout", "ngAfterViewChecked", "scrollToBottom", "connectionSub", "connectionStatus$", "subscribe", "status", "push", "messagesSub", "messages$", "message", "chatId", "markMessagesAsRead", "typingSub", "typing$", "notification", "handleTypingNotification", "loadChat", "clear", "loadMessages", "subscribeToChatMessages", "getChatMessages", "next", "reverse", "error", "console", "get", "value", "trim", "reset", "stopTyping", "sendTypingNotification", "setTimeout", "userId", "add", "delete", "messagesContainer", "element", "nativeElement", "scrollTop", "scrollHeight", "err", "doctor", "patient", "size", "formatMessageTime", "dateString", "date", "Date", "toLocaleTimeString", "hour", "minute", "today", "yesterday", "setDate", "getDate", "toDateString", "toLocaleDateString", "index", "currentMessage", "previousMessage", "currentDate", "previousDate", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ChatService", "i3", "AuthService", "selectors", "viewQuery", "ChatWindowComponent_Query", "rf", "ctx", "ChatWindowComponent_div_0_Template", "ChatWindowComponent_div_1_Template"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/chat/chat-window/chat-window.component.ts", "/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/chat/chat-window/chat-window.component.html"], "sourcesContent": ["import { Component, Input, OnInit, OnDestroy, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { Chat, Message, TypingNotification } from '../../core/models/chat.model';\nimport { ChatService } from '../../core/services/chat.service';\nimport { AuthService } from '../../core/services/auth.service';\n\n@Component({\n  selector: 'app-chat-window',\n  templateUrl: './chat-window.component.html',\n  styleUrls: ['./chat-window.component.scss']\n})\nexport class ChatWindowComponent implements OnInit, OnDestroy, AfterViewChecked {\n  @Input() chat: Chat | null = null;\n  @ViewChild('messagesContainer') messagesContainer!: ElementRef;\n  \n  messages: Message[] = [];\n  messageForm: FormGroup;\n  currentUser: any;\n  loading = false;\n  typingUsers: Set<number> = new Set();\n  connectionStatus = false;\n  \n  private subscriptions: Subscription[] = [];\n  private typingTimeout: any;\n  private shouldScrollToBottom = false;\n\n  constructor(\n    private fb: FormBuilder,\n    private chatService: ChatService,\n    private authService: AuthService\n  ) {\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]]\n    });\n  }\n\n  ngOnInit(): void {\n    this.currentUser = this.authService.getCurrentUser();\n    this.subscribeToServices();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n  }\n\n  ngAfterViewChecked(): void {\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n\n  private subscribeToServices(): void {\n    // Subscribe to connection status\n    const connectionSub = this.chatService.connectionStatus$.subscribe(status => {\n      this.connectionStatus = status;\n    });\n    this.subscriptions.push(connectionSub);\n\n    // Subscribe to new messages\n    const messagesSub = this.chatService.messages$.subscribe(message => {\n      if (this.chat && message.chatId === this.chat.id) {\n        this.messages.push(message);\n        this.shouldScrollToBottom = true;\n        \n        // Mark as read if not from current user\n        if (message.sender.id !== this.currentUser?.id) {\n          this.chatService.markMessagesAsRead(this.chat.id).subscribe();\n        }\n      }\n    });\n    this.subscriptions.push(messagesSub);\n\n    // Subscribe to typing notifications\n    const typingSub = this.chatService.typing$.subscribe(notification => {\n      this.handleTypingNotification(notification);\n    });\n    this.subscriptions.push(typingSub);\n  }\n\n  loadChat(chat: Chat): void {\n    this.chat = chat;\n    this.messages = [];\n    this.typingUsers.clear();\n    \n    if (chat) {\n      this.loadMessages();\n      this.chatService.subscribeToChatMessages(chat.id);\n    }\n  }\n\n  private loadMessages(): void {\n    if (!this.chat) return;\n    \n    this.loading = true;\n    this.chatService.getChatMessages(this.chat.id).subscribe({\n      next: (messages) => {\n        this.messages = messages.reverse(); // Reverse to show oldest first\n        this.loading = false;\n        this.shouldScrollToBottom = true;\n      },\n      error: (error) => {\n        console.error('Failed to load messages:', error);\n        this.loading = false;\n      }\n    });\n  }\n\n  sendMessage(): void {\n    if (!this.messageForm.valid || !this.chat || !this.connectionStatus) {\n      return;\n    }\n\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) {\n      return;\n    }\n\n    try {\n      this.chatService.sendMessage(this.chat.id, content);\n      this.messageForm.reset();\n      this.stopTyping();\n    } catch (error) {\n      console.error('Failed to send message:', error);\n    }\n  }\n\n  onTyping(): void {\n    if (!this.chat) return;\n\n    this.chatService.sendTypingNotification(this.chat.id, true);\n    \n    // Clear existing timeout\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    \n    // Set timeout to stop typing after 3 seconds\n    this.typingTimeout = setTimeout(() => {\n      this.stopTyping();\n    }, 3000);\n  }\n\n  private stopTyping(): void {\n    if (!this.chat) return;\n    \n    this.chatService.sendTypingNotification(this.chat.id, false);\n    \n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n      this.typingTimeout = null;\n    }\n  }\n\n  private handleTypingNotification(notification: TypingNotification): void {\n    if (notification.userId === this.currentUser?.id) {\n      return; // Ignore own typing notifications\n    }\n\n    if (notification.status === 'typing') {\n      this.typingUsers.add(notification.userId);\n    } else {\n      this.typingUsers.delete(notification.userId);\n    }\n  }\n\n  private scrollToBottom(): void {\n    try {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    } catch (err) {\n      console.error('Error scrolling to bottom:', err);\n    }\n  }\n\n  getOtherParticipant(): any {\n    if (!this.chat) return null;\n    return this.currentUser?.role === 'PATIENT' ? this.chat.doctor : this.chat.patient;\n  }\n\n  isTyping(): boolean {\n    return this.typingUsers.size > 0;\n  }\n\n  formatMessageTime(dateString: string): string {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  }\n\n  formatMessageDate(dateString: string): string {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n\n    if (date.toDateString() === today.toDateString()) {\n      return 'Today';\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Yesterday';\n    } else {\n      return date.toLocaleDateString();\n    }\n  }\n\n  shouldShowDateSeparator(index: number): boolean {\n    if (index === 0) return true;\n    \n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    \n    const currentDate = new Date(currentMessage.createdAt).toDateString();\n    const previousDate = new Date(previousMessage.createdAt).toDateString();\n    \n    return currentDate !== previousDate;\n  }\n}\n", "<div class=\"chat-window\" *ngIf=\"chat\">\n  <!-- Cha<PERSON> -->\n  <div class=\"chat-header\">\n    <div class=\"participant-info\">\n      <img \n        [src]=\"getOtherParticipant()?.avatar || '/assets/images/default-avatar.png'\" \n        [alt]=\"getOtherParticipant()?.fullName\"\n        class=\"participant-avatar\">\n      <div class=\"participant-details\">\n        <h6 class=\"mb-0\">{{ getOtherParticipant()?.fullName }}</h6>\n        <small class=\"text-muted\">\n          {{ getOtherParticipant()?.role === 'DOCTOR' ? 'Dr. ' + getOtherParticipant()?.specialization : 'Patient' }}\n        </small>\n      </div>\n    </div>\n    \n    <div class=\"connection-status\">\n      <span \n        class=\"status-indicator\"\n        [class.connected]=\"connectionStatus\"\n        [class.disconnected]=\"!connectionStatus\">\n        <i class=\"bi\" [class.bi-wifi]=\"connectionStatus\" [class.bi-wifi-off]=\"!connectionStatus\"></i>\n      </span>\n    </div>\n  </div>\n\n  <!-- Messages Area -->\n  <div class=\"messages-container\" #messagesContainer>\n    <div *ngIf=\"loading\" class=\"text-center p-3\">\n      <div class=\"spinner-border spinner-border-sm\" role=\"status\">\n        <span class=\"visually-hidden\">Loading messages...</span>\n      </div>\n    </div>\n\n    <div *ngIf=\"!loading && messages.length === 0\" class=\"text-center p-4 text-muted\">\n      <i class=\"bi bi-chat-square-text fs-1 mb-3 d-block\"></i>\n      <p>No messages yet</p>\n      <small>Start the conversation!</small>\n    </div>\n\n    <div *ngIf=\"!loading && messages.length > 0\" class=\"messages-list\">\n      <ng-container *ngFor=\"let message of messages; let i = index\">\n        <!-- Date Separator -->\n        <div *ngIf=\"shouldShowDateSeparator(i)\" class=\"date-separator\">\n          <span class=\"date-label\">{{ formatMessageDate(message.createdAt) }}</span>\n        </div>\n\n        <!-- Message Item -->\n        <app-message-item \n          [message]=\"message\" \n          [isOwn]=\"message.sender.id === currentUser?.id\">\n        </app-message-item>\n      </ng-container>\n\n      <!-- Typing Indicator -->\n      <div *ngIf=\"isTyping()\" class=\"typing-indicator\">\n        <div class=\"typing-dots\">\n          <span></span>\n          <span></span>\n          <span></span>\n        </div>\n        <small class=\"text-muted ms-2\">{{ getOtherParticipant()?.fullName }} is typing...</small>\n      </div>\n    </div>\n  </div>\n\n  <!-- Message Input -->\n  <div class=\"message-input-container\">\n    <form [formGroup]=\"messageForm\" (ngSubmit)=\"sendMessage()\" class=\"message-form\">\n      <div class=\"input-group\">\n        <input \n          type=\"text\" \n          class=\"form-control\" \n          formControlName=\"content\"\n          placeholder=\"Type a message...\"\n          (input)=\"onTyping()\"\n          [disabled]=\"!connectionStatus\"\n          autocomplete=\"off\">\n        \n        <button \n          type=\"submit\" \n          class=\"btn btn-primary\"\n          [disabled]=\"!messageForm.valid || !connectionStatus\">\n          <i class=\"bi bi-send\"></i>\n        </button>\n      </div>\n    </form>\n    \n    <div *ngIf=\"!connectionStatus\" class=\"connection-warning\">\n      <small class=\"text-warning\">\n        <i class=\"bi bi-exclamation-triangle me-1\"></i>\n        Connection lost. Trying to reconnect...\n      </small>\n    </div>\n  </div>\n</div>\n\n<div class=\"chat-placeholder\" *ngIf=\"!chat\">\n  <div class=\"text-center text-muted\">\n    <i class=\"bi bi-chat-square-text fs-1 mb-3 d-block\"></i>\n    <h5>Select a conversation</h5>\n    <p>Choose a conversation from the list to start messaging</p>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;IC2B/DC,EAAA,CAAAC,cAAA,cAA6C;IAEXD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAI5DH,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAAI,SAAA,YAAwD;IACxDJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtBH,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAMpCH,EAAA,CAAAC,cAAA,cAA+D;IACpCD,EAAA,CAAAE,MAAA,GAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAAjDH,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAC,iBAAA,CAAAC,UAAA,CAAAC,SAAA,EAA0C;;;;;IAHvEV,EAAA,CAAAW,uBAAA,GAA8D;IAE5DX,EAAA,CAAAY,UAAA,IAAAC,8DAAA,kBAEM;IAGNb,EAAA,CAAAI,SAAA,2BAGmB;IACrBJ,EAAA,CAAAc,qBAAA,EAAe;;;;;;IATPd,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAe,UAAA,SAAAC,MAAA,CAAAC,uBAAA,CAAAC,KAAA,EAAgC;IAMpClB,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAe,UAAA,YAAAN,UAAA,CAAmB,UAAAA,UAAA,CAAAU,MAAA,CAAAC,EAAA,MAAAJ,MAAA,CAAAK,WAAA,kBAAAL,MAAA,CAAAK,WAAA,CAAAD,EAAA;;;;;IAMvBpB,EAAA,CAAAC,cAAA,cAAiD;IAE7CD,EAAA,CAAAI,SAAA,WAAa;IAGfJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAE,MAAA,GAAkD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAA1DH,EAAA,CAAAK,SAAA,GAAkD;IAAlDL,EAAA,CAAAsB,kBAAA,MAAAC,OAAA,GAAAC,MAAA,CAAAC,mBAAA,qBAAAF,OAAA,CAAAG,QAAA,kBAAkD;;;;;IArBrF1B,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAY,UAAA,IAAAe,wDAAA,2BAWe;IAGf3B,EAAA,CAAAY,UAAA,IAAAgB,+CAAA,kBAOM;IACR5B,EAAA,CAAAG,YAAA,EAAM;;;;IAtB8BH,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAe,UAAA,YAAAc,MAAA,CAAAC,QAAA,CAAa;IAczC9B,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAe,UAAA,SAAAc,MAAA,CAAAE,QAAA,GAAgB;;;;;IAiCxB/B,EAAA,CAAAC,cAAA,cAA0D;IAEtDD,EAAA,CAAAI,SAAA,YAA+C;IAC/CJ,EAAA,CAAAE,MAAA,gDACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;;IA5FdH,EAAA,CAAAC,cAAA,aAAsC;IAIhCD,EAAA,CAAAI,SAAA,aAG6B;IAC7BJ,EAAA,CAAAC,cAAA,aAAiC;IACdD,EAAA,CAAAE,MAAA,GAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3DH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAIZH,EAAA,CAAAC,cAAA,aAA+B;IAK3BD,EAAA,CAAAI,SAAA,aAA6F;IAC/FJ,EAAA,CAAAG,YAAA,EAAO;IAKXH,EAAA,CAAAC,cAAA,mBAAmD;IACjDD,EAAA,CAAAY,UAAA,KAAAoB,yCAAA,kBAIM;IAENhC,EAAA,CAAAY,UAAA,KAAAqB,yCAAA,kBAIM;IAENjC,EAAA,CAAAY,UAAA,KAAAsB,yCAAA,kBAuBM;IACRlC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAqC;IACHD,EAAA,CAAAmC,UAAA,sBAAAC,6DAAA;MAAApC,EAAA,CAAAqC,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAvC,EAAA,CAAAwC,aAAA;MAAA,OAAYxC,EAAA,CAAAyC,WAAA,CAAAF,OAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IACxD1C,EAAA,CAAAC,cAAA,eAAyB;IAMrBD,EAAA,CAAAmC,UAAA,mBAAAQ,2DAAA;MAAA3C,EAAA,CAAAqC,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAA5C,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAAG,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IALtB7C,EAAA,CAAAG,YAAA,EAOqB;IAErBH,EAAA,CAAAC,cAAA,kBAGuD;IACrDD,EAAA,CAAAI,SAAA,aAA0B;IAC5BJ,EAAA,CAAAG,YAAA,EAAS;IAIbH,EAAA,CAAAY,UAAA,KAAAkC,yCAAA,kBAKM;IACR9C,EAAA,CAAAG,YAAA,EAAM;;;;;;;;IAzFAH,EAAA,CAAAK,SAAA,GAA4E;IAA5EL,EAAA,CAAAe,UAAA,UAAAQ,OAAA,GAAAwB,MAAA,CAAAtB,mBAAA,qBAAAF,OAAA,CAAAyB,MAAA,0CAAAhD,EAAA,CAAAiD,aAAA,CAA4E,SAAAC,OAAA,GAAAH,MAAA,CAAAtB,mBAAA,qBAAAyB,OAAA,CAAAxB,QAAA;IAI3D1B,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAM,iBAAA,EAAA6C,OAAA,GAAAJ,MAAA,CAAAtB,mBAAA,qBAAA0B,OAAA,CAAAzB,QAAA,CAAqC;IAEpD1B,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAsB,kBAAA,QAAA8B,OAAA,GAAAL,MAAA,CAAAtB,mBAAA,qBAAA2B,OAAA,CAAAC,IAAA,4BAAAD,OAAA,GAAAL,MAAA,CAAAtB,mBAAA,qBAAA2B,OAAA,CAAAE,cAAA,mBACF;IAOAtD,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAuD,WAAA,cAAAR,MAAA,CAAAS,gBAAA,CAAoC,kBAAAT,MAAA,CAAAS,gBAAA;IAEtBxD,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAuD,WAAA,YAAAR,MAAA,CAAAS,gBAAA,CAAkC,iBAAAT,MAAA,CAAAS,gBAAA;IAO9CxD,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAe,UAAA,SAAAgC,MAAA,CAAAU,OAAA,CAAa;IAMbzD,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAe,UAAA,UAAAgC,MAAA,CAAAU,OAAA,IAAAV,MAAA,CAAAjB,QAAA,CAAA4B,MAAA,OAAuC;IAMvC1D,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAe,UAAA,UAAAgC,MAAA,CAAAU,OAAA,IAAAV,MAAA,CAAAjB,QAAA,CAAA4B,MAAA,KAAqC;IA4BrC1D,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAe,UAAA,cAAAgC,MAAA,CAAAY,WAAA,CAAyB;IAQzB3D,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAe,UAAA,cAAAgC,MAAA,CAAAS,gBAAA,CAA8B;IAM9BxD,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAAe,UAAA,cAAAgC,MAAA,CAAAY,WAAA,CAAAC,KAAA,KAAAb,MAAA,CAAAS,gBAAA,CAAoD;IAMpDxD,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAe,UAAA,UAAAgC,MAAA,CAAAS,gBAAA,CAAuB;;;;;IASjCxD,EAAA,CAAAC,cAAA,cAA4C;IAExCD,EAAA,CAAAI,SAAA,YAAwD;IACxDJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,6DAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;ADzFjE,OAAM,MAAO0D,mBAAmB;EAe9BC,YACUC,EAAe,EACfC,WAAwB,EACxBC,WAAwB;IAFxB,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IAjBZ,KAAAC,IAAI,GAAgB,IAAI;IAGjC,KAAApC,QAAQ,GAAc,EAAE;IAGxB,KAAA2B,OAAO,GAAG,KAAK;IACf,KAAAU,WAAW,GAAgB,IAAIC,GAAG,EAAE;IACpC,KAAAZ,gBAAgB,GAAG,KAAK;IAEhB,KAAAa,aAAa,GAAmB,EAAE;IAElC,KAAAC,oBAAoB,GAAG,KAAK;IAOlC,IAAI,CAACX,WAAW,GAAG,IAAI,CAACI,EAAE,CAACQ,KAAK,CAAC;MAC/BC,OAAO,EAAE,CAAC,EAAE,EAAE,CAACzE,UAAU,CAAC0E,QAAQ,EAAE1E,UAAU,CAAC2E,SAAS,CAAC,CAAC,CAAC,CAAC;KAC7D,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACtD,WAAW,GAAG,IAAI,CAAC4C,WAAW,CAACW,cAAc,EAAE;IACpD,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACT,aAAa,CAACU,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,IAAI,CAACC,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;EAEpC;EAEAE,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACd,oBAAoB,EAAE;MAC7B,IAAI,CAACe,cAAc,EAAE;MACrB,IAAI,CAACf,oBAAoB,GAAG,KAAK;;EAErC;EAEQO,mBAAmBA,CAAA;IACzB;IACA,MAAMS,aAAa,GAAG,IAAI,CAACtB,WAAW,CAACuB,iBAAiB,CAACC,SAAS,CAACC,MAAM,IAAG;MAC1E,IAAI,CAACjC,gBAAgB,GAAGiC,MAAM;IAChC,CAAC,CAAC;IACF,IAAI,CAACpB,aAAa,CAACqB,IAAI,CAACJ,aAAa,CAAC;IAEtC;IACA,MAAMK,WAAW,GAAG,IAAI,CAAC3B,WAAW,CAAC4B,SAAS,CAACJ,SAAS,CAACK,OAAO,IAAG;MACjE,IAAI,IAAI,CAAC3B,IAAI,IAAI2B,OAAO,CAACC,MAAM,KAAK,IAAI,CAAC5B,IAAI,CAAC9C,EAAE,EAAE;QAChD,IAAI,CAACU,QAAQ,CAAC4D,IAAI,CAACG,OAAO,CAAC;QAC3B,IAAI,CAACvB,oBAAoB,GAAG,IAAI;QAEhC;QACA,IAAIuB,OAAO,CAAC1E,MAAM,CAACC,EAAE,KAAK,IAAI,CAACC,WAAW,EAAED,EAAE,EAAE;UAC9C,IAAI,CAAC4C,WAAW,CAAC+B,kBAAkB,CAAC,IAAI,CAAC7B,IAAI,CAAC9C,EAAE,CAAC,CAACoE,SAAS,EAAE;;;IAGnE,CAAC,CAAC;IACF,IAAI,CAACnB,aAAa,CAACqB,IAAI,CAACC,WAAW,CAAC;IAEpC;IACA,MAAMK,SAAS,GAAG,IAAI,CAAChC,WAAW,CAACiC,OAAO,CAACT,SAAS,CAACU,YAAY,IAAG;MAClE,IAAI,CAACC,wBAAwB,CAACD,YAAY,CAAC;IAC7C,CAAC,CAAC;IACF,IAAI,CAAC7B,aAAa,CAACqB,IAAI,CAACM,SAAS,CAAC;EACpC;EAEAI,QAAQA,CAAClC,IAAU;IACjB,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACpC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACqC,WAAW,CAACkC,KAAK,EAAE;IAExB,IAAInC,IAAI,EAAE;MACR,IAAI,CAACoC,YAAY,EAAE;MACnB,IAAI,CAACtC,WAAW,CAACuC,uBAAuB,CAACrC,IAAI,CAAC9C,EAAE,CAAC;;EAErD;EAEQkF,YAAYA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACpC,IAAI,EAAE;IAEhB,IAAI,CAACT,OAAO,GAAG,IAAI;IACnB,IAAI,CAACO,WAAW,CAACwC,eAAe,CAAC,IAAI,CAACtC,IAAI,CAAC9C,EAAE,CAAC,CAACoE,SAAS,CAAC;MACvDiB,IAAI,EAAG3E,QAAQ,IAAI;QACjB,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAAC4E,OAAO,EAAE,CAAC,CAAC;QACpC,IAAI,CAACjD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACa,oBAAoB,GAAG,IAAI;MAClC,CAAC;MACDqC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAAClD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAf,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACiB,WAAW,CAACC,KAAK,IAAI,CAAC,IAAI,CAACM,IAAI,IAAI,CAAC,IAAI,CAACV,gBAAgB,EAAE;MACnE;;IAGF,MAAMgB,OAAO,GAAG,IAAI,CAACb,WAAW,CAACkD,GAAG,CAAC,SAAS,CAAC,EAAEC,KAAK,EAAEC,IAAI,EAAE;IAC9D,IAAI,CAACvC,OAAO,EAAE;MACZ;;IAGF,IAAI;MACF,IAAI,CAACR,WAAW,CAACtB,WAAW,CAAC,IAAI,CAACwB,IAAI,CAAC9C,EAAE,EAAEoD,OAAO,CAAC;MACnD,IAAI,CAACb,WAAW,CAACqD,KAAK,EAAE;MACxB,IAAI,CAACC,UAAU,EAAE;KAClB,CAAC,OAAON,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;;EAEnD;EAEA9D,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACqB,IAAI,EAAE;IAEhB,IAAI,CAACF,WAAW,CAACkD,sBAAsB,CAAC,IAAI,CAAChD,IAAI,CAAC9C,EAAE,EAAE,IAAI,CAAC;IAE3D;IACA,IAAI,IAAI,CAAC8D,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAGlC;IACA,IAAI,CAACA,aAAa,GAAGiC,UAAU,CAAC,MAAK;MACnC,IAAI,CAACF,UAAU,EAAE;IACnB,CAAC,EAAE,IAAI,CAAC;EACV;EAEQA,UAAUA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAC/C,IAAI,EAAE;IAEhB,IAAI,CAACF,WAAW,CAACkD,sBAAsB,CAAC,IAAI,CAAChD,IAAI,CAAC9C,EAAE,EAAE,KAAK,CAAC;IAE5D,IAAI,IAAI,CAAC8D,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;MAChC,IAAI,CAACA,aAAa,GAAG,IAAI;;EAE7B;EAEQiB,wBAAwBA,CAACD,YAAgC;IAC/D,IAAIA,YAAY,CAACkB,MAAM,KAAK,IAAI,CAAC/F,WAAW,EAAED,EAAE,EAAE;MAChD,OAAO,CAAC;;;IAGV,IAAI8E,YAAY,CAACT,MAAM,KAAK,QAAQ,EAAE;MACpC,IAAI,CAACtB,WAAW,CAACkD,GAAG,CAACnB,YAAY,CAACkB,MAAM,CAAC;KAC1C,MAAM;MACL,IAAI,CAACjD,WAAW,CAACmD,MAAM,CAACpB,YAAY,CAACkB,MAAM,CAAC;;EAEhD;EAEQ/B,cAAcA,CAAA;IACpB,IAAI;MACF,IAAI,IAAI,CAACkC,iBAAiB,EAAE;QAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAACE,aAAa;QACpDD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;KAE3C,CAAC,OAAOC,GAAG,EAAE;MACZhB,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEiB,GAAG,CAAC;;EAEpD;EAEAnG,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACyC,IAAI,EAAE,OAAO,IAAI;IAC3B,OAAO,IAAI,CAAC7C,WAAW,EAAEgC,IAAI,KAAK,SAAS,GAAG,IAAI,CAACa,IAAI,CAAC2D,MAAM,GAAG,IAAI,CAAC3D,IAAI,CAAC4D,OAAO;EACpF;EAEA/F,QAAQA,CAAA;IACN,OAAO,IAAI,CAACoC,WAAW,CAAC4D,IAAI,GAAG,CAAC;EAClC;EAEAC,iBAAiBA,CAACC,UAAkB;IAClC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,EAAE,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAS,CAAE,CAAC;EAC5E;EAEA9H,iBAAiBA,CAACyH,UAAkB;IAClC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMM,KAAK,GAAG,IAAIJ,IAAI,EAAE;IACxB,MAAMK,SAAS,GAAG,IAAIL,IAAI,CAACI,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,IAAIR,IAAI,CAACS,YAAY,EAAE,KAAKJ,KAAK,CAACI,YAAY,EAAE,EAAE;MAChD,OAAO,OAAO;KACf,MAAM,IAAIT,IAAI,CAACS,YAAY,EAAE,KAAKH,SAAS,CAACG,YAAY,EAAE,EAAE;MAC3D,OAAO,WAAW;KACnB,MAAM;MACL,OAAOT,IAAI,CAACU,kBAAkB,EAAE;;EAEpC;EAEA3H,uBAAuBA,CAAC4H,KAAa;IACnC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,MAAMC,cAAc,GAAG,IAAI,CAAChH,QAAQ,CAAC+G,KAAK,CAAC;IAC3C,MAAME,eAAe,GAAG,IAAI,CAACjH,QAAQ,CAAC+G,KAAK,GAAG,CAAC,CAAC;IAEhD,MAAMG,WAAW,GAAG,IAAIb,IAAI,CAACW,cAAc,CAACpI,SAAS,CAAC,CAACiI,YAAY,EAAE;IACrE,MAAMM,YAAY,GAAG,IAAId,IAAI,CAACY,eAAe,CAACrI,SAAS,CAAC,CAACiI,YAAY,EAAE;IAEvE,OAAOK,WAAW,KAAKC,YAAY;EACrC;;;uBAhNWpF,mBAAmB,EAAA7D,EAAA,CAAAkJ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApJ,EAAA,CAAAkJ,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAtJ,EAAA,CAAAkJ,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAnB3F,mBAAmB;MAAA4F,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UCZhC5J,EAAA,CAAAY,UAAA,IAAAkJ,kCAAA,mBA+FM;UAEN9J,EAAA,CAAAY,UAAA,IAAAmJ,kCAAA,iBAMM;;;UAvGoB/J,EAAA,CAAAe,UAAA,SAAA8I,GAAA,CAAA3F,IAAA,CAAU;UAiGLlE,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAe,UAAA,UAAA8I,GAAA,CAAA3F,IAAA,CAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}