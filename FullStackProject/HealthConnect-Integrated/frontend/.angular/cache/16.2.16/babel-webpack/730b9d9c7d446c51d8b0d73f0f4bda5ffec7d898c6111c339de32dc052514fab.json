{"ast": null, "code": "export const environment = {\n  production: false,\n  apiUrl: 'http://localhost:8080/api',\n  appName: 'HealthConnect',\n  version: '1.0.0'\n};", "map": {"version": 3, "names": ["environment", "production", "apiUrl", "appName", "version"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/environments/environment.ts"], "sourcesContent": ["export const environment = {\n  production: false,\n  apiUrl: 'http://localhost:8080/api',\n  appName: 'HealthConnect',\n  version: '1.0.0'\n};\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,2BAA2B;EACnCC,OAAO,EAAE,eAAe;EACxBC,OAAO,EAAE;CACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}