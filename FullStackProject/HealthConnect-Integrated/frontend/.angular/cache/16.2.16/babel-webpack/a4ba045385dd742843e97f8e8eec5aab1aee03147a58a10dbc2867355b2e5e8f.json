{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { AppointmentType } from '../../core/models/appointment.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../core/services/appointment.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction AppointmentBookingComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"i\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.success, \" \");\n  }\n}\nfunction AppointmentBookingComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelement(1, \"i\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction AppointmentBookingComponent_form_11_option_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const doctor_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", doctor_r15.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", doctor_r15.fullName, \" - \", doctor_r15.specialization || \"General Practice\", \" \");\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1, \" Please select a doctor. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_9_p_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.selectedDoctor.affiliation, \" \");\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 7)(2, \"div\", 47)(3, \"div\", 48);\n    i0.ɵɵelement(4, \"i\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h6\", 50);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 51);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, AppointmentBookingComponent_form_11_div_9_p_10_Template, 2, 1, \"p\", 52);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r5.selectedDoctor.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.selectedDoctor.specialization || \"General Practice\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.selectedDoctor.affiliation);\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1, \" Please select a date. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_form_11_option_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r17.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r17.label, \" \");\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1, \" Please select an appointment type. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55)(2, \"span\", 56);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtext(4, \" Loading available time slots... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_26_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"label\", 60);\n    i0.ɵɵelement(2, \"input\", 61);\n    i0.ɵɵelementStart(3, \"span\", 62);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const slot_r19 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r18.getTimeSlotValue(slot_r19));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.getTimeSlotDisplay(slot_r19), \" \");\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 23);\n    i0.ɵɵtemplate(2, AppointmentBookingComponent_form_11_div_26_div_2_Template, 5, 2, \"div\", 58);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.availableSlots);\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" No available time slots for the selected date. Please choose a different date. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1, \" Please provide a reason for the visit. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_form_11_span_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 65)(1, \"span\", 56);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppointmentBookingComponent_form_11_i_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 66);\n  }\n}\nfunction AppointmentBookingComponent_form_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 15);\n    i0.ɵɵlistener(\"ngSubmit\", function AppointmentBookingComponent_form_11_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 16)(2, \"label\", 17);\n    i0.ɵɵtext(3, \"Select Doctor *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"select\", 18)(5, \"option\", 19);\n    i0.ɵɵtext(6, \"Choose a doctor...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, AppointmentBookingComponent_form_11_option_7_Template, 2, 3, \"option\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, AppointmentBookingComponent_form_11_div_8_Template, 2, 0, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, AppointmentBookingComponent_form_11_div_9_Template, 11, 3, \"div\", 22);\n    i0.ɵɵelementStart(10, \"div\", 23)(11, \"div\", 24)(12, \"label\", 25);\n    i0.ɵɵtext(13, \"Appointment Date *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 26);\n    i0.ɵɵtemplate(15, AppointmentBookingComponent_form_11_div_15_Template, 2, 0, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 24)(17, \"label\", 27);\n    i0.ɵɵtext(18, \"Appointment Type *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"select\", 28);\n    i0.ɵɵtemplate(20, AppointmentBookingComponent_form_11_option_20_Template, 2, 2, \"option\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, AppointmentBookingComponent_form_11_div_21_Template, 2, 0, \"div\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 29)(23, \"label\", 30);\n    i0.ɵɵtext(24, \"Available Time Slots *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, AppointmentBookingComponent_form_11_div_25_Template, 5, 0, \"div\", 31);\n    i0.ɵɵtemplate(26, AppointmentBookingComponent_form_11_div_26_Template, 3, 1, \"div\", 32);\n    i0.ɵɵtemplate(27, AppointmentBookingComponent_form_11_div_27_Template, 3, 0, \"div\", 33);\n    i0.ɵɵelementStart(28, \"div\", 34);\n    i0.ɵɵtext(29, \" Please select a time slot. \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 29)(31, \"label\", 35);\n    i0.ɵɵtext(32, \"Reason for Visit *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(33, \"input\", 36);\n    i0.ɵɵtemplate(34, AppointmentBookingComponent_form_11_div_34_Template, 2, 0, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 16)(36, \"label\", 37);\n    i0.ɵɵtext(37, \"Additional Notes (Optional)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"textarea\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 39)(40, \"button\", 40);\n    i0.ɵɵelement(41, \"i\", 41);\n    i0.ɵɵtext(42, \" Back to Doctors \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"button\", 42);\n    i0.ɵɵtemplate(44, AppointmentBookingComponent_form_11_span_44_Template, 3, 0, \"span\", 43);\n    i0.ɵɵtemplate(45, AppointmentBookingComponent_form_11_i_45_Template, 1, 0, \"i\", 44);\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    let tmp_3_0;\n    let tmp_5_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_10_0;\n    let tmp_14_0;\n    let tmp_15_0;\n    let tmp_16_0;\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.bookingForm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_1_0 = ctx_r2.bookingForm.get(\"doctorId\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r2.bookingForm.get(\"doctorId\")) == null ? null : tmp_1_0.touched));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.doctors);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r2.bookingForm.get(\"doctorId\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r2.bookingForm.get(\"doctorId\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedDoctor);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_5_0 = ctx_r2.bookingForm.get(\"date\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r2.bookingForm.get(\"date\")) == null ? null : tmp_5_0.touched));\n    i0.ɵɵproperty(\"min\", ctx_r2.getTodayDate());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r2.bookingForm.get(\"date\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r2.bookingForm.get(\"date\")) == null ? null : tmp_7_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_8_0 = ctx_r2.bookingForm.get(\"type\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx_r2.bookingForm.get(\"type\")) == null ? null : tmp_8_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.appointmentTypes);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx_r2.bookingForm.get(\"type\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx_r2.bookingForm.get(\"type\")) == null ? null : tmp_10_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading && ctx_r2.availableSlots.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading && ctx_r2.selectedDoctor && ctx_r2.availableSlots.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"display\", ((tmp_14_0 = ctx_r2.bookingForm.get(\"timeSlot\")) == null ? null : tmp_14_0.invalid) && ((tmp_14_0 = ctx_r2.bookingForm.get(\"timeSlot\")) == null ? null : tmp_14_0.touched) ? \"block\" : \"none\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_15_0 = ctx_r2.bookingForm.get(\"reasonForVisit\")) == null ? null : tmp_15_0.invalid) && ((tmp_15_0 = ctx_r2.bookingForm.get(\"reasonForVisit\")) == null ? null : tmp_15_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_16_0 = ctx_r2.bookingForm.get(\"reasonForVisit\")) == null ? null : tmp_16_0.invalid) && ((tmp_16_0 = ctx_r2.bookingForm.get(\"reasonForVisit\")) == null ? null : tmp_16_0.touched));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.bookingForm.invalid || ctx_r2.submitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.submitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.submitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.submitting ? \"Booking...\" : \"Book Appointment\", \" \");\n  }\n}\nexport class AppointmentBookingComponent {\n  constructor(fb, appointmentService, route, router) {\n    this.fb = fb;\n    this.appointmentService = appointmentService;\n    this.route = route;\n    this.router = router;\n    this.doctors = [];\n    this.selectedDoctor = null;\n    this.timeSlots = [];\n    this.availableSlots = [];\n    this.loading = false;\n    this.submitting = false;\n    this.error = null;\n    this.success = null;\n    this.appointmentTypes = [{\n      value: AppointmentType.IN_PERSON,\n      label: 'In Person'\n    }, {\n      value: AppointmentType.VIDEO_CALL,\n      label: 'Video Call'\n    }];\n    this.initializeForm();\n  }\n  initializeForm() {\n    this.bookingForm = this.fb.group({\n      doctorId: ['', Validators.required],\n      date: ['', Validators.required],\n      timeSlot: ['', Validators.required],\n      type: [AppointmentType.IN_PERSON, Validators.required],\n      reasonForVisit: ['', Validators.required],\n      notes: ['']\n    });\n  }\n  ngOnInit() {\n    this.loadDoctors();\n    // Check if doctor ID is provided in query params\n    this.route.queryParams.subscribe(params => {\n      if (params['doctorId']) {\n        this.bookingForm.patchValue({\n          doctorId: params['doctorId']\n        });\n        this.onDoctorChange();\n      }\n    });\n    // Set minimum date to tomorrow (backend requires future dates)\n    const tomorrow = new Date();\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    const tomorrowStr = tomorrow.toISOString().split('T')[0];\n    this.bookingForm.get('date')?.setValue(tomorrowStr);\n    // Watch for form changes\n    this.bookingForm.get('doctorId')?.valueChanges.subscribe(() => this.onDoctorChange());\n    this.bookingForm.get('date')?.valueChanges.subscribe(() => this.onDateChange());\n  }\n  loadDoctors() {\n    this.appointmentService.getDoctors().subscribe({\n      next: doctors => {\n        this.doctors = doctors;\n      },\n      error: error => {\n        this.error = 'Failed to load doctors. Please try again.';\n        console.error('Error loading doctors:', error);\n      }\n    });\n  }\n  onDoctorChange() {\n    const doctorId = this.bookingForm.get('doctorId')?.value;\n    if (doctorId) {\n      this.selectedDoctor = this.doctors.find(d => d.id == doctorId) || null;\n      this.loadTimeSlots();\n    } else {\n      this.selectedDoctor = null;\n      this.timeSlots = [];\n      this.availableSlots = [];\n    }\n  }\n  onDateChange() {\n    if (this.selectedDoctor) {\n      this.loadTimeSlots();\n    }\n  }\n  loadTimeSlots() {\n    const doctorId = this.bookingForm.get('doctorId')?.value;\n    const date = this.bookingForm.get('date')?.value;\n    if (!doctorId || !date) return;\n    this.loading = true;\n    this.appointmentService.getAvailableTimeSlots(doctorId, date).subscribe({\n      next: slots => {\n        this.timeSlots = slots;\n        this.availableSlots = slots.filter(slot => slot.available);\n        this.loading = false;\n        // Clear selected time slot if it's no longer available\n        const currentSlot = this.bookingForm.get('timeSlot')?.value;\n        if (currentSlot && !this.availableSlots.find(slot => slot.startTime === currentSlot.split('-')[0] && slot.endTime === currentSlot.split('-')[1])) {\n          this.bookingForm.patchValue({\n            timeSlot: ''\n          });\n        }\n      },\n      error: error => {\n        this.error = 'Failed to load available time slots. Please try again.';\n        this.loading = false;\n        console.error('Error loading time slots:', error);\n      }\n    });\n  }\n  onSubmit() {\n    if (this.bookingForm.valid) {\n      this.submitting = true;\n      this.error = null;\n      this.success = null;\n      const formValue = this.bookingForm.value;\n      const [startTime, endTime] = formValue.timeSlot.split('-');\n      const request = {\n        doctorId: parseInt(formValue.doctorId),\n        date: formValue.date,\n        startTime: startTime,\n        endTime: endTime,\n        type: formValue.type,\n        reasonForVisit: formValue.reasonForVisit,\n        notes: formValue.notes || undefined\n      };\n      this.appointmentService.createAppointment(request).subscribe({\n        next: appointment => {\n          this.success = 'Appointment booked successfully!';\n          this.submitting = false;\n          // Redirect to appointment details after 2 seconds\n          setTimeout(() => {\n            this.router.navigate(['/appointments', appointment.id]);\n          }, 2000);\n        },\n        error: error => {\n          this.error = 'Failed to book appointment. Please try again.';\n          this.submitting = false;\n          console.error('Error booking appointment:', error);\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n  markFormGroupTouched() {\n    Object.keys(this.bookingForm.controls).forEach(key => {\n      const control = this.bookingForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  getTimeSlotDisplay(slot) {\n    return `${this.formatTime(slot.startTime)} - ${this.formatTime(slot.endTime)}`;\n  }\n  getTimeSlotValue(slot) {\n    return `${slot.startTime}-${slot.endTime}`;\n  }\n  formatTime(time) {\n    const [hours, minutes] = time.split(':');\n    const hour = parseInt(hours);\n    const ampm = hour >= 12 ? 'PM' : 'AM';\n    const displayHour = hour % 12 || 12;\n    return `${displayHour}:${minutes} ${ampm}`;\n  }\n  getTodayDate() {\n    // Return tomorrow's date as minimum (backend requires future dates)\n    const tomorrow = new Date();\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    return tomorrow.toISOString().split('T')[0];\n  }\n  static {\n    this.ɵfac = function AppointmentBookingComponent_Factory(t) {\n      return new (t || AppointmentBookingComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AppointmentService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppointmentBookingComponent,\n      selectors: [[\"app-appointment-booking\"]],\n      decls: 12,\n      vars: 3,\n      consts: [[1, \"container-fluid\", \"py-4\"], [1, \"row\", \"justify-content-center\"], [1, \"col-lg-8\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\", \"mb-0\"], [1, \"fas\", \"fa-calendar-plus\", \"me-2\"], [1, \"card-body\"], [\"class\", \"alert alert-success\", \"role\", \"alert\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [\"role\", \"alert\", 1, \"alert\", \"alert-success\"], [1, \"fas\", \"fa-check-circle\", \"me-2\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-2\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-4\"], [\"for\", \"doctorId\", 1, \"form-label\"], [\"id\", \"doctorId\", \"formControlName\", \"doctorId\", 1, \"form-select\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"class\", \"card bg-light mb-4\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-md-6\", \"mb-3\"], [\"for\", \"date\", 1, \"form-label\"], [\"type\", \"date\", \"id\", \"date\", \"formControlName\", \"date\", 1, \"form-control\", 3, \"min\"], [\"for\", \"type\", 1, \"form-label\"], [\"id\", \"type\", \"formControlName\", \"type\", 1, \"form-select\"], [1, \"mb-3\"], [\"for\", \"timeSlot\", 1, \"form-label\"], [\"class\", \"text-center py-3\", 4, \"ngIf\"], [\"class\", \"time-slots-grid\", 4, \"ngIf\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [1, \"invalid-feedback\"], [\"for\", \"reasonForVisit\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"reasonForVisit\", \"formControlName\", \"reasonForVisit\", \"placeholder\", \"e.g., Regular checkup, Follow-up, Consultation\", 1, \"form-control\"], [\"for\", \"notes\", 1, \"form-label\"], [\"id\", \"notes\", \"formControlName\", \"notes\", \"rows\", \"3\", \"placeholder\", \"Any additional information or special requests...\", 1, \"form-control\"], [1, \"d-flex\", \"justify-content-between\"], [\"type\", \"button\", \"routerLink\", \"/appointments/doctors\", 1, \"btn\", \"btn-outline-secondary\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", 4, \"ngIf\"], [\"class\", \"fas fa-calendar-check me-2\", 4, \"ngIf\"], [3, \"value\"], [1, \"card\", \"bg-light\", \"mb-4\"], [1, \"d-flex\", \"align-items-center\"], [1, \"avatar-circle\", \"me-3\"], [1, \"fas\", \"fa-user-md\"], [1, \"mb-1\"], [1, \"text-muted\", \"mb-1\"], [\"class\", \"text-muted mb-0\", 4, \"ngIf\"], [1, \"text-muted\", \"mb-0\"], [1, \"text-center\", \"py-3\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"text-primary\", \"me-2\"], [1, \"visually-hidden\"], [1, \"time-slots-grid\"], [\"class\", \"col-md-4 col-sm-6 mb-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\", \"col-sm-6\", \"mb-2\"], [1, \"time-slot-option\"], [\"type\", \"radio\", \"name\", \"timeSlot\", \"formControlName\", \"timeSlot\", 3, \"value\"], [1, \"time-slot-label\"], [1, \"alert\", \"alert-info\"], [1, \"fas\", \"fa-info-circle\", \"me-2\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [1, \"fas\", \"fa-calendar-check\", \"me-2\"]],\n      template: function AppointmentBookingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h4\", 5);\n          i0.ɵɵelement(6, \"i\", 6);\n          i0.ɵɵtext(7, \"Book an Appointment \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 7);\n          i0.ɵɵtemplate(9, AppointmentBookingComponent_div_9_Template, 3, 1, \"div\", 8);\n          i0.ɵɵtemplate(10, AppointmentBookingComponent_div_10_Template, 3, 1, \"div\", 9);\n          i0.ɵɵtemplate(11, AppointmentBookingComponent_form_11_Template, 47, 26, \"form\", 10);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.success);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.bookingForm);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.RadioControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink],\n      styles: [\".avatar-circle[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-size: 1.2rem;\\n  flex-shrink: 0;\\n}\\n\\n.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%] {\\n  display: block;\\n  cursor: pointer;\\n  margin: 0;\\n}\\n.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%]   .time-slot-label[_ngcontent-%COMP%] {\\n  display: block;\\n  padding: 0.75rem 1rem;\\n  border: 2px solid #e3e6f0;\\n  border-radius: 0.5rem;\\n  text-align: center;\\n  transition: all 0.3s ease;\\n  background: white;\\n  font-weight: 500;\\n}\\n.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%]   .time-slot-label[_ngcontent-%COMP%]:hover {\\n  border-color: #667eea;\\n  background: #f8f9fc;\\n}\\n.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked    + .time-slot-label[_ngcontent-%COMP%] {\\n  border-color: #667eea;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n}\\n\\n.form-select[_ngcontent-%COMP%]:focus, .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #667eea;\\n  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border: none;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\\n  transform: translateY(-1px);\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:disabled {\\n  background: #6c757d;\\n  opacity: 0.65;\\n}\\n\\n.alert-success[_ngcontent-%COMP%] {\\n  border-left: 4px solid #28a745;\\n}\\n\\n.alert-danger[_ngcontent-%COMP%] {\\n  border-left: 4px solid #e74a3b;\\n}\\n\\n.alert-info[_ngcontent-%COMP%] {\\n  border-left: 4px solid #17a2b8;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  border: 1px solid #e3e6f0;\\n  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);\\n}\\n\\n.bg-light[_ngcontent-%COMP%] {\\n  background-color: #f8f9fc !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "AppointmentType", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "success", "ctx_r1", "error", "ɵɵproperty", "doctor_r15", "id", "ɵɵtextInterpolate2", "fullName", "specialization", "ctx_r16", "<PERSON><PERSON><PERSON><PERSON>", "affiliation", "ɵɵtemplate", "AppointmentBookingComponent_form_11_div_9_p_10_Template", "ɵɵtextInterpolate", "ctx_r5", "type_r17", "value", "label", "ctx_r18", "getTimeSlotValue", "slot_r19", "getTimeSlotDisplay", "AppointmentBookingComponent_form_11_div_26_div_2_Template", "ctx_r10", "availableSlots", "ɵɵlistener", "AppointmentBookingComponent_form_11_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r21", "ctx_r20", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "AppointmentBookingComponent_form_11_option_7_Template", "AppointmentBookingComponent_form_11_div_8_Template", "AppointmentBookingComponent_form_11_div_9_Template", "AppointmentBookingComponent_form_11_div_15_Template", "AppointmentBookingComponent_form_11_option_20_Template", "AppointmentBookingComponent_form_11_div_21_Template", "AppointmentBookingComponent_form_11_div_25_Template", "AppointmentBookingComponent_form_11_div_26_Template", "AppointmentBookingComponent_form_11_div_27_Template", "AppointmentBookingComponent_form_11_div_34_Template", "AppointmentBookingComponent_form_11_span_44_Template", "AppointmentBookingComponent_form_11_i_45_Template", "ctx_r2", "bookingForm", "ɵɵclassProp", "tmp_1_0", "get", "invalid", "touched", "doctors", "tmp_3_0", "tmp_5_0", "getTodayDate", "tmp_7_0", "tmp_8_0", "appointmentTypes", "tmp_10_0", "loading", "length", "ɵɵstyleProp", "tmp_14_0", "tmp_15_0", "tmp_16_0", "submitting", "AppointmentBookingComponent", "constructor", "fb", "appointmentService", "route", "router", "timeSlots", "IN_PERSON", "VIDEO_CALL", "initializeForm", "group", "doctorId", "required", "date", "timeSlot", "type", "reasonForVisit", "notes", "ngOnInit", "loadDoctors", "queryParams", "subscribe", "params", "patchValue", "onDoctorChange", "tomorrow", "Date", "setDate", "getDate", "tomorrowStr", "toISOString", "split", "setValue", "valueChanges", "onDateChange", "getDoctors", "next", "console", "find", "d", "loadTimeSlots", "getAvailableTimeSlots", "slots", "filter", "slot", "available", "currentSlot", "startTime", "endTime", "valid", "formValue", "request", "parseInt", "undefined", "createAppointment", "appointment", "setTimeout", "navigate", "markFormGroupTouched", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "formatTime", "time", "hours", "minutes", "hour", "ampm", "displayHour", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AppointmentService", "i3", "ActivatedRoute", "Router", "selectors", "decls", "vars", "consts", "template", "AppointmentBookingComponent_Template", "rf", "ctx", "AppointmentBookingComponent_div_9_Template", "AppointmentBookingComponent_div_10_Template", "AppointmentBookingComponent_form_11_Template"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/appointments/appointment-booking/appointment-booking.component.ts", "/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/appointments/appointment-booking/appointment-booking.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { AppointmentService } from '../../core/services/appointment.service';\nimport { Doctor, TimeSlot, AppointmentType, AppointmentRequest } from '../../core/models/appointment.model';\n\n@Component({\n  selector: 'app-appointment-booking',\n  templateUrl: './appointment-booking.component.html',\n  styleUrls: ['./appointment-booking.component.scss']\n})\nexport class AppointmentBookingComponent implements OnInit {\n  bookingForm!: FormGroup;\n  doctors: Doctor[] = [];\n  selectedDoctor: Doctor | null = null;\n  timeSlots: TimeSlot[] = [];\n  availableSlots: TimeSlot[] = [];\n  loading = false;\n  submitting = false;\n  error: string | null = null;\n  success: string | null = null;\n\n  appointmentTypes = [\n    { value: AppointmentType.IN_PERSON, label: 'In Person' },\n    { value: AppointmentType.VIDEO_CALL, label: 'Video Call' }\n  ];\n\n  constructor(\n    private fb: FormBuilder,\n    private appointmentService: AppointmentService,\n    private route: ActivatedRoute,\n    private router: Router\n  ) {\n    this.initializeForm();\n  }\n\n  private initializeForm(): void {\n    this.bookingForm = this.fb.group({\n      doctorId: ['', Validators.required],\n      date: ['', Validators.required],\n      timeSlot: ['', Validators.required],\n      type: [AppointmentType.IN_PERSON, Validators.required],\n      reasonForVisit: ['', Validators.required],\n      notes: ['']\n    });\n  }\n\n  ngOnInit(): void {\n    this.loadDoctors();\n    \n    // Check if doctor ID is provided in query params\n    this.route.queryParams.subscribe(params => {\n      if (params['doctorId']) {\n        this.bookingForm.patchValue({ doctorId: params['doctorId'] });\n        this.onDoctorChange();\n      }\n    });\n\n    // Set minimum date to tomorrow (backend requires future dates)\n    const tomorrow = new Date();\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    const tomorrowStr = tomorrow.toISOString().split('T')[0];\n    this.bookingForm.get('date')?.setValue(tomorrowStr);\n\n    // Watch for form changes\n    this.bookingForm.get('doctorId')?.valueChanges.subscribe(() => this.onDoctorChange());\n    this.bookingForm.get('date')?.valueChanges.subscribe(() => this.onDateChange());\n  }\n\n  loadDoctors(): void {\n    this.appointmentService.getDoctors().subscribe({\n      next: (doctors) => {\n        this.doctors = doctors;\n      },\n      error: (error) => {\n        this.error = 'Failed to load doctors. Please try again.';\n        console.error('Error loading doctors:', error);\n      }\n    });\n  }\n\n  onDoctorChange(): void {\n    const doctorId = this.bookingForm.get('doctorId')?.value;\n    if (doctorId) {\n      this.selectedDoctor = this.doctors.find(d => d.id == doctorId) || null;\n      this.loadTimeSlots();\n    } else {\n      this.selectedDoctor = null;\n      this.timeSlots = [];\n      this.availableSlots = [];\n    }\n  }\n\n  onDateChange(): void {\n    if (this.selectedDoctor) {\n      this.loadTimeSlots();\n    }\n  }\n\n  loadTimeSlots(): void {\n    const doctorId = this.bookingForm.get('doctorId')?.value;\n    const date = this.bookingForm.get('date')?.value;\n    \n    if (!doctorId || !date) return;\n\n    this.loading = true;\n    this.appointmentService.getAvailableTimeSlots(doctorId, date).subscribe({\n      next: (slots) => {\n        this.timeSlots = slots;\n        this.availableSlots = slots.filter(slot => slot.available);\n        this.loading = false;\n        \n        // Clear selected time slot if it's no longer available\n        const currentSlot = this.bookingForm.get('timeSlot')?.value;\n        if (currentSlot && !this.availableSlots.find(slot => \n          slot.startTime === currentSlot.split('-')[0] && \n          slot.endTime === currentSlot.split('-')[1])) {\n          this.bookingForm.patchValue({ timeSlot: '' });\n        }\n      },\n      error: (error) => {\n        this.error = 'Failed to load available time slots. Please try again.';\n        this.loading = false;\n        console.error('Error loading time slots:', error);\n      }\n    });\n  }\n\n  onSubmit(): void {\n    if (this.bookingForm.valid) {\n      this.submitting = true;\n      this.error = null;\n      this.success = null;\n\n      const formValue = this.bookingForm.value;\n      const [startTime, endTime] = formValue.timeSlot.split('-');\n\n      const request: AppointmentRequest = {\n        doctorId: parseInt(formValue.doctorId),\n        date: formValue.date,\n        startTime: startTime,\n        endTime: endTime,\n        type: formValue.type,\n        reasonForVisit: formValue.reasonForVisit,\n        notes: formValue.notes || undefined\n      };\n\n      this.appointmentService.createAppointment(request).subscribe({\n        next: (appointment) => {\n          this.success = 'Appointment booked successfully!';\n          this.submitting = false;\n          \n          // Redirect to appointment details after 2 seconds\n          setTimeout(() => {\n            this.router.navigate(['/appointments', appointment.id]);\n          }, 2000);\n        },\n        error: (error) => {\n          this.error = 'Failed to book appointment. Please try again.';\n          this.submitting = false;\n          console.error('Error booking appointment:', error);\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.bookingForm.controls).forEach(key => {\n      const control = this.bookingForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  getTimeSlotDisplay(slot: TimeSlot): string {\n    return `${this.formatTime(slot.startTime)} - ${this.formatTime(slot.endTime)}`;\n  }\n\n  getTimeSlotValue(slot: TimeSlot): string {\n    return `${slot.startTime}-${slot.endTime}`;\n  }\n\n  private formatTime(time: string): string {\n    const [hours, minutes] = time.split(':');\n    const hour = parseInt(hours);\n    const ampm = hour >= 12 ? 'PM' : 'AM';\n    const displayHour = hour % 12 || 12;\n    return `${displayHour}:${minutes} ${ampm}`;\n  }\n\n  getTodayDate(): string {\n    // Return tomorrow's date as minimum (backend requires future dates)\n    const tomorrow = new Date();\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    return tomorrow.toISOString().split('T')[0];\n  }\n}\n", "<div class=\"container-fluid py-4\">\n  <div class=\"row justify-content-center\">\n    <div class=\"col-lg-8\">\n      <div class=\"card\">\n        <div class=\"card-header\">\n          <h4 class=\"card-title mb-0\">\n            <i class=\"fas fa-calendar-plus me-2\"></i>Book an Appointment\n          </h4>\n        </div>\n        <div class=\"card-body\">\n          <!-- Success Message -->\n          <div *ngIf=\"success\" class=\"alert alert-success\" role=\"alert\">\n            <i class=\"fas fa-check-circle me-2\"></i>\n            {{ success }}\n          </div>\n\n          <!-- Error Message -->\n          <div *ngIf=\"error\" class=\"alert alert-danger\" role=\"alert\">\n            <i class=\"fas fa-exclamation-triangle me-2\"></i>\n            {{ error }}\n          </div>\n\n          <form *ngIf=\"bookingForm\" [formGroup]=\"bookingForm\" (ngSubmit)=\"onSubmit()\">\n            <!-- Doctor Selection -->\n            <div class=\"mb-4\">\n              <label for=\"doctorId\" class=\"form-label\">Select Doctor *</label>\n              <select \n                id=\"doctorId\" \n                class=\"form-select\"\n                formControlName=\"doctorId\"\n                [class.is-invalid]=\"bookingForm.get('doctorId')?.invalid && bookingForm.get('doctorId')?.touched\">\n                <option value=\"\">Choose a doctor...</option>\n                <option *ngFor=\"let doctor of doctors\" [value]=\"doctor.id\">\n                  {{ doctor.fullName }} - {{ doctor.specialization || 'General Practice' }}\n                </option>\n              </select>\n              <div class=\"invalid-feedback\" *ngIf=\"bookingForm.get('doctorId')?.invalid && bookingForm.get('doctorId')?.touched\">\n                Please select a doctor.\n              </div>\n            </div>\n\n            <!-- Selected Doctor Info -->\n            <div *ngIf=\"selectedDoctor\" class=\"card bg-light mb-4\">\n              <div class=\"card-body\">\n                <div class=\"d-flex align-items-center\">\n                  <div class=\"avatar-circle me-3\">\n                    <i class=\"fas fa-user-md\"></i>\n                  </div>\n                  <div>\n                    <h6 class=\"mb-1\">{{ selectedDoctor.fullName }}</h6>\n                    <p class=\"text-muted mb-1\">{{ selectedDoctor.specialization || 'General Practice' }}</p>\n                    <p class=\"text-muted mb-0\" *ngIf=\"selectedDoctor.affiliation\">\n                      {{ selectedDoctor.affiliation }}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"row\">\n              <!-- Date Selection -->\n              <div class=\"col-md-6 mb-3\">\n                <label for=\"date\" class=\"form-label\">Appointment Date *</label>\n                <input \n                  type=\"date\" \n                  id=\"date\"\n                  class=\"form-control\"\n                  formControlName=\"date\"\n                  [min]=\"getTodayDate()\"\n                  [class.is-invalid]=\"bookingForm.get('date')?.invalid && bookingForm.get('date')?.touched\">\n                <div class=\"invalid-feedback\" *ngIf=\"bookingForm.get('date')?.invalid && bookingForm.get('date')?.touched\">\n                  Please select a date.\n                </div>\n              </div>\n\n              <!-- Appointment Type -->\n              <div class=\"col-md-6 mb-3\">\n                <label for=\"type\" class=\"form-label\">Appointment Type *</label>\n                <select \n                  id=\"type\" \n                  class=\"form-select\"\n                  formControlName=\"type\"\n                  [class.is-invalid]=\"bookingForm.get('type')?.invalid && bookingForm.get('type')?.touched\">\n                  <option *ngFor=\"let type of appointmentTypes\" [value]=\"type.value\">\n                    {{ type.label }}\n                  </option>\n                </select>\n                <div class=\"invalid-feedback\" *ngIf=\"bookingForm.get('type')?.invalid && bookingForm.get('type')?.touched\">\n                  Please select an appointment type.\n                </div>\n              </div>\n            </div>\n\n            <!-- Time Slot Selection -->\n            <div class=\"mb-3\">\n              <label for=\"timeSlot\" class=\"form-label\">Available Time Slots *</label>\n              \n              <!-- Loading Time Slots -->\n              <div *ngIf=\"loading\" class=\"text-center py-3\">\n                <div class=\"spinner-border spinner-border-sm text-primary me-2\" role=\"status\">\n                  <span class=\"visually-hidden\">Loading...</span>\n                </div>\n                Loading available time slots...\n              </div>\n\n              <!-- Time Slots Grid -->\n              <div *ngIf=\"!loading && availableSlots.length > 0\" class=\"time-slots-grid\">\n                <div class=\"row\">\n                  <div class=\"col-md-4 col-sm-6 mb-2\" *ngFor=\"let slot of availableSlots\">\n                    <label class=\"time-slot-option\">\n                      <input \n                        type=\"radio\" \n                        name=\"timeSlot\"\n                        [value]=\"getTimeSlotValue(slot)\"\n                        formControlName=\"timeSlot\">\n                      <span class=\"time-slot-label\">\n                        {{ getTimeSlotDisplay(slot) }}\n                      </span>\n                    </label>\n                  </div>\n                </div>\n              </div>\n\n              <!-- No Time Slots Available -->\n              <div *ngIf=\"!loading && selectedDoctor && availableSlots.length === 0\" class=\"alert alert-info\">\n                <i class=\"fas fa-info-circle me-2\"></i>\n                No available time slots for the selected date. Please choose a different date.\n              </div>\n\n              <div class=\"invalid-feedback\" \n                   [style.display]=\"bookingForm.get('timeSlot')?.invalid && bookingForm.get('timeSlot')?.touched ? 'block' : 'none'\">\n                Please select a time slot.\n              </div>\n            </div>\n\n            <!-- Reason for Visit -->\n            <div class=\"mb-3\">\n              <label for=\"reasonForVisit\" class=\"form-label\">Reason for Visit *</label>\n              <input \n                type=\"text\" \n                id=\"reasonForVisit\"\n                class=\"form-control\"\n                formControlName=\"reasonForVisit\"\n                placeholder=\"e.g., Regular checkup, Follow-up, Consultation\"\n                [class.is-invalid]=\"bookingForm.get('reasonForVisit')?.invalid && bookingForm.get('reasonForVisit')?.touched\">\n              <div class=\"invalid-feedback\" *ngIf=\"bookingForm.get('reasonForVisit')?.invalid && bookingForm.get('reasonForVisit')?.touched\">\n                Please provide a reason for the visit.\n              </div>\n            </div>\n\n            <!-- Additional Notes -->\n            <div class=\"mb-4\">\n              <label for=\"notes\" class=\"form-label\">Additional Notes (Optional)</label>\n              <textarea \n                id=\"notes\"\n                class=\"form-control\"\n                formControlName=\"notes\"\n                rows=\"3\"\n                placeholder=\"Any additional information or special requests...\"></textarea>\n            </div>\n\n            <!-- Submit Button -->\n            <div class=\"d-flex justify-content-between\">\n              <button \n                type=\"button\" \n                class=\"btn btn-outline-secondary\"\n                routerLink=\"/appointments/doctors\">\n                <i class=\"fas fa-arrow-left me-2\"></i>\n                Back to Doctors\n              </button>\n              <button \n                type=\"submit\" \n                class=\"btn btn-primary\"\n                [disabled]=\"bookingForm.invalid || submitting\">\n                <span *ngIf=\"submitting\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\">\n                  <span class=\"visually-hidden\">Loading...</span>\n                </span>\n                <i *ngIf=\"!submitting\" class=\"fas fa-calendar-check me-2\"></i>\n                {{ submitting ? 'Booking...' : 'Book Appointment' }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAGnE,SAA2BC,eAAe,QAA4B,qCAAqC;;;;;;;;ICOjGC,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAE,SAAA,YAAwC;IACxCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,OAAA,MACF;;;;;IAGAR,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAE,SAAA,YAAgD;IAChDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAG,MAAA,CAAAC,KAAA,MACF;;;;;IAYMV,EAAA,CAAAC,cAAA,iBAA2D;IACzDD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAF8BJ,EAAA,CAAAW,UAAA,UAAAC,UAAA,CAAAC,EAAA,CAAmB;IACxDb,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAc,kBAAA,MAAAF,UAAA,CAAAG,QAAA,SAAAH,UAAA,CAAAI,cAAA,4BACF;;;;;IAEFhB,EAAA,CAAAC,cAAA,cAAmH;IACjHD,EAAA,CAAAG,MAAA,gCACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAaAJ,EAAA,CAAAC,cAAA,YAA8D;IAC5DD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAW,OAAA,CAAAC,cAAA,CAAAC,WAAA,MACF;;;;;IAXRnB,EAAA,CAAAC,cAAA,cAAuD;IAI/CD,EAAA,CAAAE,SAAA,YAA8B;IAChCF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,UAAK;IACcD,EAAA,CAAAG,MAAA,GAA6B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnDJ,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAyD;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACxFJ,EAAA,CAAAoB,UAAA,KAAAC,uDAAA,gBAEI;IACNrB,EAAA,CAAAI,YAAA,EAAM;;;;IALaJ,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAsB,iBAAA,CAAAC,MAAA,CAAAL,cAAA,CAAAH,QAAA,CAA6B;IACnBf,EAAA,CAAAK,SAAA,GAAyD;IAAzDL,EAAA,CAAAsB,iBAAA,CAAAC,MAAA,CAAAL,cAAA,CAAAF,cAAA,uBAAyD;IACxDhB,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAW,UAAA,SAAAY,MAAA,CAAAL,cAAA,CAAAC,WAAA,CAAgC;;;;;IAmBhEnB,EAAA,CAAAC,cAAA,cAA2G;IACzGD,EAAA,CAAAG,MAAA,8BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAWJJ,EAAA,CAAAC,cAAA,iBAAmE;IACjED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAFqCJ,EAAA,CAAAW,UAAA,UAAAa,QAAA,CAAAC,KAAA,CAAoB;IAChEzB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAkB,QAAA,CAAAE,KAAA,MACF;;;;;IAEF1B,EAAA,CAAAC,cAAA,cAA2G;IACzGD,EAAA,CAAAG,MAAA,2CACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IASRJ,EAAA,CAAAC,cAAA,cAA8C;IAEZD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEjDJ,EAAA,CAAAG,MAAA,wCACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAKFJ,EAAA,CAAAC,cAAA,cAAwE;IAEpED,EAAA,CAAAE,SAAA,gBAI6B;IAC7BF,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAJLJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAW,UAAA,UAAAgB,OAAA,CAAAC,gBAAA,CAAAC,QAAA,EAAgC;IAGhC7B,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAqB,OAAA,CAAAG,kBAAA,CAAAD,QAAA,OACF;;;;;IAXR7B,EAAA,CAAAC,cAAA,cAA2E;IAEvED,EAAA,CAAAoB,UAAA,IAAAW,yDAAA,kBAWM;IACR/B,EAAA,CAAAI,YAAA,EAAM;;;;IAZiDJ,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAW,UAAA,YAAAqB,OAAA,CAAAC,cAAA,CAAiB;;;;;IAgB1EjC,EAAA,CAAAC,cAAA,cAAgG;IAC9FD,EAAA,CAAAE,SAAA,YAAuC;IACvCF,EAAA,CAAAG,MAAA,uFACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAkBNJ,EAAA,CAAAC,cAAA,cAA+H;IAC7HD,EAAA,CAAAG,MAAA,+CACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IA2BJJ,EAAA,CAAAC,cAAA,eAAqF;IACrDD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAEjDJ,EAAA,CAAAE,SAAA,YAA8D;;;;;;IA3JpEF,EAAA,CAAAC,cAAA,eAA4E;IAAxBD,EAAA,CAAAkC,UAAA,sBAAAC,sEAAA;MAAAnC,EAAA,CAAAoC,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAtC,EAAA,CAAAuC,aAAA;MAAA,OAAYvC,EAAA,CAAAwC,WAAA,CAAAF,OAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAEzEzC,EAAA,CAAAC,cAAA,cAAkB;IACyBD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAChEJ,EAAA,CAAAC,cAAA,iBAIoG;IACjFD,EAAA,CAAAG,MAAA,yBAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAC5CJ,EAAA,CAAAoB,UAAA,IAAAsB,qDAAA,qBAES;IACX1C,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAoB,UAAA,IAAAuB,kDAAA,kBAEM;IACR3C,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAoB,UAAA,IAAAwB,kDAAA,mBAeM;IAEN5C,EAAA,CAAAC,cAAA,eAAiB;IAGwBD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC/DJ,EAAA,CAAAE,SAAA,iBAM4F;IAC5FF,EAAA,CAAAoB,UAAA,KAAAyB,mDAAA,kBAEM;IACR7C,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAC,cAAA,eAA2B;IACYD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC/DJ,EAAA,CAAAC,cAAA,kBAI4F;IAC1FD,EAAA,CAAAoB,UAAA,KAAA0B,sDAAA,qBAES;IACX9C,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAoB,UAAA,KAAA2B,mDAAA,kBAEM;IACR/C,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAC,cAAA,eAAkB;IACyBD,EAAA,CAAAG,MAAA,8BAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAGvEJ,EAAA,CAAAoB,UAAA,KAAA4B,mDAAA,kBAKM;IAGNhD,EAAA,CAAAoB,UAAA,KAAA6B,mDAAA,kBAeM;IAGNjD,EAAA,CAAAoB,UAAA,KAAA8B,mDAAA,kBAGM;IAENlD,EAAA,CAAAC,cAAA,eACuH;IACrHD,EAAA,CAAAG,MAAA,oCACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAC,cAAA,eAAkB;IAC+BD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACzEJ,EAAA,CAAAE,SAAA,iBAMgH;IAChHF,EAAA,CAAAoB,UAAA,KAAA+B,mDAAA,kBAEM;IACRnD,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAC,cAAA,eAAkB;IACsBD,EAAA,CAAAG,MAAA,mCAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACzEJ,EAAA,CAAAE,SAAA,oBAK6E;IAC/EF,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAC,cAAA,eAA4C;IAKxCD,EAAA,CAAAE,SAAA,aAAsC;IACtCF,EAAA,CAAAG,MAAA,yBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAGiD;IAC/CD,EAAA,CAAAoB,UAAA,KAAAgC,oDAAA,mBAEO;IACPpD,EAAA,CAAAoB,UAAA,KAAAiC,iDAAA,gBAA8D;IAC9DrD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;;;;;;;;;IA7JaJ,EAAA,CAAAW,UAAA,cAAA2C,MAAA,CAAAC,WAAA,CAAyB;IAQ7CvD,EAAA,CAAAK,SAAA,GAAiG;IAAjGL,EAAA,CAAAwD,WAAA,iBAAAC,OAAA,GAAAH,MAAA,CAAAC,WAAA,CAAAG,GAAA,+BAAAD,OAAA,CAAAE,OAAA,OAAAF,OAAA,GAAAH,MAAA,CAAAC,WAAA,CAAAG,GAAA,+BAAAD,OAAA,CAAAG,OAAA,EAAiG;IAEtE5D,EAAA,CAAAK,SAAA,GAAU;IAAVL,EAAA,CAAAW,UAAA,YAAA2C,MAAA,CAAAO,OAAA,CAAU;IAIR7D,EAAA,CAAAK,SAAA,GAAkF;IAAlFL,EAAA,CAAAW,UAAA,WAAAmD,OAAA,GAAAR,MAAA,CAAAC,WAAA,CAAAG,GAAA,+BAAAI,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAR,MAAA,CAAAC,WAAA,CAAAG,GAAA,+BAAAI,OAAA,CAAAF,OAAA,EAAkF;IAM7G5D,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAW,UAAA,SAAA2C,MAAA,CAAApC,cAAA,CAAoB;IA2BpBlB,EAAA,CAAAK,SAAA,GAAyF;IAAzFL,EAAA,CAAAwD,WAAA,iBAAAO,OAAA,GAAAT,MAAA,CAAAC,WAAA,CAAAG,GAAA,2BAAAK,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAT,MAAA,CAAAC,WAAA,CAAAG,GAAA,2BAAAK,OAAA,CAAAH,OAAA,EAAyF;IADzF5D,EAAA,CAAAW,UAAA,QAAA2C,MAAA,CAAAU,YAAA,GAAsB;IAEOhE,EAAA,CAAAK,SAAA,GAA0E;IAA1EL,EAAA,CAAAW,UAAA,WAAAsD,OAAA,GAAAX,MAAA,CAAAC,WAAA,CAAAG,GAAA,2BAAAO,OAAA,CAAAN,OAAA,OAAAM,OAAA,GAAAX,MAAA,CAAAC,WAAA,CAAAG,GAAA,2BAAAO,OAAA,CAAAL,OAAA,EAA0E;IAYvG5D,EAAA,CAAAK,SAAA,GAAyF;IAAzFL,EAAA,CAAAwD,WAAA,iBAAAU,OAAA,GAAAZ,MAAA,CAAAC,WAAA,CAAAG,GAAA,2BAAAQ,OAAA,CAAAP,OAAA,OAAAO,OAAA,GAAAZ,MAAA,CAAAC,WAAA,CAAAG,GAAA,2BAAAQ,OAAA,CAAAN,OAAA,EAAyF;IAChE5D,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAW,UAAA,YAAA2C,MAAA,CAAAa,gBAAA,CAAmB;IAIfnE,EAAA,CAAAK,SAAA,GAA0E;IAA1EL,EAAA,CAAAW,UAAA,WAAAyD,QAAA,GAAAd,MAAA,CAAAC,WAAA,CAAAG,GAAA,2BAAAU,QAAA,CAAAT,OAAA,OAAAS,QAAA,GAAAd,MAAA,CAAAC,WAAA,CAAAG,GAAA,2BAAAU,QAAA,CAAAR,OAAA,EAA0E;IAWrG5D,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAW,UAAA,SAAA2C,MAAA,CAAAe,OAAA,CAAa;IAQbrE,EAAA,CAAAK,SAAA,GAA2C;IAA3CL,EAAA,CAAAW,UAAA,UAAA2C,MAAA,CAAAe,OAAA,IAAAf,MAAA,CAAArB,cAAA,CAAAqC,MAAA,KAA2C;IAkB3CtE,EAAA,CAAAK,SAAA,GAA+D;IAA/DL,EAAA,CAAAW,UAAA,UAAA2C,MAAA,CAAAe,OAAA,IAAAf,MAAA,CAAApC,cAAA,IAAAoC,MAAA,CAAArB,cAAA,CAAAqC,MAAA,OAA+D;IAMhEtE,EAAA,CAAAK,SAAA,GAAiH;IAAjHL,EAAA,CAAAuE,WAAA,cAAAC,QAAA,GAAAlB,MAAA,CAAAC,WAAA,CAAAG,GAAA,+BAAAc,QAAA,CAAAb,OAAA,OAAAa,QAAA,GAAAlB,MAAA,CAAAC,WAAA,CAAAG,GAAA,+BAAAc,QAAA,CAAAZ,OAAA,qBAAiH;IAcpH5D,EAAA,CAAAK,SAAA,GAA6G;IAA7GL,EAAA,CAAAwD,WAAA,iBAAAiB,QAAA,GAAAnB,MAAA,CAAAC,WAAA,CAAAG,GAAA,qCAAAe,QAAA,CAAAd,OAAA,OAAAc,QAAA,GAAAnB,MAAA,CAAAC,WAAA,CAAAG,GAAA,qCAAAe,QAAA,CAAAb,OAAA,EAA6G;IAChF5D,EAAA,CAAAK,SAAA,GAA8F;IAA9FL,EAAA,CAAAW,UAAA,WAAA+D,QAAA,GAAApB,MAAA,CAAAC,WAAA,CAAAG,GAAA,qCAAAgB,QAAA,CAAAf,OAAA,OAAAe,QAAA,GAAApB,MAAA,CAAAC,WAAA,CAAAG,GAAA,qCAAAgB,QAAA,CAAAd,OAAA,EAA8F;IA4B3H5D,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAW,UAAA,aAAA2C,MAAA,CAAAC,WAAA,CAAAI,OAAA,IAAAL,MAAA,CAAAqB,UAAA,CAA8C;IACvC3E,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAW,UAAA,SAAA2C,MAAA,CAAAqB,UAAA,CAAgB;IAGnB3E,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAW,UAAA,UAAA2C,MAAA,CAAAqB,UAAA,CAAiB;IACrB3E,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAgD,MAAA,CAAAqB,UAAA,0CACF;;;ADxKd,OAAM,MAAOC,2BAA2B;EAgBtCC,YACUC,EAAe,EACfC,kBAAsC,EACtCC,KAAqB,EACrBC,MAAc;IAHd,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IAlBhB,KAAApB,OAAO,GAAa,EAAE;IACtB,KAAA3C,cAAc,GAAkB,IAAI;IACpC,KAAAgE,SAAS,GAAe,EAAE;IAC1B,KAAAjD,cAAc,GAAe,EAAE;IAC/B,KAAAoC,OAAO,GAAG,KAAK;IACf,KAAAM,UAAU,GAAG,KAAK;IAClB,KAAAjE,KAAK,GAAkB,IAAI;IAC3B,KAAAF,OAAO,GAAkB,IAAI;IAE7B,KAAA2D,gBAAgB,GAAG,CACjB;MAAE1C,KAAK,EAAE1B,eAAe,CAACoF,SAAS;MAAEzD,KAAK,EAAE;IAAW,CAAE,EACxD;MAAED,KAAK,EAAE1B,eAAe,CAACqF,UAAU;MAAE1D,KAAK,EAAE;IAAY,CAAE,CAC3D;IAQC,IAAI,CAAC2D,cAAc,EAAE;EACvB;EAEQA,cAAcA,CAAA;IACpB,IAAI,CAAC9B,WAAW,GAAG,IAAI,CAACuB,EAAE,CAACQ,KAAK,CAAC;MAC/BC,QAAQ,EAAE,CAAC,EAAE,EAAEzF,UAAU,CAAC0F,QAAQ,CAAC;MACnCC,IAAI,EAAE,CAAC,EAAE,EAAE3F,UAAU,CAAC0F,QAAQ,CAAC;MAC/BE,QAAQ,EAAE,CAAC,EAAE,EAAE5F,UAAU,CAAC0F,QAAQ,CAAC;MACnCG,IAAI,EAAE,CAAC5F,eAAe,CAACoF,SAAS,EAAErF,UAAU,CAAC0F,QAAQ,CAAC;MACtDI,cAAc,EAAE,CAAC,EAAE,EAAE9F,UAAU,CAAC0F,QAAQ,CAAC;MACzCK,KAAK,EAAE,CAAC,EAAE;KACX,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAElB;IACA,IAAI,CAACf,KAAK,CAACgB,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACxC,IAAIA,MAAM,CAAC,UAAU,CAAC,EAAE;QACtB,IAAI,CAAC3C,WAAW,CAAC4C,UAAU,CAAC;UAAEZ,QAAQ,EAAEW,MAAM,CAAC,UAAU;QAAC,CAAE,CAAC;QAC7D,IAAI,CAACE,cAAc,EAAE;;IAEzB,CAAC,CAAC;IAEF;IACA,MAAMC,QAAQ,GAAG,IAAIC,IAAI,EAAE;IAC3BD,QAAQ,CAACE,OAAO,CAACF,QAAQ,CAACG,OAAO,EAAE,GAAG,CAAC,CAAC;IACxC,MAAMC,WAAW,GAAGJ,QAAQ,CAACK,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACxD,IAAI,CAACpD,WAAW,CAACG,GAAG,CAAC,MAAM,CAAC,EAAEkD,QAAQ,CAACH,WAAW,CAAC;IAEnD;IACA,IAAI,CAAClD,WAAW,CAACG,GAAG,CAAC,UAAU,CAAC,EAAEmD,YAAY,CAACZ,SAAS,CAAC,MAAM,IAAI,CAACG,cAAc,EAAE,CAAC;IACrF,IAAI,CAAC7C,WAAW,CAACG,GAAG,CAAC,MAAM,CAAC,EAAEmD,YAAY,CAACZ,SAAS,CAAC,MAAM,IAAI,CAACa,YAAY,EAAE,CAAC;EACjF;EAEAf,WAAWA,CAAA;IACT,IAAI,CAAChB,kBAAkB,CAACgC,UAAU,EAAE,CAACd,SAAS,CAAC;MAC7Ce,IAAI,EAAGnD,OAAO,IAAI;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;MACxB,CAAC;MACDnD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,2CAA2C;QACxDuG,OAAO,CAACvG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEA0F,cAAcA,CAAA;IACZ,MAAMb,QAAQ,GAAG,IAAI,CAAChC,WAAW,CAACG,GAAG,CAAC,UAAU,CAAC,EAAEjC,KAAK;IACxD,IAAI8D,QAAQ,EAAE;MACZ,IAAI,CAACrE,cAAc,GAAG,IAAI,CAAC2C,OAAO,CAACqD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtG,EAAE,IAAI0E,QAAQ,CAAC,IAAI,IAAI;MACtE,IAAI,CAAC6B,aAAa,EAAE;KACrB,MAAM;MACL,IAAI,CAAClG,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACgE,SAAS,GAAG,EAAE;MACnB,IAAI,CAACjD,cAAc,GAAG,EAAE;;EAE5B;EAEA6E,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC5F,cAAc,EAAE;MACvB,IAAI,CAACkG,aAAa,EAAE;;EAExB;EAEAA,aAAaA,CAAA;IACX,MAAM7B,QAAQ,GAAG,IAAI,CAAChC,WAAW,CAACG,GAAG,CAAC,UAAU,CAAC,EAAEjC,KAAK;IACxD,MAAMgE,IAAI,GAAG,IAAI,CAAClC,WAAW,CAACG,GAAG,CAAC,MAAM,CAAC,EAAEjC,KAAK;IAEhD,IAAI,CAAC8D,QAAQ,IAAI,CAACE,IAAI,EAAE;IAExB,IAAI,CAACpB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACU,kBAAkB,CAACsC,qBAAqB,CAAC9B,QAAQ,EAAEE,IAAI,CAAC,CAACQ,SAAS,CAAC;MACtEe,IAAI,EAAGM,KAAK,IAAI;QACd,IAAI,CAACpC,SAAS,GAAGoC,KAAK;QACtB,IAAI,CAACrF,cAAc,GAAGqF,KAAK,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,SAAS,CAAC;QAC1D,IAAI,CAACpD,OAAO,GAAG,KAAK;QAEpB;QACA,MAAMqD,WAAW,GAAG,IAAI,CAACnE,WAAW,CAACG,GAAG,CAAC,UAAU,CAAC,EAAEjC,KAAK;QAC3D,IAAIiG,WAAW,IAAI,CAAC,IAAI,CAACzF,cAAc,CAACiF,IAAI,CAACM,IAAI,IAC/CA,IAAI,CAACG,SAAS,KAAKD,WAAW,CAACf,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAC5Ca,IAAI,CAACI,OAAO,KAAKF,WAAW,CAACf,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC7C,IAAI,CAACpD,WAAW,CAAC4C,UAAU,CAAC;YAAET,QAAQ,EAAE;UAAE,CAAE,CAAC;;MAEjD,CAAC;MACDhF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,wDAAwD;QACrE,IAAI,CAAC2D,OAAO,GAAG,KAAK;QACpB4C,OAAO,CAACvG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;KACD,CAAC;EACJ;EAEA+B,QAAQA,CAAA;IACN,IAAI,IAAI,CAACc,WAAW,CAACsE,KAAK,EAAE;MAC1B,IAAI,CAAClD,UAAU,GAAG,IAAI;MACtB,IAAI,CAACjE,KAAK,GAAG,IAAI;MACjB,IAAI,CAACF,OAAO,GAAG,IAAI;MAEnB,MAAMsH,SAAS,GAAG,IAAI,CAACvE,WAAW,CAAC9B,KAAK;MACxC,MAAM,CAACkG,SAAS,EAAEC,OAAO,CAAC,GAAGE,SAAS,CAACpC,QAAQ,CAACiB,KAAK,CAAC,GAAG,CAAC;MAE1D,MAAMoB,OAAO,GAAuB;QAClCxC,QAAQ,EAAEyC,QAAQ,CAACF,SAAS,CAACvC,QAAQ,CAAC;QACtCE,IAAI,EAAEqC,SAAS,CAACrC,IAAI;QACpBkC,SAAS,EAAEA,SAAS;QACpBC,OAAO,EAAEA,OAAO;QAChBjC,IAAI,EAAEmC,SAAS,CAACnC,IAAI;QACpBC,cAAc,EAAEkC,SAAS,CAAClC,cAAc;QACxCC,KAAK,EAAEiC,SAAS,CAACjC,KAAK,IAAIoC;OAC3B;MAED,IAAI,CAAClD,kBAAkB,CAACmD,iBAAiB,CAACH,OAAO,CAAC,CAAC9B,SAAS,CAAC;QAC3De,IAAI,EAAGmB,WAAW,IAAI;UACpB,IAAI,CAAC3H,OAAO,GAAG,kCAAkC;UACjD,IAAI,CAACmE,UAAU,GAAG,KAAK;UAEvB;UACAyD,UAAU,CAAC,MAAK;YACd,IAAI,CAACnD,MAAM,CAACoD,QAAQ,CAAC,CAAC,eAAe,EAAEF,WAAW,CAACtH,EAAE,CAAC,CAAC;UACzD,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACDH,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACA,KAAK,GAAG,+CAA+C;UAC5D,IAAI,CAACiE,UAAU,GAAG,KAAK;UACvBsC,OAAO,CAACvG,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACpD;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAAC4H,oBAAoB,EAAE;;EAE/B;EAEQA,oBAAoBA,CAAA;IAC1BC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjF,WAAW,CAACkF,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACnD,MAAMC,OAAO,GAAG,IAAI,CAACrF,WAAW,CAACG,GAAG,CAACiF,GAAG,CAAC;MACzCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA/G,kBAAkBA,CAAC0F,IAAc;IAC/B,OAAO,GAAG,IAAI,CAACsB,UAAU,CAACtB,IAAI,CAACG,SAAS,CAAC,MAAM,IAAI,CAACmB,UAAU,CAACtB,IAAI,CAACI,OAAO,CAAC,EAAE;EAChF;EAEAhG,gBAAgBA,CAAC4F,IAAc;IAC7B,OAAO,GAAGA,IAAI,CAACG,SAAS,IAAIH,IAAI,CAACI,OAAO,EAAE;EAC5C;EAEQkB,UAAUA,CAACC,IAAY;IAC7B,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGF,IAAI,CAACpC,KAAK,CAAC,GAAG,CAAC;IACxC,MAAMuC,IAAI,GAAGlB,QAAQ,CAACgB,KAAK,CAAC;IAC5B,MAAMG,IAAI,GAAGD,IAAI,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI;IACrC,MAAME,WAAW,GAAGF,IAAI,GAAG,EAAE,IAAI,EAAE;IACnC,OAAO,GAAGE,WAAW,IAAIH,OAAO,IAAIE,IAAI,EAAE;EAC5C;EAEAnF,YAAYA,CAAA;IACV;IACA,MAAMqC,QAAQ,GAAG,IAAIC,IAAI,EAAE;IAC3BD,QAAQ,CAACE,OAAO,CAACF,QAAQ,CAACG,OAAO,EAAE,GAAG,CAAC,CAAC;IACxC,OAAOH,QAAQ,CAACK,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC7C;;;uBAzLW/B,2BAA2B,EAAA5E,EAAA,CAAAqJ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvJ,EAAA,CAAAqJ,iBAAA,CAAAG,EAAA,CAAAC,kBAAA,GAAAzJ,EAAA,CAAAqJ,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA3J,EAAA,CAAAqJ,iBAAA,CAAAK,EAAA,CAAAE,MAAA;IAAA;EAAA;;;YAA3BhF,2BAA2B;MAAAiF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXxCnK,EAAA,CAAAC,cAAA,aAAkC;UAMtBD,EAAA,CAAAE,SAAA,WAAyC;UAAAF,EAAA,CAAAG,MAAA,2BAC3C;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAEPJ,EAAA,CAAAC,cAAA,aAAuB;UAErBD,EAAA,CAAAoB,UAAA,IAAAiJ,0CAAA,iBAGM;UAGNrK,EAAA,CAAAoB,UAAA,KAAAkJ,2CAAA,iBAGM;UAENtK,EAAA,CAAAoB,UAAA,KAAAmJ,4CAAA,qBA+JO;UACTvK,EAAA,CAAAI,YAAA,EAAM;;;UA3KEJ,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAW,UAAA,SAAAyJ,GAAA,CAAA5J,OAAA,CAAa;UAMbR,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAW,UAAA,SAAAyJ,GAAA,CAAA1J,KAAA,CAAW;UAKVV,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAW,UAAA,SAAAyJ,GAAA,CAAA7G,WAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}