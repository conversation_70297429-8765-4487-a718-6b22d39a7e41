{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Subject } from 'rxjs';\nimport { Client } from '@stomp/stompjs';\nimport SockJS from 'sockjs-client';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nimport * as i3 from \"./notification.service\";\nexport let ChatService = /*#__PURE__*/(() => {\n  class ChatService {\n    constructor(http, authService, notificationService) {\n      this.http = http;\n      this.authService = authService;\n      this.notificationService = notificationService;\n      this.apiUrl = `${environment.apiUrl}/chats`;\n      this.wsUrl = `${environment.apiUrl}/ws`;\n      this.stompClient = null;\n      this.connectionStatusSubject = new BehaviorSubject(false);\n      this.messageSubject = new Subject();\n      this.typingSubject = new Subject();\n      this.chatsSubject = new BehaviorSubject([]);\n      this.connectionStatus$ = this.connectionStatusSubject.asObservable();\n      this.messages$ = this.messageSubject.asObservable();\n      this.typing$ = this.typingSubject.asObservable();\n      this.chats$ = this.chatsSubject.asObservable();\n      this.initializeWebSocketConnection();\n    }\n    initializeWebSocketConnection() {\n      if (this.authService.isAuthenticated()) {\n        this.connect();\n      }\n      // Listen for authentication changes\n      this.authService.currentUser$.subscribe(user => {\n        if (user) {\n          this.connect();\n        } else {\n          this.disconnect();\n        }\n      });\n    }\n    connect() {\n      if (this.stompClient?.connected) {\n        return;\n      }\n      const token = this.authService.getToken();\n      if (!token) {\n        return;\n      }\n      this.stompClient = new Client({\n        webSocketFactory: () => new SockJS(this.wsUrl),\n        connectHeaders: {\n          Authorization: `Bearer ${token}`\n        },\n        debug: str => {\n          console.log('STOMP Debug:', str);\n        },\n        onConnect: () => {\n          this.connectionStatusSubject.next(true);\n          console.log('WebSocket connected successfully');\n          this.subscribeToUserChannels();\n        },\n        onWebSocketClose: () => {\n          this.connectionStatusSubject.next(false);\n          console.log('WebSocket connection closed');\n          // Try to reconnect after 5 seconds\n          setTimeout(() => {\n            if (this.authService.isAuthenticated()) {\n              this.connect();\n            }\n          }, 5000);\n        },\n        onStompError: frame => {\n          console.error('STOMP error:', frame);\n          this.connectionStatusSubject.next(false);\n        }\n      });\n      this.stompClient.activate();\n    }\n    subscribeToUserChannels() {\n      if (!this.stompClient?.connected) {\n        return;\n      }\n      const currentUser = this.authService.getCurrentUser();\n      if (!currentUser) {\n        return;\n      }\n      // Subscribe to error messages\n      this.stompClient.subscribe('/user/queue/errors', message => {\n        console.error('WebSocket error:', message.body);\n      });\n    }\n    subscribeToChatMessages(chatId) {\n      if (!this.stompClient?.connected) {\n        return;\n      }\n      // Subscribe to chat messages\n      this.stompClient.subscribe(`/topic/chat/${chatId}`, message => {\n        const newMessage = JSON.parse(message.body);\n        this.messageSubject.next(newMessage);\n        // Add notification for new messages from other users\n        const currentUser = this.authService.getCurrentUser();\n        if (newMessage.sender.id !== currentUser?.id) {\n          this.notificationService.addMessageNotification(newMessage.sender, newMessage.content, chatId);\n        }\n      });\n      // Subscribe to typing notifications\n      this.stompClient.subscribe(`/topic/chat/${chatId}/typing`, message => {\n        const typingNotification = JSON.parse(message.body);\n        this.typingSubject.next(typingNotification);\n      });\n    }\n    sendMessage(chatId, content) {\n      if (!this.stompClient?.connected) {\n        throw new Error('WebSocket not connected');\n      }\n      const token = this.authService.getToken();\n      if (!token) {\n        throw new Error('No authentication token');\n      }\n      const messageRequest = {\n        chatId,\n        content\n      };\n      this.stompClient.publish({\n        destination: `/app/chat/${chatId}/send`,\n        body: JSON.stringify(messageRequest),\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n    }\n    sendTypingNotification(chatId, isTyping) {\n      if (!this.stompClient?.connected) {\n        return;\n      }\n      const token = this.authService.getToken();\n      if (!token) {\n        return;\n      }\n      this.stompClient.publish({\n        destination: `/app/chat/${chatId}/typing`,\n        body: isTyping ? 'typing' : 'stopped',\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n    }\n    disconnect() {\n      if (this.stompClient) {\n        this.stompClient.deactivate();\n        this.connectionStatusSubject.next(false);\n      }\n    }\n    // HTTP API methods\n    createOrGetChat(participantId) {\n      const request = {\n        participantId\n      };\n      return this.http.post(this.apiUrl, request, this.getHttpOptions());\n    }\n    getUserChats() {\n      return this.http.get(this.apiUrl, this.getHttpOptions());\n    }\n    getChatMessages(chatId, page = 0, size = 50) {\n      const params = {\n        page: page.toString(),\n        size: size.toString()\n      };\n      return this.http.get(`${this.apiUrl}/${chatId}/messages`, {\n        ...this.getHttpOptions(),\n        params\n      });\n    }\n    markMessagesAsRead(chatId) {\n      return this.http.put(`${this.apiUrl}/${chatId}/read`, {}, this.getHttpOptions());\n    }\n    loadUserChats() {\n      this.getUserChats().subscribe({\n        next: chats => {\n          this.chatsSubject.next(chats);\n        },\n        error: error => {\n          console.error('Failed to load chats:', error);\n        }\n      });\n    }\n    getHttpOptions() {\n      const token = this.authService.getToken();\n      return {\n        headers: new HttpHeaders({\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        })\n      };\n    }\n    static {\n      this.ɵfac = function ChatService_Factory(t) {\n        return new (t || ChatService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService), i0.ɵɵinject(i3.NotificationService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ChatService,\n        factory: ChatService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ChatService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}