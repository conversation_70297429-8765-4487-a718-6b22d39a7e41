{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/chat.service\";\nimport * as i2 from \"../../../core/services/appointment.service\";\nimport * as i3 from \"../../../core/services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../chat-access/chat-access.component\";\nfunction QuickChatWidgetComponent_div_10_div_5_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", chat_r4.lastMessage.content.length > 30 ? chat_r4.lastMessage.content.substring(0, 30) + \"...\" : chat_r4.lastMessage.content, \" \");\n  }\n}\nfunction QuickChatWidgetComponent_div_10_div_5_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 30);\n    i0.ɵɵtext(1, \"No messages yet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QuickChatWidgetComponent_div_10_div_5_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", chat_r4.unreadCount, \" \");\n  }\n}\nfunction QuickChatWidgetComponent_div_10_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function QuickChatWidgetComponent_div_10_div_5_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const chat_r4 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.navigateToChat(chat_r4.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 23);\n    i0.ɵɵelement(2, \"img\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"div\", 26);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 27);\n    i0.ɵɵtemplate(7, QuickChatWidgetComponent_div_10_div_5_span_7_Template, 2, 1, \"span\", 28);\n    i0.ɵɵtemplate(8, QuickChatWidgetComponent_div_10_div_5_span_8_Template, 2, 0, \"span\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 29)(10, \"small\", 30);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, QuickChatWidgetComponent_div_10_div_5_div_13_Template, 2, 1, \"div\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const chat_r4 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ((tmp_0_0 = ctx_r3.getOtherParticipant(chat_r4)) == null ? null : tmp_0_0.avatar) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", (tmp_1_0 = ctx_r3.getOtherParticipant(chat_r4)) == null ? null : tmp_1_0.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((tmp_2_0 = ctx_r3.getOtherParticipant(chat_r4)) == null ? null : tmp_2_0.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", chat_r4.lastMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !chat_r4.lastMessage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(12, 7, chat_r4.updatedAt, \"short\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", chat_r4.unreadCount > 0);\n  }\n}\nfunction QuickChatWidgetComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"h6\", 18);\n    i0.ɵɵelement(2, \"i\", 19);\n    i0.ɵɵtext(3, \"Recent Conversations \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 20);\n    i0.ɵɵtemplate(5, QuickChatWidgetComponent_div_10_div_5_Template, 14, 10, \"div\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.recentChats);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    appointmentId: a0,\n    doctorId: a1,\n    chatType: \"PRE_APPOINTMENT\",\n    buttonText: \"Pre-Chat\",\n    buttonClass: \"btn-outline-info\",\n    size: \"sm\",\n    showIcon: false\n  };\n};\nconst _c1 = function (a0) {\n  return {\n    doctorId: a0,\n    chatType: \"GENERAL\",\n    buttonText: \"General\",\n    buttonClass: \"btn-outline-primary\",\n    size: \"sm\",\n    showIcon: false\n  };\n};\nfunction QuickChatWidgetComponent_div_11_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37)(2, \"div\", 38)(3, \"strong\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 39)(6, \"small\", 30);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 40)(10, \"span\", 41);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 42)(13, \"div\", 43);\n    i0.ɵɵelement(14, \"app-chat-access\", 44)(15, \"app-chat-access\", 44);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const appointment_r13 = ctx.$implicit;\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(appointment_r13.doctor.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind2(8, 6, appointment_r13.date, \"mediumDate\"), \" at \", appointment_r13.startTime, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" In \", ctx_r12.getTimeUntilAppointment(appointment_r13), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"config\", i0.ɵɵpureFunction2(9, _c0, appointment_r13.id, appointment_r13.doctor.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"config\", i0.ɵɵpureFunction1(12, _c1, appointment_r13.doctor.id));\n  }\n}\nfunction QuickChatWidgetComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h6\", 18);\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵtext(3, \"Upcoming Appointments \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 34);\n    i0.ɵɵtemplate(5, QuickChatWidgetComponent_div_11_div_5_Template, 16, 14, \"div\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.upcomingAppointments);\n  }\n}\nfunction QuickChatWidgetComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46);\n    i0.ɵɵelement(2, \"i\", 47);\n    i0.ɵɵelementStart(3, \"h6\", 30);\n    i0.ɵɵtext(4, \"No recent conversations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 18);\n    i0.ɵɵtext(6, \"Start chatting with your healthcare providers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 48);\n    i0.ɵɵelement(8, \"i\", 49);\n    i0.ɵɵtext(9, \" Book Appointment \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nconst _c2 = function () {\n  return {\n    chatType: \"URGENT\",\n    buttonText: \"Urgent\",\n    buttonClass: \"btn-outline-danger\",\n    size: \"sm\"\n  };\n};\nexport class QuickChatWidgetComponent {\n  constructor(chatService, appointmentService, authService, router) {\n    this.chatService = chatService;\n    this.appointmentService = appointmentService;\n    this.authService = authService;\n    this.router = router;\n    this.recentChats = [];\n    this.upcomingAppointments = [];\n    this.loading = false;\n  }\n  ngOnInit() {\n    this.currentUser = this.authService.getCurrentUser();\n    this.loadRecentChats();\n    this.loadUpcomingAppointments();\n  }\n  loadRecentChats() {\n    this.chatService.getUserChats().subscribe({\n      next: chats => {\n        this.recentChats = chats.slice(0, 3); // Show only 3 most recent\n      },\n\n      error: error => {\n        console.error('Error loading recent chats:', error);\n      }\n    });\n  }\n  loadUpcomingAppointments() {\n    if (this.currentUser?.role === 'PATIENT') {\n      this.appointmentService.getPatientAppointments().subscribe({\n        next: appointments => {\n          const now = new Date();\n          this.upcomingAppointments = appointments.filter(apt => new Date(`${apt.date}T${apt.startTime}`) > now).slice(0, 3); // Show only 3 upcoming\n        },\n\n        error: error => {\n          console.error('Error loading appointments:', error);\n        }\n      });\n    }\n  }\n  navigateToChat(chatId) {\n    if (chatId) {\n      this.router.navigate(['/chat'], {\n        queryParams: {\n          chatId\n        }\n      });\n    } else {\n      this.router.navigate(['/chat']);\n    }\n  }\n  startAppointmentChat(appointment, chatType) {\n    const participantId = this.currentUser.role === 'PATIENT' ? appointment.doctor.id : appointment.patient.id;\n    this.appointmentService.createAppointmentChat(appointment.id, participantId, chatType, `${chatType.replace('_', ' ')} discussion for appointment on ${appointment.date}`).subscribe({\n      next: chat => {\n        this.router.navigate(['/chat'], {\n          queryParams: {\n            chatId: chat.id,\n            appointmentId: appointment.id\n          }\n        });\n      },\n      error: error => {\n        console.error('Error creating appointment chat:', error);\n      }\n    });\n  }\n  isBeforeAppointment(appointment) {\n    const appointmentDateTime = new Date(`${appointment.date}T${appointment.startTime}`);\n    return appointmentDateTime > new Date();\n  }\n  getTimeUntilAppointment(appointment) {\n    const appointmentDateTime = new Date(`${appointment.date}T${appointment.startTime}`);\n    const now = new Date();\n    const diffMs = appointmentDateTime.getTime() - now.getTime();\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    const diffDays = Math.floor(diffHours / 24);\n    if (diffDays > 0) {\n      return `${diffDays} day${diffDays > 1 ? 's' : ''}`;\n    } else if (diffHours > 0) {\n      return `${diffHours} hour${diffHours > 1 ? 's' : ''}`;\n    } else {\n      return 'Soon';\n    }\n  }\n  getOtherParticipant(chat) {\n    return this.currentUser.role === 'PATIENT' ? chat.doctor : chat.patient;\n  }\n  static {\n    this.ɵfac = function QuickChatWidgetComponent_Factory(t) {\n      return new (t || QuickChatWidgetComponent)(i0.ɵɵdirectiveInject(i1.ChatService), i0.ɵɵdirectiveInject(i2.AppointmentService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: QuickChatWidgetComponent,\n      selectors: [[\"app-quick-chat-widget\"]],\n      decls: 21,\n      vars: 5,\n      consts: [[1, \"quick-chat-widget\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"fas\", \"fa-comments\", \"text-primary\", \"me-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"fas\", \"fa-external-link-alt\", \"me-1\"], [1, \"card-body\"], [\"class\", \"mb-4\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"no-data\", 4, \"ngIf\"], [1, \"quick-actions\", \"mt-3\", \"pt-3\", \"border-top\"], [1, \"row\", \"g-2\"], [1, \"col-6\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"w-100\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"w-100\", 3, \"config\"], [1, \"mb-4\"], [1, \"text-muted\", \"mb-3\"], [1, \"fas\", \"fa-clock\", \"me-2\"], [1, \"chat-list\"], [\"class\", \"chat-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"chat-item\", 3, \"click\"], [1, \"chat-avatar\"], [1, \"rounded-circle\", 3, \"src\", \"alt\"], [1, \"chat-info\"], [1, \"chat-name\"], [1, \"chat-preview\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"chat-meta\"], [1, \"text-muted\"], [\"class\", \"badge bg-primary\", 4, \"ngIf\"], [1, \"badge\", \"bg-primary\"], [1, \"fas\", \"fa-calendar-alt\", \"me-2\"], [1, \"appointment-list\"], [\"class\", \"appointment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"appointment-item\"], [1, \"appointment-info\"], [1, \"appointment-doctor\"], [1, \"appointment-details\"], [1, \"appointment-time-left\"], [1, \"badge\", \"bg-info\"], [1, \"appointment-actions\"], [\"role\", \"group\", 1, \"btn-group-vertical\"], [3, \"config\"], [1, \"no-data\"], [1, \"text-center\", \"py-4\"], [1, \"fas\", \"fa-comments\", \"fa-3x\", \"text-muted\", \"mb-3\"], [\"type\", \"button\", \"routerLink\", \"/appointments/book\", 1, \"btn\", \"btn-primary\", \"btn-sm\"], [1, \"fas\", \"fa-calendar-plus\", \"me-2\"]],\n      template: function QuickChatWidgetComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h6\", 3);\n          i0.ɵɵelement(4, \"i\", 4);\n          i0.ɵɵtext(5, \" Quick Chat Access \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function QuickChatWidgetComponent_Template_button_click_6_listener() {\n            return ctx.navigateToChat();\n          });\n          i0.ɵɵelement(7, \"i\", 6);\n          i0.ɵɵtext(8, \" View All \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 7);\n          i0.ɵɵtemplate(10, QuickChatWidgetComponent_div_10_Template, 6, 1, \"div\", 8);\n          i0.ɵɵtemplate(11, QuickChatWidgetComponent_div_11_Template, 6, 1, \"div\", 9);\n          i0.ɵɵtemplate(12, QuickChatWidgetComponent_div_12_Template, 10, 0, \"div\", 10);\n          i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 12)(15, \"div\", 13)(16, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function QuickChatWidgetComponent_Template_button_click_16_listener() {\n            return ctx.navigateToChat();\n          });\n          i0.ɵɵelement(17, \"i\", 15);\n          i0.ɵɵtext(18, \" New Chat \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 13);\n          i0.ɵɵelement(20, \"app-chat-access\", 16);\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.recentChats.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.upcomingAppointments.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.recentChats.length === 0 && ctx.upcomingAppointments.length === 0);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"config\", i0.ɵɵpureFunction0(4, _c2));\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i4.RouterLink, i6.ChatAccessComponent, i5.DatePipe],\n      styles: [\".quick-chat-widget[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n  border: none;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  border-radius: 12px;\\n  overflow: hidden;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  color: white;\\n  border-bottom: none;\\n  padding: 1rem 1.25rem;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%] {\\n  border-color: rgba(255, 255, 255, 0.5);\\n  color: white;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  border-color: white;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n  padding: 1.25rem;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.75rem;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  margin-bottom: 0.5rem;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  transform: translateX(2px);\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  margin-right: 0.75rem;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  object-fit: cover;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-info[_ngcontent-%COMP%]   .chat-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #212529;\\n  margin-bottom: 0.25rem;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-info[_ngcontent-%COMP%]   .chat-preview[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #6c757d;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-meta[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  text-align: right;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-meta[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  margin-top: 0.25rem;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 1rem;\\n  border: 1px solid #e9ecef;\\n  border-radius: 8px;\\n  margin-bottom: 0.75rem;\\n  background: #f8f9fa;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-info[_ngcontent-%COMP%]   .appointment-doctor[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-info[_ngcontent-%COMP%]   .appointment-details[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-info[_ngcontent-%COMP%]   .appointment-time-left[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  margin-left: 1rem;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem 1rem;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  padding: 0.5rem 0.75rem;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   h6.text-muted[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 1rem;\\n}\\n.quick-chat-widget[_ngcontent-%COMP%]   h6.text-muted[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n}\\n\\n@media (max-width: 768px) {\\n  .quick-chat-widget[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n  .quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n  }\\n  .quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n    margin-top: 0.75rem;\\n    width: 100%;\\n  }\\n  .quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    width: 100%;\\n  }\\n  .quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    flex: 1;\\n    margin-bottom: 0;\\n    margin-right: 0.25rem;\\n  }\\n  .quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child {\\n    margin-right: 0;\\n  }\\n}\\n.quick-chat-widget[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInUp 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "chat_r4", "lastMessage", "content", "length", "substring", "unreadCount", "ɵɵlistener", "QuickChatWidgetComponent_div_10_div_5_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r11", "$implicit", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "navigateToChat", "id", "ɵɵelement", "ɵɵtemplate", "QuickChatWidgetComponent_div_10_div_5_span_7_Template", "QuickChatWidgetComponent_div_10_div_5_span_8_Template", "QuickChatWidgetComponent_div_10_div_5_div_13_Template", "ɵɵproperty", "tmp_0_0", "ctx_r3", "getOtherParticipant", "avatar", "ɵɵsanitizeUrl", "tmp_1_0", "fullName", "ɵɵtextInterpolate", "tmp_2_0", "ɵɵpipeBind2", "updatedAt", "QuickChatWidgetComponent_div_10_div_5_Template", "ctx_r0", "recentChats", "appointment_r13", "doctor", "ɵɵtextInterpolate2", "date", "startTime", "ctx_r12", "getTimeUntilAppointment", "ɵɵpureFunction2", "_c0", "ɵɵpureFunction1", "_c1", "QuickChatWidgetComponent_div_11_div_5_Template", "ctx_r1", "upcomingAppointments", "QuickChatWidgetComponent", "constructor", "chatService", "appointmentService", "authService", "router", "loading", "ngOnInit", "currentUser", "getCurrentUser", "loadRecentChats", "loadUpcomingAppointments", "getUserChats", "subscribe", "next", "chats", "slice", "error", "console", "role", "getPatientAppointments", "appointments", "now", "Date", "filter", "apt", "chatId", "navigate", "queryParams", "startAppointmentChat", "appointment", "chatType", "participantId", "patient", "createAppointmentChat", "replace", "chat", "appointmentId", "isBeforeAppointment", "appointmentDateTime", "diffMs", "getTime", "diffHours", "Math", "floor", "diffDays", "ɵɵdirectiveInject", "i1", "ChatService", "i2", "AppointmentService", "i3", "AuthService", "i4", "Router", "selectors", "decls", "vars", "consts", "template", "QuickChatWidgetComponent_Template", "rf", "ctx", "QuickChatWidgetComponent_Template_button_click_6_listener", "QuickChatWidgetComponent_div_10_Template", "QuickChatWidgetComponent_div_11_Template", "QuickChatWidgetComponent_div_12_Template", "QuickChatWidgetComponent_Template_button_click_16_listener", "ɵɵpureFunction0", "_c2"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/shared/components/quick-chat-widget/quick-chat-widget.component.ts", "/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/shared/components/quick-chat-widget/quick-chat-widget.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { ChatService } from '../../../core/services/chat.service';\nimport { AppointmentService } from '../../../core/services/appointment.service';\nimport { AuthService } from '../../../core/services/auth.service';\n\n@Component({\n  selector: 'app-quick-chat-widget',\n  templateUrl: './quick-chat-widget.component.html',\n  styleUrls: ['./quick-chat-widget.component.scss']\n})\nexport class QuickChatWidgetComponent implements OnInit {\n  recentChats: any[] = [];\n  upcomingAppointments: any[] = [];\n  loading = false;\n  currentUser: any;\n\n  constructor(\n    private chatService: ChatService,\n    private appointmentService: AppointmentService,\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.currentUser = this.authService.getCurrentUser();\n    this.loadRecentChats();\n    this.loadUpcomingAppointments();\n  }\n\n  private loadRecentChats(): void {\n    this.chatService.getUserChats().subscribe({\n      next: (chats) => {\n        this.recentChats = chats.slice(0, 3); // Show only 3 most recent\n      },\n      error: (error) => {\n        console.error('Error loading recent chats:', error);\n      }\n    });\n  }\n\n  private loadUpcomingAppointments(): void {\n    if (this.currentUser?.role === 'PATIENT') {\n      this.appointmentService.getPatientAppointments().subscribe({\n        next: (appointments) => {\n          const now = new Date();\n          this.upcomingAppointments = appointments\n            .filter(apt => new Date(`${apt.date}T${apt.startTime}`) > now)\n            .slice(0, 3); // Show only 3 upcoming\n        },\n        error: (error) => {\n          console.error('Error loading appointments:', error);\n        }\n      });\n    }\n  }\n\n  navigateToChat(chatId?: number): void {\n    if (chatId) {\n      this.router.navigate(['/chat'], { queryParams: { chatId } });\n    } else {\n      this.router.navigate(['/chat']);\n    }\n  }\n\n  startAppointmentChat(appointment: any, chatType: string): void {\n    const participantId = this.currentUser.role === 'PATIENT' \n      ? appointment.doctor.id \n      : appointment.patient.id;\n\n    this.appointmentService.createAppointmentChat(\n      appointment.id,\n      participantId,\n      chatType,\n      `${chatType.replace('_', ' ')} discussion for appointment on ${appointment.date}`\n    ).subscribe({\n      next: (chat) => {\n        this.router.navigate(['/chat'], { \n          queryParams: { \n            chatId: chat.id,\n            appointmentId: appointment.id \n          } \n        });\n      },\n      error: (error) => {\n        console.error('Error creating appointment chat:', error);\n      }\n    });\n  }\n\n  isBeforeAppointment(appointment: any): boolean {\n    const appointmentDateTime = new Date(`${appointment.date}T${appointment.startTime}`);\n    return appointmentDateTime > new Date();\n  }\n\n  getTimeUntilAppointment(appointment: any): string {\n    const appointmentDateTime = new Date(`${appointment.date}T${appointment.startTime}`);\n    const now = new Date();\n    const diffMs = appointmentDateTime.getTime() - now.getTime();\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    const diffDays = Math.floor(diffHours / 24);\n\n    if (diffDays > 0) {\n      return `${diffDays} day${diffDays > 1 ? 's' : ''}`;\n    } else if (diffHours > 0) {\n      return `${diffHours} hour${diffHours > 1 ? 's' : ''}`;\n    } else {\n      return 'Soon';\n    }\n  }\n\n  getOtherParticipant(chat: any): any {\n    return this.currentUser.role === 'PATIENT' ? chat.doctor : chat.patient;\n  }\n}\n", "<div class=\"quick-chat-widget\">\n  <div class=\"card\">\n    <div class=\"card-header d-flex justify-content-between align-items-center\">\n      <h6 class=\"mb-0\">\n        <i class=\"fas fa-comments text-primary me-2\"></i>\n        Quick Chat Access\n      </h6>\n      <button \n        type=\"button\" \n        class=\"btn btn-sm btn-outline-primary\"\n        (click)=\"navigateToChat()\">\n        <i class=\"fas fa-external-link-alt me-1\"></i>\n        View All\n      </button>\n    </div>\n\n    <div class=\"card-body\">\n      <!-- Recent Chats Section -->\n      <div *ngIf=\"recentChats.length > 0\" class=\"mb-4\">\n        <h6 class=\"text-muted mb-3\">\n          <i class=\"fas fa-clock me-2\"></i>Recent Conversations\n        </h6>\n        \n        <div class=\"chat-list\">\n          <div \n            *ngFor=\"let chat of recentChats\"\n            class=\"chat-item\"\n            (click)=\"navigateToChat(chat.id)\">\n            \n            <div class=\"chat-avatar\">\n              <img \n                [src]=\"getOtherParticipant(chat)?.avatar || '/assets/images/default-avatar.png'\"\n                [alt]=\"getOtherParticipant(chat)?.fullName\"\n                class=\"rounded-circle\">\n            </div>\n            \n            <div class=\"chat-info\">\n              <div class=\"chat-name\">{{ getOtherParticipant(chat)?.fullName }}</div>\n              <div class=\"chat-preview\">\n                <span *ngIf=\"chat.lastMessage\" class=\"text-muted\">\n                  {{ chat.lastMessage.content.length > 30 ? \n                      chat.lastMessage.content.substring(0, 30) + '...' : \n                      chat.lastMessage.content }}\n                </span>\n                <span *ngIf=\"!chat.lastMessage\" class=\"text-muted\">No messages yet</span>\n              </div>\n            </div>\n            \n            <div class=\"chat-meta\">\n              <small class=\"text-muted\">\n                {{ chat.updatedAt | date:'short' }}\n              </small>\n              <div *ngIf=\"chat.unreadCount > 0\" class=\"badge bg-primary\">\n                {{ chat.unreadCount }}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Upcoming Appointments Section -->\n      <div *ngIf=\"upcomingAppointments.length > 0\">\n        <h6 class=\"text-muted mb-3\">\n          <i class=\"fas fa-calendar-alt me-2\"></i>Upcoming Appointments\n        </h6>\n        \n        <div class=\"appointment-list\">\n          <div \n            *ngFor=\"let appointment of upcomingAppointments\"\n            class=\"appointment-item\">\n            \n            <div class=\"appointment-info\">\n              <div class=\"appointment-doctor\">\n                <strong>{{ appointment.doctor.fullName }}</strong>\n              </div>\n              <div class=\"appointment-details\">\n                <small class=\"text-muted\">\n                  {{ appointment.date | date:'mediumDate' }} at {{ appointment.startTime }}\n                </small>\n              </div>\n              <div class=\"appointment-time-left\">\n                <span class=\"badge bg-info\">\n                  In {{ getTimeUntilAppointment(appointment) }}\n                </span>\n              </div>\n            </div>\n            \n            <div class=\"appointment-actions\">\n              <div class=\"btn-group-vertical\" role=\"group\">\n                <app-chat-access \n                  [config]=\"{\n                    appointmentId: appointment.id,\n                    doctorId: appointment.doctor.id,\n                    chatType: 'PRE_APPOINTMENT',\n                    buttonText: 'Pre-Chat',\n                    buttonClass: 'btn-outline-info',\n                    size: 'sm',\n                    showIcon: false\n                  }\">\n                </app-chat-access>\n                \n                <app-chat-access \n                  [config]=\"{\n                    doctorId: appointment.doctor.id,\n                    chatType: 'GENERAL',\n                    buttonText: 'General',\n                    buttonClass: 'btn-outline-primary',\n                    size: 'sm',\n                    showIcon: false\n                  }\">\n                </app-chat-access>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- No Data States -->\n      <div *ngIf=\"recentChats.length === 0 && upcomingAppointments.length === 0\" class=\"no-data\">\n        <div class=\"text-center py-4\">\n          <i class=\"fas fa-comments fa-3x text-muted mb-3\"></i>\n          <h6 class=\"text-muted\">No recent conversations</h6>\n          <p class=\"text-muted mb-3\">Start chatting with your healthcare providers</p>\n          <button \n            type=\"button\" \n            class=\"btn btn-primary btn-sm\"\n            routerLink=\"/appointments/book\">\n            <i class=\"fas fa-calendar-plus me-2\"></i>\n            Book Appointment\n          </button>\n        </div>\n      </div>\n\n      <!-- Quick Actions -->\n      <div class=\"quick-actions mt-3 pt-3 border-top\">\n        <div class=\"row g-2\">\n          <div class=\"col-6\">\n            <button \n              type=\"button\" \n              class=\"btn btn-outline-success btn-sm w-100\"\n              (click)=\"navigateToChat()\">\n              <i class=\"fas fa-plus me-1\"></i>\n              New Chat\n            </button>\n          </div>\n          <div class=\"col-6\">\n            <app-chat-access \n              [config]=\"{\n                chatType: 'URGENT',\n                buttonText: 'Urgent',\n                buttonClass: 'btn-outline-danger',\n                size: 'sm'\n              }\"\n              class=\"w-100\">\n            </app-chat-access>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;;ICuCgBA,EAAA,CAAAC,cAAA,eAAkD;IAChDD,EAAA,CAAAE,MAAA,GAGF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAHLH,EAAA,CAAAI,SAAA,GAGF;IAHEJ,EAAA,CAAAK,kBAAA,MAAAC,OAAA,CAAAC,WAAA,CAAAC,OAAA,CAAAC,MAAA,QAAAH,OAAA,CAAAC,WAAA,CAAAC,OAAA,CAAAE,SAAA,kBAAAJ,OAAA,CAAAC,WAAA,CAAAC,OAAA,MAGF;;;;;IACAR,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAQ3EH,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,OAAA,CAAAK,WAAA,MACF;;;;;;IA9BJX,EAAA,CAAAC,cAAA,cAGoC;IAAlCD,EAAA,CAAAY,UAAA,mBAAAC,oEAAA;MAAA,MAAAC,WAAA,GAAAd,EAAA,CAAAe,aAAA,CAAAC,IAAA;MAAA,MAAAV,OAAA,GAAAQ,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAASnB,EAAA,CAAAoB,WAAA,CAAAF,OAAA,CAAAG,cAAA,CAAAf,OAAA,CAAAgB,EAAA,CAAuB;IAAA,EAAC;IAEjCtB,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAuB,SAAA,cAGyB;IAC3BvB,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,cAAuB;IACED,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACtEH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAwB,UAAA,IAAAC,qDAAA,mBAIO;IACPzB,EAAA,CAAAwB,UAAA,IAAAE,qDAAA,mBAAyE;IAC3E1B,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,cAAuB;IAEnBD,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAwB,UAAA,KAAAG,qDAAA,kBAEM;IACR3B,EAAA,CAAAG,YAAA,EAAM;;;;;;;;IAxBFH,EAAA,CAAAI,SAAA,GAAgF;IAAhFJ,EAAA,CAAA4B,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,mBAAA,CAAAzB,OAAA,oBAAAuB,OAAA,CAAAG,MAAA,0CAAAhC,EAAA,CAAAiC,aAAA,CAAgF,SAAAC,OAAA,GAAAJ,MAAA,CAAAC,mBAAA,CAAAzB,OAAA,oBAAA4B,OAAA,CAAAC,QAAA;IAM3DnC,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAoC,iBAAA,EAAAC,OAAA,GAAAP,MAAA,CAAAC,mBAAA,CAAAzB,OAAA,oBAAA+B,OAAA,CAAAF,QAAA,CAAyC;IAEvDnC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAA4B,UAAA,SAAAtB,OAAA,CAAAC,WAAA,CAAsB;IAKtBP,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAA4B,UAAA,UAAAtB,OAAA,CAAAC,WAAA,CAAuB;IAM9BP,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAsC,WAAA,QAAAhC,OAAA,CAAAiC,SAAA,gBACF;IACMvC,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAA4B,UAAA,SAAAtB,OAAA,CAAAK,WAAA,KAA0B;;;;;IAlCxCX,EAAA,CAAAC,cAAA,cAAiD;IAE7CD,EAAA,CAAAuB,SAAA,YAAiC;IAAAvB,EAAA,CAAAE,MAAA,4BACnC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAwB,UAAA,IAAAgB,8CAAA,oBAgCM;IACRxC,EAAA,CAAAG,YAAA,EAAM;;;;IAhCeH,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAA4B,UAAA,YAAAa,MAAA,CAAAC,WAAA,CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;IA0CjC1C,EAAA,CAAAC,cAAA,cAE2B;IAIbD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEpDH,EAAA,CAAAC,cAAA,cAAiC;IAE7BD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEVH,EAAA,CAAAC,cAAA,cAAmC;IAE/BD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIXH,EAAA,CAAAC,cAAA,eAAiC;IAE7BD,EAAA,CAAAuB,SAAA,2BAUkB;IAYpBvB,EAAA,CAAAG,YAAA,EAAM;;;;;IAtCIH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAoC,iBAAA,CAAAO,eAAA,CAAAC,MAAA,CAAAT,QAAA,CAAiC;IAIvCnC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA6C,kBAAA,MAAA7C,EAAA,CAAAsC,WAAA,OAAAK,eAAA,CAAAG,IAAA,yBAAAH,eAAA,CAAAI,SAAA,MACF;IAIE/C,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,SAAA2C,OAAA,CAAAC,uBAAA,CAAAN,eAAA,OACF;IAOE3C,EAAA,CAAAI,SAAA,GAQE;IARFJ,EAAA,CAAA4B,UAAA,WAAA5B,EAAA,CAAAkD,eAAA,IAAAC,GAAA,EAAAR,eAAA,CAAArB,EAAA,EAAAqB,eAAA,CAAAC,MAAA,CAAAtB,EAAA,EAQE;IAIFtB,EAAA,CAAAI,SAAA,GAOE;IAPFJ,EAAA,CAAA4B,UAAA,WAAA5B,EAAA,CAAAoD,eAAA,KAAAC,GAAA,EAAAV,eAAA,CAAAC,MAAA,CAAAtB,EAAA,EAOE;;;;;IAhDdtB,EAAA,CAAAC,cAAA,UAA6C;IAEzCD,EAAA,CAAAuB,SAAA,YAAwC;IAAAvB,EAAA,CAAAE,MAAA,6BAC1C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAwB,UAAA,IAAA8B,8CAAA,oBA8CM;IACRtD,EAAA,CAAAG,YAAA,EAAM;;;;IA9CsBH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAA4B,UAAA,YAAA2B,MAAA,CAAAC,oBAAA,CAAuB;;;;;IAkDrDxD,EAAA,CAAAC,cAAA,cAA2F;IAEvFD,EAAA,CAAAuB,SAAA,YAAqD;IACrDvB,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnDH,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAE,MAAA,oDAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5EH,EAAA,CAAAC,cAAA,iBAGkC;IAChCD,EAAA,CAAAuB,SAAA,YAAyC;IACzCvB,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;;ADtHnB,OAAM,MAAOsD,wBAAwB;EAMnCC,YACUC,WAAwB,EACxBC,kBAAsC,EACtCC,WAAwB,EACxBC,MAAc;IAHd,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAThB,KAAApB,WAAW,GAAU,EAAE;IACvB,KAAAc,oBAAoB,GAAU,EAAE;IAChC,KAAAO,OAAO,GAAG,KAAK;EAQZ;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,GAAG,IAAI,CAACJ,WAAW,CAACK,cAAc,EAAE;IACpD,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,wBAAwB,EAAE;EACjC;EAEQD,eAAeA,CAAA;IACrB,IAAI,CAACR,WAAW,CAACU,YAAY,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,KAAK,IAAI;QACd,IAAI,CAAC9B,WAAW,GAAG8B,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC;;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;KACD,CAAC;EACJ;EAEQN,wBAAwBA,CAAA;IAC9B,IAAI,IAAI,CAACH,WAAW,EAAEW,IAAI,KAAK,SAAS,EAAE;MACxC,IAAI,CAAChB,kBAAkB,CAACiB,sBAAsB,EAAE,CAACP,SAAS,CAAC;QACzDC,IAAI,EAAGO,YAAY,IAAI;UACrB,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;UACtB,IAAI,CAACxB,oBAAoB,GAAGsB,YAAY,CACrCG,MAAM,CAACC,GAAG,IAAI,IAAIF,IAAI,CAAC,GAAGE,GAAG,CAACpC,IAAI,IAAIoC,GAAG,CAACnC,SAAS,EAAE,CAAC,GAAGgC,GAAG,CAAC,CAC7DN,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;;QACDC,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;OACD,CAAC;;EAEN;EAEArD,cAAcA,CAAC8D,MAAe;IAC5B,IAAIA,MAAM,EAAE;MACV,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;QAAEC,WAAW,EAAE;UAAEF;QAAM;MAAE,CAAE,CAAC;KAC7D,MAAM;MACL,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;;EAEnC;EAEAE,oBAAoBA,CAACC,WAAgB,EAAEC,QAAgB;IACrD,MAAMC,aAAa,GAAG,IAAI,CAACxB,WAAW,CAACW,IAAI,KAAK,SAAS,GACrDW,WAAW,CAAC3C,MAAM,CAACtB,EAAE,GACrBiE,WAAW,CAACG,OAAO,CAACpE,EAAE;IAE1B,IAAI,CAACsC,kBAAkB,CAAC+B,qBAAqB,CAC3CJ,WAAW,CAACjE,EAAE,EACdmE,aAAa,EACbD,QAAQ,EACR,GAAGA,QAAQ,CAACI,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,kCAAkCL,WAAW,CAACzC,IAAI,EAAE,CAClF,CAACwB,SAAS,CAAC;MACVC,IAAI,EAAGsB,IAAI,IAAI;QACb,IAAI,CAAC/B,MAAM,CAACsB,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;UAC9BC,WAAW,EAAE;YACXF,MAAM,EAAEU,IAAI,CAACvE,EAAE;YACfwE,aAAa,EAAEP,WAAW,CAACjE;;SAE9B,CAAC;MACJ,CAAC;MACDoD,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;KACD,CAAC;EACJ;EAEAqB,mBAAmBA,CAACR,WAAgB;IAClC,MAAMS,mBAAmB,GAAG,IAAIhB,IAAI,CAAC,GAAGO,WAAW,CAACzC,IAAI,IAAIyC,WAAW,CAACxC,SAAS,EAAE,CAAC;IACpF,OAAOiD,mBAAmB,GAAG,IAAIhB,IAAI,EAAE;EACzC;EAEA/B,uBAAuBA,CAACsC,WAAgB;IACtC,MAAMS,mBAAmB,GAAG,IAAIhB,IAAI,CAAC,GAAGO,WAAW,CAACzC,IAAI,IAAIyC,WAAW,CAACxC,SAAS,EAAE,CAAC;IACpF,MAAMgC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMiB,MAAM,GAAGD,mBAAmB,CAACE,OAAO,EAAE,GAAGnB,GAAG,CAACmB,OAAO,EAAE;IAC5D,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACvD,MAAMK,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAACF,SAAS,GAAG,EAAE,CAAC;IAE3C,IAAIG,QAAQ,GAAG,CAAC,EAAE;MAChB,OAAO,GAAGA,QAAQ,OAAOA,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;KACnD,MAAM,IAAIH,SAAS,GAAG,CAAC,EAAE;MACxB,OAAO,GAAGA,SAAS,QAAQA,SAAS,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;KACtD,MAAM;MACL,OAAO,MAAM;;EAEjB;EAEApE,mBAAmBA,CAAC8D,IAAS;IAC3B,OAAO,IAAI,CAAC5B,WAAW,CAACW,IAAI,KAAK,SAAS,GAAGiB,IAAI,CAACjD,MAAM,GAAGiD,IAAI,CAACH,OAAO;EACzE;;;uBAtGWjC,wBAAwB,EAAAzD,EAAA,CAAAuG,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzG,EAAA,CAAAuG,iBAAA,CAAAG,EAAA,CAAAC,kBAAA,GAAA3G,EAAA,CAAAuG,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA7G,EAAA,CAAAuG,iBAAA,CAAAO,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAxBtD,wBAAwB;MAAAuD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXrCtH,EAAA,CAAAC,cAAA,aAA+B;UAIvBD,EAAA,CAAAuB,SAAA,WAAiD;UACjDvB,EAAA,CAAAE,MAAA,0BACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,gBAG6B;UAA3BD,EAAA,CAAAY,UAAA,mBAAA4G,0DAAA;YAAA,OAASD,GAAA,CAAAlG,cAAA,EAAgB;UAAA,EAAC;UAC1BrB,EAAA,CAAAuB,SAAA,WAA6C;UAC7CvB,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGXH,EAAA,CAAAC,cAAA,aAAuB;UAErBD,EAAA,CAAAwB,UAAA,KAAAiG,wCAAA,iBAwCM;UAGNzH,EAAA,CAAAwB,UAAA,KAAAkG,wCAAA,iBAsDM;UAGN1H,EAAA,CAAAwB,UAAA,KAAAmG,wCAAA,mBAaM;UAGN3H,EAAA,CAAAC,cAAA,eAAgD;UAMxCD,EAAA,CAAAY,UAAA,mBAAAgH,2DAAA;YAAA,OAASL,GAAA,CAAAlG,cAAA,EAAgB;UAAA,EAAC;UAC1BrB,EAAA,CAAAuB,SAAA,aAAgC;UAChCvB,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAC,cAAA,eAAmB;UACjBD,EAAA,CAAAuB,SAAA,2BAQkB;UACpBvB,EAAA,CAAAG,YAAA,EAAM;;;UAzIJH,EAAA,CAAAI,SAAA,IAA4B;UAA5BJ,EAAA,CAAA4B,UAAA,SAAA2F,GAAA,CAAA7E,WAAA,CAAAjC,MAAA,KAA4B;UA2C5BT,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAA4B,UAAA,SAAA2F,GAAA,CAAA/D,oBAAA,CAAA/C,MAAA,KAAqC;UAyDrCT,EAAA,CAAAI,SAAA,GAAmE;UAAnEJ,EAAA,CAAA4B,UAAA,SAAA2F,GAAA,CAAA7E,WAAA,CAAAjC,MAAA,UAAA8G,GAAA,CAAA/D,oBAAA,CAAA/C,MAAA,OAAmE;UA6BjET,EAAA,CAAAI,SAAA,GAKE;UALFJ,EAAA,CAAA4B,UAAA,WAAA5B,EAAA,CAAA6H,eAAA,IAAAC,GAAA,EAKE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}