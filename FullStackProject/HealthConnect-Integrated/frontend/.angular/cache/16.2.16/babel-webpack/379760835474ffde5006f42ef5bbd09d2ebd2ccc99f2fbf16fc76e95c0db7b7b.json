{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SharedModule } from '../shared/shared.module';\nimport { ProfileComponent } from './profile.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ProfileComponent\n}];\nexport class ProfileModule {\n  static {\n    this.ɵfac = function ProfileModule_Factory(t) {\n      return new (t || ProfileModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProfileModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, RouterModule.forChild(routes)]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProfileModule, {\n    declarations: [ProfileComponent],\n    imports: [SharedModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SharedModule", "ProfileComponent", "routes", "path", "component", "ProfileModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/profile/profile.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SharedModule } from '../shared/shared.module';\n\nimport { ProfileComponent } from './profile.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: ProfileComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    ProfileComponent\n  ],\n  imports: [\n    SharedModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class ProfileModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,yBAAyB;AAEtD,SAASC,gBAAgB,QAAQ,qBAAqB;;;AAEtD,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,CACF;AAWD,OAAM,MAAOI,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBAJtBL,YAAY,EACZD,YAAY,CAACO,QAAQ,CAACJ,MAAM,CAAC;IAAA;EAAA;;;2EAGpBG,aAAa;IAAAE,YAAA,GAPtBN,gBAAgB;IAAAO,OAAA,GAGhBR,YAAY,EAAAS,EAAA,CAAAV,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}