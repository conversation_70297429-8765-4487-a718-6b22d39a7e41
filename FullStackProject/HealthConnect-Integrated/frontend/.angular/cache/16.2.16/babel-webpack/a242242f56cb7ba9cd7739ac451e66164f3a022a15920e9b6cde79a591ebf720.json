{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SharedModule } from '../shared/shared.module';\nimport { DoctorDashboardComponent } from './dashboard/dashboard.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'dashboard',\n  pathMatch: 'full'\n}, {\n  path: 'dashboard',\n  component: DoctorDashboardComponent\n}];\nexport class DoctorModule {\n  static {\n    this.ɵfac = function DoctorModule_Factory(t) {\n      return new (t || DoctorModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DoctorModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, RouterModule.forChild(routes)]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DoctorModule, {\n    declarations: [DoctorDashboardComponent],\n    imports: [SharedModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SharedModule", "DoctorDashboardComponent", "routes", "path", "redirectTo", "pathMatch", "component", "DoctorModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/doctor/doctor.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SharedModule } from '../shared/shared.module';\n\nimport { DoctorDashboardComponent } from './dashboard/dashboard.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    redirectTo: 'dashboard',\n    pathMatch: 'full'\n  },\n  {\n    path: 'dashboard',\n    component: DoctorDashboardComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    DoctorDashboardComponent\n  ],\n  imports: [\n    SharedModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class DoctorModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,yBAAyB;AAEtD,SAASC,wBAAwB,QAAQ,iCAAiC;;;AAE1E,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,WAAW;EACvBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,WAAW;EACjBG,SAAS,EAAEL;CACZ,CACF;AAWD,OAAM,MAAOM,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBAJrBP,YAAY,EACZD,YAAY,CAACS,QAAQ,CAACN,MAAM,CAAC;IAAA;EAAA;;;2EAGpBK,YAAY;IAAAE,YAAA,GAPrBR,wBAAwB;IAAAS,OAAA,GAGxBV,YAAY,EAAAW,EAAA,CAAAZ,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}