{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SharedModule } from '../shared/shared.module';\nimport { ProfileComponent } from './profile.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ProfileComponent\n}];\nexport let ProfileModule = /*#__PURE__*/(() => {\n  class ProfileModule {\n    static {\n      this.ɵfac = function ProfileModule_Factory(t) {\n        return new (t || ProfileModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ProfileModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [SharedModule, RouterModule.forChild(routes)]\n      });\n    }\n  }\n  return ProfileModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}