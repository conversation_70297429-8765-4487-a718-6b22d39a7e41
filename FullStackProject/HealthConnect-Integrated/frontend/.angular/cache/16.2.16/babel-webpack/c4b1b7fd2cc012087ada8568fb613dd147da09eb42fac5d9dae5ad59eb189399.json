{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/chat.service\";\nimport * as i2 from \"../../core/services/auth.service\";\nimport * as i3 from \"@angular/common\";\nfunction ChatListComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"span\", 10);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 11);\n    i0.ɵɵtext(5, \"Loading chats...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatListComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"i\", 13);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No conversations yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"small\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"Start a conversation with a \", (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.role) === \"PATIENT\" ? \"doctor\" : \"patient\", \"\");\n  }\n}\nfunction ChatListComponent_div_8_div_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 28);\n  }\n}\nfunction ChatListComponent_div_8_div_1_p_11_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵelement(1, \"i\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"text-primary\", chat_r4.lastMessage.status === \"READ\")(\"text-muted\", chat_r4.lastMessage.status !== \"READ\");\n  }\n}\nfunction ChatListComponent_div_8_div_1_p_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 29);\n    i0.ɵɵtemplate(1, ChatListComponent_div_8_div_1_p_11_span_1_Template, 2, 4, \"span\", 30);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"slice\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", chat_r4.lastMessage.sender.id === (ctx_r6.currentUser == null ? null : ctx_r6.currentUser.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind3(3, 3, chat_r4.lastMessage.content, 0, 50), \"\", chat_r4.lastMessage.content.length > 50 ? \"...\" : \"\", \" \");\n  }\n}\nfunction ChatListComponent_div_8_div_1_p_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 29)(1, \"em\");\n    i0.ɵɵtext(2, \"No messages yet\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatListComponent_div_8_div_1_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", chat_r4.unreadCount, \" \");\n  }\n}\nfunction ChatListComponent_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵlistener(\"click\", function ChatListComponent_div_8_div_1_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const chat_r4 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.selectChat(chat_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 17);\n    i0.ɵɵelement(2, \"img\", 18);\n    i0.ɵɵtemplate(3, ChatListComponent_div_8_div_1_span_3_Template, 1, 0, \"span\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 20)(5, \"div\", 21)(6, \"h6\", 22);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"small\", 23);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 24);\n    i0.ɵɵtemplate(11, ChatListComponent_div_8_div_1_p_11_Template, 4, 7, \"p\", 25);\n    i0.ɵɵtemplate(12, ChatListComponent_div_8_div_1_p_12_Template, 3, 0, \"p\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 26);\n    i0.ɵɵtemplate(14, ChatListComponent_div_8_div_1_span_14_Template, 2, 1, \"span\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const chat_r4 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", ctx_r3.selectedChatId === chat_r4.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r3.getOtherParticipant(chat_r4).avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r3.getOtherParticipant(chat_r4).fullName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", false);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getOtherParticipant(chat_r4).fullName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", chat_r4.lastMessage ? ctx_r3.formatLastMessageTime(chat_r4.lastMessage.createdAt) : ctx_r3.formatLastMessageTime(chat_r4.createdAt), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", chat_r4.lastMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !chat_r4.lastMessage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", chat_r4.unreadCount > 0);\n  }\n}\nfunction ChatListComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, ChatListComponent_div_8_div_1_Template, 15, 10, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.chats);\n  }\n}\nexport let ChatListComponent = /*#__PURE__*/(() => {\n  class ChatListComponent {\n    constructor(chatService, authService) {\n      this.chatService = chatService;\n      this.authService = authService;\n      this.chatSelected = new EventEmitter();\n      this.chats = [];\n      this.selectedChatId = null;\n      this.loading = true;\n      this.subscriptions = [];\n    }\n    ngOnInit() {\n      this.currentUser = this.authService.getCurrentUser();\n      this.loadChats();\n      this.subscribeToChats();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    loadChats() {\n      this.chatService.loadUserChats();\n    }\n    subscribeToChats() {\n      const chatsSub = this.chatService.chats$.subscribe({\n        next: chats => {\n          this.chats = chats;\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Failed to load chats:', error);\n          this.loading = false;\n        }\n      });\n      this.subscriptions.push(chatsSub);\n      // Subscribe to new messages to update chat list\n      const messagesSub = this.chatService.messages$.subscribe({\n        next: message => {\n          this.updateChatWithNewMessage(message);\n        }\n      });\n      this.subscriptions.push(messagesSub);\n    }\n    updateChatWithNewMessage(message) {\n      const chatIndex = this.chats.findIndex(chat => chat.id === message.chatId);\n      if (chatIndex !== -1) {\n        this.chats[chatIndex].lastMessage = message;\n        this.chats[chatIndex].updatedAt = message.createdAt;\n        // Increment unread count if message is not from current user\n        if (message.sender.id !== this.currentUser?.id) {\n          this.chats[chatIndex].unreadCount++;\n        }\n        // Move chat to top\n        const chat = this.chats.splice(chatIndex, 1)[0];\n        this.chats.unshift(chat);\n      }\n    }\n    selectChat(chat) {\n      this.selectedChatId = chat.id;\n      this.chatSelected.emit(chat);\n      // Mark messages as read\n      if (chat.unreadCount > 0) {\n        this.chatService.markMessagesAsRead(chat.id).subscribe({\n          next: () => {\n            chat.unreadCount = 0;\n          },\n          error: error => {\n            console.error('Failed to mark messages as read:', error);\n          }\n        });\n      }\n    }\n    getOtherParticipant(chat) {\n      return this.currentUser?.role === 'PATIENT' ? chat.doctor : chat.patient;\n    }\n    formatLastMessageTime(dateString) {\n      const date = new Date(dateString);\n      const now = new Date();\n      const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n      if (diffInHours < 1) {\n        return 'Just now';\n      } else if (diffInHours < 24) {\n        return date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n      } else {\n        return date.toLocaleDateString();\n      }\n    }\n    static {\n      this.ɵfac = function ChatListComponent_Factory(t) {\n        return new (t || ChatListComponent)(i0.ɵɵdirectiveInject(i1.ChatService), i0.ɵɵdirectiveInject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ChatListComponent,\n        selectors: [[\"app-chat-list\"]],\n        outputs: {\n          chatSelected: \"chatSelected\"\n        },\n        decls: 9,\n        vars: 3,\n        consts: [[1, \"chat-list\"], [1, \"chat-list-header\"], [1, \"mb-0\"], [1, \"bi\", \"bi-chat-dots\", \"me-2\"], [1, \"chat-list-body\"], [\"class\", \"text-center p-3\", 4, \"ngIf\"], [\"class\", \"text-center p-4 text-muted\", 4, \"ngIf\"], [\"class\", \"chat-items\", 4, \"ngIf\"], [1, \"text-center\", \"p-3\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\"], [1, \"visually-hidden\"], [1, \"mt-2\"], [1, \"text-center\", \"p-4\", \"text-muted\"], [1, \"bi\", \"bi-chat-square-text\", \"fs-1\", \"mb-3\", \"d-block\"], [1, \"chat-items\"], [\"class\", \"chat-item\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"chat-item\", 3, \"click\"], [1, \"chat-avatar\"], [1, \"avatar-img\", 3, \"src\", \"alt\"], [\"class\", \"online-indicator\", 4, \"ngIf\"], [1, \"chat-content\"], [1, \"chat-header\"], [1, \"chat-name\", \"mb-0\"], [1, \"chat-time\", \"text-muted\"], [1, \"chat-preview\"], [\"class\", \"mb-0 text-muted\", 4, \"ngIf\"], [1, \"chat-meta\"], [\"class\", \"badge bg-primary rounded-pill\", 4, \"ngIf\"], [1, \"online-indicator\"], [1, \"mb-0\", \"text-muted\"], [\"class\", \"me-1\", 4, \"ngIf\"], [1, \"me-1\"], [1, \"bi\", \"bi-check2-all\"], [1, \"badge\", \"bg-primary\", \"rounded-pill\"]],\n        template: function ChatListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h5\", 2);\n            i0.ɵɵelement(3, \"i\", 3);\n            i0.ɵɵtext(4, \" Messages \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"div\", 4);\n            i0.ɵɵtemplate(6, ChatListComponent_div_6_Template, 6, 0, \"div\", 5);\n            i0.ɵɵtemplate(7, ChatListComponent_div_7_Template, 6, 1, \"div\", 6);\n            i0.ɵɵtemplate(8, ChatListComponent_div_8_Template, 2, 1, \"div\", 7);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.chats.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.chats.length > 0);\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i3.SlicePipe],\n        styles: [\".chat-list[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;border-right:1px solid #e9ecef}.chat-list-header[_ngcontent-%COMP%]{padding:1rem;border-bottom:1px solid #e9ecef;background-color:#f8f9fa}.chat-list-body[_ngcontent-%COMP%]{flex:1;overflow-y:auto}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.75rem 1rem;border-bottom:1px solid #f1f3f4;cursor:pointer;transition:background-color .2s ease}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.chat-items[_ngcontent-%COMP%]   .chat-item.active[_ngcontent-%COMP%]{background-color:#e3f2fd;border-left:3px solid #2196f3}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]{position:relative;margin-right:.75rem}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   .avatar-img[_ngcontent-%COMP%]{width:45px;height:45px;border-radius:50%;object-fit:cover;border:2px solid #e9ecef}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   .online-indicator[_ngcontent-%COMP%]{position:absolute;bottom:2px;right:2px;width:12px;height:12px;background-color:#4caf50;border:2px solid white;border-radius:50%}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]{flex:1;min-width:0}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:.25rem}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-name[_ngcontent-%COMP%]{font-weight:600;color:#333;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-time[_ngcontent-%COMP%]{font-size:.75rem;white-space:nowrap}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]   .chat-preview[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.3;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-meta[_ngcontent-%COMP%]{margin-left:.5rem}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-meta[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.75rem;min-width:20px;height:20px;display:flex;align-items:center;justify-content:center}@media (max-width: 768px){.chat-list[_ngcontent-%COMP%]{border-right:none}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]{padding:1rem}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   .avatar-img[_ngcontent-%COMP%]{width:50px;height:50px}}\"]\n      });\n    }\n  }\n  return ChatListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}