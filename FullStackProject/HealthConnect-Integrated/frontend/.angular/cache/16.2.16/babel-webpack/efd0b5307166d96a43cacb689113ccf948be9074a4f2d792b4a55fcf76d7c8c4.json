{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { operate } from '../util/lift';\nexport function catchError(selector) {\n  return operate((source, subscriber) => {\n    let innerSub = null;\n    let syncUnsub = false;\n    let handledResult;\n    innerSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, undefined, err => {\n      handledResult = innerFrom(selector(err, catchError(selector)(source)));\n      if (innerSub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        handledResult.subscribe(subscriber);\n      } else {\n        syncUnsub = true;\n      }\n    }));\n    if (syncUnsub) {\n      innerSub.unsubscribe();\n      innerSub = null;\n      handledResult.subscribe(subscriber);\n    }\n  });\n}", "map": {"version": 3, "names": ["innerFrom", "createOperatorSubscriber", "operate", "catchError", "selector", "source", "subscriber", "innerSub", "syncUnsub", "handledResult", "subscribe", "undefined", "err", "unsubscribe"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/rxjs/dist/esm/internal/operators/catchError.js"], "sourcesContent": ["import { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { operate } from '../util/lift';\nexport function catchError(selector) {\n    return operate((source, subscriber) => {\n        let innerSub = null;\n        let syncUnsub = false;\n        let handledResult;\n        innerSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, undefined, (err) => {\n            handledResult = innerFrom(selector(err, catchError(selector)(source)));\n            if (innerSub) {\n                innerSub.unsubscribe();\n                innerSub = null;\n                handledResult.subscribe(subscriber);\n            }\n            else {\n                syncUnsub = true;\n            }\n        }));\n        if (syncUnsub) {\n            innerSub.unsubscribe();\n            innerSub = null;\n            handledResult.subscribe(subscriber);\n        }\n    });\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,yBAAyB;AACnD,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,OAAO,QAAQ,cAAc;AACtC,OAAO,SAASC,UAAUA,CAACC,QAAQ,EAAE;EACjC,OAAOF,OAAO,CAAC,CAACG,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,aAAa;IACjBF,QAAQ,GAAGF,MAAM,CAACK,SAAS,CAACT,wBAAwB,CAACK,UAAU,EAAEK,SAAS,EAAEA,SAAS,EAAGC,GAAG,IAAK;MAC5FH,aAAa,GAAGT,SAAS,CAACI,QAAQ,CAACQ,GAAG,EAAET,UAAU,CAACC,QAAQ,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC;MACtE,IAAIE,QAAQ,EAAE;QACVA,QAAQ,CAACM,WAAW,CAAC,CAAC;QACtBN,QAAQ,GAAG,IAAI;QACfE,aAAa,CAACC,SAAS,CAACJ,UAAU,CAAC;MACvC,CAAC,MACI;QACDE,SAAS,GAAG,IAAI;MACpB;IACJ,CAAC,CAAC,CAAC;IACH,IAAIA,SAAS,EAAE;MACXD,QAAQ,CAACM,WAAW,CAAC,CAAC;MACtBN,QAAQ,GAAG,IAAI;MACfE,aAAa,CAACC,SAAS,CAACJ,UAAU,CAAC;IACvC;EACJ,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}