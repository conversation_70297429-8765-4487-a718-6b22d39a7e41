{"ast": null, "code": "import _asyncToGenerator from \"/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { <PERSON>ompHandler } from './stomp-handler.js';\nimport { ActivationState, ReconnectionTimeMode, StompSocketState, TickerStrategy } from './types.js';\nimport { Versions } from './versions.js';\n/**\n * STOMP Client Class.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class Client {\n  /**\n   * Underlying WebSocket instance, READONLY.\n   */\n  get webSocket() {\n    return this._stompHandler?._webSocket;\n  }\n  /**\n   * Disconnection headers.\n   */\n  get disconnectHeaders() {\n    return this._disconnectHeaders;\n  }\n  set disconnectHeaders(value) {\n    this._disconnectHeaders = value;\n    if (this._stompHandler) {\n      this._stompHandler.disconnectHeaders = this._disconnectHeaders;\n    }\n  }\n  /**\n   * `true` if there is an active connection to STOMP Broker\n   */\n  get connected() {\n    return !!this._stompHandler && this._stompHandler.connected;\n  }\n  /**\n   * version of STOMP protocol negotiated with the server, READONLY\n   */\n  get connectedVersion() {\n    return this._stompHandler ? this._stompHandler.connectedVersion : undefined;\n  }\n  /**\n   * if the client is active (connected or going to reconnect)\n   */\n  get active() {\n    return this.state === ActivationState.ACTIVE;\n  }\n  _changeState(state) {\n    this.state = state;\n    this.onChangeState(state);\n  }\n  /**\n   * Create an instance.\n   */\n  constructor(conf = {}) {\n    /**\n     * STOMP versions to attempt during STOMP handshake. By default, versions `1.2`, `1.1`, and `1.0` are attempted.\n     *\n     * Example:\n     * ```javascript\n     *        // Try only versions 1.1 and 1.0\n     *        client.stompVersions = new Versions(['1.1', '1.0'])\n     * ```\n     */\n    this.stompVersions = Versions.default;\n    /**\n     * Will retry if Stomp connection is not established in specified milliseconds.\n     * Default 0, which switches off automatic reconnection.\n     */\n    this.connectionTimeout = 0;\n    /**\n     *  automatically reconnect with delay in milliseconds, set to 0 to disable.\n     */\n    this.reconnectDelay = 5000;\n    /**\n     * tracking the time to the next reconnection. Initialized to [Client#reconnectDelay]{@link Client#reconnectDelay}'s value and it may\n     * change depending on the [Client#reconnectTimeMode]{@link Client#reconnectTimeMode} setting\n     */\n    this._nextReconnectDelay = 0;\n    /**\n     * Maximum time to wait between reconnects, in milliseconds. Defaults to 15 minutes.\n     * Only relevant when [Client#reconnectTimeMode]{@link Client#reconnectTimeMode} not LINEAR (e.g., EXPONENTIAL).\n     * Set to 0 for no limit on wait time.\n     */\n    this.maxReconnectDelay = 15 * 60 * 1000; // 15 minutes in ms\n    /**\n     * Reconnection wait time mode, either linear (default) or exponential.\n     * Note: See [Client#maxReconnectDelay]{@link Client#maxReconnectDelay} for setting the maximum delay when exponential\n     *\n     * ```javascript\n     * client.configure({\n     *   reconnectTimeMode: ReconnectionTimeMode.EXPONENTIAL,\n     *   reconnectDelay: 200, // It will wait 200, 400, 800 ms...\n     *   maxReconnectDelay: 10000, // Optional, when provided, it will not wait more that these ms\n     * })\n     * ```\n     */\n    this.reconnectTimeMode = ReconnectionTimeMode.LINEAR;\n    /**\n     * Incoming heartbeat interval in milliseconds. Set to 0 to disable.\n     */\n    this.heartbeatIncoming = 10000;\n    /**\n     * Outgoing heartbeat interval in milliseconds. Set to 0 to disable.\n     */\n    this.heartbeatOutgoing = 10000;\n    /**\n     * Outgoing heartbeat strategy.\n     * See https://github.com/stomp-js/stompjs/pull/579\n     *\n     * Can be worker or interval strategy, but will always use `interval`\n     * if web workers are unavailable, for example, in a non-browser environment.\n     *\n     * Using Web Workers may work better on long-running pages\n     * and mobile apps, as the browser may suspend Timers in the main page.\n     * Try the `Worker` mode if you discover disconnects when the browser tab is in the background.\n     *\n     * When used in a JS environment, use 'worker' or 'interval' as valid values.\n     *\n     * Defaults to `interval` strategy.\n     */\n    this.heartbeatStrategy = TickerStrategy.Interval;\n    /**\n     * This switches on a non-standard behavior while sending WebSocket packets.\n     * It splits larger (text) packets into chunks of [maxWebSocketChunkSize]{@link Client#maxWebSocketChunkSize}.\n     * Only Java Spring brokers seem to support this mode.\n     *\n     * WebSockets, by itself, split large (text) packets,\n     * so it is not needed with a truly compliant STOMP/WebSocket broker.\n     * Setting it for such a broker will cause large messages to fail.\n     *\n     * `false` by default.\n     *\n     * Binary frames are never split.\n     */\n    this.splitLargeFrames = false;\n    /**\n     * See [splitLargeFrames]{@link Client#splitLargeFrames}.\n     * This has no effect if [splitLargeFrames]{@link Client#splitLargeFrames} is `false`.\n     */\n    this.maxWebSocketChunkSize = 8 * 1024;\n    /**\n     * Usually the\n     * [type of WebSocket frame]{@link https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send#Parameters}\n     * is automatically decided by type of the payload.\n     * Default is `false`, which should work with all compliant brokers.\n     *\n     * Set this flag to force binary frames.\n     */\n    this.forceBinaryWSFrames = false;\n    /**\n     * A bug in ReactNative chops a string on occurrence of a NULL.\n     * See issue [https://github.com/stomp-js/stompjs/issues/89]{@link https://github.com/stomp-js/stompjs/issues/89}.\n     * This makes incoming WebSocket messages invalid STOMP packets.\n     * Setting this flag attempts to reverse the damage by appending a NULL.\n     * If the broker splits a large message into multiple WebSocket messages,\n     * this flag will cause data loss and abnormal termination of connection.\n     *\n     * This is not an ideal solution, but a stop gap until the underlying issue is fixed at ReactNative library.\n     */\n    this.appendMissingNULLonIncoming = false;\n    /**\n     * Browsers do not immediately close WebSockets when `.close` is issued.\n     * This may cause reconnection to take a significantly long time in case\n     *  of some types of failures.\n     * In case of incoming heartbeat failure, this experimental flag instructs\n     * the library to discard the socket immediately\n     * (even before it is actually closed).\n     */\n    this.discardWebsocketOnCommFailure = false;\n    /**\n     * Activation state.\n     *\n     * It will usually be ACTIVE or INACTIVE.\n     * When deactivating, it may go from ACTIVE to INACTIVE without entering DEACTIVATING.\n     */\n    this.state = ActivationState.INACTIVE;\n    // No op callbacks\n    const noOp = () => {};\n    this.debug = noOp;\n    this.beforeConnect = noOp;\n    this.onConnect = noOp;\n    this.onDisconnect = noOp;\n    this.onUnhandledMessage = noOp;\n    this.onUnhandledReceipt = noOp;\n    this.onUnhandledFrame = noOp;\n    this.onStompError = noOp;\n    this.onWebSocketClose = noOp;\n    this.onWebSocketError = noOp;\n    this.logRawCommunication = false;\n    this.onChangeState = noOp;\n    // These parameters would typically get proper values before connect is called\n    this.connectHeaders = {};\n    this._disconnectHeaders = {};\n    // Apply configuration\n    this.configure(conf);\n  }\n  /**\n   * Update configuration.\n   */\n  configure(conf) {\n    // bulk assign all properties to this\n    Object.assign(this, conf);\n    // Warn on incorrect maxReconnectDelay settings\n    if (this.maxReconnectDelay > 0 && this.maxReconnectDelay < this.reconnectDelay) {\n      this.debug(`Warning: maxReconnectDelay (${this.maxReconnectDelay}ms) is less than reconnectDelay (${this.reconnectDelay}ms). Using reconnectDelay as the maxReconnectDelay delay.`);\n      this.maxReconnectDelay = this.reconnectDelay;\n    }\n  }\n  /**\n   * Initiate the connection with the broker.\n   * If the connection breaks, as per [Client#reconnectDelay]{@link Client#reconnectDelay},\n   * it will keep trying to reconnect. If the [Client#reconnectTimeMode]{@link Client#reconnectTimeMode}\n   * is set to EXPONENTIAL it will increase the wait time exponentially\n   *\n   * Call [Client#deactivate]{@link Client#deactivate} to disconnect and stop reconnection attempts.\n   */\n  activate() {\n    const _activate = () => {\n      if (this.active) {\n        this.debug('Already ACTIVE, ignoring request to activate');\n        return;\n      }\n      this._changeState(ActivationState.ACTIVE);\n      this._nextReconnectDelay = this.reconnectDelay;\n      this._connect();\n    };\n    // if it is deactivating, wait for it to complete before activating.\n    if (this.state === ActivationState.DEACTIVATING) {\n      this.debug('Waiting for deactivation to finish before activating');\n      this.deactivate().then(() => {\n        _activate();\n      });\n    } else {\n      _activate();\n    }\n  }\n  _connect() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.beforeConnect(_this);\n      if (_this._stompHandler) {\n        _this.debug('There is already a stompHandler, skipping the call to connect');\n        return;\n      }\n      if (!_this.active) {\n        _this.debug('Client has been marked inactive, will not attempt to connect');\n        return;\n      }\n      // setup connection watcher\n      if (_this.connectionTimeout > 0) {\n        // clear first\n        if (_this._connectionWatcher) {\n          clearTimeout(_this._connectionWatcher);\n        }\n        _this._connectionWatcher = setTimeout(() => {\n          if (_this.connected) {\n            return;\n          }\n          // Connection not established, close the underlying socket\n          // a reconnection will be attempted\n          _this.debug(`Connection not established in ${_this.connectionTimeout}ms, closing socket`);\n          _this.forceDisconnect();\n        }, _this.connectionTimeout);\n      }\n      _this.debug('Opening Web Socket...');\n      // Get the actual WebSocket (or a similar object)\n      const webSocket = _this._createWebSocket();\n      _this._stompHandler = new StompHandler(_this, webSocket, {\n        debug: _this.debug,\n        stompVersions: _this.stompVersions,\n        connectHeaders: _this.connectHeaders,\n        disconnectHeaders: _this._disconnectHeaders,\n        heartbeatIncoming: _this.heartbeatIncoming,\n        heartbeatOutgoing: _this.heartbeatOutgoing,\n        heartbeatStrategy: _this.heartbeatStrategy,\n        splitLargeFrames: _this.splitLargeFrames,\n        maxWebSocketChunkSize: _this.maxWebSocketChunkSize,\n        forceBinaryWSFrames: _this.forceBinaryWSFrames,\n        logRawCommunication: _this.logRawCommunication,\n        appendMissingNULLonIncoming: _this.appendMissingNULLonIncoming,\n        discardWebsocketOnCommFailure: _this.discardWebsocketOnCommFailure,\n        onConnect: frame => {\n          // Successfully connected, stop the connection watcher\n          if (_this._connectionWatcher) {\n            clearTimeout(_this._connectionWatcher);\n            _this._connectionWatcher = undefined;\n          }\n          if (!_this.active) {\n            _this.debug('STOMP got connected while deactivate was issued, will disconnect now');\n            _this._disposeStompHandler();\n            return;\n          }\n          _this.onConnect(frame);\n        },\n        onDisconnect: frame => {\n          _this.onDisconnect(frame);\n        },\n        onStompError: frame => {\n          _this.onStompError(frame);\n        },\n        onWebSocketClose: evt => {\n          _this._stompHandler = undefined; // a new one will be created in case of a reconnect\n          if (_this.state === ActivationState.DEACTIVATING) {\n            // Mark deactivation complete\n            _this._changeState(ActivationState.INACTIVE);\n          }\n          // The callback is called before attempting to reconnect, this would allow the client\n          // to be `deactivated` in the callback.\n          _this.onWebSocketClose(evt);\n          if (_this.active) {\n            _this._schedule_reconnect();\n          }\n        },\n        onWebSocketError: evt => {\n          _this.onWebSocketError(evt);\n        },\n        onUnhandledMessage: message => {\n          _this.onUnhandledMessage(message);\n        },\n        onUnhandledReceipt: frame => {\n          _this.onUnhandledReceipt(frame);\n        },\n        onUnhandledFrame: frame => {\n          _this.onUnhandledFrame(frame);\n        }\n      });\n      _this._stompHandler.start();\n    })();\n  }\n  _createWebSocket() {\n    let webSocket;\n    if (this.webSocketFactory) {\n      webSocket = this.webSocketFactory();\n    } else if (this.brokerURL) {\n      webSocket = new WebSocket(this.brokerURL, this.stompVersions.protocolVersions());\n    } else {\n      throw new Error('Either brokerURL or webSocketFactory must be provided');\n    }\n    webSocket.binaryType = 'arraybuffer';\n    return webSocket;\n  }\n  _schedule_reconnect() {\n    if (this._nextReconnectDelay > 0) {\n      this.debug(`STOMP: scheduling reconnection in ${this._nextReconnectDelay}ms`);\n      this._reconnector = setTimeout(() => {\n        if (this.reconnectTimeMode === ReconnectionTimeMode.EXPONENTIAL) {\n          this._nextReconnectDelay = this._nextReconnectDelay * 2;\n          // Truncated exponential backoff with a set limit unless disabled\n          if (this.maxReconnectDelay !== 0) {\n            this._nextReconnectDelay = Math.min(this._nextReconnectDelay, this.maxReconnectDelay);\n          }\n        }\n        this._connect();\n      }, this._nextReconnectDelay);\n    }\n  }\n  /**\n   * Disconnect if connected and stop auto reconnect loop.\n   * Appropriate callbacks will be invoked if there is an underlying STOMP connection.\n   *\n   * This call is async. It will resolve immediately if there is no underlying active websocket,\n   * otherwise, it will resolve after the underlying websocket is properly disposed of.\n   *\n   * It is not an error to invoke this method more than once.\n   * Each of those would resolve on completion of deactivation.\n   *\n   * To reactivate, you can call [Client#activate]{@link Client#activate}.\n   *\n   * Experimental: pass `force: true` to immediately discard the underlying connection.\n   * This mode will skip both the STOMP and the Websocket shutdown sequences.\n   * In some cases, browsers take a long time in the Websocket shutdown\n   * if the underlying connection had gone stale.\n   * Using this mode can speed up.\n   * When this mode is used, the actual Websocket may linger for a while\n   * and the broker may not realize that the connection is no longer in use.\n   *\n   * It is possible to invoke this method initially without the `force` option\n   * and subsequently, say after a wait, with the `force` option.\n   */\n  deactivate() {\n    var _this2 = this;\n    return _asyncToGenerator(function* (options = {}) {\n      const force = options.force || false;\n      const needToDispose = _this2.active;\n      let retPromise;\n      if (_this2.state === ActivationState.INACTIVE) {\n        _this2.debug(`Already INACTIVE, nothing more to do`);\n        return Promise.resolve();\n      }\n      _this2._changeState(ActivationState.DEACTIVATING);\n      // Reset reconnection timer just to be safe\n      _this2._nextReconnectDelay = 0;\n      // Clear if a reconnection was scheduled\n      if (_this2._reconnector) {\n        clearTimeout(_this2._reconnector);\n        _this2._reconnector = undefined;\n      }\n      if (_this2._stompHandler &&\n      // @ts-ignore - if there is a _stompHandler, there is the webSocket\n      _this2.webSocket.readyState !== StompSocketState.CLOSED) {\n        const origOnWebSocketClose = _this2._stompHandler.onWebSocketClose;\n        // we need to wait for the underlying websocket to close\n        retPromise = new Promise((resolve, reject) => {\n          // @ts-ignore - there is a _stompHandler\n          _this2._stompHandler.onWebSocketClose = evt => {\n            origOnWebSocketClose(evt);\n            resolve();\n          };\n        });\n      } else {\n        // indicate that auto reconnect loop should terminate\n        _this2._changeState(ActivationState.INACTIVE);\n        return Promise.resolve();\n      }\n      if (force) {\n        _this2._stompHandler?.discardWebsocket();\n      } else if (needToDispose) {\n        _this2._disposeStompHandler();\n      }\n      return retPromise;\n    }).apply(this, arguments);\n  }\n  /**\n   * Force disconnect if there is an active connection by directly closing the underlying WebSocket.\n   * This is different from a normal disconnect where a DISCONNECT sequence is carried out with the broker.\n   * After forcing disconnect, automatic reconnect will be attempted.\n   * To stop further reconnects call [Client#deactivate]{@link Client#deactivate} as well.\n   */\n  forceDisconnect() {\n    if (this._stompHandler) {\n      this._stompHandler.forceDisconnect();\n    }\n  }\n  _disposeStompHandler() {\n    // Dispose STOMP Handler\n    if (this._stompHandler) {\n      this._stompHandler.dispose();\n    }\n  }\n  /**\n   * Send a message to a named destination. Refer to your STOMP broker documentation for types\n   * and naming of destinations.\n   *\n   * STOMP protocol specifies and suggests some headers and also allows broker-specific headers.\n   *\n   * `body` must be String.\n   * You will need to covert the payload to string in case it is not string (e.g. JSON).\n   *\n   * To send a binary message body, use `binaryBody` parameter. It should be a\n   * [Uint8Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint8Array).\n   * Sometimes brokers may not support binary frames out of the box.\n   * Please check your broker documentation.\n   *\n   * `content-length` header is automatically added to the STOMP Frame sent to the broker.\n   * Set `skipContentLengthHeader` to indicate that `content-length` header should not be added.\n   * For binary messages, `content-length` header is always added.\n   *\n   * Caution: The broker will, most likely, report an error and disconnect\n   * if the message body has NULL octet(s) and `content-length` header is missing.\n   *\n   * ```javascript\n   *        client.publish({destination: \"/queue/test\", headers: {priority: 9}, body: \"Hello, STOMP\"});\n   *\n   *        // Only destination is mandatory parameter\n   *        client.publish({destination: \"/queue/test\", body: \"Hello, STOMP\"});\n   *\n   *        // Skip content-length header in the frame to the broker\n   *        client.publish({\"/queue/test\", body: \"Hello, STOMP\", skipContentLengthHeader: true});\n   *\n   *        var binaryData = generateBinaryData(); // This need to be of type Uint8Array\n   *        // setting content-type header is not mandatory, however a good practice\n   *        client.publish({destination: '/topic/special', binaryBody: binaryData,\n   *                         headers: {'content-type': 'application/octet-stream'}});\n   * ```\n   */\n  publish(params) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.publish(params);\n  }\n  _checkConnection() {\n    if (!this.connected) {\n      throw new TypeError('There is no underlying STOMP connection');\n    }\n  }\n  /**\n   * STOMP brokers may carry out operation asynchronously and allow requesting for acknowledgement.\n   * To request an acknowledgement, a `receipt` header needs to be sent with the actual request.\n   * The value (say receipt-id) for this header needs to be unique for each use.\n   * Typically, a sequence, a UUID, a random number or a combination may be used.\n   *\n   * A complaint broker will send a RECEIPT frame when an operation has actually been completed.\n   * The operation needs to be matched based on the value of the receipt-id.\n   *\n   * This method allows watching for a receipt and invoking the callback\n   *  when the corresponding receipt has been received.\n   *\n   * The actual {@link IFrame} will be passed as parameter to the callback.\n   *\n   * Example:\n   * ```javascript\n   *        // Subscribing with acknowledgement\n   *        let receiptId = randomText();\n   *\n   *        client.watchForReceipt(receiptId, function() {\n   *          // Will be called after server acknowledges\n   *        });\n   *\n   *        client.subscribe(TEST.destination, onMessage, {receipt: receiptId});\n   *\n   *\n   *        // Publishing with acknowledgement\n   *        receiptId = randomText();\n   *\n   *        client.watchForReceipt(receiptId, function() {\n   *          // Will be called after server acknowledges\n   *        });\n   *        client.publish({destination: TEST.destination, headers: {receipt: receiptId}, body: msg});\n   * ```\n   */\n  watchForReceipt(receiptId, callback) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.watchForReceipt(receiptId, callback);\n  }\n  /**\n   * Subscribe to a STOMP Broker location. The callback will be invoked for each\n   * received message with the {@link IMessage} as argument.\n   *\n   * Note: The library will generate a unique ID if there is none provided in the headers.\n   *       To use your own ID, pass it using the `headers` argument.\n   *\n   * ```javascript\n   *        callback = function(message) {\n   *        // called when the client receives a STOMP message from the server\n   *          if (message.body) {\n   *            alert(\"got message with body \" + message.body)\n   *          } else {\n   *            alert(\"got empty message\");\n   *          }\n   *        });\n   *\n   *        var subscription = client.subscribe(\"/queue/test\", callback);\n   *\n   *        // Explicit subscription id\n   *        var mySubId = 'my-subscription-id-001';\n   *        var subscription = client.subscribe(destination, callback, { id: mySubId });\n   * ```\n   */\n  subscribe(destination, callback, headers = {}) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    return this._stompHandler.subscribe(destination, callback, headers);\n  }\n  /**\n   * It is preferable to unsubscribe from a subscription by calling\n   * `unsubscribe()` directly on {@link StompSubscription} returned by `client.subscribe()`:\n   *\n   * ```javascript\n   *        var subscription = client.subscribe(destination, onmessage);\n   *        // ...\n   *        subscription.unsubscribe();\n   * ```\n   *\n   * See: https://stomp.github.com/stomp-specification-1.2.html#UNSUBSCRIBE UNSUBSCRIBE Frame\n   */\n  unsubscribe(id, headers = {}) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.unsubscribe(id, headers);\n  }\n  /**\n   * Start a transaction, the returned {@link ITransaction} has methods - [commit]{@link ITransaction#commit}\n   * and [abort]{@link ITransaction#abort}.\n   *\n   * `transactionId` is optional, if not passed the library will generate it internally.\n   */\n  begin(transactionId) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    return this._stompHandler.begin(transactionId);\n  }\n  /**\n   * Commit a transaction.\n   *\n   * It is preferable to commit a transaction by calling [commit]{@link ITransaction#commit} directly on\n   * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n   *\n   * ```javascript\n   *        var tx = client.begin(txId);\n   *        //...\n   *        tx.commit();\n   * ```\n   */\n  commit(transactionId) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.commit(transactionId);\n  }\n  /**\n   * Abort a transaction.\n   * It is preferable to abort a transaction by calling [abort]{@link ITransaction#abort} directly on\n   * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n   *\n   * ```javascript\n   *        var tx = client.begin(txId);\n   *        //...\n   *        tx.abort();\n   * ```\n   */\n  abort(transactionId) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.abort(transactionId);\n  }\n  /**\n   * ACK a message. It is preferable to acknowledge a message by calling [ack]{@link IMessage#ack} directly\n   * on the {@link IMessage} handled by a subscription callback:\n   *\n   * ```javascript\n   *        var callback = function (message) {\n   *          // process the message\n   *          // acknowledge it\n   *          message.ack();\n   *        };\n   *        client.subscribe(destination, callback, {'ack': 'client'});\n   * ```\n   */\n  ack(messageId, subscriptionId, headers = {}) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.ack(messageId, subscriptionId, headers);\n  }\n  /**\n   * NACK a message. It is preferable to acknowledge a message by calling [nack]{@link IMessage#nack} directly\n   * on the {@link IMessage} handled by a subscription callback:\n   *\n   * ```javascript\n   *        var callback = function (message) {\n   *          // process the message\n   *          // an error occurs, nack it\n   *          message.nack();\n   *        };\n   *        client.subscribe(destination, callback, {'ack': 'client'});\n   * ```\n   */\n  nack(messageId, subscriptionId, headers = {}) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.nack(messageId, subscriptionId, headers);\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ActivationState", "ReconnectionTimeMode", "StompSocketState", "TickerStrategy", "Versions", "Client", "webSocket", "_stomp<PERSON><PERSON><PERSON>", "_webSocket", "disconnectHeaders", "_disconnectHeaders", "value", "connected", "connectedVersion", "undefined", "active", "state", "ACTIVE", "_changeState", "onChangeState", "constructor", "conf", "stompV<PERSON><PERSON>", "default", "connectionTimeout", "reconnectDelay", "_nextReconnectDelay", "maxReconnectDelay", "reconnectTimeMode", "LINEAR", "heartbeatIncoming", "heartbeatOutgoing", "heartbeatStrategy", "Interval", "splitLargeFrames", "maxWebSocketChunkSize", "forceBinaryWSFrames", "appendMissingNULLonIncoming", "discardWebsocketOnCommFailure", "INACTIVE", "noOp", "debug", "beforeConnect", "onConnect", "onDisconnect", "onUnhandledMessage", "onUnhandledReceipt", "onUnhandledFrame", "onStompError", "onWebSocketClose", "onWebSocketError", "logRawCommunication", "connectHeaders", "configure", "Object", "assign", "activate", "_activate", "_connect", "DEACTIVATING", "deactivate", "then", "_this", "_asyncToGenerator", "_connectionWatcher", "clearTimeout", "setTimeout", "forceDisconnect", "_createWebSocket", "frame", "_dispose<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evt", "_schedule_reconnect", "message", "start", "webSocketFactory", "brokerURL", "WebSocket", "protocolVersions", "Error", "binaryType", "_reconnector", "EXPONENTIAL", "Math", "min", "_this2", "options", "force", "needToDispose", "retPromise", "Promise", "resolve", "readyState", "CLOSED", "origOnWebSocketClose", "reject", "discardWeb<PERSON>cket", "apply", "arguments", "dispose", "publish", "params", "_checkConnection", "TypeError", "watchForReceipt", "receiptId", "callback", "subscribe", "destination", "headers", "unsubscribe", "id", "begin", "transactionId", "commit", "abort", "ack", "messageId", "subscriptionId", "nack"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/@stomp/stompjs/esm6/client.js"], "sourcesContent": ["import { <PERSON>om<PERSON><PERSON>and<PERSON> } from './stomp-handler.js';\nimport { ActivationState, ReconnectionTimeMode, StompSocketState, TickerStrategy, } from './types.js';\nimport { Versions } from './versions.js';\n/**\n * STOMP Client Class.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class Client {\n    /**\n     * Underlying WebSocket instance, READONLY.\n     */\n    get webSocket() {\n        return this._stompHandler?._webSocket;\n    }\n    /**\n     * Disconnection headers.\n     */\n    get disconnectHeaders() {\n        return this._disconnectHeaders;\n    }\n    set disconnectHeaders(value) {\n        this._disconnectHeaders = value;\n        if (this._stompHandler) {\n            this._stompHandler.disconnectHeaders = this._disconnectHeaders;\n        }\n    }\n    /**\n     * `true` if there is an active connection to STOMP Broker\n     */\n    get connected() {\n        return !!this._stompHandler && this._stompHandler.connected;\n    }\n    /**\n     * version of STOMP protocol negotiated with the server, READONLY\n     */\n    get connectedVersion() {\n        return this._stompHandler ? this._stompHandler.connectedVersion : undefined;\n    }\n    /**\n     * if the client is active (connected or going to reconnect)\n     */\n    get active() {\n        return this.state === ActivationState.ACTIVE;\n    }\n    _changeState(state) {\n        this.state = state;\n        this.onChangeState(state);\n    }\n    /**\n     * Create an instance.\n     */\n    constructor(conf = {}) {\n        /**\n         * STOMP versions to attempt during STOMP handshake. By default, versions `1.2`, `1.1`, and `1.0` are attempted.\n         *\n         * Example:\n         * ```javascript\n         *        // Try only versions 1.1 and 1.0\n         *        client.stompVersions = new Versions(['1.1', '1.0'])\n         * ```\n         */\n        this.stompVersions = Versions.default;\n        /**\n         * Will retry if Stomp connection is not established in specified milliseconds.\n         * Default 0, which switches off automatic reconnection.\n         */\n        this.connectionTimeout = 0;\n        /**\n         *  automatically reconnect with delay in milliseconds, set to 0 to disable.\n         */\n        this.reconnectDelay = 5000;\n        /**\n         * tracking the time to the next reconnection. Initialized to [Client#reconnectDelay]{@link Client#reconnectDelay}'s value and it may\n         * change depending on the [Client#reconnectTimeMode]{@link Client#reconnectTimeMode} setting\n         */\n        this._nextReconnectDelay = 0;\n        /**\n         * Maximum time to wait between reconnects, in milliseconds. Defaults to 15 minutes.\n         * Only relevant when [Client#reconnectTimeMode]{@link Client#reconnectTimeMode} not LINEAR (e.g., EXPONENTIAL).\n         * Set to 0 for no limit on wait time.\n         */\n        this.maxReconnectDelay = 15 * 60 * 1000; // 15 minutes in ms\n        /**\n         * Reconnection wait time mode, either linear (default) or exponential.\n         * Note: See [Client#maxReconnectDelay]{@link Client#maxReconnectDelay} for setting the maximum delay when exponential\n         *\n         * ```javascript\n         * client.configure({\n         *   reconnectTimeMode: ReconnectionTimeMode.EXPONENTIAL,\n         *   reconnectDelay: 200, // It will wait 200, 400, 800 ms...\n         *   maxReconnectDelay: 10000, // Optional, when provided, it will not wait more that these ms\n         * })\n         * ```\n         */\n        this.reconnectTimeMode = ReconnectionTimeMode.LINEAR;\n        /**\n         * Incoming heartbeat interval in milliseconds. Set to 0 to disable.\n         */\n        this.heartbeatIncoming = 10000;\n        /**\n         * Outgoing heartbeat interval in milliseconds. Set to 0 to disable.\n         */\n        this.heartbeatOutgoing = 10000;\n        /**\n         * Outgoing heartbeat strategy.\n         * See https://github.com/stomp-js/stompjs/pull/579\n         *\n         * Can be worker or interval strategy, but will always use `interval`\n         * if web workers are unavailable, for example, in a non-browser environment.\n         *\n         * Using Web Workers may work better on long-running pages\n         * and mobile apps, as the browser may suspend Timers in the main page.\n         * Try the `Worker` mode if you discover disconnects when the browser tab is in the background.\n         *\n         * When used in a JS environment, use 'worker' or 'interval' as valid values.\n         *\n         * Defaults to `interval` strategy.\n         */\n        this.heartbeatStrategy = TickerStrategy.Interval;\n        /**\n         * This switches on a non-standard behavior while sending WebSocket packets.\n         * It splits larger (text) packets into chunks of [maxWebSocketChunkSize]{@link Client#maxWebSocketChunkSize}.\n         * Only Java Spring brokers seem to support this mode.\n         *\n         * WebSockets, by itself, split large (text) packets,\n         * so it is not needed with a truly compliant STOMP/WebSocket broker.\n         * Setting it for such a broker will cause large messages to fail.\n         *\n         * `false` by default.\n         *\n         * Binary frames are never split.\n         */\n        this.splitLargeFrames = false;\n        /**\n         * See [splitLargeFrames]{@link Client#splitLargeFrames}.\n         * This has no effect if [splitLargeFrames]{@link Client#splitLargeFrames} is `false`.\n         */\n        this.maxWebSocketChunkSize = 8 * 1024;\n        /**\n         * Usually the\n         * [type of WebSocket frame]{@link https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send#Parameters}\n         * is automatically decided by type of the payload.\n         * Default is `false`, which should work with all compliant brokers.\n         *\n         * Set this flag to force binary frames.\n         */\n        this.forceBinaryWSFrames = false;\n        /**\n         * A bug in ReactNative chops a string on occurrence of a NULL.\n         * See issue [https://github.com/stomp-js/stompjs/issues/89]{@link https://github.com/stomp-js/stompjs/issues/89}.\n         * This makes incoming WebSocket messages invalid STOMP packets.\n         * Setting this flag attempts to reverse the damage by appending a NULL.\n         * If the broker splits a large message into multiple WebSocket messages,\n         * this flag will cause data loss and abnormal termination of connection.\n         *\n         * This is not an ideal solution, but a stop gap until the underlying issue is fixed at ReactNative library.\n         */\n        this.appendMissingNULLonIncoming = false;\n        /**\n         * Browsers do not immediately close WebSockets when `.close` is issued.\n         * This may cause reconnection to take a significantly long time in case\n         *  of some types of failures.\n         * In case of incoming heartbeat failure, this experimental flag instructs\n         * the library to discard the socket immediately\n         * (even before it is actually closed).\n         */\n        this.discardWebsocketOnCommFailure = false;\n        /**\n         * Activation state.\n         *\n         * It will usually be ACTIVE or INACTIVE.\n         * When deactivating, it may go from ACTIVE to INACTIVE without entering DEACTIVATING.\n         */\n        this.state = ActivationState.INACTIVE;\n        // No op callbacks\n        const noOp = () => { };\n        this.debug = noOp;\n        this.beforeConnect = noOp;\n        this.onConnect = noOp;\n        this.onDisconnect = noOp;\n        this.onUnhandledMessage = noOp;\n        this.onUnhandledReceipt = noOp;\n        this.onUnhandledFrame = noOp;\n        this.onStompError = noOp;\n        this.onWebSocketClose = noOp;\n        this.onWebSocketError = noOp;\n        this.logRawCommunication = false;\n        this.onChangeState = noOp;\n        // These parameters would typically get proper values before connect is called\n        this.connectHeaders = {};\n        this._disconnectHeaders = {};\n        // Apply configuration\n        this.configure(conf);\n    }\n    /**\n     * Update configuration.\n     */\n    configure(conf) {\n        // bulk assign all properties to this\n        Object.assign(this, conf);\n        // Warn on incorrect maxReconnectDelay settings\n        if (this.maxReconnectDelay > 0 &&\n            this.maxReconnectDelay < this.reconnectDelay) {\n            this.debug(`Warning: maxReconnectDelay (${this.maxReconnectDelay}ms) is less than reconnectDelay (${this.reconnectDelay}ms). Using reconnectDelay as the maxReconnectDelay delay.`);\n            this.maxReconnectDelay = this.reconnectDelay;\n        }\n    }\n    /**\n     * Initiate the connection with the broker.\n     * If the connection breaks, as per [Client#reconnectDelay]{@link Client#reconnectDelay},\n     * it will keep trying to reconnect. If the [Client#reconnectTimeMode]{@link Client#reconnectTimeMode}\n     * is set to EXPONENTIAL it will increase the wait time exponentially\n     *\n     * Call [Client#deactivate]{@link Client#deactivate} to disconnect and stop reconnection attempts.\n     */\n    activate() {\n        const _activate = () => {\n            if (this.active) {\n                this.debug('Already ACTIVE, ignoring request to activate');\n                return;\n            }\n            this._changeState(ActivationState.ACTIVE);\n            this._nextReconnectDelay = this.reconnectDelay;\n            this._connect();\n        };\n        // if it is deactivating, wait for it to complete before activating.\n        if (this.state === ActivationState.DEACTIVATING) {\n            this.debug('Waiting for deactivation to finish before activating');\n            this.deactivate().then(() => {\n                _activate();\n            });\n        }\n        else {\n            _activate();\n        }\n    }\n    async _connect() {\n        await this.beforeConnect(this);\n        if (this._stompHandler) {\n            this.debug('There is already a stompHandler, skipping the call to connect');\n            return;\n        }\n        if (!this.active) {\n            this.debug('Client has been marked inactive, will not attempt to connect');\n            return;\n        }\n        // setup connection watcher\n        if (this.connectionTimeout > 0) {\n            // clear first\n            if (this._connectionWatcher) {\n                clearTimeout(this._connectionWatcher);\n            }\n            this._connectionWatcher = setTimeout(() => {\n                if (this.connected) {\n                    return;\n                }\n                // Connection not established, close the underlying socket\n                // a reconnection will be attempted\n                this.debug(`Connection not established in ${this.connectionTimeout}ms, closing socket`);\n                this.forceDisconnect();\n            }, this.connectionTimeout);\n        }\n        this.debug('Opening Web Socket...');\n        // Get the actual WebSocket (or a similar object)\n        const webSocket = this._createWebSocket();\n        this._stompHandler = new StompHandler(this, webSocket, {\n            debug: this.debug,\n            stompVersions: this.stompVersions,\n            connectHeaders: this.connectHeaders,\n            disconnectHeaders: this._disconnectHeaders,\n            heartbeatIncoming: this.heartbeatIncoming,\n            heartbeatOutgoing: this.heartbeatOutgoing,\n            heartbeatStrategy: this.heartbeatStrategy,\n            splitLargeFrames: this.splitLargeFrames,\n            maxWebSocketChunkSize: this.maxWebSocketChunkSize,\n            forceBinaryWSFrames: this.forceBinaryWSFrames,\n            logRawCommunication: this.logRawCommunication,\n            appendMissingNULLonIncoming: this.appendMissingNULLonIncoming,\n            discardWebsocketOnCommFailure: this.discardWebsocketOnCommFailure,\n            onConnect: frame => {\n                // Successfully connected, stop the connection watcher\n                if (this._connectionWatcher) {\n                    clearTimeout(this._connectionWatcher);\n                    this._connectionWatcher = undefined;\n                }\n                if (!this.active) {\n                    this.debug('STOMP got connected while deactivate was issued, will disconnect now');\n                    this._disposeStompHandler();\n                    return;\n                }\n                this.onConnect(frame);\n            },\n            onDisconnect: frame => {\n                this.onDisconnect(frame);\n            },\n            onStompError: frame => {\n                this.onStompError(frame);\n            },\n            onWebSocketClose: evt => {\n                this._stompHandler = undefined; // a new one will be created in case of a reconnect\n                if (this.state === ActivationState.DEACTIVATING) {\n                    // Mark deactivation complete\n                    this._changeState(ActivationState.INACTIVE);\n                }\n                // The callback is called before attempting to reconnect, this would allow the client\n                // to be `deactivated` in the callback.\n                this.onWebSocketClose(evt);\n                if (this.active) {\n                    this._schedule_reconnect();\n                }\n            },\n            onWebSocketError: evt => {\n                this.onWebSocketError(evt);\n            },\n            onUnhandledMessage: message => {\n                this.onUnhandledMessage(message);\n            },\n            onUnhandledReceipt: frame => {\n                this.onUnhandledReceipt(frame);\n            },\n            onUnhandledFrame: frame => {\n                this.onUnhandledFrame(frame);\n            },\n        });\n        this._stompHandler.start();\n    }\n    _createWebSocket() {\n        let webSocket;\n        if (this.webSocketFactory) {\n            webSocket = this.webSocketFactory();\n        }\n        else if (this.brokerURL) {\n            webSocket = new WebSocket(this.brokerURL, this.stompVersions.protocolVersions());\n        }\n        else {\n            throw new Error('Either brokerURL or webSocketFactory must be provided');\n        }\n        webSocket.binaryType = 'arraybuffer';\n        return webSocket;\n    }\n    _schedule_reconnect() {\n        if (this._nextReconnectDelay > 0) {\n            this.debug(`STOMP: scheduling reconnection in ${this._nextReconnectDelay}ms`);\n            this._reconnector = setTimeout(() => {\n                if (this.reconnectTimeMode === ReconnectionTimeMode.EXPONENTIAL) {\n                    this._nextReconnectDelay = this._nextReconnectDelay * 2;\n                    // Truncated exponential backoff with a set limit unless disabled\n                    if (this.maxReconnectDelay !== 0) {\n                        this._nextReconnectDelay = Math.min(this._nextReconnectDelay, this.maxReconnectDelay);\n                    }\n                }\n                this._connect();\n            }, this._nextReconnectDelay);\n        }\n    }\n    /**\n     * Disconnect if connected and stop auto reconnect loop.\n     * Appropriate callbacks will be invoked if there is an underlying STOMP connection.\n     *\n     * This call is async. It will resolve immediately if there is no underlying active websocket,\n     * otherwise, it will resolve after the underlying websocket is properly disposed of.\n     *\n     * It is not an error to invoke this method more than once.\n     * Each of those would resolve on completion of deactivation.\n     *\n     * To reactivate, you can call [Client#activate]{@link Client#activate}.\n     *\n     * Experimental: pass `force: true` to immediately discard the underlying connection.\n     * This mode will skip both the STOMP and the Websocket shutdown sequences.\n     * In some cases, browsers take a long time in the Websocket shutdown\n     * if the underlying connection had gone stale.\n     * Using this mode can speed up.\n     * When this mode is used, the actual Websocket may linger for a while\n     * and the broker may not realize that the connection is no longer in use.\n     *\n     * It is possible to invoke this method initially without the `force` option\n     * and subsequently, say after a wait, with the `force` option.\n     */\n    async deactivate(options = {}) {\n        const force = options.force || false;\n        const needToDispose = this.active;\n        let retPromise;\n        if (this.state === ActivationState.INACTIVE) {\n            this.debug(`Already INACTIVE, nothing more to do`);\n            return Promise.resolve();\n        }\n        this._changeState(ActivationState.DEACTIVATING);\n        // Reset reconnection timer just to be safe\n        this._nextReconnectDelay = 0;\n        // Clear if a reconnection was scheduled\n        if (this._reconnector) {\n            clearTimeout(this._reconnector);\n            this._reconnector = undefined;\n        }\n        if (this._stompHandler &&\n            // @ts-ignore - if there is a _stompHandler, there is the webSocket\n            this.webSocket.readyState !== StompSocketState.CLOSED) {\n            const origOnWebSocketClose = this._stompHandler.onWebSocketClose;\n            // we need to wait for the underlying websocket to close\n            retPromise = new Promise((resolve, reject) => {\n                // @ts-ignore - there is a _stompHandler\n                this._stompHandler.onWebSocketClose = evt => {\n                    origOnWebSocketClose(evt);\n                    resolve();\n                };\n            });\n        }\n        else {\n            // indicate that auto reconnect loop should terminate\n            this._changeState(ActivationState.INACTIVE);\n            return Promise.resolve();\n        }\n        if (force) {\n            this._stompHandler?.discardWebsocket();\n        }\n        else if (needToDispose) {\n            this._disposeStompHandler();\n        }\n        return retPromise;\n    }\n    /**\n     * Force disconnect if there is an active connection by directly closing the underlying WebSocket.\n     * This is different from a normal disconnect where a DISCONNECT sequence is carried out with the broker.\n     * After forcing disconnect, automatic reconnect will be attempted.\n     * To stop further reconnects call [Client#deactivate]{@link Client#deactivate} as well.\n     */\n    forceDisconnect() {\n        if (this._stompHandler) {\n            this._stompHandler.forceDisconnect();\n        }\n    }\n    _disposeStompHandler() {\n        // Dispose STOMP Handler\n        if (this._stompHandler) {\n            this._stompHandler.dispose();\n        }\n    }\n    /**\n     * Send a message to a named destination. Refer to your STOMP broker documentation for types\n     * and naming of destinations.\n     *\n     * STOMP protocol specifies and suggests some headers and also allows broker-specific headers.\n     *\n     * `body` must be String.\n     * You will need to covert the payload to string in case it is not string (e.g. JSON).\n     *\n     * To send a binary message body, use `binaryBody` parameter. It should be a\n     * [Uint8Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint8Array).\n     * Sometimes brokers may not support binary frames out of the box.\n     * Please check your broker documentation.\n     *\n     * `content-length` header is automatically added to the STOMP Frame sent to the broker.\n     * Set `skipContentLengthHeader` to indicate that `content-length` header should not be added.\n     * For binary messages, `content-length` header is always added.\n     *\n     * Caution: The broker will, most likely, report an error and disconnect\n     * if the message body has NULL octet(s) and `content-length` header is missing.\n     *\n     * ```javascript\n     *        client.publish({destination: \"/queue/test\", headers: {priority: 9}, body: \"Hello, STOMP\"});\n     *\n     *        // Only destination is mandatory parameter\n     *        client.publish({destination: \"/queue/test\", body: \"Hello, STOMP\"});\n     *\n     *        // Skip content-length header in the frame to the broker\n     *        client.publish({\"/queue/test\", body: \"Hello, STOMP\", skipContentLengthHeader: true});\n     *\n     *        var binaryData = generateBinaryData(); // This need to be of type Uint8Array\n     *        // setting content-type header is not mandatory, however a good practice\n     *        client.publish({destination: '/topic/special', binaryBody: binaryData,\n     *                         headers: {'content-type': 'application/octet-stream'}});\n     * ```\n     */\n    publish(params) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.publish(params);\n    }\n    _checkConnection() {\n        if (!this.connected) {\n            throw new TypeError('There is no underlying STOMP connection');\n        }\n    }\n    /**\n     * STOMP brokers may carry out operation asynchronously and allow requesting for acknowledgement.\n     * To request an acknowledgement, a `receipt` header needs to be sent with the actual request.\n     * The value (say receipt-id) for this header needs to be unique for each use.\n     * Typically, a sequence, a UUID, a random number or a combination may be used.\n     *\n     * A complaint broker will send a RECEIPT frame when an operation has actually been completed.\n     * The operation needs to be matched based on the value of the receipt-id.\n     *\n     * This method allows watching for a receipt and invoking the callback\n     *  when the corresponding receipt has been received.\n     *\n     * The actual {@link IFrame} will be passed as parameter to the callback.\n     *\n     * Example:\n     * ```javascript\n     *        // Subscribing with acknowledgement\n     *        let receiptId = randomText();\n     *\n     *        client.watchForReceipt(receiptId, function() {\n     *          // Will be called after server acknowledges\n     *        });\n     *\n     *        client.subscribe(TEST.destination, onMessage, {receipt: receiptId});\n     *\n     *\n     *        // Publishing with acknowledgement\n     *        receiptId = randomText();\n     *\n     *        client.watchForReceipt(receiptId, function() {\n     *          // Will be called after server acknowledges\n     *        });\n     *        client.publish({destination: TEST.destination, headers: {receipt: receiptId}, body: msg});\n     * ```\n     */\n    watchForReceipt(receiptId, callback) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.watchForReceipt(receiptId, callback);\n    }\n    /**\n     * Subscribe to a STOMP Broker location. The callback will be invoked for each\n     * received message with the {@link IMessage} as argument.\n     *\n     * Note: The library will generate a unique ID if there is none provided in the headers.\n     *       To use your own ID, pass it using the `headers` argument.\n     *\n     * ```javascript\n     *        callback = function(message) {\n     *        // called when the client receives a STOMP message from the server\n     *          if (message.body) {\n     *            alert(\"got message with body \" + message.body)\n     *          } else {\n     *            alert(\"got empty message\");\n     *          }\n     *        });\n     *\n     *        var subscription = client.subscribe(\"/queue/test\", callback);\n     *\n     *        // Explicit subscription id\n     *        var mySubId = 'my-subscription-id-001';\n     *        var subscription = client.subscribe(destination, callback, { id: mySubId });\n     * ```\n     */\n    subscribe(destination, callback, headers = {}) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        return this._stompHandler.subscribe(destination, callback, headers);\n    }\n    /**\n     * It is preferable to unsubscribe from a subscription by calling\n     * `unsubscribe()` directly on {@link StompSubscription} returned by `client.subscribe()`:\n     *\n     * ```javascript\n     *        var subscription = client.subscribe(destination, onmessage);\n     *        // ...\n     *        subscription.unsubscribe();\n     * ```\n     *\n     * See: https://stomp.github.com/stomp-specification-1.2.html#UNSUBSCRIBE UNSUBSCRIBE Frame\n     */\n    unsubscribe(id, headers = {}) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.unsubscribe(id, headers);\n    }\n    /**\n     * Start a transaction, the returned {@link ITransaction} has methods - [commit]{@link ITransaction#commit}\n     * and [abort]{@link ITransaction#abort}.\n     *\n     * `transactionId` is optional, if not passed the library will generate it internally.\n     */\n    begin(transactionId) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        return this._stompHandler.begin(transactionId);\n    }\n    /**\n     * Commit a transaction.\n     *\n     * It is preferable to commit a transaction by calling [commit]{@link ITransaction#commit} directly on\n     * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n     *\n     * ```javascript\n     *        var tx = client.begin(txId);\n     *        //...\n     *        tx.commit();\n     * ```\n     */\n    commit(transactionId) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.commit(transactionId);\n    }\n    /**\n     * Abort a transaction.\n     * It is preferable to abort a transaction by calling [abort]{@link ITransaction#abort} directly on\n     * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n     *\n     * ```javascript\n     *        var tx = client.begin(txId);\n     *        //...\n     *        tx.abort();\n     * ```\n     */\n    abort(transactionId) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.abort(transactionId);\n    }\n    /**\n     * ACK a message. It is preferable to acknowledge a message by calling [ack]{@link IMessage#ack} directly\n     * on the {@link IMessage} handled by a subscription callback:\n     *\n     * ```javascript\n     *        var callback = function (message) {\n     *          // process the message\n     *          // acknowledge it\n     *          message.ack();\n     *        };\n     *        client.subscribe(destination, callback, {'ack': 'client'});\n     * ```\n     */\n    ack(messageId, subscriptionId, headers = {}) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.ack(messageId, subscriptionId, headers);\n    }\n    /**\n     * NACK a message. It is preferable to acknowledge a message by calling [nack]{@link IMessage#nack} directly\n     * on the {@link IMessage} handled by a subscription callback:\n     *\n     * ```javascript\n     *        var callback = function (message) {\n     *          // process the message\n     *          // an error occurs, nack it\n     *          message.nack();\n     *        };\n     *        client.subscribe(destination, callback, {'ack': 'client'});\n     * ```\n     */\n    nack(messageId, subscriptionId, headers = {}) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.nack(messageId, subscriptionId, headers);\n    }\n}\n"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,eAAe,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAEC,cAAc,QAAS,YAAY;AACrG,SAASC,QAAQ,QAAQ,eAAe;AACxC;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,MAAM,CAAC;EAChB;AACJ;AACA;EACI,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,aAAa,EAAEC,UAAU;EACzC;EACA;AACJ;AACA;EACI,IAAIC,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,kBAAkB;EAClC;EACA,IAAID,iBAAiBA,CAACE,KAAK,EAAE;IACzB,IAAI,CAACD,kBAAkB,GAAGC,KAAK;IAC/B,IAAI,IAAI,CAACJ,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACE,iBAAiB,GAAG,IAAI,CAACC,kBAAkB;IAClE;EACJ;EACA;AACJ;AACA;EACI,IAAIE,SAASA,CAAA,EAAG;IACZ,OAAO,CAAC,CAAC,IAAI,CAACL,aAAa,IAAI,IAAI,CAACA,aAAa,CAACK,SAAS;EAC/D;EACA;AACJ;AACA;EACI,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACN,aAAa,GAAG,IAAI,CAACA,aAAa,CAACM,gBAAgB,GAAGC,SAAS;EAC/E;EACA;AACJ;AACA;EACI,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,KAAK,KAAKhB,eAAe,CAACiB,MAAM;EAChD;EACAC,YAAYA,CAACF,KAAK,EAAE;IAChB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACG,aAAa,CAACH,KAAK,CAAC;EAC7B;EACA;AACJ;AACA;EACII,WAAWA,CAACC,IAAI,GAAG,CAAC,CAAC,EAAE;IACnB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAGlB,QAAQ,CAACmB,OAAO;IACrC;AACR;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B;AACR;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B;AACR;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,CAAC;IAC5B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACzC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG3B,oBAAoB,CAAC4B,MAAM;IACpD;AACR;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B;AACR;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG7B,cAAc,CAAC8B,QAAQ;IAChD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B;AACR;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,CAAC,GAAG,IAAI;IACrC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,2BAA2B,GAAG,KAAK;IACxC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,6BAA6B,GAAG,KAAK;IAC1C;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACtB,KAAK,GAAGhB,eAAe,CAACuC,QAAQ;IACrC;IACA,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAE,CAAC;IACtB,IAAI,CAACC,KAAK,GAAGD,IAAI;IACjB,IAAI,CAACE,aAAa,GAAGF,IAAI;IACzB,IAAI,CAACG,SAAS,GAAGH,IAAI;IACrB,IAAI,CAACI,YAAY,GAAGJ,IAAI;IACxB,IAAI,CAACK,kBAAkB,GAAGL,IAAI;IAC9B,IAAI,CAACM,kBAAkB,GAAGN,IAAI;IAC9B,IAAI,CAACO,gBAAgB,GAAGP,IAAI;IAC5B,IAAI,CAACQ,YAAY,GAAGR,IAAI;IACxB,IAAI,CAACS,gBAAgB,GAAGT,IAAI;IAC5B,IAAI,CAACU,gBAAgB,GAAGV,IAAI;IAC5B,IAAI,CAACW,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAAChC,aAAa,GAAGqB,IAAI;IACzB;IACA,IAAI,CAACY,cAAc,GAAG,CAAC,CAAC;IACxB,IAAI,CAAC1C,kBAAkB,GAAG,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC2C,SAAS,CAAChC,IAAI,CAAC;EACxB;EACA;AACJ;AACA;EACIgC,SAASA,CAAChC,IAAI,EAAE;IACZ;IACAiC,MAAM,CAACC,MAAM,CAAC,IAAI,EAAElC,IAAI,CAAC;IACzB;IACA,IAAI,IAAI,CAACM,iBAAiB,GAAG,CAAC,IAC1B,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACF,cAAc,EAAE;MAC9C,IAAI,CAACgB,KAAK,CAAE,+BAA8B,IAAI,CAACd,iBAAkB,oCAAmC,IAAI,CAACF,cAAe,2DAA0D,CAAC;MACnL,IAAI,CAACE,iBAAiB,GAAG,IAAI,CAACF,cAAc;IAChD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI+B,QAAQA,CAAA,EAAG;IACP,MAAMC,SAAS,GAAGA,CAAA,KAAM;MACpB,IAAI,IAAI,CAAC1C,MAAM,EAAE;QACb,IAAI,CAAC0B,KAAK,CAAC,8CAA8C,CAAC;QAC1D;MACJ;MACA,IAAI,CAACvB,YAAY,CAAClB,eAAe,CAACiB,MAAM,CAAC;MACzC,IAAI,CAACS,mBAAmB,GAAG,IAAI,CAACD,cAAc;MAC9C,IAAI,CAACiC,QAAQ,CAAC,CAAC;IACnB,CAAC;IACD;IACA,IAAI,IAAI,CAAC1C,KAAK,KAAKhB,eAAe,CAAC2D,YAAY,EAAE;MAC7C,IAAI,CAAClB,KAAK,CAAC,sDAAsD,CAAC;MAClE,IAAI,CAACmB,UAAU,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzBJ,SAAS,CAAC,CAAC;MACf,CAAC,CAAC;IACN,CAAC,MACI;MACDA,SAAS,CAAC,CAAC;IACf;EACJ;EACMC,QAAQA,CAAA,EAAG;IAAA,IAAAI,KAAA;IAAA,OAAAC,iBAAA;MACb,MAAMD,KAAI,CAACpB,aAAa,CAACoB,KAAI,CAAC;MAC9B,IAAIA,KAAI,CAACvD,aAAa,EAAE;QACpBuD,KAAI,CAACrB,KAAK,CAAC,+DAA+D,CAAC;QAC3E;MACJ;MACA,IAAI,CAACqB,KAAI,CAAC/C,MAAM,EAAE;QACd+C,KAAI,CAACrB,KAAK,CAAC,8DAA8D,CAAC;QAC1E;MACJ;MACA;MACA,IAAIqB,KAAI,CAACtC,iBAAiB,GAAG,CAAC,EAAE;QAC5B;QACA,IAAIsC,KAAI,CAACE,kBAAkB,EAAE;UACzBC,YAAY,CAACH,KAAI,CAACE,kBAAkB,CAAC;QACzC;QACAF,KAAI,CAACE,kBAAkB,GAAGE,UAAU,CAAC,MAAM;UACvC,IAAIJ,KAAI,CAAClD,SAAS,EAAE;YAChB;UACJ;UACA;UACA;UACAkD,KAAI,CAACrB,KAAK,CAAE,iCAAgCqB,KAAI,CAACtC,iBAAkB,oBAAmB,CAAC;UACvFsC,KAAI,CAACK,eAAe,CAAC,CAAC;QAC1B,CAAC,EAAEL,KAAI,CAACtC,iBAAiB,CAAC;MAC9B;MACAsC,KAAI,CAACrB,KAAK,CAAC,uBAAuB,CAAC;MACnC;MACA,MAAMnC,SAAS,GAAGwD,KAAI,CAACM,gBAAgB,CAAC,CAAC;MACzCN,KAAI,CAACvD,aAAa,GAAG,IAAIR,YAAY,CAAC+D,KAAI,EAAExD,SAAS,EAAE;QACnDmC,KAAK,EAAEqB,KAAI,CAACrB,KAAK;QACjBnB,aAAa,EAAEwC,KAAI,CAACxC,aAAa;QACjC8B,cAAc,EAAEU,KAAI,CAACV,cAAc;QACnC3C,iBAAiB,EAAEqD,KAAI,CAACpD,kBAAkB;QAC1CoB,iBAAiB,EAAEgC,KAAI,CAAChC,iBAAiB;QACzCC,iBAAiB,EAAE+B,KAAI,CAAC/B,iBAAiB;QACzCC,iBAAiB,EAAE8B,KAAI,CAAC9B,iBAAiB;QACzCE,gBAAgB,EAAE4B,KAAI,CAAC5B,gBAAgB;QACvCC,qBAAqB,EAAE2B,KAAI,CAAC3B,qBAAqB;QACjDC,mBAAmB,EAAE0B,KAAI,CAAC1B,mBAAmB;QAC7Ce,mBAAmB,EAAEW,KAAI,CAACX,mBAAmB;QAC7Cd,2BAA2B,EAAEyB,KAAI,CAACzB,2BAA2B;QAC7DC,6BAA6B,EAAEwB,KAAI,CAACxB,6BAA6B;QACjEK,SAAS,EAAE0B,KAAK,IAAI;UAChB;UACA,IAAIP,KAAI,CAACE,kBAAkB,EAAE;YACzBC,YAAY,CAACH,KAAI,CAACE,kBAAkB,CAAC;YACrCF,KAAI,CAACE,kBAAkB,GAAGlD,SAAS;UACvC;UACA,IAAI,CAACgD,KAAI,CAAC/C,MAAM,EAAE;YACd+C,KAAI,CAACrB,KAAK,CAAC,sEAAsE,CAAC;YAClFqB,KAAI,CAACQ,oBAAoB,CAAC,CAAC;YAC3B;UACJ;UACAR,KAAI,CAACnB,SAAS,CAAC0B,KAAK,CAAC;QACzB,CAAC;QACDzB,YAAY,EAAEyB,KAAK,IAAI;UACnBP,KAAI,CAAClB,YAAY,CAACyB,KAAK,CAAC;QAC5B,CAAC;QACDrB,YAAY,EAAEqB,KAAK,IAAI;UACnBP,KAAI,CAACd,YAAY,CAACqB,KAAK,CAAC;QAC5B,CAAC;QACDpB,gBAAgB,EAAEsB,GAAG,IAAI;UACrBT,KAAI,CAACvD,aAAa,GAAGO,SAAS,CAAC,CAAC;UAChC,IAAIgD,KAAI,CAAC9C,KAAK,KAAKhB,eAAe,CAAC2D,YAAY,EAAE;YAC7C;YACAG,KAAI,CAAC5C,YAAY,CAAClB,eAAe,CAACuC,QAAQ,CAAC;UAC/C;UACA;UACA;UACAuB,KAAI,CAACb,gBAAgB,CAACsB,GAAG,CAAC;UAC1B,IAAIT,KAAI,CAAC/C,MAAM,EAAE;YACb+C,KAAI,CAACU,mBAAmB,CAAC,CAAC;UAC9B;QACJ,CAAC;QACDtB,gBAAgB,EAAEqB,GAAG,IAAI;UACrBT,KAAI,CAACZ,gBAAgB,CAACqB,GAAG,CAAC;QAC9B,CAAC;QACD1B,kBAAkB,EAAE4B,OAAO,IAAI;UAC3BX,KAAI,CAACjB,kBAAkB,CAAC4B,OAAO,CAAC;QACpC,CAAC;QACD3B,kBAAkB,EAAEuB,KAAK,IAAI;UACzBP,KAAI,CAAChB,kBAAkB,CAACuB,KAAK,CAAC;QAClC,CAAC;QACDtB,gBAAgB,EAAEsB,KAAK,IAAI;UACvBP,KAAI,CAACf,gBAAgB,CAACsB,KAAK,CAAC;QAChC;MACJ,CAAC,CAAC;MACFP,KAAI,CAACvD,aAAa,CAACmE,KAAK,CAAC,CAAC;IAAC;EAC/B;EACAN,gBAAgBA,CAAA,EAAG;IACf,IAAI9D,SAAS;IACb,IAAI,IAAI,CAACqE,gBAAgB,EAAE;MACvBrE,SAAS,GAAG,IAAI,CAACqE,gBAAgB,CAAC,CAAC;IACvC,CAAC,MACI,IAAI,IAAI,CAACC,SAAS,EAAE;MACrBtE,SAAS,GAAG,IAAIuE,SAAS,CAAC,IAAI,CAACD,SAAS,EAAE,IAAI,CAACtD,aAAa,CAACwD,gBAAgB,CAAC,CAAC,CAAC;IACpF,CAAC,MACI;MACD,MAAM,IAAIC,KAAK,CAAC,uDAAuD,CAAC;IAC5E;IACAzE,SAAS,CAAC0E,UAAU,GAAG,aAAa;IACpC,OAAO1E,SAAS;EACpB;EACAkE,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAAC9C,mBAAmB,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACe,KAAK,CAAE,qCAAoC,IAAI,CAACf,mBAAoB,IAAG,CAAC;MAC7E,IAAI,CAACuD,YAAY,GAAGf,UAAU,CAAC,MAAM;QACjC,IAAI,IAAI,CAACtC,iBAAiB,KAAK3B,oBAAoB,CAACiF,WAAW,EAAE;UAC7D,IAAI,CAACxD,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,GAAG,CAAC;UACvD;UACA,IAAI,IAAI,CAACC,iBAAiB,KAAK,CAAC,EAAE;YAC9B,IAAI,CAACD,mBAAmB,GAAGyD,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC1D,mBAAmB,EAAE,IAAI,CAACC,iBAAiB,CAAC;UACzF;QACJ;QACA,IAAI,CAAC+B,QAAQ,CAAC,CAAC;MACnB,CAAC,EAAE,IAAI,CAAChC,mBAAmB,CAAC;IAChC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUkC,UAAUA,CAAA,EAAe;IAAA,IAAAyB,MAAA;IAAA,OAAAtB,iBAAA,YAAduB,OAAO,GAAG,CAAC,CAAC;MACzB,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK,IAAI,KAAK;MACpC,MAAMC,aAAa,GAAGH,MAAI,CAACtE,MAAM;MACjC,IAAI0E,UAAU;MACd,IAAIJ,MAAI,CAACrE,KAAK,KAAKhB,eAAe,CAACuC,QAAQ,EAAE;QACzC8C,MAAI,CAAC5C,KAAK,CAAE,sCAAqC,CAAC;QAClD,OAAOiD,OAAO,CAACC,OAAO,CAAC,CAAC;MAC5B;MACAN,MAAI,CAACnE,YAAY,CAAClB,eAAe,CAAC2D,YAAY,CAAC;MAC/C;MACA0B,MAAI,CAAC3D,mBAAmB,GAAG,CAAC;MAC5B;MACA,IAAI2D,MAAI,CAACJ,YAAY,EAAE;QACnBhB,YAAY,CAACoB,MAAI,CAACJ,YAAY,CAAC;QAC/BI,MAAI,CAACJ,YAAY,GAAGnE,SAAS;MACjC;MACA,IAAIuE,MAAI,CAAC9E,aAAa;MAClB;MACA8E,MAAI,CAAC/E,SAAS,CAACsF,UAAU,KAAK1F,gBAAgB,CAAC2F,MAAM,EAAE;QACvD,MAAMC,oBAAoB,GAAGT,MAAI,CAAC9E,aAAa,CAAC0C,gBAAgB;QAChE;QACAwC,UAAU,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEI,MAAM,KAAK;UAC1C;UACAV,MAAI,CAAC9E,aAAa,CAAC0C,gBAAgB,GAAGsB,GAAG,IAAI;YACzCuB,oBAAoB,CAACvB,GAAG,CAAC;YACzBoB,OAAO,CAAC,CAAC;UACb,CAAC;QACL,CAAC,CAAC;MACN,CAAC,MACI;QACD;QACAN,MAAI,CAACnE,YAAY,CAAClB,eAAe,CAACuC,QAAQ,CAAC;QAC3C,OAAOmD,OAAO,CAACC,OAAO,CAAC,CAAC;MAC5B;MACA,IAAIJ,KAAK,EAAE;QACPF,MAAI,CAAC9E,aAAa,EAAEyF,gBAAgB,CAAC,CAAC;MAC1C,CAAC,MACI,IAAIR,aAAa,EAAE;QACpBH,MAAI,CAACf,oBAAoB,CAAC,CAAC;MAC/B;MACA,OAAOmB,UAAU;IAAC,GAAAQ,KAAA,OAAAC,SAAA;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;EACI/B,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC5D,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAAC4D,eAAe,CAAC,CAAC;IACxC;EACJ;EACAG,oBAAoBA,CAAA,EAAG;IACnB;IACA,IAAI,IAAI,CAAC/D,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAAC4F,OAAO,CAAC,CAAC;IAChC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACC,MAAM,EAAE;IACZ,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB;IACA,IAAI,CAAC/F,aAAa,CAAC6F,OAAO,CAACC,MAAM,CAAC;EACtC;EACAC,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAAC1F,SAAS,EAAE;MACjB,MAAM,IAAI2F,SAAS,CAAC,yCAAyC,CAAC;IAClE;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAACC,SAAS,EAAEC,QAAQ,EAAE;IACjC,IAAI,CAACJ,gBAAgB,CAAC,CAAC;IACvB;IACA,IAAI,CAAC/F,aAAa,CAACiG,eAAe,CAACC,SAAS,EAAEC,QAAQ,CAAC;EAC3D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACC,WAAW,EAAEF,QAAQ,EAAEG,OAAO,GAAG,CAAC,CAAC,EAAE;IAC3C,IAAI,CAACP,gBAAgB,CAAC,CAAC;IACvB;IACA,OAAO,IAAI,CAAC/F,aAAa,CAACoG,SAAS,CAACC,WAAW,EAAEF,QAAQ,EAAEG,OAAO,CAAC;EACvE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACC,EAAE,EAAEF,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1B,IAAI,CAACP,gBAAgB,CAAC,CAAC;IACvB;IACA,IAAI,CAAC/F,aAAa,CAACuG,WAAW,CAACC,EAAE,EAAEF,OAAO,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIG,KAAKA,CAACC,aAAa,EAAE;IACjB,IAAI,CAACX,gBAAgB,CAAC,CAAC;IACvB;IACA,OAAO,IAAI,CAAC/F,aAAa,CAACyG,KAAK,CAACC,aAAa,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,MAAMA,CAACD,aAAa,EAAE;IAClB,IAAI,CAACX,gBAAgB,CAAC,CAAC;IACvB;IACA,IAAI,CAAC/F,aAAa,CAAC2G,MAAM,CAACD,aAAa,CAAC;EAC5C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,KAAKA,CAACF,aAAa,EAAE;IACjB,IAAI,CAACX,gBAAgB,CAAC,CAAC;IACvB;IACA,IAAI,CAAC/F,aAAa,CAAC4G,KAAK,CAACF,aAAa,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,GAAGA,CAACC,SAAS,EAAEC,cAAc,EAAET,OAAO,GAAG,CAAC,CAAC,EAAE;IACzC,IAAI,CAACP,gBAAgB,CAAC,CAAC;IACvB;IACA,IAAI,CAAC/F,aAAa,CAAC6G,GAAG,CAACC,SAAS,EAAEC,cAAc,EAAET,OAAO,CAAC;EAC9D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIU,IAAIA,CAACF,SAAS,EAAEC,cAAc,EAAET,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1C,IAAI,CAACP,gBAAgB,CAAC,CAAC;IACvB;IACA,IAAI,CAAC/F,aAAa,CAACgH,IAAI,CAACF,SAAS,EAAEC,cAAc,EAAET,OAAO,CAAC;EAC/D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}