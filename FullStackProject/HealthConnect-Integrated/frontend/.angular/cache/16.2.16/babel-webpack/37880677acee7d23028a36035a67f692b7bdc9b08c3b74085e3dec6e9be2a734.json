{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  AjaxBasedTransport = require('./lib/ajax-based'),\n  XhrReceiver = require('./receiver/xhr'),\n  XDRObject = require('./sender/xdr');\n\n// According to:\n//   http://stackoverflow.com/questions/1641507/detect-browser-support-for-cross-domain-xmlhttprequests\n//   http://hacks.mozilla.org/2009/07/cross-site-xmlhttprequest-with-cors/\n\nfunction XdrStreamingTransport(transUrl) {\n  if (!XDRObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr_streaming', XhrReceiver, XDRObject);\n}\ninherits(XdrStreamingTransport, AjaxBasedTransport);\nXdrStreamingTransport.enabled = function (info) {\n  if (info.cookie_needed || info.nullOrigin) {\n    return false;\n  }\n  return XDRObject.enabled && info.sameScheme;\n};\nXdrStreamingTransport.transportName = 'xdr-streaming';\nXdrStreamingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XdrStreamingTransport;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}