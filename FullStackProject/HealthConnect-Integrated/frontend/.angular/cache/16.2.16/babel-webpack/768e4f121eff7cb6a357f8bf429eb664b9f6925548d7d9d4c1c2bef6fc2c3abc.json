{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SharedModule } from '../shared/shared.module';\nimport { ChatModule } from '../chat/chat.module';\nimport { DoctorDashboardComponent } from './dashboard/dashboard.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'dashboard',\n  pathMatch: 'full'\n}, {\n  path: 'dashboard',\n  component: DoctorDashboardComponent\n}];\nexport let DoctorModule = /*#__PURE__*/(() => {\n  class DoctorModule {\n    static {\n      this.ɵfac = function DoctorModule_Factory(t) {\n        return new (t || DoctorModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: DoctorModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [SharedModule, ChatModule, RouterModule.forChild(routes)]\n      });\n    }\n  }\n  return DoctorModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}