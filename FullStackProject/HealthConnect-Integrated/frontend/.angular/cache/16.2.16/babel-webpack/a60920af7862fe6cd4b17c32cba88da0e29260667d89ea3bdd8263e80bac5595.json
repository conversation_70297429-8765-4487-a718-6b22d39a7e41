{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { AppointmentType } from '../../core/models/appointment.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../core/services/appointment.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction AppointmentBookingComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"i\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.success, \" \");\n  }\n}\nfunction AppointmentBookingComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelement(1, \"i\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction AppointmentBookingComponent_form_11_option_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const doctor_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", doctor_r15.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", doctor_r15.fullName, \" - \", doctor_r15.specialization || \"General Practice\", \" \");\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1, \" Please select a doctor. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_9_p_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.selectedDoctor.affiliation, \" \");\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 7)(2, \"div\", 47)(3, \"div\", 48);\n    i0.ɵɵelement(4, \"i\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h6\", 50);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 51);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, AppointmentBookingComponent_form_11_div_9_p_10_Template, 2, 1, \"p\", 52);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r5.selectedDoctor.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.selectedDoctor.specialization || \"General Practice\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.selectedDoctor.affiliation);\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1, \" Please select a date. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_form_11_option_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r17.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r17.label, \" \");\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1, \" Please select an appointment type. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55)(2, \"span\", 56);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtext(4, \" Loading available time slots... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_26_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"label\", 60);\n    i0.ɵɵelement(2, \"input\", 61);\n    i0.ɵɵelementStart(3, \"span\", 62);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const slot_r19 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r18.getTimeSlotValue(slot_r19));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.getTimeSlotDisplay(slot_r19), \" \");\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 23);\n    i0.ɵɵtemplate(2, AppointmentBookingComponent_form_11_div_26_div_2_Template, 5, 2, \"div\", 58);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.availableSlots);\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" No available time slots for the selected date. Please choose a different date. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_form_11_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1, \" Please provide a reason for the visit. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_form_11_span_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 65)(1, \"span\", 56);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppointmentBookingComponent_form_11_i_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 66);\n  }\n}\nfunction AppointmentBookingComponent_form_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 15);\n    i0.ɵɵlistener(\"ngSubmit\", function AppointmentBookingComponent_form_11_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 16)(2, \"label\", 17);\n    i0.ɵɵtext(3, \"Select Doctor *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"select\", 18)(5, \"option\", 19);\n    i0.ɵɵtext(6, \"Choose a doctor...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, AppointmentBookingComponent_form_11_option_7_Template, 2, 3, \"option\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, AppointmentBookingComponent_form_11_div_8_Template, 2, 0, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, AppointmentBookingComponent_form_11_div_9_Template, 11, 3, \"div\", 22);\n    i0.ɵɵelementStart(10, \"div\", 23)(11, \"div\", 24)(12, \"label\", 25);\n    i0.ɵɵtext(13, \"Appointment Date *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 26);\n    i0.ɵɵtemplate(15, AppointmentBookingComponent_form_11_div_15_Template, 2, 0, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 24)(17, \"label\", 27);\n    i0.ɵɵtext(18, \"Appointment Type *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"select\", 28);\n    i0.ɵɵtemplate(20, AppointmentBookingComponent_form_11_option_20_Template, 2, 2, \"option\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, AppointmentBookingComponent_form_11_div_21_Template, 2, 0, \"div\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 29)(23, \"label\", 30);\n    i0.ɵɵtext(24, \"Available Time Slots *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, AppointmentBookingComponent_form_11_div_25_Template, 5, 0, \"div\", 31);\n    i0.ɵɵtemplate(26, AppointmentBookingComponent_form_11_div_26_Template, 3, 1, \"div\", 32);\n    i0.ɵɵtemplate(27, AppointmentBookingComponent_form_11_div_27_Template, 3, 0, \"div\", 33);\n    i0.ɵɵelementStart(28, \"div\", 34);\n    i0.ɵɵtext(29, \" Please select a time slot. \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 29)(31, \"label\", 35);\n    i0.ɵɵtext(32, \"Reason for Visit *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(33, \"input\", 36);\n    i0.ɵɵtemplate(34, AppointmentBookingComponent_form_11_div_34_Template, 2, 0, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 16)(36, \"label\", 37);\n    i0.ɵɵtext(37, \"Additional Notes (Optional)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"textarea\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 39)(40, \"button\", 40);\n    i0.ɵɵelement(41, \"i\", 41);\n    i0.ɵɵtext(42, \" Back to Doctors \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"button\", 42);\n    i0.ɵɵtemplate(44, AppointmentBookingComponent_form_11_span_44_Template, 3, 0, \"span\", 43);\n    i0.ɵɵtemplate(45, AppointmentBookingComponent_form_11_i_45_Template, 1, 0, \"i\", 44);\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    let tmp_3_0;\n    let tmp_5_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_10_0;\n    let tmp_14_0;\n    let tmp_15_0;\n    let tmp_16_0;\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.bookingForm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_1_0 = ctx_r2.bookingForm.get(\"doctorId\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r2.bookingForm.get(\"doctorId\")) == null ? null : tmp_1_0.touched));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.doctors);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r2.bookingForm.get(\"doctorId\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r2.bookingForm.get(\"doctorId\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedDoctor);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_5_0 = ctx_r2.bookingForm.get(\"date\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r2.bookingForm.get(\"date\")) == null ? null : tmp_5_0.touched));\n    i0.ɵɵproperty(\"min\", ctx_r2.getTodayDate());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r2.bookingForm.get(\"date\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r2.bookingForm.get(\"date\")) == null ? null : tmp_7_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_8_0 = ctx_r2.bookingForm.get(\"type\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx_r2.bookingForm.get(\"type\")) == null ? null : tmp_8_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.appointmentTypes);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx_r2.bookingForm.get(\"type\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx_r2.bookingForm.get(\"type\")) == null ? null : tmp_10_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading && ctx_r2.availableSlots.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading && ctx_r2.selectedDoctor && ctx_r2.availableSlots.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"display\", ((tmp_14_0 = ctx_r2.bookingForm.get(\"timeSlot\")) == null ? null : tmp_14_0.invalid) && ((tmp_14_0 = ctx_r2.bookingForm.get(\"timeSlot\")) == null ? null : tmp_14_0.touched) ? \"block\" : \"none\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_15_0 = ctx_r2.bookingForm.get(\"reasonForVisit\")) == null ? null : tmp_15_0.invalid) && ((tmp_15_0 = ctx_r2.bookingForm.get(\"reasonForVisit\")) == null ? null : tmp_15_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_16_0 = ctx_r2.bookingForm.get(\"reasonForVisit\")) == null ? null : tmp_16_0.invalid) && ((tmp_16_0 = ctx_r2.bookingForm.get(\"reasonForVisit\")) == null ? null : tmp_16_0.touched));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.bookingForm.invalid || ctx_r2.submitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.submitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.submitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.submitting ? \"Booking...\" : \"Book Appointment\", \" \");\n  }\n}\nexport let AppointmentBookingComponent = /*#__PURE__*/(() => {\n  class AppointmentBookingComponent {\n    constructor(fb, appointmentService, route, router) {\n      this.fb = fb;\n      this.appointmentService = appointmentService;\n      this.route = route;\n      this.router = router;\n      this.doctors = [];\n      this.selectedDoctor = null;\n      this.timeSlots = [];\n      this.availableSlots = [];\n      this.loading = false;\n      this.submitting = false;\n      this.error = null;\n      this.success = null;\n      this.appointmentTypes = [{\n        value: AppointmentType.IN_PERSON,\n        label: 'In Person'\n      }, {\n        value: AppointmentType.VIDEO_CALL,\n        label: 'Video Call'\n      }];\n      this.initializeForm();\n    }\n    initializeForm() {\n      this.bookingForm = this.fb.group({\n        doctorId: ['', Validators.required],\n        date: ['', Validators.required],\n        timeSlot: ['', Validators.required],\n        type: [AppointmentType.IN_PERSON, Validators.required],\n        reasonForVisit: ['', Validators.required],\n        notes: ['']\n      });\n    }\n    ngOnInit() {\n      this.loadDoctors();\n      // Check if doctor ID is provided in query params\n      this.route.queryParams.subscribe(params => {\n        if (params['doctorId']) {\n          this.bookingForm.patchValue({\n            doctorId: params['doctorId']\n          });\n          this.onDoctorChange();\n        }\n      });\n      // Set minimum date to tomorrow (backend requires future dates)\n      const tomorrow = new Date();\n      tomorrow.setDate(tomorrow.getDate() + 1);\n      const tomorrowStr = tomorrow.toISOString().split('T')[0];\n      this.bookingForm.get('date')?.setValue(tomorrowStr);\n      // Watch for form changes\n      this.bookingForm.get('doctorId')?.valueChanges.subscribe(() => this.onDoctorChange());\n      this.bookingForm.get('date')?.valueChanges.subscribe(() => this.onDateChange());\n    }\n    loadDoctors() {\n      this.appointmentService.getDoctors().subscribe({\n        next: doctors => {\n          this.doctors = doctors;\n        },\n        error: error => {\n          this.error = 'Failed to load doctors. Please try again.';\n          console.error('Error loading doctors:', error);\n        }\n      });\n    }\n    onDoctorChange() {\n      const doctorId = this.bookingForm.get('doctorId')?.value;\n      if (doctorId) {\n        this.selectedDoctor = this.doctors.find(d => d.id == doctorId) || null;\n        this.loadTimeSlots();\n      } else {\n        this.selectedDoctor = null;\n        this.timeSlots = [];\n        this.availableSlots = [];\n      }\n    }\n    onDateChange() {\n      if (this.selectedDoctor) {\n        this.loadTimeSlots();\n      }\n    }\n    loadTimeSlots() {\n      const doctorId = this.bookingForm.get('doctorId')?.value;\n      const date = this.bookingForm.get('date')?.value;\n      if (!doctorId || !date) return;\n      this.loading = true;\n      this.appointmentService.getAvailableTimeSlots(doctorId, date).subscribe({\n        next: slots => {\n          this.timeSlots = slots;\n          this.availableSlots = slots.filter(slot => slot.available);\n          this.loading = false;\n          // Clear selected time slot if it's no longer available\n          const currentSlot = this.bookingForm.get('timeSlot')?.value;\n          if (currentSlot && !this.availableSlots.find(slot => slot.startTime === currentSlot.split('-')[0] && slot.endTime === currentSlot.split('-')[1])) {\n            this.bookingForm.patchValue({\n              timeSlot: ''\n            });\n          }\n        },\n        error: error => {\n          this.error = 'Failed to load available time slots. Please try again.';\n          this.loading = false;\n          console.error('Error loading time slots:', error);\n        }\n      });\n    }\n    onSubmit() {\n      console.log('Form submission started');\n      console.log('Form valid:', this.bookingForm.valid);\n      console.log('Form errors:', this.bookingForm.errors);\n      console.log('Form value:', this.bookingForm.value);\n      if (this.bookingForm.valid) {\n        this.submitting = true;\n        this.error = null;\n        this.success = null;\n        const formValue = this.bookingForm.value;\n        if (!formValue.timeSlot || !formValue.timeSlot.includes('-')) {\n          this.error = 'Please select a valid time slot.';\n          this.submitting = false;\n          console.error('Invalid time slot format:', formValue.timeSlot);\n          return;\n        }\n        const [startTime, endTime] = formValue.timeSlot.split('-');\n        const request = {\n          doctorId: parseInt(formValue.doctorId),\n          date: formValue.date,\n          startTime: startTime,\n          endTime: endTime,\n          type: formValue.type,\n          reasonForVisit: formValue.reasonForVisit,\n          notes: formValue.notes || undefined\n        };\n        console.log('Submitting appointment request:', request);\n        console.log('Form value:', formValue);\n        this.appointmentService.createAppointment(request).subscribe({\n          next: appointment => {\n            this.success = 'Appointment booked successfully!';\n            this.submitting = false;\n            // Redirect to appointment details after 2 seconds\n            setTimeout(() => {\n              this.router.navigate(['/appointments', appointment.id]);\n            }, 2000);\n          },\n          error: error => {\n            this.error = 'Failed to book appointment. Please try again.';\n            this.submitting = false;\n            console.error('Error booking appointment:', error);\n            console.error('Error details:', error.error);\n            console.error('Request data:', request);\n          }\n        });\n      } else {\n        console.log('Form is invalid');\n        console.log('Form errors:', this.getFormValidationErrors());\n        this.error = 'Please fill in all required fields correctly.';\n        this.markFormGroupTouched();\n      }\n    }\n    getFormValidationErrors() {\n      const formErrors = {};\n      Object.keys(this.bookingForm.controls).forEach(key => {\n        const controlErrors = this.bookingForm.get(key)?.errors;\n        if (controlErrors) {\n          formErrors[key] = controlErrors;\n        }\n      });\n      return formErrors;\n    }\n    markFormGroupTouched() {\n      Object.keys(this.bookingForm.controls).forEach(key => {\n        const control = this.bookingForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    getTimeSlotDisplay(slot) {\n      return `${this.formatTime(slot.startTime)} - ${this.formatTime(slot.endTime)}`;\n    }\n    getTimeSlotValue(slot) {\n      return `${slot.startTime}-${slot.endTime}`;\n    }\n    formatTime(time) {\n      const [hours, minutes] = time.split(':');\n      const hour = parseInt(hours);\n      const ampm = hour >= 12 ? 'PM' : 'AM';\n      const displayHour = hour % 12 || 12;\n      return `${displayHour}:${minutes} ${ampm}`;\n    }\n    getTodayDate() {\n      // Return tomorrow's date as minimum (backend requires future dates)\n      const tomorrow = new Date();\n      tomorrow.setDate(tomorrow.getDate() + 1);\n      return tomorrow.toISOString().split('T')[0];\n    }\n    static {\n      this.ɵfac = function AppointmentBookingComponent_Factory(t) {\n        return new (t || AppointmentBookingComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AppointmentService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppointmentBookingComponent,\n        selectors: [[\"app-appointment-booking\"]],\n        decls: 12,\n        vars: 3,\n        consts: [[1, \"container-fluid\", \"py-4\"], [1, \"row\", \"justify-content-center\"], [1, \"col-lg-8\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\", \"mb-0\"], [1, \"fas\", \"fa-calendar-plus\", \"me-2\"], [1, \"card-body\"], [\"class\", \"alert alert-success\", \"role\", \"alert\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [\"role\", \"alert\", 1, \"alert\", \"alert-success\"], [1, \"fas\", \"fa-check-circle\", \"me-2\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-2\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-4\"], [\"for\", \"doctorId\", 1, \"form-label\"], [\"id\", \"doctorId\", \"formControlName\", \"doctorId\", 1, \"form-select\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"class\", \"card bg-light mb-4\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-md-6\", \"mb-3\"], [\"for\", \"date\", 1, \"form-label\"], [\"type\", \"date\", \"id\", \"date\", \"formControlName\", \"date\", 1, \"form-control\", 3, \"min\"], [\"for\", \"type\", 1, \"form-label\"], [\"id\", \"type\", \"formControlName\", \"type\", 1, \"form-select\"], [1, \"mb-3\"], [\"for\", \"timeSlot\", 1, \"form-label\"], [\"class\", \"text-center py-3\", 4, \"ngIf\"], [\"class\", \"time-slots-grid\", 4, \"ngIf\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [1, \"invalid-feedback\"], [\"for\", \"reasonForVisit\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"reasonForVisit\", \"formControlName\", \"reasonForVisit\", \"placeholder\", \"e.g., Regular checkup, Follow-up, Consultation\", 1, \"form-control\"], [\"for\", \"notes\", 1, \"form-label\"], [\"id\", \"notes\", \"formControlName\", \"notes\", \"rows\", \"3\", \"placeholder\", \"Any additional information or special requests...\", 1, \"form-control\"], [1, \"d-flex\", \"justify-content-between\"], [\"type\", \"button\", \"routerLink\", \"/appointments/doctors\", 1, \"btn\", \"btn-outline-secondary\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", 4, \"ngIf\"], [\"class\", \"fas fa-calendar-check me-2\", 4, \"ngIf\"], [3, \"value\"], [1, \"card\", \"bg-light\", \"mb-4\"], [1, \"d-flex\", \"align-items-center\"], [1, \"avatar-circle\", \"me-3\"], [1, \"fas\", \"fa-user-md\"], [1, \"mb-1\"], [1, \"text-muted\", \"mb-1\"], [\"class\", \"text-muted mb-0\", 4, \"ngIf\"], [1, \"text-muted\", \"mb-0\"], [1, \"text-center\", \"py-3\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"text-primary\", \"me-2\"], [1, \"visually-hidden\"], [1, \"time-slots-grid\"], [\"class\", \"col-md-4 col-sm-6 mb-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\", \"col-sm-6\", \"mb-2\"], [1, \"time-slot-option\"], [\"type\", \"radio\", \"name\", \"timeSlot\", \"formControlName\", \"timeSlot\", 3, \"value\"], [1, \"time-slot-label\"], [1, \"alert\", \"alert-info\"], [1, \"fas\", \"fa-info-circle\", \"me-2\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [1, \"fas\", \"fa-calendar-check\", \"me-2\"]],\n        template: function AppointmentBookingComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h4\", 5);\n            i0.ɵɵelement(6, \"i\", 6);\n            i0.ɵɵtext(7, \"Book an Appointment \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"div\", 7);\n            i0.ɵɵtemplate(9, AppointmentBookingComponent_div_9_Template, 3, 1, \"div\", 8);\n            i0.ɵɵtemplate(10, AppointmentBookingComponent_div_10_Template, 3, 1, \"div\", 9);\n            i0.ɵɵtemplate(11, AppointmentBookingComponent_form_11_Template, 47, 26, \"form\", 10);\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.success);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.bookingForm);\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.RadioControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink],\n        styles: [\".avatar-circle[_ngcontent-%COMP%]{width:50px;height:50px;border-radius:50%;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);display:flex;align-items:center;justify-content:center;color:#fff;font-size:1.2rem;flex-shrink:0}.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%]{display:block;cursor:pointer;margin:0}.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]{display:none}.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%]   .time-slot-label[_ngcontent-%COMP%]{display:block;padding:.75rem 1rem;border:2px solid #e3e6f0;border-radius:.5rem;text-align:center;transition:all .3s ease;background:white;font-weight:500}.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%]   .time-slot-label[_ngcontent-%COMP%]:hover{border-color:#667eea;background:#f8f9fc}.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked + .time-slot-label[_ngcontent-%COMP%]{border-color:#667eea;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:#fff}.form-select[_ngcontent-%COMP%]:focus, .form-control[_ngcontent-%COMP%]:focus{border-color:#667eea;box-shadow:0 0 0 .2rem #667eea40}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);border:none;transition:all .3s ease}.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){background:linear-gradient(135deg,#5a6fd8 0%,#6a4190 100%);transform:translateY(-1px)}.btn-primary[_ngcontent-%COMP%]:disabled{background:#6c757d;opacity:.65}.alert-success[_ngcontent-%COMP%]{border-left:4px solid #28a745}.alert-danger[_ngcontent-%COMP%]{border-left:4px solid #e74a3b}.alert-info[_ngcontent-%COMP%]{border-left:4px solid #17a2b8}.card[_ngcontent-%COMP%]{border:1px solid #e3e6f0;box-shadow:0 .15rem 1.75rem #3a3b4526}.bg-light[_ngcontent-%COMP%]{background-color:#f8f9fc!important}\"]\n      });\n    }\n  }\n  return AppointmentBookingComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}