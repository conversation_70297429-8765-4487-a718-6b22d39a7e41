{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/appointment.service\";\nimport * as i2 from \"../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction AppointmentCalendarComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵelement(1, \"i\", 27);\n    i0.ɵɵtext(2, \" Book Appointment \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentCalendarComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"i\", 29);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction AppointmentCalendarComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31)(2, \"span\", 32);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 33);\n    i0.ɵɵtext(5, \"Loading calendar...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppointmentCalendarComponent_div_22_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dayName_r6 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", dayName_r6, \" \");\n  }\n}\nfunction AppointmentCalendarComponent_div_22_div_4_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"span\", 48);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const appointment_r12 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r10.getStatusBadgeClass(appointment_r12.status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.formatTime(appointment_r12.startTime), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.isDoctor() ? appointment_r12.patient.fullName : appointment_r12.doctor.fullName, \" \");\n  }\n}\nfunction AppointmentCalendarComponent_div_22_div_4_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" +\", day_r7.appointments.length - 2, \" more \");\n  }\n}\nfunction AppointmentCalendarComponent_div_22_div_4_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, AppointmentCalendarComponent_div_22_div_4_div_3_div_1_Template, 5, 3, \"div\", 45);\n    i0.ɵɵtemplate(2, AppointmentCalendarComponent_div_22_div_4_div_3_div_2_Template, 2, 1, \"div\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", day_r7.appointments.slice(0, 2));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", day_r7.appointments.length > 2);\n  }\n}\nfunction AppointmentCalendarComponent_div_22_div_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵelement(1, \"i\", 52);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0, a1, a2, a3) {\n  return {\n    \"other-month\": a0,\n    \"today\": a1,\n    \"has-appointments\": a2,\n    \"clickable\": a3\n  };\n};\nfunction AppointmentCalendarComponent_div_22_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵlistener(\"click\", function AppointmentCalendarComponent_div_22_div_4_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r16);\n      const day_r7 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.onDayClick(day_r7));\n    });\n    i0.ɵɵelementStart(1, \"div\", 41);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AppointmentCalendarComponent_div_22_div_4_div_3_Template, 3, 2, \"div\", 42);\n    i0.ɵɵtemplate(4, AppointmentCalendarComponent_div_22_div_4_div_4_Template, 2, 0, \"div\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r7 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(4, _c0, !day_r7.isCurrentMonth, day_r7.isToday, day_r7.appointments.length > 0, day_r7.isCurrentMonth));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", day_r7.date.getDate(), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", day_r7.appointments.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", day_r7.appointments.length === 0 && day_r7.isCurrentMonth && day_r7.date >= ctx_r5.currentDate);\n  }\n}\nfunction AppointmentCalendarComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵtemplate(2, AppointmentCalendarComponent_div_22_div_2_Template, 2, 1, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 37);\n    i0.ɵɵtemplate(4, AppointmentCalendarComponent_div_22_div_4_Template, 5, 9, \"div\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.dayNames);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.calendarDays);\n  }\n}\nexport let AppointmentCalendarComponent = /*#__PURE__*/(() => {\n  class AppointmentCalendarComponent {\n    constructor(appointmentService, authService, router) {\n      this.appointmentService = appointmentService;\n      this.authService = authService;\n      this.router = router;\n      this.currentDate = new Date();\n      this.currentMonth = new Date();\n      this.calendarDays = [];\n      this.appointments = [];\n      this.currentUser = null;\n      this.loading = false;\n      this.error = null;\n      this.monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];\n      this.dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\n    }\n    ngOnInit() {\n      this.currentUser = this.authService.getCurrentUser();\n      this.loadAppointments();\n      this.generateCalendar();\n    }\n    loadAppointments() {\n      this.loading = true;\n      this.error = null;\n      // Get appointments for the current month\n      const startDate = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth(), 1);\n      const endDate = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() + 1, 0);\n      this.appointmentService.getAppointments(undefined,\n      // status\n      undefined,\n      // type\n      startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0]).subscribe({\n        next: appointments => {\n          this.appointments = appointments;\n          this.generateCalendar();\n          this.loading = false;\n        },\n        error: error => {\n          this.error = 'Failed to load appointments.';\n          this.loading = false;\n          console.error('Error loading appointments:', error);\n        }\n      });\n    }\n    generateCalendar() {\n      const year = this.currentMonth.getFullYear();\n      const month = this.currentMonth.getMonth();\n      // First day of the month\n      const firstDay = new Date(year, month, 1);\n      // Last day of the month\n      const lastDay = new Date(year, month + 1, 0);\n      // Start from the first Sunday of the week containing the first day\n      const startDate = new Date(firstDay);\n      startDate.setDate(startDate.getDate() - startDate.getDay());\n      // End at the last Saturday of the week containing the last day\n      const endDate = new Date(lastDay);\n      endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));\n      this.calendarDays = [];\n      const currentDate = new Date(startDate);\n      while (currentDate <= endDate) {\n        const dayAppointments = this.getAppointmentsForDate(currentDate);\n        this.calendarDays.push({\n          date: new Date(currentDate),\n          isCurrentMonth: currentDate.getMonth() === month,\n          isToday: this.isSameDay(currentDate, new Date()),\n          appointments: dayAppointments\n        });\n        currentDate.setDate(currentDate.getDate() + 1);\n      }\n    }\n    getAppointmentsForDate(date) {\n      const dateString = date.toISOString().split('T')[0];\n      return this.appointments.filter(appointment => appointment.date === dateString);\n    }\n    isSameDay(date1, date2) {\n      return date1.getDate() === date2.getDate() && date1.getMonth() === date2.getMonth() && date1.getFullYear() === date2.getFullYear();\n    }\n    previousMonth() {\n      this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() - 1, 1);\n      this.loadAppointments();\n    }\n    nextMonth() {\n      this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() + 1, 1);\n      this.loadAppointments();\n    }\n    goToToday() {\n      this.currentMonth = new Date();\n      this.loadAppointments();\n    }\n    onDayClick(day) {\n      if (day.appointments.length > 0) {\n        // If there are appointments, show the first one\n        this.router.navigate(['/appointments', day.appointments[0].id]);\n      } else if (day.isCurrentMonth && day.date >= new Date()) {\n        // If it's a future date in current month, navigate to booking\n        this.router.navigate(['/appointments/book'], {\n          queryParams: {\n            date: day.date.toISOString().split('T')[0]\n          }\n        });\n      }\n    }\n    getStatusDisplayName(status) {\n      return this.appointmentService.getStatusDisplayName(status);\n    }\n    getStatusBadgeClass(status) {\n      return this.appointmentService.getStatusBadgeClass(status);\n    }\n    formatTime(timeString) {\n      const [hours, minutes] = timeString.split(':');\n      const hour = parseInt(hours);\n      const ampm = hour >= 12 ? 'PM' : 'AM';\n      const displayHour = hour % 12 || 12;\n      return `${displayHour}:${minutes} ${ampm}`;\n    }\n    getCurrentMonthYear() {\n      return `${this.monthNames[this.currentMonth.getMonth()]} ${this.currentMonth.getFullYear()}`;\n    }\n    isDoctor() {\n      return this.currentUser?.role === 'DOCTOR';\n    }\n    isPatient() {\n      return this.currentUser?.role === 'PATIENT';\n    }\n    static {\n      this.ɵfac = function AppointmentCalendarComponent_Factory(t) {\n        return new (t || AppointmentCalendarComponent)(i0.ɵɵdirectiveInject(i1.AppointmentService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppointmentCalendarComponent,\n        selectors: [[\"app-appointment-calendar\"]],\n        decls: 43,\n        vars: 5,\n        consts: [[1, \"container-fluid\", \"py-4\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"card-title\", \"mb-0\"], [1, \"fas\", \"fa-calendar\", \"me-2\"], [\"class\", \"btn btn-primary\", \"routerLink\", \"/appointments/book\", 4, \"ngIf\"], [1, \"card-body\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [1, \"calendar-nav\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"d-flex\", \"align-items-center\"], [1, \"mb-0\", \"me-3\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-chevron-right\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"calendar-grid\", 4, \"ngIf\"], [1, \"calendar-legend\", \"mt-4\"], [1, \"d-flex\", \"flex-wrap\", \"gap-3\"], [1, \"legend-item\"], [1, \"legend-color\", \"badge-primary\"], [1, \"legend-color\", \"badge-warning\"], [1, \"legend-color\", \"badge-success\"], [1, \"legend-color\", \"badge-danger\"], [\"routerLink\", \"/appointments/book\", 1, \"btn\", \"btn-primary\"], [1, \"fas\", \"fa-plus\", \"me-2\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-2\"], [1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\", \"text-muted\"], [1, \"calendar-grid\"], [1, \"calendar-header\"], [\"class\", \"day-header\", 4, \"ngFor\", \"ngForOf\"], [1, \"calendar-body\"], [\"class\", \"calendar-day\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"day-header\"], [1, \"calendar-day\", 3, \"ngClass\", \"click\"], [1, \"day-number\"], [\"class\", \"appointments-container\", 4, \"ngIf\"], [\"class\", \"add-appointment\", 4, \"ngIf\"], [1, \"appointments-container\"], [\"class\", \"appointment-item\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"more-appointments\", 4, \"ngIf\"], [1, \"appointment-item\", 3, \"ngClass\"], [1, \"appointment-time\"], [1, \"appointment-title\"], [1, \"more-appointments\"], [1, \"add-appointment\"], [1, \"fas\", \"fa-plus\"]],\n        template: function AppointmentCalendarComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h4\", 5);\n            i0.ɵɵelement(6, \"i\", 6);\n            i0.ɵɵtext(7, \"Appointment Calendar \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(8, AppointmentCalendarComponent_button_8_Template, 3, 0, \"button\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"div\", 8);\n            i0.ɵɵtemplate(10, AppointmentCalendarComponent_div_10_Template, 3, 1, \"div\", 9);\n            i0.ɵɵelementStart(11, \"div\", 10)(12, \"button\", 11);\n            i0.ɵɵlistener(\"click\", function AppointmentCalendarComponent_Template_button_click_12_listener() {\n              return ctx.previousMonth();\n            });\n            i0.ɵɵelement(13, \"i\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"div\", 13)(15, \"h5\", 14);\n            i0.ɵɵtext(16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"button\", 15);\n            i0.ɵɵlistener(\"click\", function AppointmentCalendarComponent_Template_button_click_17_listener() {\n              return ctx.goToToday();\n            });\n            i0.ɵɵtext(18, \" Today \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(19, \"button\", 11);\n            i0.ɵɵlistener(\"click\", function AppointmentCalendarComponent_Template_button_click_19_listener() {\n              return ctx.nextMonth();\n            });\n            i0.ɵɵelement(20, \"i\", 16);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(21, AppointmentCalendarComponent_div_21_Template, 6, 0, \"div\", 17);\n            i0.ɵɵtemplate(22, AppointmentCalendarComponent_div_22_Template, 5, 2, \"div\", 18);\n            i0.ɵɵelementStart(23, \"div\", 19)(24, \"h6\");\n            i0.ɵɵtext(25, \"Legend:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"div\", 20)(27, \"div\", 21);\n            i0.ɵɵelement(28, \"span\", 22);\n            i0.ɵɵelementStart(29, \"span\");\n            i0.ɵɵtext(30, \"Confirmed\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 21);\n            i0.ɵɵelement(32, \"span\", 23);\n            i0.ɵɵelementStart(33, \"span\");\n            i0.ɵɵtext(34, \"Pending\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(35, \"div\", 21);\n            i0.ɵɵelement(36, \"span\", 24);\n            i0.ɵɵelementStart(37, \"span\");\n            i0.ɵɵtext(38, \"Completed\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(39, \"div\", 21);\n            i0.ɵɵelement(40, \"span\", 25);\n            i0.ɵɵelementStart(41, \"span\");\n            i0.ɵɵtext(42, \"Cancelled\");\n            i0.ɵɵelementEnd()()()()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.isPatient());\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.getCurrentMonthYear());\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i3.RouterLink],\n        styles: [\".calendar-grid[_ngcontent-%COMP%]{border:1px solid #e3e6f0;border-radius:.5rem;overflow:hidden}.calendar-header[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(7,1fr);background:#f8f9fc;border-bottom:1px solid #e3e6f0}.day-header[_ngcontent-%COMP%]{padding:1rem;text-align:center;font-weight:600;color:#5a5c69;border-right:1px solid #e3e6f0}.day-header[_ngcontent-%COMP%]:last-child{border-right:none}.calendar-body[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(7,1fr)}.calendar-day[_ngcontent-%COMP%]{min-height:120px;padding:.5rem;border-right:1px solid #e3e6f0;border-bottom:1px solid #e3e6f0;position:relative;background:white;transition:background-color .2s ease}.calendar-day[_ngcontent-%COMP%]:nth-child(7n){border-right:none}.calendar-day.clickable[_ngcontent-%COMP%]{cursor:pointer}.calendar-day.clickable[_ngcontent-%COMP%]:hover{background:#f8f9fc}.calendar-day.other-month[_ngcontent-%COMP%]{background:#f8f9fc;color:#858796}.calendar-day.today[_ngcontent-%COMP%]{background:#e3f2fd;border:2px solid #667eea}.calendar-day.has-appointments[_ngcontent-%COMP%]{background:#fff3cd}.day-number[_ngcontent-%COMP%]{font-weight:600;margin-bottom:.5rem;color:#5a5c69}.appointments-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.25rem}.appointment-item[_ngcontent-%COMP%]{padding:.25rem .5rem;border-radius:.25rem;font-size:.75rem;color:#fff;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.appointment-time[_ngcontent-%COMP%]{font-weight:600;display:block}.appointment-title[_ngcontent-%COMP%]{display:block;opacity:.9}.more-appointments[_ngcontent-%COMP%]{font-size:.7rem;color:#858796;text-align:center;margin-top:.25rem}.add-appointment[_ngcontent-%COMP%]{position:absolute;bottom:.5rem;right:.5rem;color:#858796;font-size:.8rem;opacity:0;transition:opacity .2s ease}.calendar-day.clickable[_ngcontent-%COMP%]:hover   .add-appointment[_ngcontent-%COMP%]{opacity:1}.badge-primary[_ngcontent-%COMP%]{background-color:#4e73df}.badge-warning[_ngcontent-%COMP%]{background-color:#f6c23e;color:#1a1a1a}.badge-success[_ngcontent-%COMP%]{background-color:#1cc88a}.badge-danger[_ngcontent-%COMP%]{background-color:#e74a3b}.badge-info[_ngcontent-%COMP%]{background-color:#36b9cc}.badge-secondary[_ngcontent-%COMP%]{background-color:#858796}.calendar-legend[_ngcontent-%COMP%]{border-top:1px solid #e3e6f0;padding-top:1rem}.legend-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-size:.9rem}.legend-color[_ngcontent-%COMP%]{width:16px;height:16px;border-radius:.25rem;display:inline-block}.calendar-nav[_ngcontent-%COMP%]{background:#f8f9fc;padding:1rem;border-radius:.5rem;border:1px solid #e3e6f0}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);border:none;transition:all .3s ease}.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#5a6fd8 0%,#6a4190 100%);transform:translateY(-1px)}.btn-outline-primary[_ngcontent-%COMP%]{border-color:#667eea;color:#667eea}.btn-outline-primary[_ngcontent-%COMP%]:hover{background-color:#667eea;border-color:#667eea}.card[_ngcontent-%COMP%]{border:1px solid #e3e6f0;box-shadow:0 .15rem 1.75rem #3a3b4526}.alert-danger[_ngcontent-%COMP%]{border-left:4px solid #e74a3b}.spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}\"]\n      });\n    }\n  }\n  return AppointmentCalendarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}