{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { AppointmentStatus, AppointmentType } from '../models/appointment.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AppointmentService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:8080/api';\n  }\n  // Doctor discovery\n  getDoctors(specialization) {\n    let params = new HttpParams();\n    if (specialization) {\n      params = params.set('specialization', specialization);\n    }\n    return this.http.get(`${this.apiUrl}/doctors`, {\n      params\n    });\n  }\n  getDoctor(id) {\n    return this.http.get(`${this.apiUrl}/doctors/${id}`);\n  }\n  getSpecializations() {\n    return this.http.get(`${this.apiUrl}/doctors/specializations`);\n  }\n  // Time slots\n  getAvailableTimeSlots(doctorId, date) {\n    const params = new HttpParams().set('date', date);\n    return this.http.get(`${this.apiUrl}/doctors/${doctorId}/time-slots`, {\n      params\n    });\n  }\n  // Appointments CRUD\n  getAppointments(status, type, startDate, endDate) {\n    let params = new HttpParams();\n    if (status) params = params.set('status', status);\n    if (type) params = params.set('type', type);\n    if (startDate) params = params.set('startDate', startDate);\n    if (endDate) params = params.set('endDate', endDate);\n    return this.http.get(`${this.apiUrl}/appointments`, {\n      params\n    });\n  }\n  getAppointment(id) {\n    return this.http.get(`${this.apiUrl}/appointments/${id}`);\n  }\n  createAppointment(request) {\n    return this.http.post(`${this.apiUrl}/appointments`, request);\n  }\n  updateAppointment(id, request) {\n    return this.http.put(`${this.apiUrl}/appointments/${id}`, request);\n  }\n  cancelAppointment(id) {\n    return this.http.delete(`${this.apiUrl}/appointments/${id}`);\n  }\n  getTodayAppointments() {\n    return this.http.get(`${this.apiUrl}/appointments/today`);\n  }\n  getPatientAppointments() {\n    return this.http.get(`${this.apiUrl}/appointments`);\n  }\n  // Helper methods\n  getStatusDisplayName(status) {\n    switch (status) {\n      case AppointmentStatus.PENDING:\n        return 'Pending';\n      case AppointmentStatus.SCHEDULED:\n        return 'Scheduled';\n      case AppointmentStatus.CONFIRMED:\n        return 'Confirmed';\n      case AppointmentStatus.COMPLETED:\n        return 'Completed';\n      case AppointmentStatus.CANCELLED:\n        return 'Cancelled';\n      case AppointmentStatus.NO_SHOW:\n        return 'No Show';\n      default:\n        return status;\n    }\n  }\n  getTypeDisplayName(type) {\n    switch (type) {\n      case AppointmentType.IN_PERSON:\n        return 'In Person';\n      case AppointmentType.VIDEO_CALL:\n        return 'Video Call';\n      default:\n        return type;\n    }\n  }\n  getStatusBadgeClass(status) {\n    switch (status) {\n      case AppointmentStatus.PENDING:\n        return 'badge-warning';\n      case AppointmentStatus.SCHEDULED:\n        return 'badge-info';\n      case AppointmentStatus.CONFIRMED:\n        return 'badge-primary';\n      case AppointmentStatus.COMPLETED:\n        return 'badge-success';\n      case AppointmentStatus.CANCELLED:\n        return 'badge-danger';\n      case AppointmentStatus.NO_SHOW:\n        return 'badge-secondary';\n      default:\n        return 'badge-secondary';\n    }\n  }\n  static {\n    this.ɵfac = function AppointmentService_Factory(t) {\n      return new (t || AppointmentService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AppointmentService,\n      factory: AppointmentService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "AppointmentStatus", "AppointmentType", "AppointmentService", "constructor", "http", "apiUrl", "getDoctors", "specialization", "params", "set", "get", "getDoctor", "id", "getSpecializations", "getAvailableTimeSlots", "doctorId", "date", "getAppointments", "status", "type", "startDate", "endDate", "getAppointment", "createAppointment", "request", "post", "updateAppointment", "put", "cancelAppointment", "delete", "getTodayAppointments", "getPatientAppointments", "getStatusDisplayName", "PENDING", "SCHEDULED", "CONFIRMED", "COMPLETED", "CANCELLED", "NO_SHOW", "getTypeDisplayName", "IN_PERSON", "VIDEO_CALL", "getStatusBadgeClass", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/core/services/appointment.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { \n  Appointment, \n  AppointmentRequest, \n  AppointmentUpdateRequest, \n  TimeSlot, \n  Doctor,\n  AppointmentStatus,\n  AppointmentType \n} from '../models/appointment.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AppointmentService {\n  private apiUrl = 'http://localhost:8080/api';\n\n  constructor(private http: HttpClient) {}\n\n  // Doctor discovery\n  getDoctors(specialization?: string): Observable<Doctor[]> {\n    let params = new HttpParams();\n    if (specialization) {\n      params = params.set('specialization', specialization);\n    }\n    return this.http.get<Doctor[]>(`${this.apiUrl}/doctors`, { params });\n  }\n\n  getDoctor(id: number): Observable<Doctor> {\n    return this.http.get<Doctor>(`${this.apiUrl}/doctors/${id}`);\n  }\n\n  getSpecializations(): Observable<string[]> {\n    return this.http.get<string[]>(`${this.apiUrl}/doctors/specializations`);\n  }\n\n  // Time slots\n  getAvailableTimeSlots(doctorId: number, date: string): Observable<TimeSlot[]> {\n    const params = new HttpParams().set('date', date);\n    return this.http.get<TimeSlot[]>(`${this.apiUrl}/doctors/${doctorId}/time-slots`, { params });\n  }\n\n  // Appointments CRUD\n  getAppointments(\n    status?: AppointmentStatus,\n    type?: AppointmentType,\n    startDate?: string,\n    endDate?: string\n  ): Observable<Appointment[]> {\n    let params = new HttpParams();\n    if (status) params = params.set('status', status);\n    if (type) params = params.set('type', type);\n    if (startDate) params = params.set('startDate', startDate);\n    if (endDate) params = params.set('endDate', endDate);\n    \n    return this.http.get<Appointment[]>(`${this.apiUrl}/appointments`, { params });\n  }\n\n  getAppointment(id: number): Observable<Appointment> {\n    return this.http.get<Appointment>(`${this.apiUrl}/appointments/${id}`);\n  }\n\n  createAppointment(request: AppointmentRequest): Observable<Appointment> {\n    return this.http.post<Appointment>(`${this.apiUrl}/appointments`, request);\n  }\n\n  updateAppointment(id: number, request: AppointmentUpdateRequest): Observable<Appointment> {\n    return this.http.put<Appointment>(`${this.apiUrl}/appointments/${id}`, request);\n  }\n\n  cancelAppointment(id: number): Observable<void> {\n    return this.http.delete<void>(`${this.apiUrl}/appointments/${id}`);\n  }\n\n  getTodayAppointments(): Observable<Appointment[]> {\n    return this.http.get<Appointment[]>(`${this.apiUrl}/appointments/today`);\n  }\n\n  getPatientAppointments(): Observable<Appointment[]> {\n    return this.http.get<Appointment[]>(`${this.apiUrl}/appointments`);\n  }\n\n  // Helper methods\n  getStatusDisplayName(status: AppointmentStatus): string {\n    switch (status) {\n      case AppointmentStatus.PENDING: return 'Pending';\n      case AppointmentStatus.SCHEDULED: return 'Scheduled';\n      case AppointmentStatus.CONFIRMED: return 'Confirmed';\n      case AppointmentStatus.COMPLETED: return 'Completed';\n      case AppointmentStatus.CANCELLED: return 'Cancelled';\n      case AppointmentStatus.NO_SHOW: return 'No Show';\n      default: return status;\n    }\n  }\n\n  getTypeDisplayName(type: AppointmentType): string {\n    switch (type) {\n      case AppointmentType.IN_PERSON: return 'In Person';\n      case AppointmentType.VIDEO_CALL: return 'Video Call';\n      default: return type;\n    }\n  }\n\n  getStatusBadgeClass(status: AppointmentStatus): string {\n    switch (status) {\n      case AppointmentStatus.PENDING: return 'badge-warning';\n      case AppointmentStatus.SCHEDULED: return 'badge-info';\n      case AppointmentStatus.CONFIRMED: return 'badge-primary';\n      case AppointmentStatus.COMPLETED: return 'badge-success';\n      case AppointmentStatus.CANCELLED: return 'badge-danger';\n      case AppointmentStatus.NO_SHOW: return 'badge-secondary';\n      default: return 'badge-secondary';\n    }\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAMEC,iBAAiB,EACjBC,eAAe,QACV,6BAA6B;;;AAKpC,OAAM,MAAOC,kBAAkB;EAG7BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,2BAA2B;EAEL;EAEvC;EACAC,UAAUA,CAACC,cAAuB;IAChC,IAAIC,MAAM,GAAG,IAAIT,UAAU,EAAE;IAC7B,IAAIQ,cAAc,EAAE;MAClBC,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,gBAAgB,EAAEF,cAAc,CAAC;;IAEvD,OAAO,IAAI,CAACH,IAAI,CAACM,GAAG,CAAW,GAAG,IAAI,CAACL,MAAM,UAAU,EAAE;MAAEG;IAAM,CAAE,CAAC;EACtE;EAEAG,SAASA,CAACC,EAAU;IAClB,OAAO,IAAI,CAACR,IAAI,CAACM,GAAG,CAAS,GAAG,IAAI,CAACL,MAAM,YAAYO,EAAE,EAAE,CAAC;EAC9D;EAEAC,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACT,IAAI,CAACM,GAAG,CAAW,GAAG,IAAI,CAACL,MAAM,0BAA0B,CAAC;EAC1E;EAEA;EACAS,qBAAqBA,CAACC,QAAgB,EAAEC,IAAY;IAClD,MAAMR,MAAM,GAAG,IAAIT,UAAU,EAAE,CAACU,GAAG,CAAC,MAAM,EAAEO,IAAI,CAAC;IACjD,OAAO,IAAI,CAACZ,IAAI,CAACM,GAAG,CAAa,GAAG,IAAI,CAACL,MAAM,YAAYU,QAAQ,aAAa,EAAE;MAAEP;IAAM,CAAE,CAAC;EAC/F;EAEA;EACAS,eAAeA,CACbC,MAA0B,EAC1BC,IAAsB,EACtBC,SAAkB,EAClBC,OAAgB;IAEhB,IAAIb,MAAM,GAAG,IAAIT,UAAU,EAAE;IAC7B,IAAImB,MAAM,EAAEV,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,QAAQ,EAAES,MAAM,CAAC;IACjD,IAAIC,IAAI,EAAEX,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAEU,IAAI,CAAC;IAC3C,IAAIC,SAAS,EAAEZ,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,WAAW,EAAEW,SAAS,CAAC;IAC1D,IAAIC,OAAO,EAAEb,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,SAAS,EAAEY,OAAO,CAAC;IAEpD,OAAO,IAAI,CAACjB,IAAI,CAACM,GAAG,CAAgB,GAAG,IAAI,CAACL,MAAM,eAAe,EAAE;MAAEG;IAAM,CAAE,CAAC;EAChF;EAEAc,cAAcA,CAACV,EAAU;IACvB,OAAO,IAAI,CAACR,IAAI,CAACM,GAAG,CAAc,GAAG,IAAI,CAACL,MAAM,iBAAiBO,EAAE,EAAE,CAAC;EACxE;EAEAW,iBAAiBA,CAACC,OAA2B;IAC3C,OAAO,IAAI,CAACpB,IAAI,CAACqB,IAAI,CAAc,GAAG,IAAI,CAACpB,MAAM,eAAe,EAAEmB,OAAO,CAAC;EAC5E;EAEAE,iBAAiBA,CAACd,EAAU,EAAEY,OAAiC;IAC7D,OAAO,IAAI,CAACpB,IAAI,CAACuB,GAAG,CAAc,GAAG,IAAI,CAACtB,MAAM,iBAAiBO,EAAE,EAAE,EAAEY,OAAO,CAAC;EACjF;EAEAI,iBAAiBA,CAAChB,EAAU;IAC1B,OAAO,IAAI,CAACR,IAAI,CAACyB,MAAM,CAAO,GAAG,IAAI,CAACxB,MAAM,iBAAiBO,EAAE,EAAE,CAAC;EACpE;EAEAkB,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC1B,IAAI,CAACM,GAAG,CAAgB,GAAG,IAAI,CAACL,MAAM,qBAAqB,CAAC;EAC1E;EAEA0B,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAAC3B,IAAI,CAACM,GAAG,CAAgB,GAAG,IAAI,CAACL,MAAM,eAAe,CAAC;EACpE;EAEA;EACA2B,oBAAoBA,CAACd,MAAyB;IAC5C,QAAQA,MAAM;MACZ,KAAKlB,iBAAiB,CAACiC,OAAO;QAAE,OAAO,SAAS;MAChD,KAAKjC,iBAAiB,CAACkC,SAAS;QAAE,OAAO,WAAW;MACpD,KAAKlC,iBAAiB,CAACmC,SAAS;QAAE,OAAO,WAAW;MACpD,KAAKnC,iBAAiB,CAACoC,SAAS;QAAE,OAAO,WAAW;MACpD,KAAKpC,iBAAiB,CAACqC,SAAS;QAAE,OAAO,WAAW;MACpD,KAAKrC,iBAAiB,CAACsC,OAAO;QAAE,OAAO,SAAS;MAChD;QAAS,OAAOpB,MAAM;;EAE1B;EAEAqB,kBAAkBA,CAACpB,IAAqB;IACtC,QAAQA,IAAI;MACV,KAAKlB,eAAe,CAACuC,SAAS;QAAE,OAAO,WAAW;MAClD,KAAKvC,eAAe,CAACwC,UAAU;QAAE,OAAO,YAAY;MACpD;QAAS,OAAOtB,IAAI;;EAExB;EAEAuB,mBAAmBA,CAACxB,MAAyB;IAC3C,QAAQA,MAAM;MACZ,KAAKlB,iBAAiB,CAACiC,OAAO;QAAE,OAAO,eAAe;MACtD,KAAKjC,iBAAiB,CAACkC,SAAS;QAAE,OAAO,YAAY;MACrD,KAAKlC,iBAAiB,CAACmC,SAAS;QAAE,OAAO,eAAe;MACxD,KAAKnC,iBAAiB,CAACoC,SAAS;QAAE,OAAO,eAAe;MACxD,KAAKpC,iBAAiB,CAACqC,SAAS;QAAE,OAAO,cAAc;MACvD,KAAKrC,iBAAiB,CAACsC,OAAO;QAAE,OAAO,iBAAiB;MACxD;QAAS,OAAO,iBAAiB;;EAErC;;;uBAnGWpC,kBAAkB,EAAAyC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlB5C,kBAAkB;MAAA6C,OAAA,EAAlB7C,kBAAkB,CAAA8C,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}