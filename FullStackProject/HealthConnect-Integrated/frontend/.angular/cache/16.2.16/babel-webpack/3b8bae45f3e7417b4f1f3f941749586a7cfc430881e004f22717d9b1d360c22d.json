{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function repeatWhen(notifier) {\n  return operate((source, subscriber) => {\n    let innerSub;\n    let syncResub = false;\n    let completions$;\n    let isNotifierComplete = false;\n    let isMainComplete = false;\n    const checkComplete = () => isMainComplete && isNotifierComplete && (subscriber.complete(), true);\n    const getCompletionSubject = () => {\n      if (!completions$) {\n        completions$ = new Subject();\n        innerFrom(notifier(completions$)).subscribe(createOperatorSubscriber(subscriber, () => {\n          if (innerSub) {\n            subscribeForRepeatWhen();\n          } else {\n            syncResub = true;\n          }\n        }, () => {\n          isNotifierComplete = true;\n          checkComplete();\n        }));\n      }\n      return completions$;\n    };\n    const subscribeForRepeatWhen = () => {\n      isMainComplete = false;\n      innerSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, () => {\n        isMainComplete = true;\n        !checkComplete() && getCompletionSubject().next();\n      }));\n      if (syncResub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        syncResub = false;\n        subscribeForRepeatWhen();\n      }\n    };\n    subscribeForRepeatWhen();\n  });\n}", "map": {"version": 3, "names": ["innerFrom", "Subject", "operate", "createOperatorSubscriber", "repeatWhen", "notifier", "source", "subscriber", "innerSub", "syncResub", "completions$", "isNotifierComplete", "isMainComplete", "checkComplete", "complete", "getCompletionSubject", "subscribe", "subscribeForRepeatWhen", "undefined", "next", "unsubscribe"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/rxjs/dist/esm/internal/operators/repeatWhen.js"], "sourcesContent": ["import { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function repeatWhen(notifier) {\n    return operate((source, subscriber) => {\n        let innerSub;\n        let syncResub = false;\n        let completions$;\n        let isNotifierComplete = false;\n        let isMainComplete = false;\n        const checkComplete = () => isMainComplete && isNotifierComplete && (subscriber.complete(), true);\n        const getCompletionSubject = () => {\n            if (!completions$) {\n                completions$ = new Subject();\n                innerFrom(notifier(completions$)).subscribe(createOperatorSubscriber(subscriber, () => {\n                    if (innerSub) {\n                        subscribeForRepeatWhen();\n                    }\n                    else {\n                        syncResub = true;\n                    }\n                }, () => {\n                    isNotifierComplete = true;\n                    checkComplete();\n                }));\n            }\n            return completions$;\n        };\n        const subscribeForRepeatWhen = () => {\n            isMainComplete = false;\n            innerSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, () => {\n                isMainComplete = true;\n                !checkComplete() && getCompletionSubject().next();\n            }));\n            if (syncResub) {\n                innerSub.unsubscribe();\n                innerSub = null;\n                syncResub = false;\n                subscribeForRepeatWhen();\n            }\n        };\n        subscribeForRepeatWhen();\n    });\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,yBAAyB;AACnD,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,UAAUA,CAACC,QAAQ,EAAE;EACjC,OAAOH,OAAO,CAAC,CAACI,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,QAAQ;IACZ,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,YAAY;IAChB,IAAIC,kBAAkB,GAAG,KAAK;IAC9B,IAAIC,cAAc,GAAG,KAAK;IAC1B,MAAMC,aAAa,GAAGA,CAAA,KAAMD,cAAc,IAAID,kBAAkB,KAAKJ,UAAU,CAACO,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC;IACjG,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;MAC/B,IAAI,CAACL,YAAY,EAAE;QACfA,YAAY,GAAG,IAAIT,OAAO,CAAC,CAAC;QAC5BD,SAAS,CAACK,QAAQ,CAACK,YAAY,CAAC,CAAC,CAACM,SAAS,CAACb,wBAAwB,CAACI,UAAU,EAAE,MAAM;UACnF,IAAIC,QAAQ,EAAE;YACVS,sBAAsB,CAAC,CAAC;UAC5B,CAAC,MACI;YACDR,SAAS,GAAG,IAAI;UACpB;QACJ,CAAC,EAAE,MAAM;UACLE,kBAAkB,GAAG,IAAI;UACzBE,aAAa,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;MACP;MACA,OAAOH,YAAY;IACvB,CAAC;IACD,MAAMO,sBAAsB,GAAGA,CAAA,KAAM;MACjCL,cAAc,GAAG,KAAK;MACtBJ,QAAQ,GAAGF,MAAM,CAACU,SAAS,CAACb,wBAAwB,CAACI,UAAU,EAAEW,SAAS,EAAE,MAAM;QAC9EN,cAAc,GAAG,IAAI;QACrB,CAACC,aAAa,CAAC,CAAC,IAAIE,oBAAoB,CAAC,CAAC,CAACI,IAAI,CAAC,CAAC;MACrD,CAAC,CAAC,CAAC;MACH,IAAIV,SAAS,EAAE;QACXD,QAAQ,CAACY,WAAW,CAAC,CAAC;QACtBZ,QAAQ,GAAG,IAAI;QACfC,SAAS,GAAG,KAAK;QACjBQ,sBAAsB,CAAC,CAAC;MAC5B;IACJ,CAAC;IACDA,sBAAsB,CAAC,CAAC;EAC5B,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}