{"ast": null, "code": "'use strict';\n\nvar eventUtils = require('./event'),\n  browser = require('./browser');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:utils:iframe');\n}\nmodule.exports = {\n  WPrefix: '_jp',\n  currentWindowId: null,\n  polluteGlobalNamespace: function () {\n    if (!(module.exports.WPrefix in global)) {\n      global[module.exports.WPrefix] = {};\n    }\n  },\n  postMessage: function (type, data) {\n    if (global.parent !== global) {\n      global.parent.postMessage(JSON.stringify({\n        windowId: module.exports.currentWindowId,\n        type: type,\n        data: data || ''\n      }), '*');\n    } else {\n      debug('Cannot postMessage, no parent window.', type, data);\n    }\n  },\n  createIframe: function (iframeUrl, errorCallback) {\n    var iframe = global.document.createElement('iframe');\n    var tref, unloadRef;\n    var unattach = function () {\n      debug('unattach');\n      clearTimeout(tref);\n      // Explorer had problems with that.\n      try {\n        iframe.onload = null;\n      } catch (x) {\n        // intentionally empty\n      }\n      iframe.onerror = null;\n    };\n    var cleanup = function () {\n      debug('cleanup');\n      if (iframe) {\n        unattach();\n        // This timeout makes chrome fire onbeforeunload event\n        // within iframe. Without the timeout it goes straight to\n        // onunload.\n        setTimeout(function () {\n          if (iframe) {\n            iframe.parentNode.removeChild(iframe);\n          }\n          iframe = null;\n        }, 0);\n        eventUtils.unloadDel(unloadRef);\n      }\n    };\n    var onerror = function (err) {\n      debug('onerror', err);\n      if (iframe) {\n        cleanup();\n        errorCallback(err);\n      }\n    };\n    var post = function (msg, origin) {\n      debug('post', msg, origin);\n      setTimeout(function () {\n        try {\n          // When the iframe is not loaded, IE raises an exception\n          // on 'contentWindow'.\n          if (iframe && iframe.contentWindow) {\n            iframe.contentWindow.postMessage(msg, origin);\n          }\n        } catch (x) {\n          // intentionally empty\n        }\n      }, 0);\n    };\n    iframe.src = iframeUrl;\n    iframe.style.display = 'none';\n    iframe.style.position = 'absolute';\n    iframe.onerror = function () {\n      onerror('onerror');\n    };\n    iframe.onload = function () {\n      debug('onload');\n      // `onload` is triggered before scripts on the iframe are\n      // executed. Give it few seconds to actually load stuff.\n      clearTimeout(tref);\n      tref = setTimeout(function () {\n        onerror('onload timeout');\n      }, 2000);\n    };\n    global.document.body.appendChild(iframe);\n    tref = setTimeout(function () {\n      onerror('timeout');\n    }, 15000);\n    unloadRef = eventUtils.unloadAdd(cleanup);\n    return {\n      post: post,\n      cleanup: cleanup,\n      loaded: unattach\n    };\n  }\n\n  /* eslint no-undef: \"off\", new-cap: \"off\" */,\n  createHtmlfile: function (iframeUrl, errorCallback) {\n    var axo = ['Active'].concat('Object').join('X');\n    var doc = new global[axo]('htmlfile');\n    var tref, unloadRef;\n    var iframe;\n    var unattach = function () {\n      clearTimeout(tref);\n      iframe.onerror = null;\n    };\n    var cleanup = function () {\n      if (doc) {\n        unattach();\n        eventUtils.unloadDel(unloadRef);\n        iframe.parentNode.removeChild(iframe);\n        iframe = doc = null;\n        CollectGarbage();\n      }\n    };\n    var onerror = function (r) {\n      debug('onerror', r);\n      if (doc) {\n        cleanup();\n        errorCallback(r);\n      }\n    };\n    var post = function (msg, origin) {\n      try {\n        // When the iframe is not loaded, IE raises an exception\n        // on 'contentWindow'.\n        setTimeout(function () {\n          if (iframe && iframe.contentWindow) {\n            iframe.contentWindow.postMessage(msg, origin);\n          }\n        }, 0);\n      } catch (x) {\n        // intentionally empty\n      }\n    };\n    doc.open();\n    doc.write('<html><s' + 'cript>' + 'document.domain=\"' + global.document.domain + '\";' + '</s' + 'cript></html>');\n    doc.close();\n    doc.parentWindow[module.exports.WPrefix] = global[module.exports.WPrefix];\n    var c = doc.createElement('div');\n    doc.body.appendChild(c);\n    iframe = doc.createElement('iframe');\n    c.appendChild(iframe);\n    iframe.src = iframeUrl;\n    iframe.onerror = function () {\n      onerror('onerror');\n    };\n    tref = setTimeout(function () {\n      onerror('timeout');\n    }, 15000);\n    unloadRef = eventUtils.unloadAdd(cleanup);\n    return {\n      post: post,\n      cleanup: cleanup,\n      loaded: unattach\n    };\n  }\n};\nmodule.exports.iframeEnabled = false;\nif (global.document) {\n  // postMessage misbehaves in konqueror 4.6.5 - the messages are delivered with\n  // huge delay, or not at all.\n  module.exports.iframeEnabled = (typeof global.postMessage === 'function' || typeof global.postMessage === 'object') && !browser.isKonqueror();\n}", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}