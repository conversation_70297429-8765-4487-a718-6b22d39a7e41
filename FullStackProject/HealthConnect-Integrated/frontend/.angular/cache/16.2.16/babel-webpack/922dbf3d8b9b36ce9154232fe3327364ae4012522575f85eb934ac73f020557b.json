{"ast": null, "code": "import { switchMap } from './switchMap';\nimport { identity } from '../util/identity';\nexport function switchAll() {\n  return switchMap(identity);\n}", "map": {"version": 3, "names": ["switchMap", "identity", "switchAll"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/rxjs/dist/esm/internal/operators/switchAll.js"], "sourcesContent": ["import { switchMap } from './switchMap';\nimport { identity } from '../util/identity';\nexport function switchAll() {\n    return switchMap(identity);\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAO,SAASC,SAASA,CAAA,EAAG;EACxB,OAAOF,SAAS,CAACC,QAAQ,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}