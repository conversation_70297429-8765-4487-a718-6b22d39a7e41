{"ast": null, "code": "/**\n * Possible states for the IStompSocket\n */\nexport var StompSocketState = /*#__PURE__*/function (StompSocketState) {\n  StompSocketState[StompSocketState[\"CONNECTING\"] = 0] = \"CONNECTING\";\n  StompSocketState[StompSocketState[\"OPEN\"] = 1] = \"OPEN\";\n  StompSocketState[StompSocketState[\"CLOSING\"] = 2] = \"CLOSING\";\n  StompSocketState[StompSocketState[\"CLOSED\"] = 3] = \"CLOSED\";\n  return StompSocketState;\n}(StompSocketState || {});\n/**\n * Possible activation state\n */\nexport var ActivationState = /*#__PURE__*/function (ActivationState) {\n  ActivationState[ActivationState[\"ACTIVE\"] = 0] = \"ACTIVE\";\n  ActivationState[ActivationState[\"DEACTIVATING\"] = 1] = \"DEACTIVATING\";\n  ActivationState[ActivationState[\"INACTIVE\"] = 2] = \"INACTIVE\";\n  return ActivationState;\n}(ActivationState || {});\n/**\n * Possible reconnection wait time modes\n */\nexport var ReconnectionTimeMode = /*#__PURE__*/function (ReconnectionTimeMode) {\n  ReconnectionTimeMode[ReconnectionTimeMode[\"LINEAR\"] = 0] = \"LINEAR\";\n  ReconnectionTimeMode[ReconnectionTimeMode[\"EXPONENTIAL\"] = 1] = \"EXPONENTIAL\";\n  return ReconnectionTimeMode;\n}(ReconnectionTimeMode || {});\n/**\n * Possible ticker strategies for outgoing heartbeat ping\n */\nexport var TickerStrategy = /*#__PURE__*/function (TickerStrategy) {\n  TickerStrategy[\"Interval\"] = \"interval\";\n  TickerStrategy[\"Worker\"] = \"worker\";\n  return TickerStrategy;\n}(TickerStrategy || {});\n\n//# sourceMappingURL=types.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}