{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { AppointmentStatus, AppointmentType } from '../../core/models/appointment.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../core/services/appointment.service\";\nimport * as i4 from \"../../core/services/auth.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../../shared/components/chat-access/chat-access.component\";\nfunction AppointmentDetailsComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"span\", 7);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 8);\n    i0.ɵɵtext(5, \"Loading appointment details...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppointmentDetailsComponent_div_4_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"i\", 31);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.success, \" \");\n  }\n}\nfunction AppointmentDetailsComponent_div_4_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.error, \" \");\n  }\n}\nfunction AppointmentDetailsComponent_div_4_form_29_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r11.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r11.label, \" \");\n  }\n}\nfunction AppointmentDetailsComponent_div_4_form_29_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r12.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r12.label, \" \");\n  }\n}\nfunction AppointmentDetailsComponent_div_4_form_29_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"label\", 52);\n    i0.ɵɵtext(2, \"Meeting Link\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 53);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentDetailsComponent_div_4_form_29_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 54)(1, \"span\", 7);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppointmentDetailsComponent_div_4_form_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 34);\n    i0.ɵɵlistener(\"ngSubmit\", function AppointmentDetailsComponent_div_4_form_29_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.onUpdate());\n    });\n    i0.ɵɵelementStart(1, \"div\", 35)(2, \"div\", 18)(3, \"label\", 36);\n    i0.ɵɵtext(4, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"select\", 37);\n    i0.ɵɵtemplate(6, AppointmentDetailsComponent_div_4_form_29_option_6_Template, 2, 2, \"option\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 18)(8, \"label\", 39);\n    i0.ɵɵtext(9, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"select\", 40);\n    i0.ɵɵtemplate(11, AppointmentDetailsComponent_div_4_form_29_option_11_Template, 2, 2, \"option\", 38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 41)(13, \"label\", 42);\n    i0.ɵɵtext(14, \"Reason for Visit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 41)(17, \"label\", 44);\n    i0.ɵɵtext(18, \"Notes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"textarea\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, AppointmentDetailsComponent_div_4_form_29_div_20_Template, 4, 0, \"div\", 46);\n    i0.ɵɵelementStart(21, \"div\", 47)(22, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function AppointmentDetailsComponent_div_4_form_29_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.toggleEdit());\n    });\n    i0.ɵɵtext(23, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 49);\n    i0.ɵɵtemplate(25, AppointmentDetailsComponent_div_4_form_29_span_25_Template, 3, 0, \"span\", 50);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    let tmp_3_0;\n    i0.ɵɵproperty(\"formGroup\", ctx_r4.editForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.statusOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.typeOptions);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r4.editForm.get(\"type\")) == null ? null : tmp_3_0.value) === \"VIDEO_CALL\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.editForm.invalid || ctx_r4.updating);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.updating);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.updating ? \"Updating...\" : \"Update Appointment\", \" \");\n  }\n}\nfunction AppointmentDetailsComponent_div_4_div_30_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 62);\n  }\n}\nfunction AppointmentDetailsComponent_div_4_div_30_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 63);\n  }\n}\nfunction AppointmentDetailsComponent_div_4_div_30_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 19)(2, \"h6\", 20);\n    i0.ɵɵelement(3, \"i\", 65);\n    i0.ɵɵtext(4, \" Notes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 22);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r18.appointment.notes);\n  }\n}\nfunction AppointmentDetailsComponent_div_4_div_30_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 19)(2, \"h6\", 20);\n    i0.ɵɵelement(3, \"i\", 62);\n    i0.ɵɵtext(4, \" Video Call \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"a\", 66);\n    i0.ɵɵelement(6, \"i\", 67);\n    i0.ɵɵtext(7, \" Join Meeting \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"href\", ctx_r19.appointment.meetingLink, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AppointmentDetailsComponent_div_4_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 17)(2, \"div\", 18)(3, \"div\", 19)(4, \"h6\", 20);\n    i0.ɵɵtemplate(5, AppointmentDetailsComponent_div_4_div_30_i_5_Template, 1, 0, \"i\", 55);\n    i0.ɵɵtemplate(6, AppointmentDetailsComponent_div_4_div_30_i_6_Template, 1, 0, \"i\", 56);\n    i0.ɵɵtext(7, \" Appointment Type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 22);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 18)(11, \"div\", 19)(12, \"h6\", 20);\n    i0.ɵɵelement(13, \"i\", 57);\n    i0.ɵɵtext(14, \" Reason for Visit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 22);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(17, AppointmentDetailsComponent_div_4_div_30_div_17_Template, 7, 1, \"div\", 58);\n    i0.ɵɵtemplate(18, AppointmentDetailsComponent_div_4_div_30_div_18_Template, 8, 1, \"div\", 58);\n    i0.ɵɵelementStart(19, \"div\", 59)(20, \"div\", 18)(21, \"div\", 19)(22, \"h6\", 20);\n    i0.ɵɵelement(23, \"i\", 60);\n    i0.ɵɵtext(24, \" Created \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"p\", 22);\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 18)(29, \"div\", 19)(30, \"h6\", 20);\n    i0.ɵɵelement(31, \"i\", 61);\n    i0.ɵɵtext(32, \" Last Updated \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\", 22);\n    i0.ɵɵtext(34);\n    i0.ɵɵpipe(35, \"date\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.appointment.type === \"VIDEO_CALL\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.appointment.type === \"IN_PERSON\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.getTypeDisplayName(ctx_r5.appointment.type));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r5.appointment.reasonForVisit || \"Not specified\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.appointment.notes);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.appointment.meetingLink && ctx_r5.appointment.type === \"VIDEO_CALL\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(27, 8, ctx_r5.appointment.createdAt, \"medium\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(35, 11, ctx_r5.appointment.updatedAt, \"medium\"));\n  }\n}\nconst _c0 = function (a0, a1, a2, a4) {\n  return {\n    appointmentId: a0,\n    doctorId: a1,\n    patientId: a2,\n    chatType: \"PRE_APPOINTMENT\",\n    subject: a4,\n    buttonText: \"Chat Before Appointment\",\n    buttonClass: \"btn-info\",\n    size: \"sm\"\n  };\n};\nfunction AppointmentDetailsComponent_div_4_div_35_app_chat_access_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-chat-access\", 70);\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"config\", i0.ɵɵpureFunction4(1, _c0, ctx_r20.appointment.id, ctx_r20.appointment.doctor.id, ctx_r20.appointment.patient.id, \"Pre-appointment discussion for \" + ctx_r20.appointment.date));\n  }\n}\nconst _c1 = function (a0, a1, a2, a4) {\n  return {\n    appointmentId: a0,\n    doctorId: a1,\n    patientId: a2,\n    chatType: \"POST_APPOINTMENT\",\n    subject: a4,\n    buttonText: \"Follow-up Chat\",\n    buttonClass: \"btn-success\",\n    size: \"sm\"\n  };\n};\nfunction AppointmentDetailsComponent_div_4_div_35_app_chat_access_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-chat-access\", 70);\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"config\", i0.ɵɵpureFunction4(1, _c1, ctx_r21.appointment.id, ctx_r21.appointment.doctor.id, ctx_r21.appointment.patient.id, \"Follow-up for appointment on \" + ctx_r21.appointment.date));\n  }\n}\nfunction AppointmentDetailsComponent_div_4_div_35_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function AppointmentDetailsComponent_div_4_div_35_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r24.toggleEdit());\n    });\n    i0.ɵɵelement(1, \"i\", 61);\n    i0.ɵɵtext(2, \" Edit \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentDetailsComponent_div_4_div_35_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function AppointmentDetailsComponent_div_4_div_35_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r26.onCancel());\n    });\n    i0.ɵɵelement(1, \"i\", 76);\n    i0.ɵɵtext(2, \" Cancel Appointment \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c2 = function (a0, a1) {\n  return {\n    doctorId: a0,\n    patientId: a1,\n    chatType: \"GENERAL\",\n    buttonText: \"General Chat\",\n    buttonClass: \"btn-outline-primary\",\n    size: \"sm\"\n  };\n};\nfunction AppointmentDetailsComponent_div_4_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 68);\n    i0.ɵɵtemplate(2, AppointmentDetailsComponent_div_4_div_35_app_chat_access_2_Template, 1, 6, \"app-chat-access\", 69);\n    i0.ɵɵtemplate(3, AppointmentDetailsComponent_div_4_div_35_app_chat_access_3_Template, 1, 6, \"app-chat-access\", 69);\n    i0.ɵɵelement(4, \"app-chat-access\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 71);\n    i0.ɵɵtemplate(6, AppointmentDetailsComponent_div_4_div_35_button_6_Template, 3, 0, \"button\", 72);\n    i0.ɵɵtemplate(7, AppointmentDetailsComponent_div_4_div_35_button_7_Template, 3, 0, \"button\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isBeforeAppointment());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isAfterAppointment());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"config\", i0.ɵɵpureFunction2(5, _c2, ctx_r6.appointment.doctor.id, ctx_r6.appointment.patient.id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.canEdit());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.canCancel());\n  }\n}\nfunction AppointmentDetailsComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"h4\", 11);\n    i0.ɵɵelement(3, \"i\", 12);\n    i0.ɵɵtext(4, \"Appointment Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 13);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 14);\n    i0.ɵɵtemplate(8, AppointmentDetailsComponent_div_4_div_8_Template, 3, 1, \"div\", 15);\n    i0.ɵɵtemplate(9, AppointmentDetailsComponent_div_4_div_9_Template, 3, 1, \"div\", 16);\n    i0.ɵɵelementStart(10, \"div\", 17)(11, \"div\", 18)(12, \"div\", 19)(13, \"h6\", 20);\n    i0.ɵɵelement(14, \"i\", 21);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\", 22);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 23);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 18)(21, \"div\", 19)(22, \"h6\", 20);\n    i0.ɵɵelement(23, \"i\", 24);\n    i0.ɵɵtext(24, \" Date & Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"p\", 22);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\", 23);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(29, AppointmentDetailsComponent_div_4_form_29_Template, 27, 7, \"form\", 25);\n    i0.ɵɵtemplate(30, AppointmentDetailsComponent_div_4_div_30_Template, 36, 14, \"div\", 26);\n    i0.ɵɵelementStart(31, \"div\", 27)(32, \"button\", 28);\n    i0.ɵɵelement(33, \"i\", 29);\n    i0.ɵɵtext(34, \" Back to Appointments \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(35, AppointmentDetailsComponent_div_4_div_35_Template, 8, 8, \"div\", 26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusBadgeClass(ctx_r1.appointment.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusDisplayName(ctx_r1.appointment.status), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.success);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.error);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isDoctor() ? \"Patient\" : \"Doctor\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getOtherParty());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getOtherPartyDetails());\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.formatDate(ctx_r1.appointment.date));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.formatTime(ctx_r1.appointment.startTime), \" - \", ctx_r1.formatTime(ctx_r1.appointment.endTime), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isEditing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isEditing);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isEditing);\n  }\n}\nexport let AppointmentDetailsComponent = /*#__PURE__*/(() => {\n  class AppointmentDetailsComponent {\n    constructor(route, router, fb, appointmentService, authService) {\n      this.route = route;\n      this.router = router;\n      this.fb = fb;\n      this.appointmentService = appointmentService;\n      this.authService = authService;\n      this.appointment = null;\n      this.currentUser = null;\n      this.loading = false;\n      this.updating = false;\n      this.error = null;\n      this.success = null;\n      this.isEditing = false;\n      this.statusOptions = [{\n        value: AppointmentStatus.PENDING,\n        label: 'Pending'\n      }, {\n        value: AppointmentStatus.SCHEDULED,\n        label: 'Scheduled'\n      }, {\n        value: AppointmentStatus.CONFIRMED,\n        label: 'Confirmed'\n      }, {\n        value: AppointmentStatus.COMPLETED,\n        label: 'Completed'\n      }, {\n        value: AppointmentStatus.CANCELLED,\n        label: 'Cancelled'\n      }];\n      this.typeOptions = [{\n        value: AppointmentType.IN_PERSON,\n        label: 'In Person'\n      }, {\n        value: AppointmentType.VIDEO_CALL,\n        label: 'Video Call'\n      }];\n      this.editForm = this.fb.group({\n        status: ['', Validators.required],\n        type: ['', Validators.required],\n        reasonForVisit: ['', Validators.required],\n        notes: [''],\n        meetingLink: ['']\n      });\n    }\n    ngOnInit() {\n      this.currentUser = this.authService.getCurrentUser();\n      this.loadAppointment();\n    }\n    loadAppointment() {\n      const id = this.route.snapshot.paramMap.get('id');\n      if (!id) {\n        this.router.navigate(['/appointments']);\n        return;\n      }\n      this.loading = true;\n      this.appointmentService.getAppointment(parseInt(id)).subscribe({\n        next: appointment => {\n          this.appointment = appointment;\n          this.initializeForm();\n          this.loading = false;\n        },\n        error: error => {\n          this.error = 'Failed to load appointment details.';\n          this.loading = false;\n          console.error('Error loading appointment:', error);\n        }\n      });\n    }\n    initializeForm() {\n      if (this.appointment) {\n        this.editForm.patchValue({\n          status: this.appointment.status,\n          type: this.appointment.type,\n          reasonForVisit: this.appointment.reasonForVisit,\n          notes: this.appointment.notes || '',\n          meetingLink: this.appointment.meetingLink || ''\n        });\n      }\n    }\n    toggleEdit() {\n      this.isEditing = !this.isEditing;\n      if (!this.isEditing) {\n        this.initializeForm(); // Reset form if canceling edit\n      }\n    }\n\n    onUpdate() {\n      if (this.editForm.valid && this.appointment) {\n        this.updating = true;\n        this.error = null;\n        this.success = null;\n        const updateRequest = {\n          status: this.editForm.value.status,\n          type: this.editForm.value.type,\n          reasonForVisit: this.editForm.value.reasonForVisit,\n          notes: this.editForm.value.notes || undefined,\n          meetingLink: this.editForm.value.meetingLink || undefined\n        };\n        this.appointmentService.updateAppointment(this.appointment.id, updateRequest).subscribe({\n          next: updatedAppointment => {\n            this.appointment = updatedAppointment;\n            this.success = 'Appointment updated successfully!';\n            this.isEditing = false;\n            this.updating = false;\n          },\n          error: error => {\n            this.error = 'Failed to update appointment. Please try again.';\n            this.updating = false;\n            console.error('Error updating appointment:', error);\n          }\n        });\n      }\n    }\n    onCancel() {\n      if (this.appointment && confirm('Are you sure you want to cancel this appointment?')) {\n        this.appointmentService.cancelAppointment(this.appointment.id).subscribe({\n          next: () => {\n            this.router.navigate(['/appointments']);\n          },\n          error: error => {\n            this.error = 'Failed to cancel appointment. Please try again.';\n            console.error('Error canceling appointment:', error);\n          }\n        });\n      }\n    }\n    getStatusDisplayName(status) {\n      return this.appointmentService.getStatusDisplayName(status);\n    }\n    getTypeDisplayName(type) {\n      return this.appointmentService.getTypeDisplayName(type);\n    }\n    isBeforeAppointment() {\n      if (!this.appointment) return false;\n      const appointmentDateTime = new Date(`${this.appointment.date}T${this.appointment.startTime}`);\n      return appointmentDateTime > new Date();\n    }\n    isAfterAppointment() {\n      if (!this.appointment) return false;\n      const appointmentDateTime = new Date(`${this.appointment.date}T${this.appointment.endTime}`);\n      return appointmentDateTime < new Date();\n    }\n    getStatusBadgeClass(status) {\n      return this.appointmentService.getStatusBadgeClass(status);\n    }\n    formatDate(dateString) {\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        weekday: 'long',\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      });\n    }\n    formatTime(timeString) {\n      const [hours, minutes] = timeString.split(':');\n      const hour = parseInt(hours);\n      const ampm = hour >= 12 ? 'PM' : 'AM';\n      const displayHour = hour % 12 || 12;\n      return `${displayHour}:${minutes} ${ampm}`;\n    }\n    isDoctor() {\n      return this.currentUser?.role === 'DOCTOR';\n    }\n    isPatient() {\n      return this.currentUser?.role === 'PATIENT';\n    }\n    canEdit() {\n      if (!this.appointment) return false;\n      // Both doctor and patient can edit certain fields\n      return this.appointment.status !== AppointmentStatus.COMPLETED && this.appointment.status !== AppointmentStatus.CANCELLED;\n    }\n    canCancel() {\n      if (!this.appointment) return false;\n      return this.appointment.status === AppointmentStatus.PENDING || this.appointment.status === AppointmentStatus.SCHEDULED || this.appointment.status === AppointmentStatus.CONFIRMED;\n    }\n    getOtherParty() {\n      if (!this.appointment) return '';\n      if (this.isDoctor()) {\n        return this.appointment.patient.fullName;\n      } else {\n        return this.appointment.doctor.fullName;\n      }\n    }\n    getOtherPartyDetails() {\n      if (!this.appointment) return '';\n      if (this.isDoctor()) {\n        return this.appointment.patient.email;\n      } else {\n        const doctor = this.appointment.doctor;\n        return `${doctor.specialization || 'General Practice'} • ${doctor.affiliation || 'Private Practice'}`;\n      }\n    }\n    static {\n      this.ɵfac = function AppointmentDetailsComponent_Factory(t) {\n        return new (t || AppointmentDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.AppointmentService), i0.ɵɵdirectiveInject(i4.AuthService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppointmentDetailsComponent,\n        selectors: [[\"app-appointment-details\"]],\n        decls: 5,\n        vars: 2,\n        consts: [[1, \"container-fluid\", \"py-4\"], [1, \"row\", \"justify-content-center\"], [1, \"col-lg-8\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [\"class\", \"card\", 4, \"ngIf\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\", \"text-muted\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"card-title\", \"mb-0\"], [1, \"fas\", \"fa-calendar-alt\", \"me-2\"], [1, \"badge\", \"badge-lg\", 3, \"ngClass\"], [1, \"card-body\"], [\"class\", \"alert alert-success\", \"role\", \"alert\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [1, \"row\", \"mb-4\"], [1, \"col-md-6\"], [1, \"info-card\"], [1, \"info-title\"], [1, \"fas\", \"fa-user\", \"me-2\"], [1, \"info-value\"], [1, \"info-subtitle\"], [1, \"fas\", \"fa-calendar\", \"me-2\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\", \"mt-4\"], [\"routerLink\", \"/appointments\", 1, \"btn\", \"btn-outline-secondary\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\"], [\"role\", \"alert\", 1, \"alert\", \"alert-success\"], [1, \"fas\", \"fa-check-circle\", \"me-2\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-2\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"row\", \"mb-3\"], [\"for\", \"status\", 1, \"form-label\"], [\"id\", \"status\", \"formControlName\", \"status\", 1, \"form-select\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"type\", 1, \"form-label\"], [\"id\", \"type\", \"formControlName\", \"type\", 1, \"form-select\"], [1, \"mb-3\"], [\"for\", \"reasonForVisit\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"reasonForVisit\", \"formControlName\", \"reasonForVisit\", 1, \"form-control\"], [\"for\", \"notes\", 1, \"form-label\"], [\"id\", \"notes\", \"formControlName\", \"notes\", \"rows\", \"3\", 1, \"form-control\"], [\"class\", \"mb-3\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"me-2\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", 4, \"ngIf\"], [3, \"value\"], [\"for\", \"meetingLink\", 1, \"form-label\"], [\"type\", \"url\", \"id\", \"meetingLink\", \"formControlName\", \"meetingLink\", \"placeholder\", \"https://...\", 1, \"form-control\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [\"class\", \"fas fa-video me-2\", 4, \"ngIf\"], [\"class\", \"fas fa-user-friends me-2\", 4, \"ngIf\"], [1, \"fas\", \"fa-notes-medical\", \"me-2\"], [\"class\", \"mb-4\", 4, \"ngIf\"], [1, \"row\"], [1, \"fas\", \"fa-clock\", \"me-2\"], [1, \"fas\", \"fa-edit\", \"me-2\"], [1, \"fas\", \"fa-video\", \"me-2\"], [1, \"fas\", \"fa-user-friends\", \"me-2\"], [1, \"mb-4\"], [1, \"fas\", \"fa-sticky-note\", \"me-2\"], [\"target\", \"_blank\", 1, \"btn\", \"btn-primary\", 3, \"href\"], [1, \"fas\", \"fa-external-link-alt\", \"me-2\"], [\"role\", \"group\", 1, \"btn-group\", \"me-3\"], [3, \"config\", 4, \"ngIf\"], [3, \"config\"], [\"role\", \"group\", 1, \"btn-group\"], [\"class\", \"btn btn-outline-primary me-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-danger\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-outline-danger\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-2\"]],\n        template: function AppointmentDetailsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n            i0.ɵɵtemplate(3, AppointmentDetailsComponent_div_3_Template, 6, 0, \"div\", 3);\n            i0.ɵɵtemplate(4, AppointmentDetailsComponent_div_4_Template, 36, 13, \"div\", 4);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.appointment);\n          }\n        },\n        dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i2.ɵNgNoValidate, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.DefaultValueAccessor, i2.SelectControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i1.RouterLink, i6.ChatAccessComponent, i5.DatePipe],\n        styles: [\".info-card[_ngcontent-%COMP%]{background:#f8f9fc;border-radius:.5rem;padding:1.5rem;margin-bottom:1rem;border-left:4px solid #667eea}.info-title[_ngcontent-%COMP%]{color:#5a5c69;font-weight:600;margin-bottom:.5rem;font-size:.9rem}.info-value[_ngcontent-%COMP%]{color:#3a3b45;font-weight:500;margin-bottom:.25rem;font-size:1.1rem}.info-subtitle[_ngcontent-%COMP%]{color:#858796;font-size:.9rem;margin-bottom:0}.badge-lg[_ngcontent-%COMP%]{font-size:.9rem;padding:.5rem 1rem}.badge-warning[_ngcontent-%COMP%]{background-color:#f6c23e;color:#1a1a1a}.badge-info[_ngcontent-%COMP%]{background-color:#36b9cc}.badge-primary[_ngcontent-%COMP%]{background-color:#4e73df}.badge-success[_ngcontent-%COMP%]{background-color:#1cc88a}.badge-danger[_ngcontent-%COMP%]{background-color:#e74a3b}.badge-secondary[_ngcontent-%COMP%]{background-color:#858796}.form-select[_ngcontent-%COMP%]:focus, .form-control[_ngcontent-%COMP%]:focus{border-color:#667eea;box-shadow:0 0 0 .2rem #667eea40}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);border:none;transition:all .3s ease}.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#5a6fd8 0%,#6a4190 100%);transform:translateY(-1px)}.btn-outline-primary[_ngcontent-%COMP%]{border-color:#667eea;color:#667eea}.btn-outline-primary[_ngcontent-%COMP%]:hover{background-color:#667eea;border-color:#667eea}.btn-outline-danger[_ngcontent-%COMP%]{border-color:#e74a3b;color:#e74a3b}.btn-outline-danger[_ngcontent-%COMP%]:hover{background-color:#e74a3b;border-color:#e74a3b}.btn-outline-secondary[_ngcontent-%COMP%]{border-color:#858796;color:#858796}.btn-outline-secondary[_ngcontent-%COMP%]:hover{background-color:#858796;border-color:#858796}.card[_ngcontent-%COMP%]{border:1px solid #e3e6f0;box-shadow:0 .15rem 1.75rem #3a3b4526}.card-title[_ngcontent-%COMP%]{color:#5a5c69;font-weight:600}.alert-success[_ngcontent-%COMP%]{border-left:4px solid #28a745}.alert-danger[_ngcontent-%COMP%]{border-left:4px solid #e74a3b}.spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}\"]\n      });\n    }\n  }\n  return AppointmentDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}