{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { AppointmentStatus, AppointmentType } from '../../core/models/appointment.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../core/services/appointment.service\";\nimport * as i4 from \"../../core/services/auth.service\";\nimport * as i5 from \"@angular/common\";\nfunction AppointmentDetailsComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"span\", 7);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 8);\n    i0.ɵɵtext(5, \"Loading appointment details...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppointmentDetailsComponent_div_4_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"i\", 31);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.success, \" \");\n  }\n}\nfunction AppointmentDetailsComponent_div_4_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.error, \" \");\n  }\n}\nfunction AppointmentDetailsComponent_div_4_form_29_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r11.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r11.label, \" \");\n  }\n}\nfunction AppointmentDetailsComponent_div_4_form_29_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r12.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r12.label, \" \");\n  }\n}\nfunction AppointmentDetailsComponent_div_4_form_29_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"label\", 52);\n    i0.ɵɵtext(2, \"Meeting Link\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 53);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentDetailsComponent_div_4_form_29_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 54)(1, \"span\", 7);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppointmentDetailsComponent_div_4_form_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 34);\n    i0.ɵɵlistener(\"ngSubmit\", function AppointmentDetailsComponent_div_4_form_29_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.onUpdate());\n    });\n    i0.ɵɵelementStart(1, \"div\", 35)(2, \"div\", 18)(3, \"label\", 36);\n    i0.ɵɵtext(4, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"select\", 37);\n    i0.ɵɵtemplate(6, AppointmentDetailsComponent_div_4_form_29_option_6_Template, 2, 2, \"option\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 18)(8, \"label\", 39);\n    i0.ɵɵtext(9, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"select\", 40);\n    i0.ɵɵtemplate(11, AppointmentDetailsComponent_div_4_form_29_option_11_Template, 2, 2, \"option\", 38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 41)(13, \"label\", 42);\n    i0.ɵɵtext(14, \"Reason for Visit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 41)(17, \"label\", 44);\n    i0.ɵɵtext(18, \"Notes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"textarea\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, AppointmentDetailsComponent_div_4_form_29_div_20_Template, 4, 0, \"div\", 46);\n    i0.ɵɵelementStart(21, \"div\", 47)(22, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function AppointmentDetailsComponent_div_4_form_29_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.toggleEdit());\n    });\n    i0.ɵɵtext(23, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 49);\n    i0.ɵɵtemplate(25, AppointmentDetailsComponent_div_4_form_29_span_25_Template, 3, 0, \"span\", 50);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    let tmp_3_0;\n    i0.ɵɵproperty(\"formGroup\", ctx_r4.editForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.statusOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.typeOptions);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r4.editForm.get(\"type\")) == null ? null : tmp_3_0.value) === \"VIDEO_CALL\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.editForm.invalid || ctx_r4.updating);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.updating);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.updating ? \"Updating...\" : \"Update Appointment\", \" \");\n  }\n}\nfunction AppointmentDetailsComponent_div_4_div_30_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 62);\n  }\n}\nfunction AppointmentDetailsComponent_div_4_div_30_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 63);\n  }\n}\nfunction AppointmentDetailsComponent_div_4_div_30_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 19)(2, \"h6\", 20);\n    i0.ɵɵelement(3, \"i\", 65);\n    i0.ɵɵtext(4, \" Notes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 22);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r18.appointment.notes);\n  }\n}\nfunction AppointmentDetailsComponent_div_4_div_30_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 19)(2, \"h6\", 20);\n    i0.ɵɵelement(3, \"i\", 62);\n    i0.ɵɵtext(4, \" Video Call \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"a\", 66);\n    i0.ɵɵelement(6, \"i\", 67);\n    i0.ɵɵtext(7, \" Join Meeting \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"href\", ctx_r19.appointment.meetingLink, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AppointmentDetailsComponent_div_4_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 17)(2, \"div\", 18)(3, \"div\", 19)(4, \"h6\", 20);\n    i0.ɵɵtemplate(5, AppointmentDetailsComponent_div_4_div_30_i_5_Template, 1, 0, \"i\", 55);\n    i0.ɵɵtemplate(6, AppointmentDetailsComponent_div_4_div_30_i_6_Template, 1, 0, \"i\", 56);\n    i0.ɵɵtext(7, \" Appointment Type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 22);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 18)(11, \"div\", 19)(12, \"h6\", 20);\n    i0.ɵɵelement(13, \"i\", 57);\n    i0.ɵɵtext(14, \" Reason for Visit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 22);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(17, AppointmentDetailsComponent_div_4_div_30_div_17_Template, 7, 1, \"div\", 58);\n    i0.ɵɵtemplate(18, AppointmentDetailsComponent_div_4_div_30_div_18_Template, 8, 1, \"div\", 58);\n    i0.ɵɵelementStart(19, \"div\", 59)(20, \"div\", 18)(21, \"div\", 19)(22, \"h6\", 20);\n    i0.ɵɵelement(23, \"i\", 60);\n    i0.ɵɵtext(24, \" Created \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"p\", 22);\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 18)(29, \"div\", 19)(30, \"h6\", 20);\n    i0.ɵɵelement(31, \"i\", 61);\n    i0.ɵɵtext(32, \" Last Updated \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\", 22);\n    i0.ɵɵtext(34);\n    i0.ɵɵpipe(35, \"date\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.appointment.type === \"VIDEO_CALL\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.appointment.type === \"IN_PERSON\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.getTypeDisplayName(ctx_r5.appointment.type));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r5.appointment.reasonForVisit || \"Not specified\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.appointment.notes);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.appointment.meetingLink && ctx_r5.appointment.type === \"VIDEO_CALL\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(27, 8, ctx_r5.appointment.createdAt, \"medium\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(35, 11, ctx_r5.appointment.updatedAt, \"medium\"));\n  }\n}\nfunction AppointmentDetailsComponent_div_4_div_35_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function AppointmentDetailsComponent_div_4_div_35_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r22.toggleEdit());\n    });\n    i0.ɵɵelement(1, \"i\", 61);\n    i0.ɵɵtext(2, \" Edit \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentDetailsComponent_div_4_div_35_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function AppointmentDetailsComponent_div_4_div_35_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r24.onCancel());\n    });\n    i0.ɵɵelement(1, \"i\", 72);\n    i0.ɵɵtext(2, \" Cancel Appointment \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentDetailsComponent_div_4_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AppointmentDetailsComponent_div_4_div_35_button_1_Template, 3, 0, \"button\", 68);\n    i0.ɵɵtemplate(2, AppointmentDetailsComponent_div_4_div_35_button_2_Template, 3, 0, \"button\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.canEdit());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.canCancel());\n  }\n}\nfunction AppointmentDetailsComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"h4\", 11);\n    i0.ɵɵelement(3, \"i\", 12);\n    i0.ɵɵtext(4, \"Appointment Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 13);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 14);\n    i0.ɵɵtemplate(8, AppointmentDetailsComponent_div_4_div_8_Template, 3, 1, \"div\", 15);\n    i0.ɵɵtemplate(9, AppointmentDetailsComponent_div_4_div_9_Template, 3, 1, \"div\", 16);\n    i0.ɵɵelementStart(10, \"div\", 17)(11, \"div\", 18)(12, \"div\", 19)(13, \"h6\", 20);\n    i0.ɵɵelement(14, \"i\", 21);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\", 22);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 23);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 18)(21, \"div\", 19)(22, \"h6\", 20);\n    i0.ɵɵelement(23, \"i\", 24);\n    i0.ɵɵtext(24, \" Date & Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"p\", 22);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\", 23);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(29, AppointmentDetailsComponent_div_4_form_29_Template, 27, 7, \"form\", 25);\n    i0.ɵɵtemplate(30, AppointmentDetailsComponent_div_4_div_30_Template, 36, 14, \"div\", 26);\n    i0.ɵɵelementStart(31, \"div\", 27)(32, \"button\", 28);\n    i0.ɵɵelement(33, \"i\", 29);\n    i0.ɵɵtext(34, \" Back to Appointments \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(35, AppointmentDetailsComponent_div_4_div_35_Template, 3, 2, \"div\", 26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusBadgeClass(ctx_r1.appointment.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusDisplayName(ctx_r1.appointment.status), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.success);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.error);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isDoctor() ? \"Patient\" : \"Doctor\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getOtherParty());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getOtherPartyDetails());\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.formatDate(ctx_r1.appointment.date));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.formatTime(ctx_r1.appointment.startTime), \" - \", ctx_r1.formatTime(ctx_r1.appointment.endTime), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isEditing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isEditing);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isEditing);\n  }\n}\nexport class AppointmentDetailsComponent {\n  constructor(route, router, fb, appointmentService, authService) {\n    this.route = route;\n    this.router = router;\n    this.fb = fb;\n    this.appointmentService = appointmentService;\n    this.authService = authService;\n    this.appointment = null;\n    this.currentUser = null;\n    this.loading = false;\n    this.updating = false;\n    this.error = null;\n    this.success = null;\n    this.isEditing = false;\n    this.statusOptions = [{\n      value: AppointmentStatus.PENDING,\n      label: 'Pending'\n    }, {\n      value: AppointmentStatus.SCHEDULED,\n      label: 'Scheduled'\n    }, {\n      value: AppointmentStatus.CONFIRMED,\n      label: 'Confirmed'\n    }, {\n      value: AppointmentStatus.COMPLETED,\n      label: 'Completed'\n    }, {\n      value: AppointmentStatus.CANCELLED,\n      label: 'Cancelled'\n    }];\n    this.typeOptions = [{\n      value: AppointmentType.IN_PERSON,\n      label: 'In Person'\n    }, {\n      value: AppointmentType.VIDEO_CALL,\n      label: 'Video Call'\n    }];\n    this.editForm = this.fb.group({\n      status: ['', Validators.required],\n      type: ['', Validators.required],\n      reasonForVisit: ['', Validators.required],\n      notes: [''],\n      meetingLink: ['']\n    });\n  }\n  ngOnInit() {\n    this.currentUser = this.authService.getCurrentUser();\n    this.loadAppointment();\n  }\n  loadAppointment() {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (!id) {\n      this.router.navigate(['/appointments']);\n      return;\n    }\n    this.loading = true;\n    this.appointmentService.getAppointment(parseInt(id)).subscribe({\n      next: appointment => {\n        this.appointment = appointment;\n        this.initializeForm();\n        this.loading = false;\n      },\n      error: error => {\n        this.error = 'Failed to load appointment details.';\n        this.loading = false;\n        console.error('Error loading appointment:', error);\n      }\n    });\n  }\n  initializeForm() {\n    if (this.appointment) {\n      this.editForm.patchValue({\n        status: this.appointment.status,\n        type: this.appointment.type,\n        reasonForVisit: this.appointment.reasonForVisit,\n        notes: this.appointment.notes || '',\n        meetingLink: this.appointment.meetingLink || ''\n      });\n    }\n  }\n  toggleEdit() {\n    this.isEditing = !this.isEditing;\n    if (!this.isEditing) {\n      this.initializeForm(); // Reset form if canceling edit\n    }\n  }\n\n  onUpdate() {\n    if (this.editForm.valid && this.appointment) {\n      this.updating = true;\n      this.error = null;\n      this.success = null;\n      const updateRequest = {\n        status: this.editForm.value.status,\n        type: this.editForm.value.type,\n        reasonForVisit: this.editForm.value.reasonForVisit,\n        notes: this.editForm.value.notes || undefined,\n        meetingLink: this.editForm.value.meetingLink || undefined\n      };\n      this.appointmentService.updateAppointment(this.appointment.id, updateRequest).subscribe({\n        next: updatedAppointment => {\n          this.appointment = updatedAppointment;\n          this.success = 'Appointment updated successfully!';\n          this.isEditing = false;\n          this.updating = false;\n        },\n        error: error => {\n          this.error = 'Failed to update appointment. Please try again.';\n          this.updating = false;\n          console.error('Error updating appointment:', error);\n        }\n      });\n    }\n  }\n  onCancel() {\n    if (this.appointment && confirm('Are you sure you want to cancel this appointment?')) {\n      this.appointmentService.cancelAppointment(this.appointment.id).subscribe({\n        next: () => {\n          this.router.navigate(['/appointments']);\n        },\n        error: error => {\n          this.error = 'Failed to cancel appointment. Please try again.';\n          console.error('Error canceling appointment:', error);\n        }\n      });\n    }\n  }\n  getStatusDisplayName(status) {\n    return this.appointmentService.getStatusDisplayName(status);\n  }\n  getTypeDisplayName(type) {\n    return this.appointmentService.getTypeDisplayName(type);\n  }\n  getStatusBadgeClass(status) {\n    return this.appointmentService.getStatusBadgeClass(status);\n  }\n  formatDate(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n  formatTime(timeString) {\n    const [hours, minutes] = timeString.split(':');\n    const hour = parseInt(hours);\n    const ampm = hour >= 12 ? 'PM' : 'AM';\n    const displayHour = hour % 12 || 12;\n    return `${displayHour}:${minutes} ${ampm}`;\n  }\n  isDoctor() {\n    return this.currentUser?.role === 'DOCTOR';\n  }\n  isPatient() {\n    return this.currentUser?.role === 'PATIENT';\n  }\n  canEdit() {\n    if (!this.appointment) return false;\n    // Both doctor and patient can edit certain fields\n    return this.appointment.status !== AppointmentStatus.COMPLETED && this.appointment.status !== AppointmentStatus.CANCELLED;\n  }\n  canCancel() {\n    if (!this.appointment) return false;\n    return this.appointment.status === AppointmentStatus.PENDING || this.appointment.status === AppointmentStatus.SCHEDULED || this.appointment.status === AppointmentStatus.CONFIRMED;\n  }\n  getOtherParty() {\n    if (!this.appointment) return '';\n    if (this.isDoctor()) {\n      return this.appointment.patient.fullName;\n    } else {\n      return this.appointment.doctor.fullName;\n    }\n  }\n  getOtherPartyDetails() {\n    if (!this.appointment) return '';\n    if (this.isDoctor()) {\n      return this.appointment.patient.email;\n    } else {\n      const doctor = this.appointment.doctor;\n      return `${doctor.specialization || 'General Practice'} • ${doctor.affiliation || 'Private Practice'}`;\n    }\n  }\n  static {\n    this.ɵfac = function AppointmentDetailsComponent_Factory(t) {\n      return new (t || AppointmentDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.AppointmentService), i0.ɵɵdirectiveInject(i4.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppointmentDetailsComponent,\n      selectors: [[\"app-appointment-details\"]],\n      decls: 5,\n      vars: 2,\n      consts: [[1, \"container-fluid\", \"py-4\"], [1, \"row\", \"justify-content-center\"], [1, \"col-lg-8\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [\"class\", \"card\", 4, \"ngIf\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\", \"text-muted\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"card-title\", \"mb-0\"], [1, \"fas\", \"fa-calendar-alt\", \"me-2\"], [1, \"badge\", \"badge-lg\", 3, \"ngClass\"], [1, \"card-body\"], [\"class\", \"alert alert-success\", \"role\", \"alert\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [1, \"row\", \"mb-4\"], [1, \"col-md-6\"], [1, \"info-card\"], [1, \"info-title\"], [1, \"fas\", \"fa-user\", \"me-2\"], [1, \"info-value\"], [1, \"info-subtitle\"], [1, \"fas\", \"fa-calendar\", \"me-2\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\", \"mt-4\"], [\"routerLink\", \"/appointments\", 1, \"btn\", \"btn-outline-secondary\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\"], [\"role\", \"alert\", 1, \"alert\", \"alert-success\"], [1, \"fas\", \"fa-check-circle\", \"me-2\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-2\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"row\", \"mb-3\"], [\"for\", \"status\", 1, \"form-label\"], [\"id\", \"status\", \"formControlName\", \"status\", 1, \"form-select\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"type\", 1, \"form-label\"], [\"id\", \"type\", \"formControlName\", \"type\", 1, \"form-select\"], [1, \"mb-3\"], [\"for\", \"reasonForVisit\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"reasonForVisit\", \"formControlName\", \"reasonForVisit\", 1, \"form-control\"], [\"for\", \"notes\", 1, \"form-label\"], [\"id\", \"notes\", \"formControlName\", \"notes\", \"rows\", \"3\", 1, \"form-control\"], [\"class\", \"mb-3\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"me-2\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", 4, \"ngIf\"], [3, \"value\"], [\"for\", \"meetingLink\", 1, \"form-label\"], [\"type\", \"url\", \"id\", \"meetingLink\", \"formControlName\", \"meetingLink\", \"placeholder\", \"https://...\", 1, \"form-control\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [\"class\", \"fas fa-video me-2\", 4, \"ngIf\"], [\"class\", \"fas fa-user-friends me-2\", 4, \"ngIf\"], [1, \"fas\", \"fa-notes-medical\", \"me-2\"], [\"class\", \"mb-4\", 4, \"ngIf\"], [1, \"row\"], [1, \"fas\", \"fa-clock\", \"me-2\"], [1, \"fas\", \"fa-edit\", \"me-2\"], [1, \"fas\", \"fa-video\", \"me-2\"], [1, \"fas\", \"fa-user-friends\", \"me-2\"], [1, \"mb-4\"], [1, \"fas\", \"fa-sticky-note\", \"me-2\"], [\"target\", \"_blank\", 1, \"btn\", \"btn-primary\", 3, \"href\"], [1, \"fas\", \"fa-external-link-alt\", \"me-2\"], [\"class\", \"btn btn-outline-primary me-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-danger\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-outline-danger\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-2\"]],\n      template: function AppointmentDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, AppointmentDetailsComponent_div_3_Template, 6, 0, \"div\", 3);\n          i0.ɵɵtemplate(4, AppointmentDetailsComponent_div_4_Template, 36, 13, \"div\", 4);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.appointment);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i2.ɵNgNoValidate, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.DefaultValueAccessor, i2.SelectControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i1.RouterLink, i5.DatePipe],\n      styles: [\".info-card[_ngcontent-%COMP%] {\\n  background: #f8f9fc;\\n  border-radius: 0.5rem;\\n  padding: 1.5rem;\\n  margin-bottom: 1rem;\\n  border-left: 4px solid #667eea;\\n}\\n\\n.info-title[_ngcontent-%COMP%] {\\n  color: #5a5c69;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n  font-size: 0.9rem;\\n}\\n\\n.info-value[_ngcontent-%COMP%] {\\n  color: #3a3b45;\\n  font-weight: 500;\\n  margin-bottom: 0.25rem;\\n  font-size: 1.1rem;\\n}\\n\\n.info-subtitle[_ngcontent-%COMP%] {\\n  color: #858796;\\n  font-size: 0.9rem;\\n  margin-bottom: 0;\\n}\\n\\n.badge-lg[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  padding: 0.5rem 1rem;\\n}\\n\\n.badge-warning[_ngcontent-%COMP%] {\\n  background-color: #f6c23e;\\n  color: #1a1a1a;\\n}\\n\\n.badge-info[_ngcontent-%COMP%] {\\n  background-color: #36b9cc;\\n}\\n\\n.badge-primary[_ngcontent-%COMP%] {\\n  background-color: #4e73df;\\n}\\n\\n.badge-success[_ngcontent-%COMP%] {\\n  background-color: #1cc88a;\\n}\\n\\n.badge-danger[_ngcontent-%COMP%] {\\n  background-color: #e74a3b;\\n}\\n\\n.badge-secondary[_ngcontent-%COMP%] {\\n  background-color: #858796;\\n}\\n\\n.form-select[_ngcontent-%COMP%]:focus, .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #667eea;\\n  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border: none;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\\n  transform: translateY(-1px);\\n}\\n\\n.btn-outline-primary[_ngcontent-%COMP%] {\\n  border-color: #667eea;\\n  color: #667eea;\\n}\\n\\n.btn-outline-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #667eea;\\n  border-color: #667eea;\\n}\\n\\n.btn-outline-danger[_ngcontent-%COMP%] {\\n  border-color: #e74a3b;\\n  color: #e74a3b;\\n}\\n\\n.btn-outline-danger[_ngcontent-%COMP%]:hover {\\n  background-color: #e74a3b;\\n  border-color: #e74a3b;\\n}\\n\\n.btn-outline-secondary[_ngcontent-%COMP%] {\\n  border-color: #858796;\\n  color: #858796;\\n}\\n\\n.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #858796;\\n  border-color: #858796;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  border: 1px solid #e3e6f0;\\n  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);\\n}\\n\\n.card-title[_ngcontent-%COMP%] {\\n  color: #5a5c69;\\n  font-weight: 600;\\n}\\n\\n.alert-success[_ngcontent-%COMP%] {\\n  border-left: 4px solid #28a745;\\n}\\n\\n.alert-danger[_ngcontent-%COMP%] {\\n  border-left: 4px solid #e74a3b;\\n}\\n\\n.spinner-border[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwb2ludG1lbnRzL2FwcG9pbnRtZW50LWRldGFpbHMvYXBwb2ludG1lbnQtZGV0YWlscy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLG1CQUFBO0VBQ0EscUJBQUE7RUFDQSxlQUFBO0VBQ0EsbUJBQUE7RUFDQSw4QkFBQTtBQUNGOztBQUVBO0VBQ0UsY0FBQTtFQUNBLGdCQUFBO0VBQ0EscUJBQUE7RUFDQSxpQkFBQTtBQUNGOztBQUVBO0VBQ0UsY0FBQTtFQUNBLGdCQUFBO0VBQ0Esc0JBQUE7RUFDQSxpQkFBQTtBQUNGOztBQUVBO0VBQ0UsY0FBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7QUFDRjs7QUFFQTtFQUNFLGlCQUFBO0VBQ0Esb0JBQUE7QUFDRjs7QUFFQTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtBQUNGOztBQUVBO0VBQ0UseUJBQUE7QUFDRjs7QUFFQTtFQUNFLHlCQUFBO0FBQ0Y7O0FBRUE7RUFDRSx5QkFBQTtBQUNGOztBQUVBO0VBQ0UseUJBQUE7QUFDRjs7QUFFQTtFQUNFLHlCQUFBO0FBQ0Y7O0FBRUE7O0VBRUUscUJBQUE7RUFDQSxrREFBQTtBQUNGOztBQUVBO0VBQ0UsNkRBQUE7RUFDQSxZQUFBO0VBQ0EseUJBQUE7QUFDRjs7QUFFQTtFQUNFLDZEQUFBO0VBQ0EsMkJBQUE7QUFDRjs7QUFFQTtFQUNFLHFCQUFBO0VBQ0EsY0FBQTtBQUNGOztBQUVBO0VBQ0UseUJBQUE7RUFDQSxxQkFBQTtBQUNGOztBQUVBO0VBQ0UscUJBQUE7RUFDQSxjQUFBO0FBQ0Y7O0FBRUE7RUFDRSx5QkFBQTtFQUNBLHFCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxxQkFBQTtFQUNBLGNBQUE7QUFDRjs7QUFFQTtFQUNFLHlCQUFBO0VBQ0EscUJBQUE7QUFDRjs7QUFFQTtFQUNFLHlCQUFBO0VBQ0Esc0RBQUE7QUFDRjs7QUFFQTtFQUNFLGNBQUE7RUFDQSxnQkFBQTtBQUNGOztBQUVBO0VBQ0UsOEJBQUE7QUFDRjs7QUFFQTtFQUNFLDhCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxXQUFBO0VBQ0EsWUFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLmluZm8tY2FyZCB7XG4gIGJhY2tncm91bmQ6ICNmOGY5ZmM7XG4gIGJvcmRlci1yYWRpdXM6IDAuNXJlbTtcbiAgcGFkZGluZzogMS41cmVtO1xuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuICBib3JkZXItbGVmdDogNHB4IHNvbGlkICM2NjdlZWE7XG59XG5cbi5pbmZvLXRpdGxlIHtcbiAgY29sb3I6ICM1YTVjNjk7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcbiAgZm9udC1zaXplOiAwLjlyZW07XG59XG5cbi5pbmZvLXZhbHVlIHtcbiAgY29sb3I6ICMzYTNiNDU7XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG4gIG1hcmdpbi1ib3R0b206IDAuMjVyZW07XG4gIGZvbnQtc2l6ZTogMS4xcmVtO1xufVxuXG4uaW5mby1zdWJ0aXRsZSB7XG4gIGNvbG9yOiAjODU4Nzk2O1xuICBmb250LXNpemU6IDAuOXJlbTtcbiAgbWFyZ2luLWJvdHRvbTogMDtcbn1cblxuLmJhZGdlLWxnIHtcbiAgZm9udC1zaXplOiAwLjlyZW07XG4gIHBhZGRpbmc6IDAuNXJlbSAxcmVtO1xufVxuXG4uYmFkZ2Utd2FybmluZyB7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmNmMyM2U7XG4gIGNvbG9yOiAjMWExYTFhO1xufVxuXG4uYmFkZ2UtaW5mbyB7XG4gIGJhY2tncm91bmQtY29sb3I6ICMzNmI5Y2M7XG59XG5cbi5iYWRnZS1wcmltYXJ5IHtcbiAgYmFja2dyb3VuZC1jb2xvcjogIzRlNzNkZjtcbn1cblxuLmJhZGdlLXN1Y2Nlc3Mge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMWNjODhhO1xufVxuXG4uYmFkZ2UtZGFuZ2VyIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2U3NGEzYjtcbn1cblxuLmJhZGdlLXNlY29uZGFyeSB7XG4gIGJhY2tncm91bmQtY29sb3I6ICM4NTg3OTY7XG59XG5cbi5mb3JtLXNlbGVjdDpmb2N1cyxcbi5mb3JtLWNvbnRyb2w6Zm9jdXMge1xuICBib3JkZXItY29sb3I6ICM2NjdlZWE7XG4gIGJveC1zaGFkb3c6IDAgMCAwIDAuMnJlbSByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMjUpO1xufVxuXG4uYnRuLXByaW1hcnkge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpO1xuICBib3JkZXI6IG5vbmU7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG59XG5cbi5idG4tcHJpbWFyeTpob3ZlciB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM1YTZmZDggMCUsICM2YTQxOTAgMTAwJSk7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcbn1cblxuLmJ0bi1vdXRsaW5lLXByaW1hcnkge1xuICBib3JkZXItY29sb3I6ICM2NjdlZWE7XG4gIGNvbG9yOiAjNjY3ZWVhO1xufVxuXG4uYnRuLW91dGxpbmUtcHJpbWFyeTpob3ZlciB7XG4gIGJhY2tncm91bmQtY29sb3I6ICM2NjdlZWE7XG4gIGJvcmRlci1jb2xvcjogIzY2N2VlYTtcbn1cblxuLmJ0bi1vdXRsaW5lLWRhbmdlciB7XG4gIGJvcmRlci1jb2xvcjogI2U3NGEzYjtcbiAgY29sb3I6ICNlNzRhM2I7XG59XG5cbi5idG4tb3V0bGluZS1kYW5nZXI6aG92ZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTc0YTNiO1xuICBib3JkZXItY29sb3I6ICNlNzRhM2I7XG59XG5cbi5idG4tb3V0bGluZS1zZWNvbmRhcnkge1xuICBib3JkZXItY29sb3I6ICM4NTg3OTY7XG4gIGNvbG9yOiAjODU4Nzk2O1xufVxuXG4uYnRuLW91dGxpbmUtc2Vjb25kYXJ5OmhvdmVyIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogIzg1ODc5NjtcbiAgYm9yZGVyLWNvbG9yOiAjODU4Nzk2O1xufVxuXG4uY2FyZCB7XG4gIGJvcmRlcjogMXB4IHNvbGlkICNlM2U2ZjA7XG4gIGJveC1zaGFkb3c6IDAgMC4xNXJlbSAxLjc1cmVtIDAgcmdiYSg1OCwgNTksIDY5LCAwLjE1KTtcbn1cblxuLmNhcmQtdGl0bGUge1xuICBjb2xvcjogIzVhNWM2OTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbn1cblxuLmFsZXJ0LXN1Y2Nlc3Mge1xuICBib3JkZXItbGVmdDogNHB4IHNvbGlkICMyOGE3NDU7XG59XG5cbi5hbGVydC1kYW5nZXIge1xuICBib3JkZXItbGVmdDogNHB4IHNvbGlkICNlNzRhM2I7XG59XG5cbi5zcGlubmVyLWJvcmRlciB7XG4gIHdpZHRoOiAzcmVtO1xuICBoZWlnaHQ6IDNyZW07XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "AppointmentStatus", "AppointmentType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "success", "ctx_r3", "error", "ɵɵproperty", "option_r11", "value", "label", "option_r12", "ɵɵlistener", "AppointmentDetailsComponent_div_4_form_29_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r14", "ctx_r13", "ɵɵnextContext", "ɵɵresetView", "onUpdate", "ɵɵtemplate", "AppointmentDetailsComponent_div_4_form_29_option_6_Template", "AppointmentDetailsComponent_div_4_form_29_option_11_Template", "AppointmentDetailsComponent_div_4_form_29_div_20_Template", "AppointmentDetailsComponent_div_4_form_29_Template_button_click_22_listener", "ctx_r15", "toggleEdit", "AppointmentDetailsComponent_div_4_form_29_span_25_Template", "ctx_r4", "editForm", "statusOptions", "typeOptions", "tmp_3_0", "get", "invalid", "updating", "ɵɵtextInterpolate", "ctx_r18", "appointment", "notes", "ctx_r19", "meetingLink", "ɵɵsanitizeUrl", "AppointmentDetailsComponent_div_4_div_30_i_5_Template", "AppointmentDetailsComponent_div_4_div_30_i_6_Template", "AppointmentDetailsComponent_div_4_div_30_div_17_Template", "AppointmentDetailsComponent_div_4_div_30_div_18_Template", "ctx_r5", "type", "getTypeDisplayName", "reasonForVisit", "ɵɵpipeBind2", "createdAt", "updatedAt", "AppointmentDetailsComponent_div_4_div_35_button_1_Template_button_click_0_listener", "_r23", "ctx_r22", "AppointmentDetailsComponent_div_4_div_35_button_2_Template_button_click_0_listener", "_r25", "ctx_r24", "onCancel", "AppointmentDetailsComponent_div_4_div_35_button_1_Template", "AppointmentDetailsComponent_div_4_div_35_button_2_Template", "ctx_r6", "canEdit", "canCancel", "AppointmentDetailsComponent_div_4_div_8_Template", "AppointmentDetailsComponent_div_4_div_9_Template", "AppointmentDetailsComponent_div_4_form_29_Template", "AppointmentDetailsComponent_div_4_div_30_Template", "AppointmentDetailsComponent_div_4_div_35_Template", "ctx_r1", "getStatusBadgeClass", "status", "getStatusDisplayName", "isDoctor", "getOther<PERSON><PERSON>y", "getOtherPartyDetails", "formatDate", "date", "ɵɵtextInterpolate2", "formatTime", "startTime", "endTime", "isEditing", "AppointmentDetailsComponent", "constructor", "route", "router", "fb", "appointmentService", "authService", "currentUser", "loading", "PENDING", "SCHEDULED", "CONFIRMED", "COMPLETED", "CANCELLED", "IN_PERSON", "VIDEO_CALL", "group", "required", "ngOnInit", "getCurrentUser", "loadAppointment", "id", "snapshot", "paramMap", "navigate", "getAppointment", "parseInt", "subscribe", "next", "initializeForm", "console", "patchValue", "valid", "updateRequest", "undefined", "updateAppointment", "updatedAppointment", "confirm", "cancelAppointment", "dateString", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "timeString", "hours", "minutes", "split", "hour", "ampm", "displayHour", "role", "isPatient", "patient", "fullName", "doctor", "email", "specialization", "affiliation", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "FormBuilder", "i3", "AppointmentService", "i4", "AuthService", "selectors", "decls", "vars", "consts", "template", "AppointmentDetailsComponent_Template", "rf", "ctx", "AppointmentDetailsComponent_div_3_Template", "AppointmentDetailsComponent_div_4_Template"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/appointments/appointment-details/appointment-details.component.ts", "/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/appointments/appointment-details/appointment-details.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\nimport { AppointmentService } from '../../core/services/appointment.service';\nimport { AuthService } from '../../core/services/auth.service';\nimport { Appointment, AppointmentStatus, AppointmentType, AppointmentUpdateRequest } from '../../core/models/appointment.model';\nimport { User } from '../../core/models/user.model';\n\n@Component({\n  selector: 'app-appointment-details',\n  templateUrl: './appointment-details.component.html',\n  styleUrls: ['./appointment-details.component.scss']\n})\nexport class AppointmentDetailsComponent implements OnInit {\n  appointment: Appointment | null = null;\n  currentUser: User | null = null;\n  editForm: FormGroup;\n  loading = false;\n  updating = false;\n  error: string | null = null;\n  success: string | null = null;\n  isEditing = false;\n\n  statusOptions = [\n    { value: AppointmentStatus.PENDING, label: 'Pending' },\n    { value: AppointmentStatus.SCHEDULED, label: 'Scheduled' },\n    { value: AppointmentStatus.CONFIRMED, label: 'Confirmed' },\n    { value: AppointmentStatus.COMPLETED, label: 'Completed' },\n    { value: AppointmentStatus.CANCELLED, label: 'Cancelled' }\n  ];\n\n  typeOptions = [\n    { value: AppointmentType.IN_PERSON, label: 'In Person' },\n    { value: AppointmentType.VIDEO_CALL, label: 'Video Call' }\n  ];\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private fb: FormBuilder,\n    private appointmentService: AppointmentService,\n    private authService: AuthService\n  ) {\n    this.editForm = this.fb.group({\n      status: ['', Validators.required],\n      type: ['', Validators.required],\n      reasonForVisit: ['', Validators.required],\n      notes: [''],\n      meetingLink: ['']\n    });\n  }\n\n  ngOnInit(): void {\n    this.currentUser = this.authService.getCurrentUser();\n    this.loadAppointment();\n  }\n\n  loadAppointment(): void {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (!id) {\n      this.router.navigate(['/appointments']);\n      return;\n    }\n\n    this.loading = true;\n    this.appointmentService.getAppointment(parseInt(id)).subscribe({\n      next: (appointment) => {\n        this.appointment = appointment;\n        this.initializeForm();\n        this.loading = false;\n      },\n      error: (error) => {\n        this.error = 'Failed to load appointment details.';\n        this.loading = false;\n        console.error('Error loading appointment:', error);\n      }\n    });\n  }\n\n  initializeForm(): void {\n    if (this.appointment) {\n      this.editForm.patchValue({\n        status: this.appointment.status,\n        type: this.appointment.type,\n        reasonForVisit: this.appointment.reasonForVisit,\n        notes: this.appointment.notes || '',\n        meetingLink: this.appointment.meetingLink || ''\n      });\n    }\n  }\n\n  toggleEdit(): void {\n    this.isEditing = !this.isEditing;\n    if (!this.isEditing) {\n      this.initializeForm(); // Reset form if canceling edit\n    }\n  }\n\n  onUpdate(): void {\n    if (this.editForm.valid && this.appointment) {\n      this.updating = true;\n      this.error = null;\n      this.success = null;\n\n      const updateRequest: AppointmentUpdateRequest = {\n        status: this.editForm.value.status,\n        type: this.editForm.value.type,\n        reasonForVisit: this.editForm.value.reasonForVisit,\n        notes: this.editForm.value.notes || undefined,\n        meetingLink: this.editForm.value.meetingLink || undefined\n      };\n\n      this.appointmentService.updateAppointment(this.appointment.id, updateRequest).subscribe({\n        next: (updatedAppointment) => {\n          this.appointment = updatedAppointment;\n          this.success = 'Appointment updated successfully!';\n          this.isEditing = false;\n          this.updating = false;\n        },\n        error: (error) => {\n          this.error = 'Failed to update appointment. Please try again.';\n          this.updating = false;\n          console.error('Error updating appointment:', error);\n        }\n      });\n    }\n  }\n\n  onCancel(): void {\n    if (this.appointment && confirm('Are you sure you want to cancel this appointment?')) {\n      this.appointmentService.cancelAppointment(this.appointment.id).subscribe({\n        next: () => {\n          this.router.navigate(['/appointments']);\n        },\n        error: (error) => {\n          this.error = 'Failed to cancel appointment. Please try again.';\n          console.error('Error canceling appointment:', error);\n        }\n      });\n    }\n  }\n\n  getStatusDisplayName(status: AppointmentStatus): string {\n    return this.appointmentService.getStatusDisplayName(status);\n  }\n\n  getTypeDisplayName(type: AppointmentType): string {\n    return this.appointmentService.getTypeDisplayName(type);\n  }\n\n  getStatusBadgeClass(status: AppointmentStatus): string {\n    return this.appointmentService.getStatusBadgeClass(status);\n  }\n\n  formatDate(dateString: string): string {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n\n  formatTime(timeString: string): string {\n    const [hours, minutes] = timeString.split(':');\n    const hour = parseInt(hours);\n    const ampm = hour >= 12 ? 'PM' : 'AM';\n    const displayHour = hour % 12 || 12;\n    return `${displayHour}:${minutes} ${ampm}`;\n  }\n\n  isDoctor(): boolean {\n    return this.currentUser?.role === 'DOCTOR';\n  }\n\n  isPatient(): boolean {\n    return this.currentUser?.role === 'PATIENT';\n  }\n\n  canEdit(): boolean {\n    if (!this.appointment) return false;\n    \n    // Both doctor and patient can edit certain fields\n    return this.appointment.status !== AppointmentStatus.COMPLETED &&\n           this.appointment.status !== AppointmentStatus.CANCELLED;\n  }\n\n  canCancel(): boolean {\n    if (!this.appointment) return false;\n    \n    return this.appointment.status === AppointmentStatus.PENDING ||\n           this.appointment.status === AppointmentStatus.SCHEDULED ||\n           this.appointment.status === AppointmentStatus.CONFIRMED;\n  }\n\n  getOtherParty(): string {\n    if (!this.appointment) return '';\n    \n    if (this.isDoctor()) {\n      return this.appointment.patient.fullName;\n    } else {\n      return this.appointment.doctor.fullName;\n    }\n  }\n\n  getOtherPartyDetails(): string {\n    if (!this.appointment) return '';\n    \n    if (this.isDoctor()) {\n      return this.appointment.patient.email;\n    } else {\n      const doctor = this.appointment.doctor;\n      return `${doctor.specialization || 'General Practice'} • ${doctor.affiliation || 'Private Practice'}`;\n    }\n  }\n}\n", "<div class=\"container-fluid py-4\">\n  <div class=\"row justify-content-center\">\n    <div class=\"col-lg-8\">\n      <!-- Loading -->\n      <div *ngIf=\"loading\" class=\"text-center py-5\">\n        <div class=\"spinner-border text-primary\" role=\"status\">\n          <span class=\"visually-hidden\">Loading...</span>\n        </div>\n        <p class=\"mt-2 text-muted\">Loading appointment details...</p>\n      </div>\n\n      <!-- Appointment Details -->\n      <div *ngIf=\"!loading && appointment\" class=\"card\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h4 class=\"card-title mb-0\">\n            <i class=\"fas fa-calendar-alt me-2\"></i>Appointment Details\n          </h4>\n          <span class=\"badge badge-lg\" [ngClass]=\"getStatusBadgeClass(appointment.status)\">\n            {{ getStatusDisplayName(appointment.status) }}\n          </span>\n        </div>\n        <div class=\"card-body\">\n          <!-- Success/Error Messages -->\n          <div *ngIf=\"success\" class=\"alert alert-success\" role=\"alert\">\n            <i class=\"fas fa-check-circle me-2\"></i>\n            {{ success }}\n          </div>\n          <div *ngIf=\"error\" class=\"alert alert-danger\" role=\"alert\">\n            <i class=\"fas fa-exclamation-triangle me-2\"></i>\n            {{ error }}\n          </div>\n\n          <!-- Appointment Information -->\n          <div class=\"row mb-4\">\n            <div class=\"col-md-6\">\n              <div class=\"info-card\">\n                <h6 class=\"info-title\">\n                  <i class=\"fas fa-user me-2\"></i>\n                  {{ isDoctor() ? 'Patient' : 'Doctor' }}\n                </h6>\n                <p class=\"info-value\">{{ getOtherParty() }}</p>\n                <p class=\"info-subtitle\">{{ getOtherPartyDetails() }}</p>\n              </div>\n            </div>\n            <div class=\"col-md-6\">\n              <div class=\"info-card\">\n                <h6 class=\"info-title\">\n                  <i class=\"fas fa-calendar me-2\"></i>\n                  Date & Time\n                </h6>\n                <p class=\"info-value\">{{ formatDate(appointment.date) }}</p>\n                <p class=\"info-subtitle\">\n                  {{ formatTime(appointment.startTime) }} - {{ formatTime(appointment.endTime) }}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <!-- Edit Form -->\n          <form *ngIf=\"isEditing\" [formGroup]=\"editForm\" (ngSubmit)=\"onUpdate()\">\n            <div class=\"row mb-3\">\n              <div class=\"col-md-6\">\n                <label for=\"status\" class=\"form-label\">Status</label>\n                <select id=\"status\" class=\"form-select\" formControlName=\"status\">\n                  <option *ngFor=\"let option of statusOptions\" [value]=\"option.value\">\n                    {{ option.label }}\n                  </option>\n                </select>\n              </div>\n              <div class=\"col-md-6\">\n                <label for=\"type\" class=\"form-label\">Type</label>\n                <select id=\"type\" class=\"form-select\" formControlName=\"type\">\n                  <option *ngFor=\"let option of typeOptions\" [value]=\"option.value\">\n                    {{ option.label }}\n                  </option>\n                </select>\n              </div>\n            </div>\n\n            <div class=\"mb-3\">\n              <label for=\"reasonForVisit\" class=\"form-label\">Reason for Visit</label>\n              <input \n                type=\"text\" \n                id=\"reasonForVisit\"\n                class=\"form-control\"\n                formControlName=\"reasonForVisit\">\n            </div>\n\n            <div class=\"mb-3\">\n              <label for=\"notes\" class=\"form-label\">Notes</label>\n              <textarea \n                id=\"notes\"\n                class=\"form-control\"\n                formControlName=\"notes\"\n                rows=\"3\"></textarea>\n            </div>\n\n            <div class=\"mb-3\" *ngIf=\"editForm.get('type')?.value === 'VIDEO_CALL'\">\n              <label for=\"meetingLink\" class=\"form-label\">Meeting Link</label>\n              <input \n                type=\"url\" \n                id=\"meetingLink\"\n                class=\"form-control\"\n                formControlName=\"meetingLink\"\n                placeholder=\"https://...\">\n            </div>\n\n            <div class=\"d-flex justify-content-end\">\n              <button \n                type=\"button\" \n                class=\"btn btn-outline-secondary me-2\"\n                (click)=\"toggleEdit()\">\n                Cancel\n              </button>\n              <button \n                type=\"submit\" \n                class=\"btn btn-primary\"\n                [disabled]=\"editForm.invalid || updating\">\n                <span *ngIf=\"updating\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\">\n                  <span class=\"visually-hidden\">Loading...</span>\n                </span>\n                {{ updating ? 'Updating...' : 'Update Appointment' }}\n              </button>\n            </div>\n          </form>\n\n          <!-- View Mode -->\n          <div *ngIf=\"!isEditing\">\n            <div class=\"row mb-4\">\n              <div class=\"col-md-6\">\n                <div class=\"info-card\">\n                  <h6 class=\"info-title\">\n                    <i class=\"fas fa-video me-2\" *ngIf=\"appointment.type === 'VIDEO_CALL'\"></i>\n                    <i class=\"fas fa-user-friends me-2\" *ngIf=\"appointment.type === 'IN_PERSON'\"></i>\n                    Appointment Type\n                  </h6>\n                  <p class=\"info-value\">{{ getTypeDisplayName(appointment.type) }}</p>\n                </div>\n              </div>\n              <div class=\"col-md-6\">\n                <div class=\"info-card\">\n                  <h6 class=\"info-title\">\n                    <i class=\"fas fa-notes-medical me-2\"></i>\n                    Reason for Visit\n                  </h6>\n                  <p class=\"info-value\">{{ appointment.reasonForVisit || 'Not specified' }}</p>\n                </div>\n              </div>\n            </div>\n\n            <div *ngIf=\"appointment.notes\" class=\"mb-4\">\n              <div class=\"info-card\">\n                <h6 class=\"info-title\">\n                  <i class=\"fas fa-sticky-note me-2\"></i>\n                  Notes\n                </h6>\n                <p class=\"info-value\">{{ appointment.notes }}</p>\n              </div>\n            </div>\n\n            <!-- Meeting Link -->\n            <div *ngIf=\"appointment.meetingLink && appointment.type === 'VIDEO_CALL'\" class=\"mb-4\">\n              <div class=\"info-card\">\n                <h6 class=\"info-title\">\n                  <i class=\"fas fa-video me-2\"></i>\n                  Video Call\n                </h6>\n                <a \n                  [href]=\"appointment.meetingLink\" \n                  target=\"_blank\" \n                  class=\"btn btn-primary\">\n                  <i class=\"fas fa-external-link-alt me-2\"></i>\n                  Join Meeting\n                </a>\n              </div>\n            </div>\n\n            <!-- Appointment Metadata -->\n            <div class=\"row\">\n              <div class=\"col-md-6\">\n                <div class=\"info-card\">\n                  <h6 class=\"info-title\">\n                    <i class=\"fas fa-clock me-2\"></i>\n                    Created\n                  </h6>\n                  <p class=\"info-value\">{{ appointment.createdAt | date:'medium' }}</p>\n                </div>\n              </div>\n              <div class=\"col-md-6\">\n                <div class=\"info-card\">\n                  <h6 class=\"info-title\">\n                    <i class=\"fas fa-edit me-2\"></i>\n                    Last Updated\n                  </h6>\n                  <p class=\"info-value\">{{ appointment.updatedAt | date:'medium' }}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Actions -->\n          <div class=\"d-flex justify-content-between mt-4\">\n            <button \n              class=\"btn btn-outline-secondary\"\n              routerLink=\"/appointments\">\n              <i class=\"fas fa-arrow-left me-2\"></i>\n              Back to Appointments\n            </button>\n            <div *ngIf=\"!isEditing\">\n              <button \n                *ngIf=\"canEdit()\"\n                class=\"btn btn-outline-primary me-2\"\n                (click)=\"toggleEdit()\">\n                <i class=\"fas fa-edit me-2\"></i>\n                Edit\n              </button>\n              <button \n                *ngIf=\"canCancel()\"\n                class=\"btn btn-outline-danger\"\n                (click)=\"onCancel()\">\n                <i class=\"fas fa-times me-2\"></i>\n                Cancel Appointment\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAEA,SAAiCA,UAAU,QAAQ,gBAAgB;AAGnE,SAAsBC,iBAAiB,EAAEC,eAAe,QAAkC,qCAAqC;;;;;;;;;ICDzHC,EAAA,CAAAC,cAAA,aAA8C;IAEZD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEjDH,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,qCAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAe3DH,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAI,SAAA,YAAwC;IACxCJ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,OAAA,MACF;;;;;IACAR,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAI,SAAA,YAAgD;IAChDJ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAG,MAAA,CAAAC,KAAA,MACF;;;;;IAkCQV,EAAA,CAAAC,cAAA,iBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFoCH,EAAA,CAAAW,UAAA,UAAAC,UAAA,CAAAC,KAAA,CAAsB;IACjEb,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAM,UAAA,CAAAE,KAAA,MACF;;;;;IAMAd,EAAA,CAAAC,cAAA,iBAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFkCH,EAAA,CAAAW,UAAA,UAAAI,UAAA,CAAAF,KAAA,CAAsB;IAC/Db,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAS,UAAA,CAAAD,KAAA,MACF;;;;;IAuBNd,EAAA,CAAAC,cAAA,cAAuE;IACzBD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAChEH,EAAA,CAAAI,SAAA,gBAK4B;IAC9BJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAaFH,EAAA,CAAAC,cAAA,eAAmF;IACnDD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IA5DvDH,EAAA,CAAAC,cAAA,eAAuE;IAAxBD,EAAA,CAAAgB,UAAA,sBAAAC,4EAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAYrB,EAAA,CAAAsB,WAAA,CAAAF,OAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IACpEvB,EAAA,CAAAC,cAAA,cAAsB;IAEqBD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrDH,EAAA,CAAAC,cAAA,iBAAiE;IAC/DD,EAAA,CAAAwB,UAAA,IAAAC,2DAAA,qBAES;IACXzB,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,cAAsB;IACiBD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjDH,EAAA,CAAAC,cAAA,kBAA6D;IAC3DD,EAAA,CAAAwB,UAAA,KAAAE,4DAAA,qBAES;IACX1B,EAAA,CAAAG,YAAA,EAAS;IAIbH,EAAA,CAAAC,cAAA,eAAkB;IAC+BD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvEH,EAAA,CAAAI,SAAA,iBAImC;IACrCJ,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAkB;IACsBD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnDH,EAAA,CAAAI,SAAA,oBAIsB;IACxBJ,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAwB,UAAA,KAAAG,yDAAA,kBAQM;IAEN3B,EAAA,CAAAC,cAAA,eAAwC;IAIpCD,EAAA,CAAAgB,UAAA,mBAAAY,4EAAA;MAAA5B,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAU,OAAA,GAAA7B,EAAA,CAAAqB,aAAA;MAAA,OAASrB,EAAA,CAAAsB,WAAA,CAAAO,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IACtB9B,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAG4C;IAC1CD,EAAA,CAAAwB,UAAA,KAAAO,0DAAA,mBAEO;IACP/B,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IA/DWH,EAAA,CAAAW,UAAA,cAAAqB,MAAA,CAAAC,QAAA,CAAsB;IAKXjC,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAW,UAAA,YAAAqB,MAAA,CAAAE,aAAA,CAAgB;IAQhBlC,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAAW,UAAA,YAAAqB,MAAA,CAAAG,WAAA,CAAc;IAyB5BnC,EAAA,CAAAK,SAAA,GAAkD;IAAlDL,EAAA,CAAAW,UAAA,WAAAyB,OAAA,GAAAJ,MAAA,CAAAC,QAAA,CAAAI,GAAA,2BAAAD,OAAA,CAAAvB,KAAA,mBAAkD;IAoBjEb,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAW,UAAA,aAAAqB,MAAA,CAAAC,QAAA,CAAAK,OAAA,IAAAN,MAAA,CAAAO,QAAA,CAAyC;IAClCvC,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAAW,UAAA,SAAAqB,MAAA,CAAAO,QAAA,CAAc;IAGrBvC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA0B,MAAA,CAAAO,QAAA,6CACF;;;;;IAUMvC,EAAA,CAAAI,SAAA,YAA2E;;;;;IAC3EJ,EAAA,CAAAI,SAAA,YAAiF;;;;;IAiBzFJ,EAAA,CAAAC,cAAA,cAA4C;IAGtCD,EAAA,CAAAI,SAAA,YAAuC;IACvCJ,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAA3BH,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAwC,iBAAA,CAAAC,OAAA,CAAAC,WAAA,CAAAC,KAAA,CAAuB;;;;;IAKjD3C,EAAA,CAAAC,cAAA,cAAuF;IAGjFD,EAAA,CAAAI,SAAA,YAAiC;IACjCJ,EAAA,CAAAE,MAAA,mBACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAG0B;IACxBD,EAAA,CAAAI,SAAA,YAA6C;IAC7CJ,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IALFH,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAW,UAAA,SAAAiC,OAAA,CAAAF,WAAA,CAAAG,WAAA,EAAA7C,EAAA,CAAA8C,aAAA,CAAgC;;;;;IAzCxC9C,EAAA,CAAAC,cAAA,UAAwB;IAKdD,EAAA,CAAAwB,UAAA,IAAAuB,qDAAA,gBAA2E;IAC3E/C,EAAA,CAAAwB,UAAA,IAAAwB,qDAAA,gBAAiF;IACjFhD,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,GAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGxEH,EAAA,CAAAC,cAAA,eAAsB;IAGhBD,EAAA,CAAAI,SAAA,aAAyC;IACzCJ,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAsB;IAAAD,EAAA,CAAAE,MAAA,IAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAKnFH,EAAA,CAAAwB,UAAA,KAAAyB,wDAAA,kBAQM;IAGNjD,EAAA,CAAAwB,UAAA,KAAA0B,wDAAA,kBAcM;IAGNlD,EAAA,CAAAC,cAAA,eAAiB;IAITD,EAAA,CAAAI,SAAA,aAAiC;IACjCJ,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAsB;IAAAD,EAAA,CAAAE,MAAA,IAA2C;;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGzEH,EAAA,CAAAC,cAAA,eAAsB;IAGhBD,EAAA,CAAAI,SAAA,aAAgC;IAChCJ,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAsB;IAAAD,EAAA,CAAAE,MAAA,IAA2C;;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IA9DrCH,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAW,UAAA,SAAAwC,MAAA,CAAAT,WAAA,CAAAU,IAAA,kBAAuC;IAChCpD,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAW,UAAA,SAAAwC,MAAA,CAAAT,WAAA,CAAAU,IAAA,iBAAsC;IAGvDpD,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAwC,iBAAA,CAAAW,MAAA,CAAAE,kBAAA,CAAAF,MAAA,CAAAT,WAAA,CAAAU,IAAA,EAA0C;IAS1CpD,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAwC,iBAAA,CAAAW,MAAA,CAAAT,WAAA,CAAAY,cAAA,oBAAmD;IAKzEtD,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAW,UAAA,SAAAwC,MAAA,CAAAT,WAAA,CAAAC,KAAA,CAAuB;IAWvB3C,EAAA,CAAAK,SAAA,GAAkE;IAAlEL,EAAA,CAAAW,UAAA,SAAAwC,MAAA,CAAAT,WAAA,CAAAG,WAAA,IAAAM,MAAA,CAAAT,WAAA,CAAAU,IAAA,kBAAkE;IAwB5CpD,EAAA,CAAAK,SAAA,GAA2C;IAA3CL,EAAA,CAAAwC,iBAAA,CAAAxC,EAAA,CAAAuD,WAAA,QAAAJ,MAAA,CAAAT,WAAA,CAAAc,SAAA,YAA2C;IAS3CxD,EAAA,CAAAK,SAAA,GAA2C;IAA3CL,EAAA,CAAAwC,iBAAA,CAAAxC,EAAA,CAAAuD,WAAA,SAAAJ,MAAA,CAAAT,WAAA,CAAAe,SAAA,YAA2C;;;;;;IAerEzD,EAAA,CAAAC,cAAA,iBAGyB;IAAvBD,EAAA,CAAAgB,UAAA,mBAAA0C,mFAAA;MAAA1D,EAAA,CAAAkB,aAAA,CAAAyC,IAAA;MAAA,MAAAC,OAAA,GAAA5D,EAAA,CAAAqB,aAAA;MAAA,OAASrB,EAAA,CAAAsB,WAAA,CAAAsC,OAAA,CAAA9B,UAAA,EAAY;IAAA,EAAC;IACtB9B,EAAA,CAAAI,SAAA,YAAgC;IAChCJ,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAGuB;IAArBD,EAAA,CAAAgB,UAAA,mBAAA6C,mFAAA;MAAA7D,EAAA,CAAAkB,aAAA,CAAA4C,IAAA;MAAA,MAAAC,OAAA,GAAA/D,EAAA,CAAAqB,aAAA;MAAA,OAASrB,EAAA,CAAAsB,WAAA,CAAAyC,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IACpBhE,EAAA,CAAAI,SAAA,YAAiC;IACjCJ,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAdXH,EAAA,CAAAC,cAAA,UAAwB;IACtBD,EAAA,CAAAwB,UAAA,IAAAyC,0DAAA,qBAMS;IACTjE,EAAA,CAAAwB,UAAA,IAAA0C,0DAAA,qBAMS;IACXlE,EAAA,CAAAG,YAAA,EAAM;;;;IAbDH,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAW,UAAA,SAAAwD,MAAA,CAAAC,OAAA,GAAe;IAOfpE,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAW,UAAA,SAAAwD,MAAA,CAAAE,SAAA,GAAiB;;;;;IA7M5BrE,EAAA,CAAAC,cAAA,aAAkD;IAG5CD,EAAA,CAAAI,SAAA,YAAwC;IAAAJ,EAAA,CAAAE,MAAA,2BAC1C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,cAAuB;IAErBD,EAAA,CAAAwB,UAAA,IAAA8C,gDAAA,kBAGM;IACNtE,EAAA,CAAAwB,UAAA,IAAA+C,gDAAA,kBAGM;IAGNvE,EAAA,CAAAC,cAAA,eAAsB;IAIdD,EAAA,CAAAI,SAAA,aAAgC;IAChCJ,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAsB;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/CH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAG7DH,EAAA,CAAAC,cAAA,eAAsB;IAGhBD,EAAA,CAAAI,SAAA,aAAoC;IACpCJ,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAsB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5DH,EAAA,CAAAC,cAAA,aAAyB;IACvBD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAMVH,EAAA,CAAAwB,UAAA,KAAAgD,kDAAA,oBAiEO;IAGPxE,EAAA,CAAAwB,UAAA,KAAAiD,iDAAA,oBAuEM;IAGNzE,EAAA,CAAAC,cAAA,eAAiD;IAI7CD,EAAA,CAAAI,SAAA,aAAsC;IACtCJ,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAwB,UAAA,KAAAkD,iDAAA,kBAeM;IACR1E,EAAA,CAAAG,YAAA,EAAM;;;;IA/MuBH,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAW,UAAA,YAAAgE,MAAA,CAAAC,mBAAA,CAAAD,MAAA,CAAAjC,WAAA,CAAAmC,MAAA,EAAmD;IAC9E7E,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAqE,MAAA,CAAAG,oBAAA,CAAAH,MAAA,CAAAjC,WAAA,CAAAmC,MAAA,OACF;IAIM7E,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAW,UAAA,SAAAgE,MAAA,CAAAnE,OAAA,CAAa;IAIbR,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAW,UAAA,SAAAgE,MAAA,CAAAjE,KAAA,CAAW;IAWTV,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAqE,MAAA,CAAAI,QAAA,+BACF;IACsB/E,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAwC,iBAAA,CAAAmC,MAAA,CAAAK,aAAA,GAAqB;IAClBhF,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAwC,iBAAA,CAAAmC,MAAA,CAAAM,oBAAA,GAA4B;IAS/BjF,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAwC,iBAAA,CAAAmC,MAAA,CAAAO,UAAA,CAAAP,MAAA,CAAAjC,WAAA,CAAAyC,IAAA,EAAkC;IAEtDnF,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAoF,kBAAA,MAAAT,MAAA,CAAAU,UAAA,CAAAV,MAAA,CAAAjC,WAAA,CAAA4C,SAAA,UAAAX,MAAA,CAAAU,UAAA,CAAAV,MAAA,CAAAjC,WAAA,CAAA6C,OAAA,OACF;IAMCvF,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAW,UAAA,SAAAgE,MAAA,CAAAa,SAAA,CAAe;IAoEhBxF,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAW,UAAA,UAAAgE,MAAA,CAAAa,SAAA,CAAgB;IAiFdxF,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAW,UAAA,UAAAgE,MAAA,CAAAa,SAAA,CAAgB;;;ADnMlC,OAAM,MAAOC,2BAA2B;EAuBtCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,EAAe,EACfC,kBAAsC,EACtCC,WAAwB;IAJxB,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,WAAW,GAAXA,WAAW;IA3BrB,KAAArD,WAAW,GAAuB,IAAI;IACtC,KAAAsD,WAAW,GAAgB,IAAI;IAE/B,KAAAC,OAAO,GAAG,KAAK;IACf,KAAA1D,QAAQ,GAAG,KAAK;IAChB,KAAA7B,KAAK,GAAkB,IAAI;IAC3B,KAAAF,OAAO,GAAkB,IAAI;IAC7B,KAAAgF,SAAS,GAAG,KAAK;IAEjB,KAAAtD,aAAa,GAAG,CACd;MAAErB,KAAK,EAAEf,iBAAiB,CAACoG,OAAO;MAAEpF,KAAK,EAAE;IAAS,CAAE,EACtD;MAAED,KAAK,EAAEf,iBAAiB,CAACqG,SAAS;MAAErF,KAAK,EAAE;IAAW,CAAE,EAC1D;MAAED,KAAK,EAAEf,iBAAiB,CAACsG,SAAS;MAAEtF,KAAK,EAAE;IAAW,CAAE,EAC1D;MAAED,KAAK,EAAEf,iBAAiB,CAACuG,SAAS;MAAEvF,KAAK,EAAE;IAAW,CAAE,EAC1D;MAAED,KAAK,EAAEf,iBAAiB,CAACwG,SAAS;MAAExF,KAAK,EAAE;IAAW,CAAE,CAC3D;IAED,KAAAqB,WAAW,GAAG,CACZ;MAAEtB,KAAK,EAAEd,eAAe,CAACwG,SAAS;MAAEzF,KAAK,EAAE;IAAW,CAAE,EACxD;MAAED,KAAK,EAAEd,eAAe,CAACyG,UAAU;MAAE1F,KAAK,EAAE;IAAY,CAAE,CAC3D;IASC,IAAI,CAACmB,QAAQ,GAAG,IAAI,CAAC4D,EAAE,CAACY,KAAK,CAAC;MAC5B5B,MAAM,EAAE,CAAC,EAAE,EAAEhF,UAAU,CAAC6G,QAAQ,CAAC;MACjCtD,IAAI,EAAE,CAAC,EAAE,EAAEvD,UAAU,CAAC6G,QAAQ,CAAC;MAC/BpD,cAAc,EAAE,CAAC,EAAE,EAAEzD,UAAU,CAAC6G,QAAQ,CAAC;MACzC/D,KAAK,EAAE,CAAC,EAAE,CAAC;MACXE,WAAW,EAAE,CAAC,EAAE;KACjB,CAAC;EACJ;EAEA8D,QAAQA,CAAA;IACN,IAAI,CAACX,WAAW,GAAG,IAAI,CAACD,WAAW,CAACa,cAAc,EAAE;IACpD,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,MAAMC,EAAE,GAAG,IAAI,CAACnB,KAAK,CAACoB,QAAQ,CAACC,QAAQ,CAAC3E,GAAG,CAAC,IAAI,CAAC;IACjD,IAAI,CAACyE,EAAE,EAAE;MACP,IAAI,CAAClB,MAAM,CAACqB,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;MACvC;;IAGF,IAAI,CAAChB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACH,kBAAkB,CAACoB,cAAc,CAACC,QAAQ,CAACL,EAAE,CAAC,CAAC,CAACM,SAAS,CAAC;MAC7DC,IAAI,EAAG3E,WAAW,IAAI;QACpB,IAAI,CAACA,WAAW,GAAGA,WAAW;QAC9B,IAAI,CAAC4E,cAAc,EAAE;QACrB,IAAI,CAACrB,OAAO,GAAG,KAAK;MACtB,CAAC;MACDvF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,qCAAqC;QAClD,IAAI,CAACuF,OAAO,GAAG,KAAK;QACpBsB,OAAO,CAAC7G,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD;KACD,CAAC;EACJ;EAEA4G,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC5E,WAAW,EAAE;MACpB,IAAI,CAACT,QAAQ,CAACuF,UAAU,CAAC;QACvB3C,MAAM,EAAE,IAAI,CAACnC,WAAW,CAACmC,MAAM;QAC/BzB,IAAI,EAAE,IAAI,CAACV,WAAW,CAACU,IAAI;QAC3BE,cAAc,EAAE,IAAI,CAACZ,WAAW,CAACY,cAAc;QAC/CX,KAAK,EAAE,IAAI,CAACD,WAAW,CAACC,KAAK,IAAI,EAAE;QACnCE,WAAW,EAAE,IAAI,CAACH,WAAW,CAACG,WAAW,IAAI;OAC9C,CAAC;;EAEN;EAEAf,UAAUA,CAAA;IACR,IAAI,CAAC0D,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAChC,IAAI,CAAC,IAAI,CAACA,SAAS,EAAE;MACnB,IAAI,CAAC8B,cAAc,EAAE,CAAC,CAAC;;EAE3B;;EAEA/F,QAAQA,CAAA;IACN,IAAI,IAAI,CAACU,QAAQ,CAACwF,KAAK,IAAI,IAAI,CAAC/E,WAAW,EAAE;MAC3C,IAAI,CAACH,QAAQ,GAAG,IAAI;MACpB,IAAI,CAAC7B,KAAK,GAAG,IAAI;MACjB,IAAI,CAACF,OAAO,GAAG,IAAI;MAEnB,MAAMkH,aAAa,GAA6B;QAC9C7C,MAAM,EAAE,IAAI,CAAC5C,QAAQ,CAACpB,KAAK,CAACgE,MAAM;QAClCzB,IAAI,EAAE,IAAI,CAACnB,QAAQ,CAACpB,KAAK,CAACuC,IAAI;QAC9BE,cAAc,EAAE,IAAI,CAACrB,QAAQ,CAACpB,KAAK,CAACyC,cAAc;QAClDX,KAAK,EAAE,IAAI,CAACV,QAAQ,CAACpB,KAAK,CAAC8B,KAAK,IAAIgF,SAAS;QAC7C9E,WAAW,EAAE,IAAI,CAACZ,QAAQ,CAACpB,KAAK,CAACgC,WAAW,IAAI8E;OACjD;MAED,IAAI,CAAC7B,kBAAkB,CAAC8B,iBAAiB,CAAC,IAAI,CAAClF,WAAW,CAACoE,EAAE,EAAEY,aAAa,CAAC,CAACN,SAAS,CAAC;QACtFC,IAAI,EAAGQ,kBAAkB,IAAI;UAC3B,IAAI,CAACnF,WAAW,GAAGmF,kBAAkB;UACrC,IAAI,CAACrH,OAAO,GAAG,mCAAmC;UAClD,IAAI,CAACgF,SAAS,GAAG,KAAK;UACtB,IAAI,CAACjD,QAAQ,GAAG,KAAK;QACvB,CAAC;QACD7B,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACA,KAAK,GAAG,iDAAiD;UAC9D,IAAI,CAAC6B,QAAQ,GAAG,KAAK;UACrBgF,OAAO,CAAC7G,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;OACD,CAAC;;EAEN;EAEAsD,QAAQA,CAAA;IACN,IAAI,IAAI,CAACtB,WAAW,IAAIoF,OAAO,CAAC,mDAAmD,CAAC,EAAE;MACpF,IAAI,CAAChC,kBAAkB,CAACiC,iBAAiB,CAAC,IAAI,CAACrF,WAAW,CAACoE,EAAE,CAAC,CAACM,SAAS,CAAC;QACvEC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACzB,MAAM,CAACqB,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;QACzC,CAAC;QACDvG,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACA,KAAK,GAAG,iDAAiD;UAC9D6G,OAAO,CAAC7G,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACtD;OACD,CAAC;;EAEN;EAEAoE,oBAAoBA,CAACD,MAAyB;IAC5C,OAAO,IAAI,CAACiB,kBAAkB,CAAChB,oBAAoB,CAACD,MAAM,CAAC;EAC7D;EAEAxB,kBAAkBA,CAACD,IAAqB;IACtC,OAAO,IAAI,CAAC0C,kBAAkB,CAACzC,kBAAkB,CAACD,IAAI,CAAC;EACzD;EAEAwB,mBAAmBA,CAACC,MAAyB;IAC3C,OAAO,IAAI,CAACiB,kBAAkB,CAAClB,mBAAmB,CAACC,MAAM,CAAC;EAC5D;EAEAK,UAAUA,CAAC8C,UAAkB;IAC3B,MAAM7C,IAAI,GAAG,IAAI8C,IAAI,CAACD,UAAU,CAAC;IACjC,OAAO7C,IAAI,CAAC+C,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;KACN,CAAC;EACJ;EAEAjD,UAAUA,CAACkD,UAAkB;IAC3B,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGF,UAAU,CAACG,KAAK,CAAC,GAAG,CAAC;IAC9C,MAAMC,IAAI,GAAGxB,QAAQ,CAACqB,KAAK,CAAC;IAC5B,MAAMI,IAAI,GAAGD,IAAI,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI;IACrC,MAAME,WAAW,GAAGF,IAAI,GAAG,EAAE,IAAI,EAAE;IACnC,OAAO,GAAGE,WAAW,IAAIJ,OAAO,IAAIG,IAAI,EAAE;EAC5C;EAEA7D,QAAQA,CAAA;IACN,OAAO,IAAI,CAACiB,WAAW,EAAE8C,IAAI,KAAK,QAAQ;EAC5C;EAEAC,SAASA,CAAA;IACP,OAAO,IAAI,CAAC/C,WAAW,EAAE8C,IAAI,KAAK,SAAS;EAC7C;EAEA1E,OAAOA,CAAA;IACL,IAAI,CAAC,IAAI,CAAC1B,WAAW,EAAE,OAAO,KAAK;IAEnC;IACA,OAAO,IAAI,CAACA,WAAW,CAACmC,MAAM,KAAK/E,iBAAiB,CAACuG,SAAS,IACvD,IAAI,CAAC3D,WAAW,CAACmC,MAAM,KAAK/E,iBAAiB,CAACwG,SAAS;EAChE;EAEAjC,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAAC3B,WAAW,EAAE,OAAO,KAAK;IAEnC,OAAO,IAAI,CAACA,WAAW,CAACmC,MAAM,KAAK/E,iBAAiB,CAACoG,OAAO,IACrD,IAAI,CAACxD,WAAW,CAACmC,MAAM,KAAK/E,iBAAiB,CAACqG,SAAS,IACvD,IAAI,CAACzD,WAAW,CAACmC,MAAM,KAAK/E,iBAAiB,CAACsG,SAAS;EAChE;EAEApB,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACtC,WAAW,EAAE,OAAO,EAAE;IAEhC,IAAI,IAAI,CAACqC,QAAQ,EAAE,EAAE;MACnB,OAAO,IAAI,CAACrC,WAAW,CAACsG,OAAO,CAACC,QAAQ;KACzC,MAAM;MACL,OAAO,IAAI,CAACvG,WAAW,CAACwG,MAAM,CAACD,QAAQ;;EAE3C;EAEAhE,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACvC,WAAW,EAAE,OAAO,EAAE;IAEhC,IAAI,IAAI,CAACqC,QAAQ,EAAE,EAAE;MACnB,OAAO,IAAI,CAACrC,WAAW,CAACsG,OAAO,CAACG,KAAK;KACtC,MAAM;MACL,MAAMD,MAAM,GAAG,IAAI,CAACxG,WAAW,CAACwG,MAAM;MACtC,OAAO,GAAGA,MAAM,CAACE,cAAc,IAAI,kBAAkB,MAAMF,MAAM,CAACG,WAAW,IAAI,kBAAkB,EAAE;;EAEzG;;;uBA1MW5D,2BAA2B,EAAAzF,EAAA,CAAAsJ,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAxJ,EAAA,CAAAsJ,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAzJ,EAAA,CAAAsJ,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAA3J,EAAA,CAAAsJ,iBAAA,CAAAM,EAAA,CAAAC,kBAAA,GAAA7J,EAAA,CAAAsJ,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA3BtE,2BAA2B;MAAAuE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCbxCtK,EAAA,CAAAC,cAAA,aAAkC;UAI5BD,EAAA,CAAAwB,UAAA,IAAAgJ,0CAAA,iBAKM;UAGNxK,EAAA,CAAAwB,UAAA,IAAAiJ,0CAAA,mBAsNM;UACRzK,EAAA,CAAAG,YAAA,EAAM;;;UA/NEH,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAW,UAAA,SAAA4J,GAAA,CAAAtE,OAAA,CAAa;UAQbjG,EAAA,CAAAK,SAAA,GAA6B;UAA7BL,EAAA,CAAAW,UAAA,UAAA4J,GAAA,CAAAtE,OAAA,IAAAsE,GAAA,CAAA7H,WAAA,CAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}