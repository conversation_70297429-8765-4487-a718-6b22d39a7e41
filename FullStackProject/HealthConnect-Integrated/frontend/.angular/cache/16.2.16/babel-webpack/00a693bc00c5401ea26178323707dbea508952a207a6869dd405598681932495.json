{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../core/services/chat.service\";\nimport * as i3 from \"../../core/services/auth.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../../shared/components/doctor-availability/doctor-availability.component\";\nimport * as i6 from \"../message-item/message-item.component\";\nimport * as i7 from \"../appointment-context/appointment-context.component\";\nconst _c0 = [\"messagesContainer\"];\nfunction ChatWindowComponent_div_0_app_doctor_availability_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-doctor-availability\", 26);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵproperty(\"doctorId\", (tmp_0_0 = ctx_r2.getOtherParticipant()) == null ? null : tmp_0_0.id)(\"showDetails\", false);\n  }\n}\nfunction ChatWindowComponent_div_0_app_appointment_context_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-appointment-context\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"appointmentId\", ctx_r3.chat.relatedAppointment.id)(\"chatType\", ctx_r3.chat.type);\n  }\n}\nfunction ChatWindowComponent_div_0_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"span\", 30);\n    i0.ɵɵtext(3, \"Loading messages...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ChatWindowComponent_div_0_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"i\", 32);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No messages yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"small\");\n    i0.ɵɵtext(5, \"Start the conversation!\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatWindowComponent_div_0_div_18_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"span\", 39);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r13.formatMessageDate(message_r11.createdAt));\n  }\n}\nfunction ChatWindowComponent_div_0_div_18_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ChatWindowComponent_div_0_div_18_ng_container_1_div_1_Template, 3, 1, \"div\", 36);\n    i0.ɵɵelement(2, \"app-message-item\", 37);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r11 = ctx.$implicit;\n    const i_r12 = ctx.index;\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.shouldShowDateSeparator(i_r12));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"message\", message_r11)(\"isOwn\", message_r11.sender.id === (ctx_r9.currentUser == null ? null : ctx_r9.currentUser.id));\n  }\n}\nfunction ChatWindowComponent_div_0_div_18_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41);\n    i0.ɵɵelement(2, \"span\")(3, \"span\")(4, \"span\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"small\", 42);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    let tmp_0_0;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", (tmp_0_0 = ctx_r10.getOtherParticipant()) == null ? null : tmp_0_0.fullName, \" is typing...\");\n  }\n}\nfunction ChatWindowComponent_div_0_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtemplate(1, ChatWindowComponent_div_0_div_18_ng_container_1_Template, 3, 3, \"ng-container\", 34);\n    i0.ɵɵtemplate(2, ChatWindowComponent_div_0_div_18_div_2_Template, 7, 1, \"div\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.messages);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.isTyping());\n  }\n}\nfunction ChatWindowComponent_div_0_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"small\", 44);\n    i0.ɵɵelement(2, \"i\", 45);\n    i0.ɵɵtext(3, \" Connection lost. Trying to reconnect... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatWindowComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n    i0.ɵɵelement(3, \"img\", 5);\n    i0.ɵɵelementStart(4, \"div\", 6)(5, \"h6\", 7);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"small\", 8);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, ChatWindowComponent_div_0_app_doctor_availability_9_Template, 1, 2, \"app-doctor-availability\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 10)(11, \"span\", 11);\n    i0.ɵɵelement(12, \"i\", 12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(13, ChatWindowComponent_div_0_app_appointment_context_13_Template, 1, 2, \"app-appointment-context\", 13);\n    i0.ɵɵelementStart(14, \"div\", 14, 15);\n    i0.ɵɵtemplate(16, ChatWindowComponent_div_0_div_16_Template, 4, 0, \"div\", 16);\n    i0.ɵɵtemplate(17, ChatWindowComponent_div_0_div_17_Template, 6, 0, \"div\", 17);\n    i0.ɵɵtemplate(18, ChatWindowComponent_div_0_div_18_Template, 3, 2, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 19)(20, \"form\", 20);\n    i0.ɵɵlistener(\"ngSubmit\", function ChatWindowComponent_div_0_Template_form_ngSubmit_20_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.sendMessage());\n    });\n    i0.ɵɵelementStart(21, \"div\", 21)(22, \"input\", 22);\n    i0.ɵɵlistener(\"input\", function ChatWindowComponent_div_0_Template_input_input_22_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.onTyping());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 23);\n    i0.ɵɵelement(24, \"i\", 24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(25, ChatWindowComponent_div_0_div_25_Template, 4, 0, \"div\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ((tmp_0_0 = ctx_r0.getOtherParticipant()) == null ? null : tmp_0_0.avatar) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", (tmp_1_0 = ctx_r0.getOtherParticipant()) == null ? null : tmp_1_0.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((tmp_2_0 = ctx_r0.getOtherParticipant()) == null ? null : tmp_2_0.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ((tmp_3_0 = ctx_r0.getOtherParticipant()) == null ? null : tmp_3_0.role) === \"DOCTOR\" ? \"Dr. \" + ((tmp_3_0 = ctx_r0.getOtherParticipant()) == null ? null : tmp_3_0.specialization) : \"Patient\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r0.getOtherParticipant()) == null ? null : tmp_4_0.role) === \"DOCTOR\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"connected\", ctx_r0.connectionStatus)(\"disconnected\", !ctx_r0.connectionStatus);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"bi-wifi\", ctx_r0.connectionStatus)(\"bi-wifi-off\", !ctx_r0.connectionStatus);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.chat == null ? null : ctx_r0.chat.relatedAppointment);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading && ctx_r0.messages.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading && ctx_r0.messages.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.messageForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.connectionStatus);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.messageForm.valid || !ctx_r0.connectionStatus);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.connectionStatus);\n  }\n}\nfunction ChatWindowComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47);\n    i0.ɵɵelement(2, \"i\", 32);\n    i0.ɵɵelementStart(3, \"h5\");\n    i0.ɵɵtext(4, \"Select a conversation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Choose a conversation from the list to start messaging\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport let ChatWindowComponent = /*#__PURE__*/(() => {\n  class ChatWindowComponent {\n    constructor(fb, chatService, authService) {\n      this.fb = fb;\n      this.chatService = chatService;\n      this.authService = authService;\n      this.chat = null;\n      this.messages = [];\n      this.loading = false;\n      this.typingUsers = new Set();\n      this.connectionStatus = false;\n      this.subscriptions = [];\n      this.shouldScrollToBottom = false;\n      this.messageForm = this.fb.group({\n        content: ['', [Validators.required, Validators.minLength(1)]]\n      });\n    }\n    ngOnInit() {\n      this.currentUser = this.authService.getCurrentUser();\n      this.subscribeToServices();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n      if (this.typingTimeout) {\n        clearTimeout(this.typingTimeout);\n      }\n    }\n    ngAfterViewChecked() {\n      if (this.shouldScrollToBottom) {\n        this.scrollToBottom();\n        this.shouldScrollToBottom = false;\n      }\n    }\n    subscribeToServices() {\n      // Subscribe to connection status\n      const connectionSub = this.chatService.connectionStatus$.subscribe(status => {\n        this.connectionStatus = status;\n      });\n      this.subscriptions.push(connectionSub);\n      // Subscribe to new messages\n      const messagesSub = this.chatService.messages$.subscribe(message => {\n        if (this.chat && message.chatId === this.chat.id) {\n          this.messages.push(message);\n          this.shouldScrollToBottom = true;\n          // Mark as read if not from current user\n          if (message.sender.id !== this.currentUser?.id) {\n            this.chatService.markMessagesAsRead(this.chat.id).subscribe();\n          }\n        }\n      });\n      this.subscriptions.push(messagesSub);\n      // Subscribe to typing notifications\n      const typingSub = this.chatService.typing$.subscribe(notification => {\n        this.handleTypingNotification(notification);\n      });\n      this.subscriptions.push(typingSub);\n    }\n    loadChat(chat) {\n      this.chat = chat;\n      this.messages = [];\n      this.typingUsers.clear();\n      if (chat) {\n        this.loadMessages();\n        this.chatService.subscribeToChatMessages(chat.id);\n      }\n    }\n    loadMessages() {\n      if (!this.chat) return;\n      this.loading = true;\n      this.chatService.getChatMessages(this.chat.id).subscribe({\n        next: messages => {\n          this.messages = messages.reverse(); // Reverse to show oldest first\n          this.loading = false;\n          this.shouldScrollToBottom = true;\n        },\n        error: error => {\n          console.error('Failed to load messages:', error);\n          this.loading = false;\n        }\n      });\n    }\n    sendMessage() {\n      if (!this.messageForm.valid || !this.chat || !this.connectionStatus) {\n        return;\n      }\n      const content = this.messageForm.get('content')?.value?.trim();\n      if (!content) {\n        return;\n      }\n      try {\n        this.chatService.sendMessage(this.chat.id, content);\n        this.messageForm.reset();\n        this.stopTyping();\n      } catch (error) {\n        console.error('Failed to send message:', error);\n      }\n    }\n    onTyping() {\n      if (!this.chat) return;\n      this.chatService.sendTypingNotification(this.chat.id, true);\n      // Clear existing timeout\n      if (this.typingTimeout) {\n        clearTimeout(this.typingTimeout);\n      }\n      // Set timeout to stop typing after 3 seconds\n      this.typingTimeout = setTimeout(() => {\n        this.stopTyping();\n      }, 3000);\n    }\n    stopTyping() {\n      if (!this.chat) return;\n      this.chatService.sendTypingNotification(this.chat.id, false);\n      if (this.typingTimeout) {\n        clearTimeout(this.typingTimeout);\n        this.typingTimeout = null;\n      }\n    }\n    handleTypingNotification(notification) {\n      if (notification.userId === this.currentUser?.id) {\n        return; // Ignore own typing notifications\n      }\n\n      if (notification.status === 'typing') {\n        this.typingUsers.add(notification.userId);\n      } else {\n        this.typingUsers.delete(notification.userId);\n      }\n    }\n    scrollToBottom() {\n      try {\n        if (this.messagesContainer) {\n          const element = this.messagesContainer.nativeElement;\n          element.scrollTop = element.scrollHeight;\n        }\n      } catch (err) {\n        console.error('Error scrolling to bottom:', err);\n      }\n    }\n    getOtherParticipant() {\n      if (!this.chat) return null;\n      return this.currentUser?.role === 'PATIENT' ? this.chat.doctor : this.chat.patient;\n    }\n    isTyping() {\n      return this.typingUsers.size > 0;\n    }\n    formatMessageTime(dateString) {\n      const date = new Date(dateString);\n      return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n    formatMessageDate(dateString) {\n      const date = new Date(dateString);\n      const today = new Date();\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      if (date.toDateString() === today.toDateString()) {\n        return 'Today';\n      } else if (date.toDateString() === yesterday.toDateString()) {\n        return 'Yesterday';\n      } else {\n        return date.toLocaleDateString();\n      }\n    }\n    shouldShowDateSeparator(index) {\n      if (index === 0) return true;\n      const currentMessage = this.messages[index];\n      const previousMessage = this.messages[index - 1];\n      const currentDate = new Date(currentMessage.createdAt).toDateString();\n      const previousDate = new Date(previousMessage.createdAt).toDateString();\n      return currentDate !== previousDate;\n    }\n    static {\n      this.ɵfac = function ChatWindowComponent_Factory(t) {\n        return new (t || ChatWindowComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ChatService), i0.ɵɵdirectiveInject(i3.AuthService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ChatWindowComponent,\n        selectors: [[\"app-chat-window\"]],\n        viewQuery: function ChatWindowComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          }\n        },\n        inputs: {\n          chat: \"chat\"\n        },\n        decls: 2,\n        vars: 2,\n        consts: [[\"class\", \"chat-window\", 4, \"ngIf\"], [\"class\", \"chat-placeholder\", 4, \"ngIf\"], [1, \"chat-window\"], [1, \"chat-header\"], [1, \"participant-info\"], [1, \"participant-avatar\", 3, \"src\", \"alt\"], [1, \"participant-details\"], [1, \"mb-0\"], [1, \"text-muted\"], [\"size\", \"sm\", 3, \"doctorId\", \"showDetails\", 4, \"ngIf\"], [1, \"connection-status\"], [1, \"status-indicator\"], [1, \"bi\"], [3, \"appointmentId\", \"chatType\", 4, \"ngIf\"], [1, \"messages-container\"], [\"messagesContainer\", \"\"], [\"class\", \"text-center p-3\", 4, \"ngIf\"], [\"class\", \"text-center p-4 text-muted\", 4, \"ngIf\"], [\"class\", \"messages-list\", 4, \"ngIf\"], [1, \"message-input-container\"], [1, \"message-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"input-group\"], [\"type\", \"text\", \"formControlName\", \"content\", \"placeholder\", \"Type a message...\", \"autocomplete\", \"off\", 1, \"form-control\", 3, \"disabled\", \"input\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [1, \"bi\", \"bi-send\"], [\"class\", \"connection-warning\", 4, \"ngIf\"], [\"size\", \"sm\", 3, \"doctorId\", \"showDetails\"], [3, \"appointmentId\", \"chatType\"], [1, \"text-center\", \"p-3\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\"], [1, \"visually-hidden\"], [1, \"text-center\", \"p-4\", \"text-muted\"], [1, \"bi\", \"bi-chat-square-text\", \"fs-1\", \"mb-3\", \"d-block\"], [1, \"messages-list\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"typing-indicator\", 4, \"ngIf\"], [\"class\", \"date-separator\", 4, \"ngIf\"], [3, \"message\", \"isOwn\"], [1, \"date-separator\"], [1, \"date-label\"], [1, \"typing-indicator\"], [1, \"typing-dots\"], [1, \"text-muted\", \"ms-2\"], [1, \"connection-warning\"], [1, \"text-warning\"], [1, \"bi\", \"bi-exclamation-triangle\", \"me-1\"], [1, \"chat-placeholder\"], [1, \"text-center\", \"text-muted\"]],\n        template: function ChatWindowComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, ChatWindowComponent_div_0_Template, 26, 21, \"div\", 0);\n            i0.ɵɵtemplate(1, ChatWindowComponent_div_1_Template, 7, 0, \"div\", 1);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.chat);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.chat);\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.DoctorAvailabilityComponent, i6.MessageItemComponent, i7.AppointmentContextComponent],\n        styles: [\".chat-window[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column}.chat-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1rem;border-bottom:1px solid #e9ecef;background-color:#f8f9fa}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]{display:flex;align-items:center}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .participant-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;object-fit:cover;margin-right:.75rem;border:2px solid #e9ecef}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .participant-details[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{font-weight:600;color:#333}.chat-header[_ngcontent-%COMP%]   .connection-status[_ngcontent-%COMP%]   .status-indicator.connected[_ngcontent-%COMP%]{color:#28a745}.chat-header[_ngcontent-%COMP%]   .connection-status[_ngcontent-%COMP%]   .status-indicator.disconnected[_ngcontent-%COMP%]{color:#dc3545}.messages-container[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:1rem;background-color:#f8f9fa}.messages-list[_ngcontent-%COMP%]   .date-separator[_ngcontent-%COMP%]{text-align:center;margin:1rem 0}.messages-list[_ngcontent-%COMP%]   .date-separator[_ngcontent-%COMP%]   .date-label[_ngcontent-%COMP%]{background-color:#e9ecef;padding:.25rem .75rem;border-radius:1rem;font-size:.75rem;color:#6c757d}.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;margin:.5rem 0}.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%]{display:flex;align-items:center;background-color:#e9ecef;padding:.5rem .75rem;border-radius:1rem}.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{width:6px;height:6px;background-color:#6c757d;border-radius:50%;margin:0 2px;animation:_ngcontent-%COMP%_typing 1.4s infinite ease-in-out}.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1){animation-delay:-.32s}.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2){animation-delay:-.16s}.message-input-container[_ngcontent-%COMP%]{padding:1rem;border-top:1px solid #e9ecef;background-color:#fff}.message-input-container[_ngcontent-%COMP%]   .message-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{border-right:none}.message-input-container[_ngcontent-%COMP%]   .message-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{box-shadow:none;border-color:#80bdff}.message-input-container[_ngcontent-%COMP%]   .message-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-left:none;padding:.5rem 1rem}.message-input-container[_ngcontent-%COMP%]   .message-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled{opacity:.5}.message-input-container[_ngcontent-%COMP%]   .connection-warning[_ngcontent-%COMP%]{margin-top:.5rem;text-align:center}.chat-placeholder[_ngcontent-%COMP%]{height:100%;display:flex;align-items:center;justify-content:center;background-color:#f8f9fa}.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%]{max-width:300px}.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#6c757d}.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{color:#495057}.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d}@keyframes _ngcontent-%COMP%_typing{0%,60%,to{transform:translateY(0)}30%{transform:translateY(-10px)}}@media (max-width: 768px){.chat-header[_ngcontent-%COMP%]{padding:.75rem}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .participant-avatar[_ngcontent-%COMP%]{width:35px;height:35px}.messages-container[_ngcontent-%COMP%], .message-input-container[_ngcontent-%COMP%]{padding:.75rem}}\"]\n      });\n    }\n  }\n  return ChatWindowComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}