{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/appointment.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction AppointmentContextComponent_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"span\", 7);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"span\", 8);\n    i0.ɵɵtext(5, \"Loading appointment details...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppointmentContextComponent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"i\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.error, \" \");\n  }\n}\nfunction AppointmentContextComponent_div_0_div_3_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵelementStart(2, \"span\", 39);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.appointment.reasonForVisit);\n  }\n}\nfunction AppointmentContextComponent_div_0_div_3_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"i\", 41);\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3, \"Upcoming:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.getTimeUntilAppointment(), \" \");\n  }\n}\nfunction AppointmentContextComponent_div_0_div_3_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3, \"Completed:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Follow-up discussion \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentContextComponent_div_0_div_3_button_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function AppointmentContextComponent_div_0_div_3_button_41_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r8.openMeetingLink(ctx_r8.appointment.meetingLink));\n    });\n    i0.ɵɵelement(1, \"i\", 45);\n    i0.ɵɵtext(2, \" Join Call \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentContextComponent_div_0_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"div\", 13);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementStart(4, \"h6\", 14);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function AppointmentContextComponent_div_0_div_3_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.navigateToAppointment());\n    });\n    i0.ɵɵelement(7, \"i\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 17)(9, \"div\", 18)(10, \"div\", 19)(11, \"div\", 20);\n    i0.ɵɵelement(12, \"i\", 21);\n    i0.ɵɵelementStart(13, \"span\", 22);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 19)(17, \"div\", 20);\n    i0.ɵɵelement(18, \"i\", 23);\n    i0.ɵɵelementStart(19, \"span\", 22);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 19)(22, \"div\", 20);\n    i0.ɵɵelement(23, \"i\", 24);\n    i0.ɵɵelementStart(24, \"span\", 22);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 19)(27, \"div\", 20);\n    i0.ɵɵelement(28, \"i\", 25);\n    i0.ɵɵelementStart(29, \"span\", 26);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(31, \"div\", 27);\n    i0.ɵɵelement(32, \"i\", 28);\n    i0.ɵɵelementStart(33, \"span\", 22);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, AppointmentContextComponent_div_0_div_3_div_35_Template, 4, 1, \"div\", 29);\n    i0.ɵɵelementStart(36, \"div\", 30);\n    i0.ɵɵtemplate(37, AppointmentContextComponent_div_0_div_3_div_37_Template, 5, 1, \"div\", 31);\n    i0.ɵɵtemplate(38, AppointmentContextComponent_div_0_div_3_div_38_Template, 5, 0, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 33)(40, \"div\", 34);\n    i0.ɵɵtemplate(41, AppointmentContextComponent_div_0_div_3_button_41_Template, 3, 0, \"button\", 35);\n    i0.ɵɵelementStart(42, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function AppointmentContextComponent_div_0_div_3_Template_button_click_42_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.navigateToAppointment());\n    });\n    i0.ɵɵelement(43, \"i\", 37);\n    i0.ɵɵtext(44, \" View Details \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r3.getContextIcon() + \" me-2\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getContextTitle());\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 17, ctx_r3.appointment.date, \"mediumDate\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r3.appointment.startTime, \" - \", ctx_r3.appointment.endTime, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.appointment.doctor.fullName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(\"badge-\" + ctx_r3.appointment.status.toLowerCase());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.appointment.status, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r3.appointment.type === \"VIDEO_CALL\" ? \"fa-video\" : \"fa-user-friends\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.appointment.type === \"VIDEO_CALL\" ? \"Video Call\" : \"In-Person\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.appointment.reasonForVisit);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isBeforeAppointment());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isAfterAppointment());\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.appointment.type === \"VIDEO_CALL\" && ctx_r3.appointment.meetingLink && ctx_r3.isBeforeAppointment());\n  }\n}\nfunction AppointmentContextComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, AppointmentContextComponent_div_0_div_1_Template, 6, 0, \"div\", 2);\n    i0.ɵɵtemplate(2, AppointmentContextComponent_div_0_div_2_Template, 3, 1, \"div\", 3);\n    i0.ɵɵtemplate(3, AppointmentContextComponent_div_0_div_3_Template, 45, 20, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.error);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.appointment && !ctx_r0.loading && !ctx_r0.error);\n  }\n}\nexport let AppointmentContextComponent = /*#__PURE__*/(() => {\n  class AppointmentContextComponent {\n    constructor(appointmentService, router) {\n      this.appointmentService = appointmentService;\n      this.router = router;\n      this.appointment = null;\n      this.loading = false;\n      this.error = null;\n    }\n    ngOnInit() {\n      if (this.appointmentId) {\n        this.loadAppointment();\n      }\n    }\n    loadAppointment() {\n      if (!this.appointmentId) return;\n      this.loading = true;\n      this.error = null;\n      this.appointmentService.getAppointment(this.appointmentId).subscribe({\n        next: appointment => {\n          this.appointment = appointment;\n          this.loading = false;\n        },\n        error: error => {\n          this.error = 'Failed to load appointment details';\n          this.loading = false;\n          console.error('Error loading appointment:', error);\n        }\n      });\n    }\n    navigateToAppointment() {\n      if (this.appointment) {\n        this.router.navigate(['/appointments', this.appointment.id]);\n      }\n    }\n    getContextTitle() {\n      switch (this.chatType) {\n        case 'PRE_APPOINTMENT':\n          return 'Pre-Appointment Discussion';\n        case 'POST_APPOINTMENT':\n          return 'Post-Appointment Follow-up';\n        case 'URGENT':\n          return 'Urgent Medical Consultation';\n        case 'PRESCRIPTION_INQUIRY':\n          return 'Prescription Questions';\n        case 'FOLLOW_UP':\n          return 'Follow-up Care';\n        default:\n          return 'Appointment Discussion';\n      }\n    }\n    getContextIcon() {\n      switch (this.chatType) {\n        case 'PRE_APPOINTMENT':\n          return 'fas fa-clock text-info';\n        case 'POST_APPOINTMENT':\n          return 'fas fa-check-circle text-success';\n        case 'URGENT':\n          return 'fas fa-exclamation-triangle text-danger';\n        case 'PRESCRIPTION_INQUIRY':\n          return 'fas fa-pills text-primary';\n        case 'FOLLOW_UP':\n          return 'fas fa-stethoscope text-secondary';\n        default:\n          return 'fas fa-calendar text-primary';\n      }\n    }\n    isBeforeAppointment() {\n      if (!this.appointment) return false;\n      const appointmentDateTime = new Date(`${this.appointment.date}T${this.appointment.startTime}`);\n      return appointmentDateTime > new Date();\n    }\n    isAfterAppointment() {\n      if (!this.appointment) return false;\n      const appointmentDateTime = new Date(`${this.appointment.date}T${this.appointment.endTime}`);\n      return appointmentDateTime < new Date();\n    }\n    getTimeUntilAppointment() {\n      if (!this.appointment) return '';\n      const appointmentDateTime = new Date(`${this.appointment.date}T${this.appointment.startTime}`);\n      const now = new Date();\n      const diffMs = appointmentDateTime.getTime() - now.getTime();\n      if (diffMs <= 0) return 'Appointment has passed';\n      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n      const diffHours = Math.floor(diffMs % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n      const diffMinutes = Math.floor(diffMs % (1000 * 60 * 60) / (1000 * 60));\n      if (diffDays > 0) {\n        return `${diffDays} day${diffDays > 1 ? 's' : ''} remaining`;\n      } else if (diffHours > 0) {\n        return `${diffHours} hour${diffHours > 1 ? 's' : ''} remaining`;\n      } else {\n        return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} remaining`;\n      }\n    }\n    openMeetingLink(link) {\n      window.open(link, '_blank');\n    }\n    static {\n      this.ɵfac = function AppointmentContextComponent_Factory(t) {\n        return new (t || AppointmentContextComponent)(i0.ɵɵdirectiveInject(i1.AppointmentService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppointmentContextComponent,\n        selectors: [[\"app-appointment-context\"]],\n        inputs: {\n          appointmentId: \"appointmentId\",\n          chatType: \"chatType\"\n        },\n        decls: 1,\n        vars: 1,\n        consts: [[\"class\", \"appointment-context-card\", 4, \"ngIf\"], [1, \"appointment-context-card\"], [\"class\", \"text-center p-3\", 4, \"ngIf\"], [\"class\", \"alert alert-warning mb-0\", 4, \"ngIf\"], [\"class\", \"appointment-context\", 4, \"ngIf\"], [1, \"text-center\", \"p-3\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\"], [1, \"visually-hidden\"], [1, \"ms-2\"], [1, \"alert\", \"alert-warning\", \"mb-0\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-2\"], [1, \"appointment-context\"], [1, \"context-header\"], [1, \"d-flex\", \"align-items-center\"], [1, \"mb-0\", \"fw-bold\"], [\"type\", \"button\", \"title\", \"View full appointment details\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"fas\", \"fa-external-link-alt\"], [1, \"appointment-summary\"], [1, \"row\", \"g-2\"], [1, \"col-md-6\"], [1, \"summary-item\"], [1, \"fas\", \"fa-calendar-alt\", \"text-muted\", \"me-2\"], [1, \"fw-medium\"], [1, \"fas\", \"fa-clock\", \"text-muted\", \"me-2\"], [1, \"fas\", \"fa-user-md\", \"text-muted\", \"me-2\"], [1, \"fas\", \"fa-tag\", \"text-muted\", \"me-2\"], [1, \"badge\"], [1, \"summary-item\", \"mt-2\"], [1, \"text-muted\", \"me-2\"], [\"class\", \"summary-item mt-2\", 4, \"ngIf\"], [1, \"time-context\", \"mt-3\"], [\"class\", \"alert alert-info py-2 mb-0\", 4, \"ngIf\"], [\"class\", \"alert alert-success py-2 mb-0\", 4, \"ngIf\"], [1, \"quick-actions\", \"mt-3\"], [\"role\", \"group\", 1, \"btn-group\", \"w-100\"], [\"type\", \"button\", \"class\", \"btn btn-sm btn-primary\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-eye\", \"me-1\"], [1, \"fas\", \"fa-notes-medical\", \"text-muted\", \"me-2\"], [1, \"text-muted\"], [1, \"alert\", \"alert-info\", \"py-2\", \"mb-0\"], [1, \"fas\", \"fa-info-circle\", \"me-2\"], [1, \"alert\", \"alert-success\", \"py-2\", \"mb-0\"], [1, \"fas\", \"fa-check-circle\", \"me-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-video\", \"me-1\"]],\n        template: function AppointmentContextComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, AppointmentContextComponent_div_0_Template, 4, 3, \"div\", 0);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.appointmentId);\n          }\n        },\n        dependencies: [i3.NgIf, i3.DatePipe],\n        styles: [\".appointment-context-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8f9fa 0%,#e9ecef 100%);border:1px solid #dee2e6;border-radius:12px;margin-bottom:1rem;overflow:hidden;box-shadow:0 2px 4px #0000000d}.context-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1rem;background:white;border-bottom:1px solid #dee2e6}.context-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#495057}.appointment-summary[_ngcontent-%COMP%]{padding:1rem}.summary-item[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:.5rem}.summary-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.summary-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:16px;text-align:center}.badge[_ngcontent-%COMP%]{font-size:.75rem}.badge.badge-pending[_ngcontent-%COMP%]{background-color:#ffc107;color:#000}.badge.badge-scheduled[_ngcontent-%COMP%]{background-color:#17a2b8;color:#fff}.badge.badge-confirmed[_ngcontent-%COMP%]{background-color:#28a745;color:#fff}.badge.badge-completed[_ngcontent-%COMP%]{background-color:#6c757d;color:#fff}.badge.badge-cancelled[_ngcontent-%COMP%]{background-color:#dc3545;color:#fff}.time-context[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{border-radius:8px;font-size:.875rem}.quick-actions[_ngcontent-%COMP%]{padding:0 1rem 1rem}.quick-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-radius:6px;font-size:.875rem}@media (max-width: 768px){.context-header[_ngcontent-%COMP%], .appointment-summary[_ngcontent-%COMP%]{padding:.75rem}.quick-actions[_ngcontent-%COMP%]{padding:0 .75rem .75rem}.quick-actions[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]{flex-direction:column}.quick-actions[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-radius:6px!important;margin-bottom:.25rem}.quick-actions[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child{margin-bottom:0}}.appointment-context-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideIn .3s ease-out}@keyframes _ngcontent-%COMP%_slideIn{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}\"]\n      });\n    }\n  }\n  return AppointmentContextComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}