{"ast": null, "code": "'use strict';\n\n// Few cool transports do work only for same-origin. In order to make\n// them work cross-domain we shall use iframe, served from the\n// remote domain. New browsers have capabilities to communicate with\n// cross domain iframe using postMessage(). In IE it was implemented\n// from IE 8+, but of course, IE got some details wrong:\n//    http://msdn.microsoft.com/en-us/library/cc197015(v=VS.85).aspx\n//    http://stevesouders.com/misc/test-postmessage.php\nvar inherits = require('inherits'),\n  EventEmitter = require('events').EventEmitter,\n  version = require('../version'),\n  urlUtils = require('../utils/url'),\n  iframeUtils = require('../utils/iframe'),\n  eventUtils = require('../utils/event'),\n  random = require('../utils/random');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:transport:iframe');\n}\nfunction IframeTransport(transport, transUrl, baseUrl) {\n  if (!IframeTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n  EventEmitter.call(this);\n  var self = this;\n  this.origin = urlUtils.getOrigin(baseUrl);\n  this.baseUrl = baseUrl;\n  this.transUrl = transUrl;\n  this.transport = transport;\n  this.windowId = random.string(8);\n  var iframeUrl = urlUtils.addPath(baseUrl, '/iframe.html') + '#' + this.windowId;\n  debug(transport, transUrl, iframeUrl);\n  this.iframeObj = iframeUtils.createIframe(iframeUrl, function (r) {\n    debug('err callback');\n    self.emit('close', 1006, 'Unable to load an iframe (' + r + ')');\n    self.close();\n  });\n  this.onmessageCallback = this._message.bind(this);\n  eventUtils.attachEvent('message', this.onmessageCallback);\n}\ninherits(IframeTransport, EventEmitter);\nIframeTransport.prototype.close = function () {\n  debug('close');\n  this.removeAllListeners();\n  if (this.iframeObj) {\n    eventUtils.detachEvent('message', this.onmessageCallback);\n    try {\n      // When the iframe is not loaded, IE raises an exception\n      // on 'contentWindow'.\n      this.postMessage('c');\n    } catch (x) {\n      // intentionally empty\n    }\n    this.iframeObj.cleanup();\n    this.iframeObj = null;\n    this.onmessageCallback = this.iframeObj = null;\n  }\n};\nIframeTransport.prototype._message = function (e) {\n  debug('message', e.data);\n  if (!urlUtils.isOriginEqual(e.origin, this.origin)) {\n    debug('not same origin', e.origin, this.origin);\n    return;\n  }\n  var iframeMessage;\n  try {\n    iframeMessage = JSON.parse(e.data);\n  } catch (ignored) {\n    debug('bad json', e.data);\n    return;\n  }\n  if (iframeMessage.windowId !== this.windowId) {\n    debug('mismatched window id', iframeMessage.windowId, this.windowId);\n    return;\n  }\n  switch (iframeMessage.type) {\n    case 's':\n      this.iframeObj.loaded();\n      // window global dependency\n      this.postMessage('s', JSON.stringify([version, this.transport, this.transUrl, this.baseUrl]));\n      break;\n    case 't':\n      this.emit('message', iframeMessage.data);\n      break;\n    case 'c':\n      var cdata;\n      try {\n        cdata = JSON.parse(iframeMessage.data);\n      } catch (ignored) {\n        debug('bad json', iframeMessage.data);\n        return;\n      }\n      this.emit('close', cdata[0], cdata[1]);\n      this.close();\n      break;\n  }\n};\nIframeTransport.prototype.postMessage = function (type, data) {\n  debug('postMessage', type, data);\n  this.iframeObj.post(JSON.stringify({\n    windowId: this.windowId,\n    type: type,\n    data: data || ''\n  }), this.origin);\n};\nIframeTransport.prototype.send = function (message) {\n  debug('send', message);\n  this.postMessage('m', message);\n};\nIframeTransport.enabled = function () {\n  return iframeUtils.iframeEnabled;\n};\nIframeTransport.transportName = 'iframe';\nIframeTransport.roundTrips = 2;\nmodule.exports = IframeTransport;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}