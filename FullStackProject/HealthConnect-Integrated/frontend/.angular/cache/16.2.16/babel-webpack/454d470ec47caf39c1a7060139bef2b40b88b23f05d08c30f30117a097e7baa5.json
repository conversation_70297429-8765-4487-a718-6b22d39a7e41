{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthGuard } from './core/guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: '/auth/login',\n  pathMatch: 'full'\n}, {\n  path: 'auth',\n  loadChildren: () => import('./auth/auth.module').then(m => m.AuthModule)\n}, {\n  path: 'patient',\n  canActivate: [AuthGuard],\n  data: {\n    roles: ['PATIENT']\n  },\n  loadChildren: () => import('./patient/patient.module').then(m => m.PatientModule)\n}, {\n  path: 'doctor',\n  canActivate: [AuthGuard],\n  data: {\n    roles: ['DOCTOR']\n  },\n  loadChildren: () => import('./doctor/doctor.module').then(m => m.DoctorModule)\n}, {\n  path: 'profile',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./profile/profile.module').then(m => m.ProfileModule)\n}, {\n  path: 'appointments',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./appointments/appointments.module').then(m => m.AppointmentsModule)\n}, {\n  path: '**',\n  redirectTo: '/auth/login'\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "redirectTo", "pathMatch", "loadChildren", "then", "m", "AuthModule", "canActivate", "data", "roles", "PatientModule", "DoctorModule", "ProfileModule", "AppointmentsModule", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { AuthGuard } from './core/guards/auth.guard';\n\nconst routes: Routes = [\n  {\n    path: '',\n    redirectTo: '/auth/login',\n    pathMatch: 'full'\n  },\n  {\n    path: 'auth',\n    loadChildren: () => import('./auth/auth.module').then(m => m.AuthModule)\n  },\n  {\n    path: 'patient',\n    canActivate: [AuthGuard],\n    data: { roles: ['PATIENT'] },\n    loadChildren: () => import('./patient/patient.module').then(m => m.PatientModule)\n  },\n  {\n    path: 'doctor',\n    canActivate: [AuthGuard],\n    data: { roles: ['DOCTOR'] },\n    loadChildren: () => import('./doctor/doctor.module').then(m => m.DoctorModule)\n  },\n  {\n    path: 'profile',\n    canActivate: [AuthGuard],\n    loadChildren: () => import('./profile/profile.module').then(m => m.ProfileModule)\n  },\n  {\n    path: 'appointments',\n    canActivate: [AuthGuard],\n    loadChildren: () => import('./appointments/appointments.module').then(m => m.AppointmentsModule)\n  },\n  {\n    path: '**',\n    redirectTo: '/auth/login'\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,SAAS,QAAQ,0BAA0B;;;AAEpD,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,aAAa;EACzBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU;CACxE,EACD;EACEN,IAAI,EAAE,SAAS;EACfO,WAAW,EAAE,CAACT,SAAS,CAAC;EACxBU,IAAI,EAAE;IAAEC,KAAK,EAAE,CAAC,SAAS;EAAC,CAAE;EAC5BN,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,aAAa;CACjF,EACD;EACEV,IAAI,EAAE,QAAQ;EACdO,WAAW,EAAE,CAACT,SAAS,CAAC;EACxBU,IAAI,EAAE;IAAEC,KAAK,EAAE,CAAC,QAAQ;EAAC,CAAE;EAC3BN,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,YAAY;CAC9E,EACD;EACEX,IAAI,EAAE,SAAS;EACfO,WAAW,EAAE,CAACT,SAAS,CAAC;EACxBK,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACO,aAAa;CACjF,EACD;EACEZ,IAAI,EAAE,cAAc;EACpBO,WAAW,EAAE,CAACT,SAAS,CAAC;EACxBK,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACQ,kBAAkB;CAChG,EACD;EACEb,IAAI,EAAE,IAAI;EACVC,UAAU,EAAE;CACb,CACF;AAMD,OAAM,MAAOa,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBjB,YAAY,CAACkB,OAAO,CAAChB,MAAM,CAAC,EAC5BF,YAAY;IAAA;EAAA;;;2EAEXiB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAApB,YAAA;IAAAqB,OAAA,GAFjBrB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}