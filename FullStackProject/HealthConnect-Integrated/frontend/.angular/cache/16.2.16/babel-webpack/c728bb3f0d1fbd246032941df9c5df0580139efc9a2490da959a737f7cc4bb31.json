{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  XhrDriver = require('../driver/xhr');\nfunction XHRLocalObject(method, url, payload /*, opts */) {\n  XhrDriver.call(this, method, url, payload, {\n    noCredentials: true\n  });\n}\ninherits(XHRLocalObject, XhrDriver);\nXHRLocalObject.enabled = XhrDriver.enabled;\nmodule.exports = XHRLocalObject;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}