{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  EventEmitter = require('events').EventEmitter,\n  EventSourceDriver = require('eventsource');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:eventsource');\n}\nfunction EventSourceReceiver(url) {\n  debug(url);\n  EventEmitter.call(this);\n  var self = this;\n  var es = this.es = new EventSourceDriver(url);\n  es.onmessage = function (e) {\n    debug('message', e.data);\n    self.emit('message', decodeURI(e.data));\n  };\n  es.onerror = function (e) {\n    debug('error', es.readyState, e);\n    // ES on reconnection has readyState = 0 or 1.\n    // on network error it's CLOSED = 2\n    var reason = es.readyState !== 2 ? 'network' : 'permanent';\n    self._cleanup();\n    self._close(reason);\n  };\n}\ninherits(EventSourceReceiver, EventEmitter);\nEventSourceReceiver.prototype.abort = function () {\n  debug('abort');\n  this._cleanup();\n  this._close('user');\n};\nEventSourceReceiver.prototype._cleanup = function () {\n  debug('cleanup');\n  var es = this.es;\n  if (es) {\n    es.onmessage = es.onerror = null;\n    es.close();\n    this.es = null;\n  }\n};\nEventSourceReceiver.prototype._close = function (reason) {\n  debug('close', reason);\n  var self = this;\n  // Safari and chrome < 15 crash if we close window before\n  // waiting for ES cleanup. See:\n  // https://code.google.com/p/chromium/issues/detail?id=89155\n  setTimeout(function () {\n    self.emit('close', null, reason);\n    self.removeAllListeners();\n  }, 200);\n};\nmodule.exports = EventSourceReceiver;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}