{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction MessageItemComponent_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 7);\n    i0.ɵɵelement(1, \"i\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.getStatusClass());\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.getStatusIcon());\n  }\n}\nexport class MessageItemComponent {\n  constructor() {\n    this.isOwn = false;\n  }\n  formatTime(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString([], {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  getStatusIcon() {\n    switch (this.message.status) {\n      case 'SENT':\n        return 'bi-check';\n      case 'DELIVERED':\n        return 'bi-check2';\n      case 'READ':\n        return 'bi-check2-all';\n      default:\n        return 'bi-clock';\n    }\n  }\n  getStatusClass() {\n    return this.message.status === 'READ' ? 'text-primary' : 'text-muted';\n  }\n  static {\n    this.ɵfac = function MessageItemComponent_Factory(t) {\n      return new (t || MessageItemComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageItemComponent,\n      selectors: [[\"app-message-item\"]],\n      inputs: {\n        message: \"message\",\n        isOwn: \"isOwn\"\n      },\n      decls: 9,\n      vars: 7,\n      consts: [[1, \"message-item\"], [1, \"message-content\"], [1, \"message-bubble\"], [1, \"message-text\", \"mb-1\"], [1, \"message-meta\"], [1, \"message-time\"], [\"class\", \"message-status ms-1\", 3, \"class\", 4, \"ngIf\"], [1, \"message-status\", \"ms-1\"], [1, \"bi\"]],\n      template: function MessageItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"p\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"small\", 5);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, MessageItemComponent_span_8_Template, 2, 4, \"span\", 6);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"own-message\", ctx.isOwn)(\"other-message\", !ctx.isOwn);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.message.content);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.formatTime(ctx.message.createdAt));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isOwn);\n        }\n      },\n      dependencies: [i1.NgIf],\n      styles: [\".message-item[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n  display: flex;\\n}\\n.message-item.own-message[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n}\\n.message-item.own-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n  border-bottom-right-radius: 0.25rem;\\n}\\n.message-item.own-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.8);\\n}\\n.message-item.other-message[_ngcontent-%COMP%] {\\n  justify-content: flex-start;\\n}\\n.message-item.other-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%] {\\n  background-color: white;\\n  color: #333;\\n  border: 1px solid #e9ecef;\\n  border-bottom-left-radius: 0.25rem;\\n}\\n.message-item.other-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  max-width: 70%;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1rem;\\n  border-radius: 1rem;\\n  word-wrap: break-word;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n}\\n.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%] {\\n  line-height: 1.4;\\n  margin: 0;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-end;\\n  margin-top: 0.25rem;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%]   .message-status[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n    max-width: 85%;\\n  }\\n  .message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%] {\\n    padding: 0.5rem 0.75rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵclassMap", "ctx_r0", "getStatusClass", "ɵɵadvance", "getStatusIcon", "MessageItemComponent", "constructor", "isOwn", "formatTime", "dateString", "date", "Date", "toLocaleTimeString", "hour", "minute", "message", "status", "selectors", "inputs", "decls", "vars", "consts", "template", "MessageItemComponent_Template", "rf", "ctx", "ɵɵtext", "ɵɵtemplate", "MessageItemComponent_span_8_Template", "ɵɵclassProp", "ɵɵtextInterpolate", "content", "createdAt", "ɵɵproperty"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/chat/message-item/message-item.component.ts", "/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/chat/message-item/message-item.component.html"], "sourcesContent": ["import { Component, Input } from '@angular/core';\nimport { Message } from '../../core/models/chat.model';\n\n@Component({\n  selector: 'app-message-item',\n  templateUrl: './message-item.component.html',\n  styleUrls: ['./message-item.component.scss']\n})\nexport class MessageItemComponent {\n  @Input() message!: Message;\n  @Input() isOwn: boolean = false;\n\n  formatTime(dateString: string): string {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  }\n\n  getStatusIcon(): string {\n    switch (this.message.status) {\n      case 'SENT':\n        return 'bi-check';\n      case 'DELIVERED':\n        return 'bi-check2';\n      case 'READ':\n        return 'bi-check2-all';\n      default:\n        return 'bi-clock';\n    }\n  }\n\n  getStatusClass(): string {\n    return this.message.status === 'READ' ? 'text-primary' : 'text-muted';\n  }\n}\n", "<div class=\"message-item\" [class.own-message]=\"isOwn\" [class.other-message]=\"!isOwn\">\n  <div class=\"message-content\">\n    <div class=\"message-bubble\">\n      <p class=\"message-text mb-1\">{{ message.content }}</p>\n      <div class=\"message-meta\">\n        <small class=\"message-time\">{{ formatTime(message.createdAt) }}</small>\n        <span *ngIf=\"isOwn\" class=\"message-status ms-1\" [class]=\"getStatusClass()\">\n          <i class=\"bi\" [class]=\"getStatusIcon()\"></i>\n        </span>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;ICMQA,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAE,SAAA,WAA4C;IAC9CF,EAAA,CAAAG,YAAA,EAAO;;;;IAFyCH,EAAA,CAAAI,UAAA,CAAAC,MAAA,CAAAC,cAAA,GAA0B;IAC1DN,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAI,UAAA,CAAAC,MAAA,CAAAG,aAAA,GAAyB;;;ADCjD,OAAM,MAAOC,oBAAoB;EALjCC,YAAA;IAOW,KAAAC,KAAK,GAAY,KAAK;;EAE/BC,UAAUA,CAACC,UAAkB;IAC3B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,EAAE,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAS,CAAE,CAAC;EAC5E;EAEAV,aAAaA,CAAA;IACX,QAAQ,IAAI,CAACW,OAAO,CAACC,MAAM;MACzB,KAAK,MAAM;QACT,OAAO,UAAU;MACnB,KAAK,WAAW;QACd,OAAO,WAAW;MACpB,KAAK,MAAM;QACT,OAAO,eAAe;MACxB;QACE,OAAO,UAAU;;EAEvB;EAEAd,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACa,OAAO,CAACC,MAAM,KAAK,MAAM,GAAG,cAAc,GAAG,YAAY;EACvE;;;uBAxBWX,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAAY,SAAA;MAAAC,MAAA;QAAAH,OAAA;QAAAR,KAAA;MAAA;MAAAY,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRjC5B,EAAA,CAAAC,cAAA,aAAqF;UAGlDD,EAAA,CAAA8B,MAAA,GAAqB;UAAA9B,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,aAA0B;UACID,EAAA,CAAA8B,MAAA,GAAmC;UAAA9B,EAAA,CAAAG,YAAA,EAAQ;UACvEH,EAAA,CAAA+B,UAAA,IAAAC,oCAAA,kBAEO;UACThC,EAAA,CAAAG,YAAA,EAAM;;;UATcH,EAAA,CAAAiC,WAAA,gBAAAJ,GAAA,CAAAlB,KAAA,CAA2B,mBAAAkB,GAAA,CAAAlB,KAAA;UAGlBX,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAkC,iBAAA,CAAAL,GAAA,CAAAV,OAAA,CAAAgB,OAAA,CAAqB;UAEpBnC,EAAA,CAAAO,SAAA,GAAmC;UAAnCP,EAAA,CAAAkC,iBAAA,CAAAL,GAAA,CAAAjB,UAAA,CAAAiB,GAAA,CAAAV,OAAA,CAAAiB,SAAA,EAAmC;UACxDpC,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAqC,UAAA,SAAAR,GAAA,CAAAlB,KAAA,CAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}