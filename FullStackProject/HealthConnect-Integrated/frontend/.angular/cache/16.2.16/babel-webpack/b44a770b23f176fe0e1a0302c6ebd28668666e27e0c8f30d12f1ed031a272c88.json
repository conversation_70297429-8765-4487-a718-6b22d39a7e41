{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  AjaxBasedTransport = require('./lib/ajax-based'),\n  XhrReceiver = require('./receiver/xhr'),\n  XHRCorsObject = require('./sender/xhr-cors'),\n  XHRLocalObject = require('./sender/xhr-local');\nfunction XhrPollingTransport(transUrl) {\n  if (!XHRLocalObject.enabled && !XHRCorsObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr', XhrReceiver, XHRCorsObject);\n}\ninherits(XhrPollingTransport, AjaxBasedTransport);\nXhrPollingTransport.enabled = function (info) {\n  if (info.nullOrigin) {\n    return false;\n  }\n  if (XHRLocalObject.enabled && info.sameOrigin) {\n    return true;\n  }\n  return XHRCorsObject.enabled;\n};\nXhrPollingTransport.transportName = 'xhr-polling';\nXhrPollingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XhrPollingTransport;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}