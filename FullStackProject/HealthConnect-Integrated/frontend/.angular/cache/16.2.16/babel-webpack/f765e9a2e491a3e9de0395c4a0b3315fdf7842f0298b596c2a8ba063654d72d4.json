{"ast": null, "code": "import { interval } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction DoctorAvailabilityComponent_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.getStatusText());\n  }\n}\nfunction DoctorAvailabilityComponent_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"i\", 10);\n    i0.ɵɵelementStart(2, \"small\", 11);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.availability.expectedResponseTime);\n  }\n}\nfunction DoctorAvailabilityComponent_div_4_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"i\", 12);\n    i0.ɵɵelementStart(2, \"small\", 11);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" Chat hours: \", ctx_r3.availability.chatStartTime, \" - \", ctx_r3.availability.chatEndTime, \" \");\n  }\n}\nfunction DoctorAvailabilityComponent_div_4_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"i\", 13);\n    i0.ɵɵelementStart(2, \"small\", 11);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.availability.customMessage);\n  }\n}\nfunction DoctorAvailabilityComponent_div_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"i\", 14);\n    i0.ɵɵelementStart(2, \"small\", 11);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Last seen \", ctx_r5.getLastSeenText(), \"\");\n  }\n}\nfunction DoctorAvailabilityComponent_div_4_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16);\n    i0.ɵɵelement(2, \"i\", 17);\n    i0.ɵɵelementStart(3, \"small\");\n    i0.ɵɵtext(4, \"Outside chat hours\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DoctorAvailabilityComponent_div_4_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵelement(2, \"i\", 20);\n    i0.ɵɵelementStart(3, \"small\");\n    i0.ɵɵtext(4, \"Currently in consultation\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DoctorAvailabilityComponent_div_4_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 21);\n    i0.ɵɵelement(2, \"i\", 22);\n    i0.ɵɵelementStart(3, \"small\");\n    i0.ɵɵtext(4, \"Emergency contacts only\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DoctorAvailabilityComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵtemplate(1, DoctorAvailabilityComponent_div_4_div_1_Template, 4, 1, \"div\", 6);\n    i0.ɵɵtemplate(2, DoctorAvailabilityComponent_div_4_div_2_Template, 4, 2, \"div\", 6);\n    i0.ɵɵtemplate(3, DoctorAvailabilityComponent_div_4_div_3_Template, 4, 1, \"div\", 6);\n    i0.ɵɵtemplate(4, DoctorAvailabilityComponent_div_4_div_4_Template, 4, 1, \"div\", 6);\n    i0.ɵɵtemplate(5, DoctorAvailabilityComponent_div_4_div_5_Template, 5, 0, \"div\", 7);\n    i0.ɵɵtemplate(6, DoctorAvailabilityComponent_div_4_div_6_Template, 5, 0, \"div\", 8);\n    i0.ɵɵtemplate(7, DoctorAvailabilityComponent_div_4_div_7_Template, 5, 0, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availability.expectedResponseTime);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availability.chatStartTime && ctx_r1.availability.chatEndTime);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availability.customMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availability.status === \"OFFLINE\" && ctx_r1.availability.lastSeen);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isWithinChatHours());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availability.status === \"BUSY\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availability.status === \"DO_NOT_DISTURB\");\n  }\n}\nexport let DoctorAvailabilityComponent = /*#__PURE__*/(() => {\n  class DoctorAvailabilityComponent {\n    constructor() {\n      this.showDetails = true;\n      this.size = 'md';\n      this.availability = null;\n      this.loading = false;\n    }\n    ngOnInit() {\n      this.loadAvailability();\n      this.startPeriodicRefresh();\n    }\n    ngOnDestroy() {\n      if (this.refreshSubscription) {\n        this.refreshSubscription.unsubscribe();\n      }\n    }\n    loadAvailability() {\n      // Mock data for now - replace with actual service call\n      this.availability = {\n        status: 'ONLINE',\n        expectedResponseTime: 'Within 2 hours',\n        customMessage: 'Available for consultations',\n        chatStartTime: '09:00',\n        chatEndTime: '17:00'\n      };\n    }\n    startPeriodicRefresh() {\n      // Refresh availability every 5 minutes\n      this.refreshSubscription = interval(5 * 60 * 1000).subscribe(() => {\n        this.loadAvailability();\n      });\n    }\n    getStatusIcon() {\n      if (!this.availability) return 'fas fa-circle text-secondary';\n      switch (this.availability.status) {\n        case 'ONLINE':\n          return 'fas fa-circle text-success';\n        case 'BUSY':\n          return 'fas fa-circle text-warning';\n        case 'AWAY':\n          return 'fas fa-circle text-info';\n        case 'DO_NOT_DISTURB':\n          return 'fas fa-minus-circle text-danger';\n        case 'OFFLINE':\n        default:\n          return 'fas fa-circle text-secondary';\n      }\n    }\n    getStatusText() {\n      if (!this.availability) return 'Unknown';\n      switch (this.availability.status) {\n        case 'ONLINE':\n          return 'Online';\n        case 'BUSY':\n          return 'Busy';\n        case 'AWAY':\n          return 'Away';\n        case 'DO_NOT_DISTURB':\n          return 'Do Not Disturb';\n        case 'OFFLINE':\n        default:\n          return 'Offline';\n      }\n    }\n    getStatusClass() {\n      if (!this.availability) return 'status-offline';\n      switch (this.availability.status) {\n        case 'ONLINE':\n          return 'status-online';\n        case 'BUSY':\n          return 'status-busy';\n        case 'AWAY':\n          return 'status-away';\n        case 'DO_NOT_DISTURB':\n          return 'status-dnd';\n        case 'OFFLINE':\n        default:\n          return 'status-offline';\n      }\n    }\n    isAvailable() {\n      return this.availability?.status === 'ONLINE' || this.availability?.status === 'AWAY';\n    }\n    getLastSeenText() {\n      if (!this.availability?.lastSeen) return '';\n      const lastSeen = new Date(this.availability.lastSeen);\n      const now = new Date();\n      const diffMs = now.getTime() - lastSeen.getTime();\n      const diffMinutes = Math.floor(diffMs / (1000 * 60));\n      const diffHours = Math.floor(diffMinutes / 60);\n      const diffDays = Math.floor(diffHours / 24);\n      if (diffMinutes < 1) {\n        return 'Just now';\n      } else if (diffMinutes < 60) {\n        return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;\n      } else if (diffHours < 24) {\n        return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;\n      } else {\n        return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;\n      }\n    }\n    isWithinChatHours() {\n      if (!this.availability?.chatStartTime || !this.availability?.chatEndTime) {\n        return true; // Assume available if no hours set\n      }\n\n      const now = new Date();\n      const currentTime = now.toTimeString().slice(0, 5); // HH:MM format\n      return currentTime >= this.availability.chatStartTime && currentTime <= this.availability.chatEndTime;\n    }\n    static {\n      this.ɵfac = function DoctorAvailabilityComponent_Factory(t) {\n        return new (t || DoctorAvailabilityComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DoctorAvailabilityComponent,\n        selectors: [[\"app-doctor-availability\"]],\n        inputs: {\n          doctorId: \"doctorId\",\n          showDetails: \"showDetails\",\n          size: \"size\"\n        },\n        decls: 5,\n        vars: 8,\n        consts: [[1, \"doctor-availability\"], [1, \"status-indicator\"], [\"class\", \"status-text ms-2\", 4, \"ngIf\"], [\"class\", \"availability-details mt-2\", 4, \"ngIf\"], [1, \"status-text\", \"ms-2\"], [1, \"availability-details\", \"mt-2\"], [\"class\", \"detail-item\", 4, \"ngIf\"], [\"class\", \"availability-warning mt-2\", 4, \"ngIf\"], [\"class\", \"status-message mt-2\", 4, \"ngIf\"], [1, \"detail-item\"], [1, \"fas\", \"fa-clock\", \"text-muted\", \"me-2\"], [1, \"text-muted\"], [1, \"fas\", \"fa-calendar-clock\", \"text-muted\", \"me-2\"], [1, \"fas\", \"fa-comment\", \"text-muted\", \"me-2\"], [1, \"fas\", \"fa-eye\", \"text-muted\", \"me-2\"], [1, \"availability-warning\", \"mt-2\"], [1, \"alert\", \"alert-warning\", \"py-1\", \"px-2\", \"mb-0\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-1\"], [1, \"status-message\", \"mt-2\"], [1, \"alert\", \"alert-info\", \"py-1\", \"px-2\", \"mb-0\"], [1, \"fas\", \"fa-user-clock\", \"me-1\"], [1, \"alert\", \"alert-danger\", \"py-1\", \"px-2\", \"mb-0\"], [1, \"fas\", \"fa-ban\", \"me-1\"]],\n        template: function DoctorAvailabilityComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵelement(2, \"i\");\n            i0.ɵɵtemplate(3, DoctorAvailabilityComponent_span_3_Template, 2, 1, \"span\", 2);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(4, DoctorAvailabilityComponent_div_4_Template, 8, 7, \"div\", 3);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassMap(\"size-\" + ctx.size);\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassMap(ctx.getStatusClass());\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassMap(ctx.getStatusIcon());\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.size !== \"sm\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showDetails && ctx.availability && ctx.size !== \"sm\");\n          }\n        },\n        dependencies: [i1.NgIf],\n        styles: [\".doctor-availability[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]{display:flex;align-items:center}.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-online[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:#28a745}.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-busy[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:#ffc107}.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-away[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:#17a2b8}.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-dnd[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:#dc3545}.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-offline[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:#6c757d}.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:.25rem}.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:14px;font-size:.75rem}.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{font-size:.75rem;border-radius:4px}.doctor-availability.size-sm[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]{font-size:.875rem}.doctor-availability.size-sm[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem}.doctor-availability.size-lg[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]{font-size:1.125rem}.doctor-availability.size-lg[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.doctor-availability.size-lg[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{font-weight:500}.doctor-availability.size-lg[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]{margin-top:.75rem}.doctor-availability.size-lg[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]{margin-bottom:.5rem}.doctor-availability.size-lg[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:16px;font-size:.875rem}.doctor-availability.size-lg[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:.875rem}.status-online[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.7}to{opacity:1}}@media (max-width: 576px){.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start}.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-bottom:.25rem}}\"]\n      });\n    }\n  }\n  return DoctorAvailabilityComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}