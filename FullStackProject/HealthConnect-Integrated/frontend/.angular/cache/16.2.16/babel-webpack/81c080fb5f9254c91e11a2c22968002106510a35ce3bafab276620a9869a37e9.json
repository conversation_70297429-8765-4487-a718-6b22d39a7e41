{"ast": null, "code": "/**\n * Supported STOMP versions\n *\n * Part of `@stomp/stompjs`.\n */\nexport class Versions {\n  /**\n   * Takes an array of versions, typical elements '1.2', '1.1', or '1.0'\n   *\n   * You will be creating an instance of this class if you want to override\n   * supported versions to be declared during STOMP handshake.\n   */\n  constructor(versions) {\n    this.versions = versions;\n  }\n  /**\n   * Used as part of CONNECT STOMP Frame\n   */\n  supportedVersions() {\n    return this.versions.join(',');\n  }\n  /**\n   * Used while creating a WebSocket\n   */\n  protocolVersions() {\n    return this.versions.map(x => `v${x.replace('.', '')}.stomp`);\n  }\n}\n/**\n * Indicates protocol version 1.0\n */\nVersions.V1_0 = '1.0';\n/**\n * Indicates protocol version 1.1\n */\nVersions.V1_1 = '1.1';\n/**\n * Indicates protocol version 1.2\n */\nVersions.V1_2 = '1.2';\n/**\n * @internal\n */\nVersions.default = new Versions([Versions.V1_2, Versions.V1_1, Versions.V1_0]);\n//# sourceMappingURL=versions.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}