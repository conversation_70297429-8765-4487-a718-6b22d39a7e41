{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  Event = require('./event');\nfunction TransportMessageEvent(data) {\n  Event.call(this);\n  this.initEvent('message', false, false);\n  this.data = data;\n}\ninherits(TransportMessageEvent, Event);\nmodule.exports = TransportMessageEvent;", "map": {"version": 3, "names": ["inherits", "require", "Event", "TransportMessageEvent", "data", "call", "initEvent", "module", "exports"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/sockjs-client/lib/event/trans-message.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , Event = require('./event')\n  ;\n\nfunction TransportMessageEvent(data) {\n  Event.call(this);\n  this.initEvent('message', false, false);\n  this.data = data;\n}\n\ninherits(TransportMessageEvent, Event);\n\nmodule.exports = TransportMessageEvent;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,KAAK,GAAGD,OAAO,CAAC,SAAS,CAAC;AAG9B,SAASE,qBAAqBA,CAACC,IAAI,EAAE;EACnCF,KAAK,CAACG,IAAI,CAAC,IAAI,CAAC;EAChB,IAAI,CAACC,SAAS,CAAC,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC;EACvC,IAAI,CAACF,IAAI,GAAGA,IAAI;AAClB;AAEAJ,QAAQ,CAACG,qBAAqB,EAAED,KAAK,CAAC;AAEtCK,MAAM,CAACC,OAAO,GAAGL,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}