{"ast": null, "code": "'use strict';\n\nmodule.exports = {\n  isOpera: function () {\n    return global.navigator && /opera/i.test(global.navigator.userAgent);\n  },\n  isKonqueror: function () {\n    return global.navigator && /konqueror/i.test(global.navigator.userAgent);\n  }\n\n  // #187 wrap document.domain in try/catch because of WP8 from file:///\n  ,\n  hasDomain: function () {\n    // non-browser client always has a domain\n    if (!global.document) {\n      return true;\n    }\n    try {\n      return !!global.document.domain;\n    } catch (e) {\n      return false;\n    }\n  }\n};", "map": {"version": 3, "names": ["module", "exports", "isOpera", "global", "navigator", "test", "userAgent", "isKonqueror", "hasDomain", "document", "domain", "e"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/sockjs-client/lib/utils/browser.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = {\n  isOpera: function() {\n    return global.navigator &&\n      /opera/i.test(global.navigator.userAgent);\n  }\n\n, isKonqueror: function() {\n    return global.navigator &&\n      /konqueror/i.test(global.navigator.userAgent);\n  }\n\n  // #187 wrap document.domain in try/catch because of WP8 from file:///\n, hasDomain: function () {\n    // non-browser client always has a domain\n    if (!global.document) {\n      return true;\n    }\n\n    try {\n      return !!global.document.domain;\n    } catch (e) {\n      return false;\n    }\n  }\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG;EACfC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,OAAOC,MAAM,CAACC,SAAS,IACrB,QAAQ,CAACC,IAAI,CAACF,MAAM,CAACC,SAAS,CAACE,SAAS,CAAC;EAC7C,CAAC;EAEDC,WAAW,EAAE,SAAAA,CAAA,EAAW;IACtB,OAAOJ,MAAM,CAACC,SAAS,IACrB,YAAY,CAACC,IAAI,CAACF,MAAM,CAACC,SAAS,CAACE,SAAS,CAAC;EACjD;;EAEA;EAAA;EACAE,SAAS,EAAE,SAAAA,CAAA,EAAY;IACrB;IACA,IAAI,CAACL,MAAM,CAACM,QAAQ,EAAE;MACpB,OAAO,IAAI;IACb;IAEA,IAAI;MACF,OAAO,CAAC,CAACN,MAAM,CAACM,QAAQ,CAACC,MAAM;IACjC,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV,OAAO,KAAK;IACd;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}