{"ast": null, "code": "'use strict';\n\nmodule.exports = {\n  isOpera: function () {\n    return global.navigator && /opera/i.test(global.navigator.userAgent);\n  },\n  isKonqueror: function () {\n    return global.navigator && /konqueror/i.test(global.navigator.userAgent);\n  }\n\n  // #187 wrap document.domain in try/catch because of WP8 from file:///\n  ,\n  hasDomain: function () {\n    // non-browser client always has a domain\n    if (!global.document) {\n      return true;\n    }\n    try {\n      return !!global.document.domain;\n    } catch (e) {\n      return false;\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}