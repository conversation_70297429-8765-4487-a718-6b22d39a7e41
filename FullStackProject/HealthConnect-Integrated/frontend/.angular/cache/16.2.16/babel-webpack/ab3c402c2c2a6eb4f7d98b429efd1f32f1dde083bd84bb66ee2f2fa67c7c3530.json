{"ast": null, "code": "import _asyncToGenerator from \"/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class NotificationService {\n  constructor() {\n    this.notifications$ = new BehaviorSubject([]);\n    this.unreadCount$ = new BehaviorSubject(0);\n    this.loadNotifications();\n  }\n  getNotifications() {\n    return this.notifications$.asObservable();\n  }\n  getUnreadCount() {\n    return this.unreadCount$.asObservable();\n  }\n  addNotification(notification) {\n    const newNotification = {\n      ...notification,\n      id: this.generateId(),\n      timestamp: new Date(),\n      read: false\n    };\n    const currentNotifications = this.notifications$.value;\n    const updatedNotifications = [newNotification, ...currentNotifications];\n    this.notifications$.next(updatedNotifications);\n    this.updateUnreadCount();\n    this.saveNotifications(updatedNotifications);\n    // Show browser notification if permission granted\n    this.showBrowserNotification(newNotification);\n  }\n  markAsRead(notificationId) {\n    const notifications = this.notifications$.value.map(notification => notification.id === notificationId ? {\n      ...notification,\n      read: true\n    } : notification);\n    this.notifications$.next(notifications);\n    this.updateUnreadCount();\n    this.saveNotifications(notifications);\n  }\n  markAllAsRead() {\n    const notifications = this.notifications$.value.map(notification => ({\n      ...notification,\n      read: true\n    }));\n    this.notifications$.next(notifications);\n    this.updateUnreadCount();\n    this.saveNotifications(notifications);\n  }\n  removeNotification(notificationId) {\n    const notifications = this.notifications$.value.filter(notification => notification.id !== notificationId);\n    this.notifications$.next(notifications);\n    this.updateUnreadCount();\n    this.saveNotifications(notifications);\n  }\n  clearAll() {\n    this.notifications$.next([]);\n    this.unreadCount$.next(0);\n    this.saveNotifications([]);\n  }\n  // Specific notification types\n  addMessageNotification(fromUser, message, chatId) {\n    this.addNotification({\n      type: 'message',\n      title: `New message from ${fromUser.fullName}`,\n      message: message.length > 100 ? message.substring(0, 100) + '...' : message,\n      priority: 'medium',\n      fromUser: {\n        id: fromUser.id,\n        name: fromUser.fullName,\n        avatar: fromUser.avatar\n      },\n      actionUrl: `/chat?chatId=${chatId}`,\n      actionText: 'Reply'\n    });\n  }\n  addAppointmentNotification(type, appointment) {\n    let title = '';\n    let message = '';\n    let priority = 'medium';\n    switch (type) {\n      case 'booked':\n        title = 'Appointment Booked';\n        message = `Your appointment with ${appointment.doctor.fullName} is scheduled for ${appointment.date}`;\n        break;\n      case 'reminder':\n        title = 'Appointment Reminder';\n        message = `Your appointment with ${appointment.doctor.fullName} is in 1 hour`;\n        priority = 'high';\n        break;\n      case 'cancelled':\n        title = 'Appointment Cancelled';\n        message = `Your appointment with ${appointment.doctor.fullName} has been cancelled`;\n        priority = 'high';\n        break;\n    }\n    this.addNotification({\n      type: 'appointment',\n      title,\n      message,\n      priority,\n      actionUrl: `/appointments/${appointment.id}`,\n      actionText: 'View Details'\n    });\n  }\n  addUrgentNotification(title, message, actionUrl) {\n    this.addNotification({\n      type: 'urgent',\n      title,\n      message,\n      priority: 'urgent',\n      actionUrl,\n      actionText: actionUrl ? 'View' : undefined\n    });\n  }\n  loadNotifications() {\n    const stored = localStorage.getItem('healthconnect_notifications');\n    if (stored) {\n      try {\n        const notifications = JSON.parse(stored).map(n => ({\n          ...n,\n          timestamp: new Date(n.timestamp)\n        }));\n        this.notifications$.next(notifications);\n        this.updateUnreadCount();\n      } catch (error) {\n        console.error('Error loading notifications:', error);\n      }\n    }\n  }\n  saveNotifications(notifications) {\n    try {\n      localStorage.setItem('healthconnect_notifications', JSON.stringify(notifications));\n    } catch (error) {\n      console.error('Error saving notifications:', error);\n    }\n  }\n  updateUnreadCount() {\n    const unreadCount = this.notifications$.value.filter(n => !n.read).length;\n    this.unreadCount$.next(unreadCount);\n  }\n  generateId() {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n  }\n  showBrowserNotification(notification) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!('Notification' in window)) {\n        return;\n      }\n      if (Notification.permission === 'granted') {\n        new Notification(notification.title, {\n          body: notification.message,\n          icon: '/assets/icons/icon-192x192.png',\n          badge: '/assets/icons/icon-72x72.png',\n          tag: notification.id\n        });\n      } else if (Notification.permission !== 'denied') {\n        const permission = yield Notification.requestPermission();\n        if (permission === 'granted') {\n          _this.showBrowserNotification(notification);\n        }\n      }\n    })();\n  }\n  // Request notification permission\n  requestNotificationPermission() {\n    return _asyncToGenerator(function* () {\n      if (!('Notification' in window)) {\n        return false;\n      }\n      if (Notification.permission === 'granted') {\n        return true;\n      }\n      if (Notification.permission !== 'denied') {\n        const permission = yield Notification.requestPermission();\n        return permission === 'granted';\n      }\n      return false;\n    })();\n  }\n  static {\n    this.ɵfac = function NotificationService_Factory(t) {\n      return new (t || NotificationService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: NotificationService,\n      factory: NotificationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "NotificationService", "constructor", "notifications$", "unreadCount$", "loadNotifications", "getNotifications", "asObservable", "getUnreadCount", "addNotification", "notification", "newNotification", "id", "generateId", "timestamp", "Date", "read", "currentNotifications", "value", "updatedNotifications", "next", "updateUnreadCount", "saveNotifications", "showBrowserNotification", "mark<PERSON><PERSON><PERSON>", "notificationId", "notifications", "map", "markAllAsRead", "removeNotification", "filter", "clearAll", "addMessageNotification", "fromUser", "message", "chatId", "type", "title", "fullName", "length", "substring", "priority", "name", "avatar", "actionUrl", "actionText", "addAppointmentNotification", "appointment", "doctor", "date", "addUrgentNotification", "undefined", "stored", "localStorage", "getItem", "JSON", "parse", "n", "error", "console", "setItem", "stringify", "unreadCount", "now", "toString", "Math", "random", "substr", "_this", "_asyncToGenerator", "window", "Notification", "permission", "body", "icon", "badge", "tag", "requestPermission", "requestNotificationPermission", "factory", "ɵfac", "providedIn"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/core/services/notification.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable } from 'rxjs';\n\nexport interface Notification {\n  id: string;\n  type: 'message' | 'appointment' | 'urgent' | 'system';\n  title: string;\n  message: string;\n  timestamp: Date;\n  read: boolean;\n  actionUrl?: string;\n  actionText?: string;\n  priority: 'low' | 'medium' | 'high' | 'urgent';\n  fromUser?: {\n    id: number;\n    name: string;\n    avatar?: string;\n  };\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class NotificationService {\n  private notifications$ = new BehaviorSubject<Notification[]>([]);\n  private unreadCount$ = new BehaviorSubject<number>(0);\n\n  constructor() {\n    this.loadNotifications();\n  }\n\n  getNotifications(): Observable<Notification[]> {\n    return this.notifications$.asObservable();\n  }\n\n  getUnreadCount(): Observable<number> {\n    return this.unreadCount$.asObservable();\n  }\n\n  addNotification(notification: Omit<Notification, 'id' | 'timestamp' | 'read'>): void {\n    const newNotification: Notification = {\n      ...notification,\n      id: this.generateId(),\n      timestamp: new Date(),\n      read: false\n    };\n\n    const currentNotifications = this.notifications$.value;\n    const updatedNotifications = [newNotification, ...currentNotifications];\n    \n    this.notifications$.next(updatedNotifications);\n    this.updateUnreadCount();\n    this.saveNotifications(updatedNotifications);\n\n    // Show browser notification if permission granted\n    this.showBrowserNotification(newNotification);\n  }\n\n  markAsRead(notificationId: string): void {\n    const notifications = this.notifications$.value.map(notification =>\n      notification.id === notificationId \n        ? { ...notification, read: true }\n        : notification\n    );\n    \n    this.notifications$.next(notifications);\n    this.updateUnreadCount();\n    this.saveNotifications(notifications);\n  }\n\n  markAllAsRead(): void {\n    const notifications = this.notifications$.value.map(notification => ({\n      ...notification,\n      read: true\n    }));\n    \n    this.notifications$.next(notifications);\n    this.updateUnreadCount();\n    this.saveNotifications(notifications);\n  }\n\n  removeNotification(notificationId: string): void {\n    const notifications = this.notifications$.value.filter(\n      notification => notification.id !== notificationId\n    );\n    \n    this.notifications$.next(notifications);\n    this.updateUnreadCount();\n    this.saveNotifications(notifications);\n  }\n\n  clearAll(): void {\n    this.notifications$.next([]);\n    this.unreadCount$.next(0);\n    this.saveNotifications([]);\n  }\n\n  // Specific notification types\n  addMessageNotification(fromUser: any, message: string, chatId: number): void {\n    this.addNotification({\n      type: 'message',\n      title: `New message from ${fromUser.fullName}`,\n      message: message.length > 100 ? message.substring(0, 100) + '...' : message,\n      priority: 'medium',\n      fromUser: {\n        id: fromUser.id,\n        name: fromUser.fullName,\n        avatar: fromUser.avatar\n      },\n      actionUrl: `/chat?chatId=${chatId}`,\n      actionText: 'Reply'\n    });\n  }\n\n  addAppointmentNotification(type: 'booked' | 'reminder' | 'cancelled', appointment: any): void {\n    let title = '';\n    let message = '';\n    let priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium';\n\n    switch (type) {\n      case 'booked':\n        title = 'Appointment Booked';\n        message = `Your appointment with ${appointment.doctor.fullName} is scheduled for ${appointment.date}`;\n        break;\n      case 'reminder':\n        title = 'Appointment Reminder';\n        message = `Your appointment with ${appointment.doctor.fullName} is in 1 hour`;\n        priority = 'high';\n        break;\n      case 'cancelled':\n        title = 'Appointment Cancelled';\n        message = `Your appointment with ${appointment.doctor.fullName} has been cancelled`;\n        priority = 'high';\n        break;\n    }\n\n    this.addNotification({\n      type: 'appointment',\n      title,\n      message,\n      priority,\n      actionUrl: `/appointments/${appointment.id}`,\n      actionText: 'View Details'\n    });\n  }\n\n  addUrgentNotification(title: string, message: string, actionUrl?: string): void {\n    this.addNotification({\n      type: 'urgent',\n      title,\n      message,\n      priority: 'urgent',\n      actionUrl,\n      actionText: actionUrl ? 'View' : undefined\n    });\n  }\n\n  private loadNotifications(): void {\n    const stored = localStorage.getItem('healthconnect_notifications');\n    if (stored) {\n      try {\n        const notifications = JSON.parse(stored).map((n: any) => ({\n          ...n,\n          timestamp: new Date(n.timestamp)\n        }));\n        this.notifications$.next(notifications);\n        this.updateUnreadCount();\n      } catch (error) {\n        console.error('Error loading notifications:', error);\n      }\n    }\n  }\n\n  private saveNotifications(notifications: Notification[]): void {\n    try {\n      localStorage.setItem('healthconnect_notifications', JSON.stringify(notifications));\n    } catch (error) {\n      console.error('Error saving notifications:', error);\n    }\n  }\n\n  private updateUnreadCount(): void {\n    const unreadCount = this.notifications$.value.filter(n => !n.read).length;\n    this.unreadCount$.next(unreadCount);\n  }\n\n  private generateId(): string {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n  }\n\n  private async showBrowserNotification(notification: Notification): Promise<void> {\n    if (!('Notification' in window)) {\n      return;\n    }\n\n    if (Notification.permission === 'granted') {\n      new Notification(notification.title, {\n        body: notification.message,\n        icon: '/assets/icons/icon-192x192.png',\n        badge: '/assets/icons/icon-72x72.png',\n        tag: notification.id\n      });\n    } else if (Notification.permission !== 'denied') {\n      const permission = await Notification.requestPermission();\n      if (permission === 'granted') {\n        this.showBrowserNotification(notification);\n      }\n    }\n  }\n\n  // Request notification permission\n  async requestNotificationPermission(): Promise<boolean> {\n    if (!('Notification' in window)) {\n      return false;\n    }\n\n    if (Notification.permission === 'granted') {\n      return true;\n    }\n\n    if (Notification.permission !== 'denied') {\n      const permission = await Notification.requestPermission();\n      return permission === 'granted';\n    }\n\n    return false;\n  }\n}\n"], "mappings": ";AACA,SAASA,eAAe,QAAoB,MAAM;;AAsBlD,OAAM,MAAOC,mBAAmB;EAI9BC,YAAA;IAHQ,KAAAC,cAAc,GAAG,IAAIH,eAAe,CAAiB,EAAE,CAAC;IACxD,KAAAI,YAAY,GAAG,IAAIJ,eAAe,CAAS,CAAC,CAAC;IAGnD,IAAI,CAACK,iBAAiB,EAAE;EAC1B;EAEAC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACH,cAAc,CAACI,YAAY,EAAE;EAC3C;EAEAC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACJ,YAAY,CAACG,YAAY,EAAE;EACzC;EAEAE,eAAeA,CAACC,YAA6D;IAC3E,MAAMC,eAAe,GAAiB;MACpC,GAAGD,YAAY;MACfE,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBC,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBC,IAAI,EAAE;KACP;IAED,MAAMC,oBAAoB,GAAG,IAAI,CAACd,cAAc,CAACe,KAAK;IACtD,MAAMC,oBAAoB,GAAG,CAACR,eAAe,EAAE,GAAGM,oBAAoB,CAAC;IAEvE,IAAI,CAACd,cAAc,CAACiB,IAAI,CAACD,oBAAoB,CAAC;IAC9C,IAAI,CAACE,iBAAiB,EAAE;IACxB,IAAI,CAACC,iBAAiB,CAACH,oBAAoB,CAAC;IAE5C;IACA,IAAI,CAACI,uBAAuB,CAACZ,eAAe,CAAC;EAC/C;EAEAa,UAAUA,CAACC,cAAsB;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACvB,cAAc,CAACe,KAAK,CAACS,GAAG,CAACjB,YAAY,IAC9DA,YAAY,CAACE,EAAE,KAAKa,cAAc,GAC9B;MAAE,GAAGf,YAAY;MAAEM,IAAI,EAAE;IAAI,CAAE,GAC/BN,YAAY,CACjB;IAED,IAAI,CAACP,cAAc,CAACiB,IAAI,CAACM,aAAa,CAAC;IACvC,IAAI,CAACL,iBAAiB,EAAE;IACxB,IAAI,CAACC,iBAAiB,CAACI,aAAa,CAAC;EACvC;EAEAE,aAAaA,CAAA;IACX,MAAMF,aAAa,GAAG,IAAI,CAACvB,cAAc,CAACe,KAAK,CAACS,GAAG,CAACjB,YAAY,KAAK;MACnE,GAAGA,YAAY;MACfM,IAAI,EAAE;KACP,CAAC,CAAC;IAEH,IAAI,CAACb,cAAc,CAACiB,IAAI,CAACM,aAAa,CAAC;IACvC,IAAI,CAACL,iBAAiB,EAAE;IACxB,IAAI,CAACC,iBAAiB,CAACI,aAAa,CAAC;EACvC;EAEAG,kBAAkBA,CAACJ,cAAsB;IACvC,MAAMC,aAAa,GAAG,IAAI,CAACvB,cAAc,CAACe,KAAK,CAACY,MAAM,CACpDpB,YAAY,IAAIA,YAAY,CAACE,EAAE,KAAKa,cAAc,CACnD;IAED,IAAI,CAACtB,cAAc,CAACiB,IAAI,CAACM,aAAa,CAAC;IACvC,IAAI,CAACL,iBAAiB,EAAE;IACxB,IAAI,CAACC,iBAAiB,CAACI,aAAa,CAAC;EACvC;EAEAK,QAAQA,CAAA;IACN,IAAI,CAAC5B,cAAc,CAACiB,IAAI,CAAC,EAAE,CAAC;IAC5B,IAAI,CAAChB,YAAY,CAACgB,IAAI,CAAC,CAAC,CAAC;IACzB,IAAI,CAACE,iBAAiB,CAAC,EAAE,CAAC;EAC5B;EAEA;EACAU,sBAAsBA,CAACC,QAAa,EAAEC,OAAe,EAAEC,MAAc;IACnE,IAAI,CAAC1B,eAAe,CAAC;MACnB2B,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,oBAAoBJ,QAAQ,CAACK,QAAQ,EAAE;MAC9CJ,OAAO,EAAEA,OAAO,CAACK,MAAM,GAAG,GAAG,GAAGL,OAAO,CAACM,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,GAAGN,OAAO;MAC3EO,QAAQ,EAAE,QAAQ;MAClBR,QAAQ,EAAE;QACRrB,EAAE,EAAEqB,QAAQ,CAACrB,EAAE;QACf8B,IAAI,EAAET,QAAQ,CAACK,QAAQ;QACvBK,MAAM,EAAEV,QAAQ,CAACU;OAClB;MACDC,SAAS,EAAE,gBAAgBT,MAAM,EAAE;MACnCU,UAAU,EAAE;KACb,CAAC;EACJ;EAEAC,0BAA0BA,CAACV,IAAyC,EAAEW,WAAgB;IACpF,IAAIV,KAAK,GAAG,EAAE;IACd,IAAIH,OAAO,GAAG,EAAE;IAChB,IAAIO,QAAQ,GAAyC,QAAQ;IAE7D,QAAQL,IAAI;MACV,KAAK,QAAQ;QACXC,KAAK,GAAG,oBAAoB;QAC5BH,OAAO,GAAG,yBAAyBa,WAAW,CAACC,MAAM,CAACV,QAAQ,qBAAqBS,WAAW,CAACE,IAAI,EAAE;QACrG;MACF,KAAK,UAAU;QACbZ,KAAK,GAAG,sBAAsB;QAC9BH,OAAO,GAAG,yBAAyBa,WAAW,CAACC,MAAM,CAACV,QAAQ,eAAe;QAC7EG,QAAQ,GAAG,MAAM;QACjB;MACF,KAAK,WAAW;QACdJ,KAAK,GAAG,uBAAuB;QAC/BH,OAAO,GAAG,yBAAyBa,WAAW,CAACC,MAAM,CAACV,QAAQ,qBAAqB;QACnFG,QAAQ,GAAG,MAAM;QACjB;;IAGJ,IAAI,CAAChC,eAAe,CAAC;MACnB2B,IAAI,EAAE,aAAa;MACnBC,KAAK;MACLH,OAAO;MACPO,QAAQ;MACRG,SAAS,EAAE,iBAAiBG,WAAW,CAACnC,EAAE,EAAE;MAC5CiC,UAAU,EAAE;KACb,CAAC;EACJ;EAEAK,qBAAqBA,CAACb,KAAa,EAAEH,OAAe,EAAEU,SAAkB;IACtE,IAAI,CAACnC,eAAe,CAAC;MACnB2B,IAAI,EAAE,QAAQ;MACdC,KAAK;MACLH,OAAO;MACPO,QAAQ,EAAE,QAAQ;MAClBG,SAAS;MACTC,UAAU,EAAED,SAAS,GAAG,MAAM,GAAGO;KAClC,CAAC;EACJ;EAEQ9C,iBAAiBA,CAAA;IACvB,MAAM+C,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,6BAA6B,CAAC;IAClE,IAAIF,MAAM,EAAE;MACV,IAAI;QACF,MAAM1B,aAAa,GAAG6B,IAAI,CAACC,KAAK,CAACJ,MAAM,CAAC,CAACzB,GAAG,CAAE8B,CAAM,KAAM;UACxD,GAAGA,CAAC;UACJ3C,SAAS,EAAE,IAAIC,IAAI,CAAC0C,CAAC,CAAC3C,SAAS;SAChC,CAAC,CAAC;QACH,IAAI,CAACX,cAAc,CAACiB,IAAI,CAACM,aAAa,CAAC;QACvC,IAAI,CAACL,iBAAiB,EAAE;OACzB,CAAC,OAAOqC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;;;EAG1D;EAEQpC,iBAAiBA,CAACI,aAA6B;IACrD,IAAI;MACF2B,YAAY,CAACO,OAAO,CAAC,6BAA6B,EAAEL,IAAI,CAACM,SAAS,CAACnC,aAAa,CAAC,CAAC;KACnF,CAAC,OAAOgC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;;EAEvD;EAEQrC,iBAAiBA,CAAA;IACvB,MAAMyC,WAAW,GAAG,IAAI,CAAC3D,cAAc,CAACe,KAAK,CAACY,MAAM,CAAC2B,CAAC,IAAI,CAACA,CAAC,CAACzC,IAAI,CAAC,CAACuB,MAAM;IACzE,IAAI,CAACnC,YAAY,CAACgB,IAAI,CAAC0C,WAAW,CAAC;EACrC;EAEQjD,UAAUA,CAAA;IAChB,OAAOE,IAAI,CAACgD,GAAG,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,CAACF,QAAQ,CAAC,EAAE,CAAC,CAACG,MAAM,CAAC,CAAC,CAAC;EACvE;EAEc5C,uBAAuBA,CAACb,YAA0B;IAAA,IAAA0D,KAAA;IAAA,OAAAC,iBAAA;MAC9D,IAAI,EAAE,cAAc,IAAIC,MAAM,CAAC,EAAE;QAC/B;;MAGF,IAAIC,YAAY,CAACC,UAAU,KAAK,SAAS,EAAE;QACzC,IAAID,YAAY,CAAC7D,YAAY,CAAC2B,KAAK,EAAE;UACnCoC,IAAI,EAAE/D,YAAY,CAACwB,OAAO;UAC1BwC,IAAI,EAAE,gCAAgC;UACtCC,KAAK,EAAE,8BAA8B;UACrCC,GAAG,EAAElE,YAAY,CAACE;SACnB,CAAC;OACH,MAAM,IAAI2D,YAAY,CAACC,UAAU,KAAK,QAAQ,EAAE;QAC/C,MAAMA,UAAU,SAASD,YAAY,CAACM,iBAAiB,EAAE;QACzD,IAAIL,UAAU,KAAK,SAAS,EAAE;UAC5BJ,KAAI,CAAC7C,uBAAuB,CAACb,YAAY,CAAC;;;IAE7C;EACH;EAEA;EACMoE,6BAA6BA,CAAA;IAAA,OAAAT,iBAAA;MACjC,IAAI,EAAE,cAAc,IAAIC,MAAM,CAAC,EAAE;QAC/B,OAAO,KAAK;;MAGd,IAAIC,YAAY,CAACC,UAAU,KAAK,SAAS,EAAE;QACzC,OAAO,IAAI;;MAGb,IAAID,YAAY,CAACC,UAAU,KAAK,QAAQ,EAAE;QACxC,MAAMA,UAAU,SAASD,YAAY,CAACM,iBAAiB,EAAE;QACzD,OAAOL,UAAU,KAAK,SAAS;;MAGjC,OAAO,KAAK;IAAC;EACf;;;uBA3MWvE,mBAAmB;IAAA;EAAA;;;aAAnBA,mBAAmB;MAAA8E,OAAA,EAAnB9E,mBAAmB,CAAA+E,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}