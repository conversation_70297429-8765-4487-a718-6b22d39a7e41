{"ast": null, "code": "'use strict';\n\nvar EventEmitter = require('events').EventEmitter,\n  inherits = require('inherits'),\n  urlUtils = require('./utils/url'),\n  XDR = require('./transport/sender/xdr'),\n  XHRCors = require('./transport/sender/xhr-cors'),\n  XHRLocal = require('./transport/sender/xhr-local'),\n  XHRFake = require('./transport/sender/xhr-fake'),\n  InfoIframe = require('./info-iframe'),\n  InfoAjax = require('./info-ajax');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:info-receiver');\n}\nfunction InfoReceiver(baseUrl, urlInfo) {\n  debug(baseUrl);\n  var self = this;\n  EventEmitter.call(this);\n  setTimeout(function () {\n    self.doXhr(baseUrl, urlInfo);\n  }, 0);\n}\ninherits(InfoReceiver, EventEmitter);\n\n// TODO this is currently ignoring the list of available transports and the whitelist\n\nInfoReceiver._getReceiver = function (baseUrl, url, urlInfo) {\n  // determine method of CORS support (if needed)\n  if (urlInfo.sameOrigin) {\n    return new InfoAjax(url, XHRLocal);\n  }\n  if (XHRCors.enabled) {\n    return new InfoAjax(url, XHRCors);\n  }\n  if (XDR.enabled && urlInfo.sameScheme) {\n    return new InfoAjax(url, XDR);\n  }\n  if (InfoIframe.enabled()) {\n    return new InfoIframe(baseUrl, url);\n  }\n  return new InfoAjax(url, XHRFake);\n};\nInfoReceiver.prototype.doXhr = function (baseUrl, urlInfo) {\n  var self = this,\n    url = urlUtils.addPath(baseUrl, '/info');\n  debug('doXhr', url);\n  this.xo = InfoReceiver._getReceiver(baseUrl, url, urlInfo);\n  this.timeoutRef = setTimeout(function () {\n    debug('timeout');\n    self._cleanup(false);\n    self.emit('finish');\n  }, InfoReceiver.timeout);\n  this.xo.once('finish', function (info, rtt) {\n    debug('finish', info, rtt);\n    self._cleanup(true);\n    self.emit('finish', info, rtt);\n  });\n};\nInfoReceiver.prototype._cleanup = function (wasClean) {\n  debug('_cleanup');\n  clearTimeout(this.timeoutRef);\n  this.timeoutRef = null;\n  if (!wasClean && this.xo) {\n    this.xo.close();\n  }\n  this.xo = null;\n};\nInfoReceiver.prototype.close = function () {\n  debug('close');\n  this.removeAllListeners();\n  this._cleanup(false);\n};\nInfoReceiver.timeout = 8000;\nmodule.exports = InfoReceiver;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}