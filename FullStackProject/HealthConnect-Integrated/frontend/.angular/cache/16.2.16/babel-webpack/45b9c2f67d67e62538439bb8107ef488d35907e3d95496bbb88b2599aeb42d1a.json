{"ast": null, "code": "if (typeof Object.create === 'function') {\n  // implementation from standard node.js 'util' module\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor;\n      ctor.prototype = Object.create(superCtor.prototype, {\n        constructor: {\n          value: ctor,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n    }\n  };\n} else {\n  // old school shim for old browsers\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor;\n      var TempCtor = function () {};\n      TempCtor.prototype = superCtor.prototype;\n      ctor.prototype = new TempCtor();\n      ctor.prototype.constructor = ctor;\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}