{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nexport let AuthInterceptor = /*#__PURE__*/(() => {\n  class AuthInterceptor {\n    constructor(authService) {\n      this.authService = authService;\n    }\n    intercept(req, next) {\n      // Get the auth token from the service\n      const authToken = this.authService.getToken();\n      // Clone the request and add the authorization header if token exists\n      if (authToken) {\n        const authReq = req.clone({\n          headers: req.headers.set('Authorization', `Bearer ${authToken}`)\n        });\n        return next.handle(authReq);\n      }\n      // If no token, proceed with the original request\n      return next.handle(req);\n    }\n    static {\n      this.ɵfac = function AuthInterceptor_Factory(t) {\n        return new (t || AuthInterceptor)(i0.ɵɵinject(i1.AuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthInterceptor,\n        factory: AuthInterceptor.ɵfac\n      });\n    }\n  }\n  return AuthInterceptor;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}