{"ast": null, "code": "/**\n * Supported STOMP versions\n *\n * Part of `@stomp/stompjs`.\n */\nexport class Versions {\n  /**\n   * Takes an array of versions, typical elements '1.2', '1.1', or '1.0'\n   *\n   * You will be creating an instance of this class if you want to override\n   * supported versions to be declared during STOMP handshake.\n   */\n  constructor(versions) {\n    this.versions = versions;\n  }\n  /**\n   * Used as part of CONNECT STOMP Frame\n   */\n  supportedVersions() {\n    return this.versions.join(',');\n  }\n  /**\n   * Used while creating a WebSocket\n   */\n  protocolVersions() {\n    return this.versions.map(x => `v${x.replace('.', '')}.stomp`);\n  }\n}\n/**\n * Indicates protocol version 1.0\n */\nVersions.V1_0 = '1.0';\n/**\n * Indicates protocol version 1.1\n */\nVersions.V1_1 = '1.1';\n/**\n * Indicates protocol version 1.2\n */\nVersions.V1_2 = '1.2';\n/**\n * @internal\n */\nVersions.default = new Versions([Versions.V1_2, Versions.V1_1, Versions.V1_0]);", "map": {"version": 3, "names": ["Versions", "constructor", "versions", "supportedVersions", "join", "protocolVersions", "map", "x", "replace", "V1_0", "V1_1", "V1_2", "default"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/@stomp/stompjs/esm6/versions.js"], "sourcesContent": ["/**\n * Supported STOMP versions\n *\n * Part of `@stomp/stompjs`.\n */\nexport class Versions {\n    /**\n     * Takes an array of versions, typical elements '1.2', '1.1', or '1.0'\n     *\n     * You will be creating an instance of this class if you want to override\n     * supported versions to be declared during STOMP handshake.\n     */\n    constructor(versions) {\n        this.versions = versions;\n    }\n    /**\n     * Used as part of CONNECT STOMP Frame\n     */\n    supportedVersions() {\n        return this.versions.join(',');\n    }\n    /**\n     * Used while creating a WebSocket\n     */\n    protocolVersions() {\n        return this.versions.map(x => `v${x.replace('.', '')}.stomp`);\n    }\n}\n/**\n * Indicates protocol version 1.0\n */\nVersions.V1_0 = '1.0';\n/**\n * Indicates protocol version 1.1\n */\nVersions.V1_1 = '1.1';\n/**\n * Indicates protocol version 1.2\n */\nVersions.V1_2 = '1.2';\n/**\n * @internal\n */\nVersions.default = new Versions([\n    Versions.V1_2,\n    Versions.V1_1,\n    Versions.V1_0,\n]);\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,QAAQ,CAAC;EAClB;AACJ;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;AACJ;AACA;EACIC,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACD,QAAQ,CAACE,IAAI,CAAC,GAAG,CAAC;EAClC;EACA;AACJ;AACA;EACIC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACH,QAAQ,CAACI,GAAG,CAACC,CAAC,IAAK,IAAGA,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAE,QAAO,CAAC;EACjE;AACJ;AACA;AACA;AACA;AACAR,QAAQ,CAACS,IAAI,GAAG,KAAK;AACrB;AACA;AACA;AACAT,QAAQ,CAACU,IAAI,GAAG,KAAK;AACrB;AACA;AACA;AACAV,QAAQ,CAACW,IAAI,GAAG,KAAK;AACrB;AACA;AACA;AACAX,QAAQ,CAACY,OAAO,GAAG,IAAIZ,QAAQ,CAAC,CAC5BA,QAAQ,CAACW,IAAI,EACbX,QAAQ,CAACU,IAAI,EACbV,QAAQ,CAACS,IAAI,CAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}