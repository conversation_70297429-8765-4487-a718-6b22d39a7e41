{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { AppointmentStatus, AppointmentType } from '../models/appointment.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let AppointmentService = /*#__PURE__*/(() => {\n  class AppointmentService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = 'http://localhost:8080/api';\n    }\n    // Doctor discovery\n    getDoctors(specialization) {\n      let params = new HttpParams();\n      if (specialization) {\n        params = params.set('specialization', specialization);\n      }\n      return this.http.get(`${this.apiUrl}/doctors`, {\n        params\n      });\n    }\n    getDoctor(id) {\n      return this.http.get(`${this.apiUrl}/doctors/${id}`);\n    }\n    getSpecializations() {\n      return this.http.get(`${this.apiUrl}/doctors/specializations`);\n    }\n    // Time slots\n    getAvailableTimeSlots(doctorId, date) {\n      const params = new HttpParams().set('date', date);\n      return this.http.get(`${this.apiUrl}/doctors/${doctorId}/time-slots`, {\n        params\n      });\n    }\n    // Appointments CRUD\n    getAppointments(status, type, startDate, endDate) {\n      let params = new HttpParams();\n      if (status) params = params.set('status', status);\n      if (type) params = params.set('type', type);\n      if (startDate) params = params.set('startDate', startDate);\n      if (endDate) params = params.set('endDate', endDate);\n      return this.http.get(`${this.apiUrl}/appointments`, {\n        params\n      });\n    }\n    getAppointment(id) {\n      return this.http.get(`${this.apiUrl}/appointments/${id}`);\n    }\n    createAppointment(request) {\n      return this.http.post(`${this.apiUrl}/appointments`, request);\n    }\n    updateAppointment(id, request) {\n      return this.http.put(`${this.apiUrl}/appointments/${id}`, request);\n    }\n    cancelAppointment(id) {\n      return this.http.delete(`${this.apiUrl}/appointments/${id}`);\n    }\n    getTodayAppointments() {\n      return this.http.get(`${this.apiUrl}/appointments/today`);\n    }\n    getPatientAppointments() {\n      return this.http.get(`${this.apiUrl}/appointments`);\n    }\n    // Chat-related methods\n    createAppointmentChat(appointmentId, participantId, chatType = 'PRE_APPOINTMENT', subject) {\n      const request = {\n        participantId,\n        chatType,\n        subject\n      };\n      return this.http.post(`${this.apiUrl}/chats/appointment/${appointmentId}`, request);\n    }\n    // Helper methods\n    getStatusDisplayName(status) {\n      switch (status) {\n        case AppointmentStatus.PENDING:\n          return 'Pending';\n        case AppointmentStatus.SCHEDULED:\n          return 'Scheduled';\n        case AppointmentStatus.CONFIRMED:\n          return 'Confirmed';\n        case AppointmentStatus.COMPLETED:\n          return 'Completed';\n        case AppointmentStatus.CANCELLED:\n          return 'Cancelled';\n        case AppointmentStatus.NO_SHOW:\n          return 'No Show';\n        default:\n          return status;\n      }\n    }\n    getTypeDisplayName(type) {\n      switch (type) {\n        case AppointmentType.IN_PERSON:\n          return 'In Person';\n        case AppointmentType.VIDEO_CALL:\n          return 'Video Call';\n        default:\n          return type;\n      }\n    }\n    getStatusBadgeClass(status) {\n      switch (status) {\n        case AppointmentStatus.PENDING:\n          return 'badge-warning';\n        case AppointmentStatus.SCHEDULED:\n          return 'badge-info';\n        case AppointmentStatus.CONFIRMED:\n          return 'badge-primary';\n        case AppointmentStatus.COMPLETED:\n          return 'badge-success';\n        case AppointmentStatus.CANCELLED:\n          return 'badge-danger';\n        case AppointmentStatus.NO_SHOW:\n          return 'badge-secondary';\n        default:\n          return 'badge-secondary';\n      }\n    }\n    static {\n      this.ɵfac = function AppointmentService_Factory(t) {\n        return new (t || AppointmentService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AppointmentService,\n        factory: AppointmentService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AppointmentService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}