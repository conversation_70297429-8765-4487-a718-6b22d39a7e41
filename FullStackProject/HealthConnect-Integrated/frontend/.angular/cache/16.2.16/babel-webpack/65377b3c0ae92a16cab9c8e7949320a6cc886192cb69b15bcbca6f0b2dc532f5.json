{"ast": null, "code": "import { EmptyError } from '../util/EmptyError';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function throwIfEmpty(errorFactory = defaultErrorFactory) {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      hasValue = true;\n      subscriber.next(value);\n    }, () => hasValue ? subscriber.complete() : subscriber.error(errorFactory())));\n  });\n}\nfunction defaultErrorFactory() {\n  return new EmptyError();\n}\n//# sourceMappingURL=throwIfEmpty.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}