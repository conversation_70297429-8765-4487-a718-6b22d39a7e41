{"ast": null, "code": "'use strict';\n\nvar utils = require('../utils/event'),\n  urlUtils = require('../utils/url'),\n  inherits = require('inherits'),\n  EventEmitter = require('events').EventEmitter,\n  WebsocketDriver = require('./driver/websocket');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:websocket');\n}\nfunction WebSocketTransport(transUrl, ignore, options) {\n  if (!WebSocketTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n  EventEmitter.call(this);\n  debug('constructor', transUrl);\n  var self = this;\n  var url = urlUtils.addPath(transUrl, '/websocket');\n  if (url.slice(0, 5) === 'https') {\n    url = 'wss' + url.slice(5);\n  } else {\n    url = 'ws' + url.slice(4);\n  }\n  this.url = url;\n  this.ws = new WebsocketDriver(this.url, [], options);\n  this.ws.onmessage = function (e) {\n    debug('message event', e.data);\n    self.emit('message', e.data);\n  };\n  // Firefox has an interesting bug. If a websocket connection is\n  // created after onunload, it stays alive even when user\n  // navigates away from the page. In such situation let's lie -\n  // let's not open the ws connection at all. See:\n  // https://github.com/sockjs/sockjs-client/issues/28\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=696085\n  this.unloadRef = utils.unloadAdd(function () {\n    debug('unload');\n    self.ws.close();\n  });\n  this.ws.onclose = function (e) {\n    debug('close event', e.code, e.reason);\n    self.emit('close', e.code, e.reason);\n    self._cleanup();\n  };\n  this.ws.onerror = function (e) {\n    debug('error event', e);\n    self.emit('close', 1006, 'WebSocket connection broken');\n    self._cleanup();\n  };\n}\ninherits(WebSocketTransport, EventEmitter);\nWebSocketTransport.prototype.send = function (data) {\n  var msg = '[' + data + ']';\n  debug('send', msg);\n  this.ws.send(msg);\n};\nWebSocketTransport.prototype.close = function () {\n  debug('close');\n  var ws = this.ws;\n  this._cleanup();\n  if (ws) {\n    ws.close();\n  }\n};\nWebSocketTransport.prototype._cleanup = function () {\n  debug('_cleanup');\n  var ws = this.ws;\n  if (ws) {\n    ws.onmessage = ws.onclose = ws.onerror = null;\n  }\n  utils.unloadDel(this.unloadRef);\n  this.unloadRef = this.ws = null;\n  this.removeAllListeners();\n};\nWebSocketTransport.enabled = function () {\n  debug('enabled');\n  return !!WebsocketDriver;\n};\nWebSocketTransport.transportName = 'websocket';\n\n// In theory, ws should require 1 round trip. But in chrome, this is\n// not very stable over SSL. Most likely a ws connection requires a\n// separate SSL connection, in which case 2 round trips are an\n// absolute minumum.\nWebSocketTransport.roundTrips = 2;\nmodule.exports = WebSocketTransport;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}