{"ast": null, "code": "export * from './client.js';\nexport * from './frame-impl.js';\nexport * from './i-frame.js';\nexport * from './i-message.js';\nexport * from './parser.js';\nexport * from './stomp-config.js';\nexport * from './stomp-headers.js';\nexport * from './stomp-subscription.js';\nexport * from './i-transaction.js';\nexport * from './types.js';\nexport * from './versions.js';\n// Compatibility code\nexport * from './compatibility/compat-client.js';\nexport * from './compatibility/stomp.js';", "map": {"version": 3, "names": [], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/@stomp/stompjs/esm6/index.js"], "sourcesContent": ["export * from './client.js';\nexport * from './frame-impl.js';\nexport * from './i-frame.js';\nexport * from './i-message.js';\nexport * from './parser.js';\nexport * from './stomp-config.js';\nexport * from './stomp-headers.js';\nexport * from './stomp-subscription.js';\nexport * from './i-transaction.js';\nexport * from './types.js';\nexport * from './versions.js';\n// Compatibility code\nexport * from './compatibility/compat-client.js';\nexport * from './compatibility/stomp.js';\n"], "mappings": "AAAA,cAAc,aAAa;AAC3B,cAAc,iBAAiB;AAC/B,cAAc,cAAc;AAC5B,cAAc,gBAAgB;AAC9B,cAAc,aAAa;AAC3B,cAAc,mBAAmB;AACjC,cAAc,oBAAoB;AAClC,cAAc,yBAAyB;AACvC,cAAc,oBAAoB;AAClC,cAAc,YAAY;AAC1B,cAAc,eAAe;AAC7B;AACA,cAAc,kCAAkC;AAChD,cAAc,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}