{"ast": null, "code": "/**\n * @internal\n */\nexport function augmentWebsocket(webSocket, debug) {\n  webSocket.terminate = function () {\n    const noOp = () => {};\n    // set all callbacks to no op\n    this.onerror = noOp;\n    this.onmessage = noOp;\n    this.onopen = noOp;\n    const ts = new Date();\n    const id = Math.random().toString().substring(2, 8); // A simulated id\n    const origOnClose = this.onclose;\n    // Track delay in actual closure of the socket\n    this.onclose = closeEvent => {\n      const delay = new Date().getTime() - ts.getTime();\n      debug(`Discarded socket (#${id})  closed after ${delay}ms, with code/reason: ${closeEvent.code}/${closeEvent.reason}`);\n    };\n    this.close();\n    origOnClose?.call(webSocket, {\n      code: 4001,\n      reason: `Quick discarding socket (#${id}) without waiting for the shutdown sequence.`,\n      wasClean: false\n    });\n  };\n}\n//# sourceMappingURL=augment-websocket.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}