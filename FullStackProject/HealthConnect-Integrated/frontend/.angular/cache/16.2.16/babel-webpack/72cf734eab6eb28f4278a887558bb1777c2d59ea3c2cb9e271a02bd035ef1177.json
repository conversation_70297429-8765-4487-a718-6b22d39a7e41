{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  iframeUtils = require('../../utils/iframe'),\n  urlUtils = require('../../utils/url'),\n  EventEmitter = require('events').EventEmitter,\n  random = require('../../utils/random');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:htmlfile');\n}\nfunction HtmlfileReceiver(url) {\n  debug(url);\n  EventEmitter.call(this);\n  var self = this;\n  iframeUtils.polluteGlobalNamespace();\n  this.id = 'a' + random.string(6);\n  url = urlUtils.addQuery(url, 'c=' + decodeURIComponent(iframeUtils.WPrefix + '.' + this.id));\n  debug('using htmlfile', HtmlfileReceiver.htmlfileEnabled);\n  var constructFunc = HtmlfileReceiver.htmlfileEnabled ? iframeUtils.createHtmlfile : iframeUtils.createIframe;\n  global[iframeUtils.WPrefix][this.id] = {\n    start: function () {\n      debug('start');\n      self.iframeObj.loaded();\n    },\n    message: function (data) {\n      debug('message', data);\n      self.emit('message', data);\n    },\n    stop: function () {\n      debug('stop');\n      self._cleanup();\n      self._close('network');\n    }\n  };\n  this.iframeObj = constructFunc(url, function () {\n    debug('callback');\n    self._cleanup();\n    self._close('permanent');\n  });\n}\ninherits(HtmlfileReceiver, EventEmitter);\nHtmlfileReceiver.prototype.abort = function () {\n  debug('abort');\n  this._cleanup();\n  this._close('user');\n};\nHtmlfileReceiver.prototype._cleanup = function () {\n  debug('_cleanup');\n  if (this.iframeObj) {\n    this.iframeObj.cleanup();\n    this.iframeObj = null;\n  }\n  delete global[iframeUtils.WPrefix][this.id];\n};\nHtmlfileReceiver.prototype._close = function (reason) {\n  debug('_close', reason);\n  this.emit('close', null, reason);\n  this.removeAllListeners();\n};\nHtmlfileReceiver.htmlfileEnabled = false;\n\n// obfuscate to avoid firewalls\nvar axo = ['Active'].concat('Object').join('X');\nif (axo in global) {\n  try {\n    HtmlfileReceiver.htmlfileEnabled = !!new global[axo]('htmlfile');\n  } catch (x) {\n    // intentionally empty\n  }\n}\nHtmlfileReceiver.enabled = HtmlfileReceiver.htmlfileEnabled || iframeUtils.iframeEnabled;\nmodule.exports = HtmlfileReceiver;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}