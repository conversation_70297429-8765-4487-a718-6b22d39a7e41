{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { ChatRoutingModule } from './chat-routing.module';\nimport * as i0 from \"@angular/core\";\nexport let ChatModule = /*#__PURE__*/(() => {\n  class ChatModule {\n    static {\n      this.ɵfac = function ChatModule_Factory(t) {\n        return new (t || ChatModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ChatModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, ChatRoutingModule]\n      });\n    }\n  }\n  return ChatModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}