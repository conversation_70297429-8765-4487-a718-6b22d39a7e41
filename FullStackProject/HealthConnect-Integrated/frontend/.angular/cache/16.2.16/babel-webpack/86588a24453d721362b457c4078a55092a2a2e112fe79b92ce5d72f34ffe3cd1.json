{"ast": null, "code": "/**\n * Configuration options for STOMP Client, each key corresponds to\n * field by the same name in {@link Client}. This can be passed to\n * the constructor of {@link Client} or to [Client#configure]{@link Client#configure}.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class StompConfig {}", "map": {"version": 3, "names": ["StompConfig"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/@stomp/stompjs/esm6/stomp-config.js"], "sourcesContent": ["/**\n * Configuration options for STOMP Client, each key corresponds to\n * field by the same name in {@link Client}. This can be passed to\n * the constructor of {@link Client} or to [Client#configure]{@link Client#configure}.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class StompConfig {\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}