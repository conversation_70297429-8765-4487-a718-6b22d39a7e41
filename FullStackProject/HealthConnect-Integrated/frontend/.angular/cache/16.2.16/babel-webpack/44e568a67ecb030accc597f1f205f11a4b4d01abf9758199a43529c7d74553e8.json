{"ast": null, "code": "'use strict';\n\nfunction Event(eventType) {\n  this.type = eventType;\n}\nEvent.prototype.initEvent = function (eventType, canBubble, cancelable) {\n  this.type = eventType;\n  this.bubbles = canBubble;\n  this.cancelable = cancelable;\n  this.timeStamp = +new Date();\n  return this;\n};\nEvent.prototype.stopPropagation = function () {};\nEvent.prototype.preventDefault = function () {};\nEvent.CAPTURING_PHASE = 1;\nEvent.AT_TARGET = 2;\nEvent.BUBBLING_PHASE = 3;\nmodule.exports = Event;", "map": {"version": 3, "names": ["Event", "eventType", "type", "prototype", "initEvent", "canBubble", "cancelable", "bubbles", "timeStamp", "Date", "stopPropagation", "preventDefault", "CAPTURING_PHASE", "AT_TARGET", "BUBBLING_PHASE", "module", "exports"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/sockjs-client/lib/event/event.js"], "sourcesContent": ["'use strict';\n\nfunction Event(eventType) {\n  this.type = eventType;\n}\n\nEvent.prototype.initEvent = function(eventType, canBubble, cancelable) {\n  this.type = eventType;\n  this.bubbles = canBubble;\n  this.cancelable = cancelable;\n  this.timeStamp = +new Date();\n  return this;\n};\n\nEvent.prototype.stopPropagation = function() {};\nEvent.prototype.preventDefault = function() {};\n\nEvent.CAPTURING_PHASE = 1;\nEvent.AT_TARGET = 2;\nEvent.BUBBLING_PHASE = 3;\n\nmodule.exports = Event;\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,KAAKA,CAACC,SAAS,EAAE;EACxB,IAAI,CAACC,IAAI,GAAGD,SAAS;AACvB;AAEAD,KAAK,CAACG,SAAS,CAACC,SAAS,GAAG,UAASH,SAAS,EAAEI,SAAS,EAAEC,UAAU,EAAE;EACrE,IAAI,CAACJ,IAAI,GAAGD,SAAS;EACrB,IAAI,CAACM,OAAO,GAAGF,SAAS;EACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC5B,IAAI,CAACE,SAAS,GAAG,CAAC,IAAIC,IAAI,CAAC,CAAC;EAC5B,OAAO,IAAI;AACb,CAAC;AAEDT,KAAK,CAACG,SAAS,CAACO,eAAe,GAAG,YAAW,CAAC,CAAC;AAC/CV,KAAK,CAACG,SAAS,CAACQ,cAAc,GAAG,YAAW,CAAC,CAAC;AAE9CX,KAAK,CAACY,eAAe,GAAG,CAAC;AACzBZ,KAAK,CAACa,SAAS,GAAG,CAAC;AACnBb,KAAK,CAACc,cAAc,GAAG,CAAC;AAExBC,MAAM,CAACC,OAAO,GAAGhB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}