{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Subject } from 'rxjs';\nimport { Client } from '@stomp/stompjs';\nimport SockJS from 'sockjs-client';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nimport * as i3 from \"./notification.service\";\nexport class ChatService {\n  constructor(http, authService, notificationService) {\n    this.http = http;\n    this.authService = authService;\n    this.notificationService = notificationService;\n    this.apiUrl = `${environment.apiUrl}/chats`;\n    this.wsUrl = `${environment.apiUrl}/ws`;\n    this.stompClient = null;\n    this.connectionStatusSubject = new BehaviorSubject(false);\n    this.messageSubject = new Subject();\n    this.typingSubject = new Subject();\n    this.chatsSubject = new BehaviorSubject([]);\n    this.connectionStatus$ = this.connectionStatusSubject.asObservable();\n    this.messages$ = this.messageSubject.asObservable();\n    this.typing$ = this.typingSubject.asObservable();\n    this.chats$ = this.chatsSubject.asObservable();\n    this.initializeWebSocketConnection();\n  }\n  initializeWebSocketConnection() {\n    if (this.authService.isAuthenticated()) {\n      this.connect();\n    }\n    // Listen for authentication changes\n    this.authService.currentUser$.subscribe(user => {\n      if (user) {\n        this.connect();\n      } else {\n        this.disconnect();\n      }\n    });\n  }\n  connect() {\n    if (this.stompClient?.connected) {\n      return;\n    }\n    const token = this.authService.getToken();\n    if (!token) {\n      return;\n    }\n    this.stompClient = new Client({\n      webSocketFactory: () => new SockJS(this.wsUrl),\n      connectHeaders: {\n        Authorization: `Bearer ${token}`\n      },\n      debug: str => {\n        console.log('STOMP Debug:', str);\n      },\n      onConnect: () => {\n        this.connectionStatusSubject.next(true);\n        console.log('WebSocket connected successfully');\n        this.subscribeToUserChannels();\n      },\n      onWebSocketClose: () => {\n        this.connectionStatusSubject.next(false);\n        console.log('WebSocket connection closed');\n        // Try to reconnect after 5 seconds\n        setTimeout(() => {\n          if (this.authService.isAuthenticated()) {\n            this.connect();\n          }\n        }, 5000);\n      },\n      onStompError: frame => {\n        console.error('STOMP error:', frame);\n        this.connectionStatusSubject.next(false);\n      }\n    });\n    this.stompClient.activate();\n  }\n  subscribeToUserChannels() {\n    if (!this.stompClient?.connected) {\n      return;\n    }\n    const currentUser = this.authService.getCurrentUser();\n    if (!currentUser) {\n      return;\n    }\n    // Subscribe to error messages\n    this.stompClient.subscribe('/user/queue/errors', message => {\n      console.error('WebSocket error:', message.body);\n    });\n  }\n  subscribeToChatMessages(chatId) {\n    if (!this.stompClient?.connected) {\n      return;\n    }\n    // Subscribe to chat messages\n    this.stompClient.subscribe(`/topic/chat/${chatId}`, message => {\n      const newMessage = JSON.parse(message.body);\n      this.messageSubject.next(newMessage);\n      // Add notification for new messages from other users\n      const currentUser = this.authService.getCurrentUser();\n      if (newMessage.sender.id !== currentUser?.id) {\n        this.notificationService.addMessageNotification(newMessage.sender, newMessage.content, chatId);\n      }\n    });\n    // Subscribe to typing notifications\n    this.stompClient.subscribe(`/topic/chat/${chatId}/typing`, message => {\n      const typingNotification = JSON.parse(message.body);\n      this.typingSubject.next(typingNotification);\n    });\n  }\n  sendMessage(chatId, content) {\n    if (!this.stompClient?.connected) {\n      throw new Error('WebSocket not connected');\n    }\n    const token = this.authService.getToken();\n    if (!token) {\n      throw new Error('No authentication token');\n    }\n    const messageRequest = {\n      chatId,\n      content\n    };\n    this.stompClient.publish({\n      destination: `/app/chat/${chatId}/send`,\n      body: JSON.stringify(messageRequest),\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  sendTypingNotification(chatId, isTyping) {\n    if (!this.stompClient?.connected) {\n      return;\n    }\n    const token = this.authService.getToken();\n    if (!token) {\n      return;\n    }\n    this.stompClient.publish({\n      destination: `/app/chat/${chatId}/typing`,\n      body: isTyping ? 'typing' : 'stopped',\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  disconnect() {\n    if (this.stompClient) {\n      this.stompClient.deactivate();\n      this.connectionStatusSubject.next(false);\n    }\n  }\n  // HTTP API methods\n  createOrGetChat(participantId) {\n    const request = {\n      participantId\n    };\n    return this.http.post(this.apiUrl, request, this.getHttpOptions());\n  }\n  getUserChats() {\n    return this.http.get(this.apiUrl, this.getHttpOptions());\n  }\n  getChatMessages(chatId, page = 0, size = 50) {\n    const params = {\n      page: page.toString(),\n      size: size.toString()\n    };\n    return this.http.get(`${this.apiUrl}/${chatId}/messages`, {\n      ...this.getHttpOptions(),\n      params\n    });\n  }\n  markMessagesAsRead(chatId) {\n    return this.http.put(`${this.apiUrl}/${chatId}/read`, {}, this.getHttpOptions());\n  }\n  loadUserChats() {\n    this.getUserChats().subscribe({\n      next: chats => {\n        this.chatsSubject.next(chats);\n      },\n      error: error => {\n        console.error('Failed to load chats:', error);\n      }\n    });\n  }\n  getHttpOptions() {\n    const token = this.authService.getToken();\n    return {\n      headers: new HttpHeaders({\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${token}`\n      })\n    };\n  }\n  static {\n    this.ɵfac = function ChatService_Factory(t) {\n      return new (t || ChatService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService), i0.ɵɵinject(i3.NotificationService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ChatService,\n      factory: ChatService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "Subject", "Client", "SockJS", "environment", "ChatService", "constructor", "http", "authService", "notificationService", "apiUrl", "wsUrl", "stompClient", "connectionStatusSubject", "messageSubject", "typingSubject", "chatsSubject", "connectionStatus$", "asObservable", "messages$", "typing$", "chats$", "initializeWebSocketConnection", "isAuthenticated", "connect", "currentUser$", "subscribe", "user", "disconnect", "connected", "token", "getToken", "webSocketFactory", "connectHeaders", "Authorization", "debug", "str", "console", "log", "onConnect", "next", "subscribeToUserChannels", "onWebSocketClose", "setTimeout", "onStompError", "frame", "error", "activate", "currentUser", "getCurrentUser", "message", "body", "subscribeToChatMessages", "chatId", "newMessage", "JSON", "parse", "sender", "id", "addMessageNotification", "content", "typingNotification", "sendMessage", "Error", "messageRequest", "publish", "destination", "stringify", "headers", "sendTypingNotification", "isTyping", "deactivate", "createOrGetChat", "participantId", "request", "post", "getHttpOptions", "getUserChats", "get", "getChatMessages", "page", "size", "params", "toString", "markMessagesAsRead", "put", "loadUserChats", "chats", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "i3", "NotificationService", "factory", "ɵfac", "providedIn"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/core/services/chat.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, Subject } from 'rxjs';\nimport { Client } from '@stomp/stompjs';\nimport SockJS from 'sockjs-client';\nimport { environment } from '../../../environments/environment';\nimport { Chat, Message, ChatRequest, MessageRequest, TypingNotification } from '../models/chat.model';\nimport { AuthService } from './auth.service';\nimport { NotificationService } from './notification.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ChatService {\n  private apiUrl = `${environment.apiUrl}/chats`;\n  private wsUrl = `${environment.apiUrl}/ws`;\n  \n  private stompClient: Client | null = null;\n  private connectionStatusSubject = new BehaviorSubject<boolean>(false);\n  private messageSubject = new Subject<Message>();\n  private typingSubject = new Subject<TypingNotification>();\n  private chatsSubject = new BehaviorSubject<Chat[]>([]);\n  \n  public connectionStatus$ = this.connectionStatusSubject.asObservable();\n  public messages$ = this.messageSubject.asObservable();\n  public typing$ = this.typingSubject.asObservable();\n  public chats$ = this.chatsSubject.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private authService: AuthService,\n    private notificationService: NotificationService\n  ) {\n    this.initializeWebSocketConnection();\n  }\n\n  private initializeWebSocketConnection(): void {\n    if (this.authService.isAuthenticated()) {\n      this.connect();\n    }\n    \n    // Listen for authentication changes\n    this.authService.currentUser$.subscribe(user => {\n      if (user) {\n        this.connect();\n      } else {\n        this.disconnect();\n      }\n    });\n  }\n\n  private connect(): void {\n    if (this.stompClient?.connected) {\n      return;\n    }\n\n    const token = this.authService.getToken();\n    if (!token) {\n      return;\n    }\n\n    this.stompClient = new Client({\n      webSocketFactory: () => new SockJS(this.wsUrl),\n      connectHeaders: {\n        Authorization: `Bearer ${token}`\n      },\n      debug: (str) => {\n        console.log('STOMP Debug:', str);\n      },\n      onConnect: () => {\n        this.connectionStatusSubject.next(true);\n        console.log('WebSocket connected successfully');\n        this.subscribeToUserChannels();\n      },\n      onWebSocketClose: () => {\n        this.connectionStatusSubject.next(false);\n        console.log('WebSocket connection closed');\n        // Try to reconnect after 5 seconds\n        setTimeout(() => {\n          if (this.authService.isAuthenticated()) {\n            this.connect();\n          }\n        }, 5000);\n      },\n      onStompError: (frame) => {\n        console.error('STOMP error:', frame);\n        this.connectionStatusSubject.next(false);\n      }\n    });\n\n    this.stompClient.activate();\n  }\n\n  private subscribeToUserChannels(): void {\n    if (!this.stompClient?.connected) {\n      return;\n    }\n\n    const currentUser = this.authService.getCurrentUser();\n    if (!currentUser) {\n      return;\n    }\n\n    // Subscribe to error messages\n    this.stompClient.subscribe('/user/queue/errors', (message) => {\n      console.error('WebSocket error:', message.body);\n    });\n  }\n\n  public subscribeToChatMessages(chatId: number): void {\n    if (!this.stompClient?.connected) {\n      return;\n    }\n\n    // Subscribe to chat messages\n    this.stompClient.subscribe(`/topic/chat/${chatId}`, (message) => {\n      const newMessage: Message = JSON.parse(message.body);\n      this.messageSubject.next(newMessage);\n\n      // Add notification for new messages from other users\n      const currentUser = this.authService.getCurrentUser();\n      if (newMessage.sender.id !== currentUser?.id) {\n        this.notificationService.addMessageNotification(\n          newMessage.sender,\n          newMessage.content,\n          chatId\n        );\n      }\n    });\n\n    // Subscribe to typing notifications\n    this.stompClient.subscribe(`/topic/chat/${chatId}/typing`, (message) => {\n      const typingNotification: TypingNotification = JSON.parse(message.body);\n      this.typingSubject.next(typingNotification);\n    });\n  }\n\n  public sendMessage(chatId: number, content: string): void {\n    if (!this.stompClient?.connected) {\n      throw new Error('WebSocket not connected');\n    }\n\n    const token = this.authService.getToken();\n    if (!token) {\n      throw new Error('No authentication token');\n    }\n\n    const messageRequest: MessageRequest = {\n      chatId,\n      content\n    };\n\n    this.stompClient.publish({\n      destination: `/app/chat/${chatId}/send`,\n      body: JSON.stringify(messageRequest),\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n\n  public sendTypingNotification(chatId: number, isTyping: boolean): void {\n    if (!this.stompClient?.connected) {\n      return;\n    }\n\n    const token = this.authService.getToken();\n    if (!token) {\n      return;\n    }\n\n    this.stompClient.publish({\n      destination: `/app/chat/${chatId}/typing`,\n      body: isTyping ? 'typing' : 'stopped',\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n\n  public disconnect(): void {\n    if (this.stompClient) {\n      this.stompClient.deactivate();\n      this.connectionStatusSubject.next(false);\n    }\n  }\n\n  // HTTP API methods\n  public createOrGetChat(participantId: number): Observable<Chat> {\n    const request: ChatRequest = { participantId };\n    return this.http.post<Chat>(this.apiUrl, request, this.getHttpOptions());\n  }\n\n  public getUserChats(): Observable<Chat[]> {\n    return this.http.get<Chat[]>(this.apiUrl, this.getHttpOptions());\n  }\n\n  public getChatMessages(chatId: number, page: number = 0, size: number = 50): Observable<Message[]> {\n    const params = { page: page.toString(), size: size.toString() };\n    return this.http.get<Message[]>(`${this.apiUrl}/${chatId}/messages`, {\n      ...this.getHttpOptions(),\n      params\n    });\n  }\n\n  public markMessagesAsRead(chatId: number): Observable<void> {\n    return this.http.put<void>(`${this.apiUrl}/${chatId}/read`, {}, this.getHttpOptions());\n  }\n\n  public loadUserChats(): void {\n    this.getUserChats().subscribe({\n      next: (chats) => {\n        this.chatsSubject.next(chats);\n      },\n      error: (error) => {\n        console.error('Failed to load chats:', error);\n      }\n    });\n  }\n\n  private getHttpOptions() {\n    const token = this.authService.getToken();\n    return {\n      headers: new HttpHeaders({\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${token}`\n      })\n    };\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,eAAe,EAAcC,OAAO,QAAQ,MAAM;AAC3D,SAASC,MAAM,QAAQ,gBAAgB;AACvC,OAAOC,MAAM,MAAM,eAAe;AAClC,SAASC,WAAW,QAAQ,mCAAmC;;;;;AAQ/D,OAAM,MAAOC,WAAW;EAetBC,YACUC,IAAgB,EAChBC,WAAwB,EACxBC,mBAAwC;IAFxC,KAAAF,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAjBrB,KAAAC,MAAM,GAAG,GAAGN,WAAW,CAACM,MAAM,QAAQ;IACtC,KAAAC,KAAK,GAAG,GAAGP,WAAW,CAACM,MAAM,KAAK;IAElC,KAAAE,WAAW,GAAkB,IAAI;IACjC,KAAAC,uBAAuB,GAAG,IAAIb,eAAe,CAAU,KAAK,CAAC;IAC7D,KAAAc,cAAc,GAAG,IAAIb,OAAO,EAAW;IACvC,KAAAc,aAAa,GAAG,IAAId,OAAO,EAAsB;IACjD,KAAAe,YAAY,GAAG,IAAIhB,eAAe,CAAS,EAAE,CAAC;IAE/C,KAAAiB,iBAAiB,GAAG,IAAI,CAACJ,uBAAuB,CAACK,YAAY,EAAE;IAC/D,KAAAC,SAAS,GAAG,IAAI,CAACL,cAAc,CAACI,YAAY,EAAE;IAC9C,KAAAE,OAAO,GAAG,IAAI,CAACL,aAAa,CAACG,YAAY,EAAE;IAC3C,KAAAG,MAAM,GAAG,IAAI,CAACL,YAAY,CAACE,YAAY,EAAE;IAO9C,IAAI,CAACI,6BAA6B,EAAE;EACtC;EAEQA,6BAA6BA,CAAA;IACnC,IAAI,IAAI,CAACd,WAAW,CAACe,eAAe,EAAE,EAAE;MACtC,IAAI,CAACC,OAAO,EAAE;;IAGhB;IACA,IAAI,CAAChB,WAAW,CAACiB,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAIA,IAAI,EAAE;QACR,IAAI,CAACH,OAAO,EAAE;OACf,MAAM;QACL,IAAI,CAACI,UAAU,EAAE;;IAErB,CAAC,CAAC;EACJ;EAEQJ,OAAOA,CAAA;IACb,IAAI,IAAI,CAACZ,WAAW,EAAEiB,SAAS,EAAE;MAC/B;;IAGF,MAAMC,KAAK,GAAG,IAAI,CAACtB,WAAW,CAACuB,QAAQ,EAAE;IACzC,IAAI,CAACD,KAAK,EAAE;MACV;;IAGF,IAAI,CAAClB,WAAW,GAAG,IAAIV,MAAM,CAAC;MAC5B8B,gBAAgB,EAAEA,CAAA,KAAM,IAAI7B,MAAM,CAAC,IAAI,CAACQ,KAAK,CAAC;MAC9CsB,cAAc,EAAE;QACdC,aAAa,EAAE,UAAUJ,KAAK;OAC/B;MACDK,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,GAAG,CAAC;MAClC,CAAC;MACDG,SAAS,EAAEA,CAAA,KAAK;QACd,IAAI,CAAC1B,uBAAuB,CAAC2B,IAAI,CAAC,IAAI,CAAC;QACvCH,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C,IAAI,CAACG,uBAAuB,EAAE;MAChC,CAAC;MACDC,gBAAgB,EAAEA,CAAA,KAAK;QACrB,IAAI,CAAC7B,uBAAuB,CAAC2B,IAAI,CAAC,KAAK,CAAC;QACxCH,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1C;QACAK,UAAU,CAAC,MAAK;UACd,IAAI,IAAI,CAACnC,WAAW,CAACe,eAAe,EAAE,EAAE;YACtC,IAAI,CAACC,OAAO,EAAE;;QAElB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDoB,YAAY,EAAGC,KAAK,IAAI;QACtBR,OAAO,CAACS,KAAK,CAAC,cAAc,EAAED,KAAK,CAAC;QACpC,IAAI,CAAChC,uBAAuB,CAAC2B,IAAI,CAAC,KAAK,CAAC;MAC1C;KACD,CAAC;IAEF,IAAI,CAAC5B,WAAW,CAACmC,QAAQ,EAAE;EAC7B;EAEQN,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAC7B,WAAW,EAAEiB,SAAS,EAAE;MAChC;;IAGF,MAAMmB,WAAW,GAAG,IAAI,CAACxC,WAAW,CAACyC,cAAc,EAAE;IACrD,IAAI,CAACD,WAAW,EAAE;MAChB;;IAGF;IACA,IAAI,CAACpC,WAAW,CAACc,SAAS,CAAC,oBAAoB,EAAGwB,OAAO,IAAI;MAC3Db,OAAO,CAACS,KAAK,CAAC,kBAAkB,EAAEI,OAAO,CAACC,IAAI,CAAC;IACjD,CAAC,CAAC;EACJ;EAEOC,uBAAuBA,CAACC,MAAc;IAC3C,IAAI,CAAC,IAAI,CAACzC,WAAW,EAAEiB,SAAS,EAAE;MAChC;;IAGF;IACA,IAAI,CAACjB,WAAW,CAACc,SAAS,CAAC,eAAe2B,MAAM,EAAE,EAAGH,OAAO,IAAI;MAC9D,MAAMI,UAAU,GAAYC,IAAI,CAACC,KAAK,CAACN,OAAO,CAACC,IAAI,CAAC;MACpD,IAAI,CAACrC,cAAc,CAAC0B,IAAI,CAACc,UAAU,CAAC;MAEpC;MACA,MAAMN,WAAW,GAAG,IAAI,CAACxC,WAAW,CAACyC,cAAc,EAAE;MACrD,IAAIK,UAAU,CAACG,MAAM,CAACC,EAAE,KAAKV,WAAW,EAAEU,EAAE,EAAE;QAC5C,IAAI,CAACjD,mBAAmB,CAACkD,sBAAsB,CAC7CL,UAAU,CAACG,MAAM,EACjBH,UAAU,CAACM,OAAO,EAClBP,MAAM,CACP;;IAEL,CAAC,CAAC;IAEF;IACA,IAAI,CAACzC,WAAW,CAACc,SAAS,CAAC,eAAe2B,MAAM,SAAS,EAAGH,OAAO,IAAI;MACrE,MAAMW,kBAAkB,GAAuBN,IAAI,CAACC,KAAK,CAACN,OAAO,CAACC,IAAI,CAAC;MACvE,IAAI,CAACpC,aAAa,CAACyB,IAAI,CAACqB,kBAAkB,CAAC;IAC7C,CAAC,CAAC;EACJ;EAEOC,WAAWA,CAACT,MAAc,EAAEO,OAAe;IAChD,IAAI,CAAC,IAAI,CAAChD,WAAW,EAAEiB,SAAS,EAAE;MAChC,MAAM,IAAIkC,KAAK,CAAC,yBAAyB,CAAC;;IAG5C,MAAMjC,KAAK,GAAG,IAAI,CAACtB,WAAW,CAACuB,QAAQ,EAAE;IACzC,IAAI,CAACD,KAAK,EAAE;MACV,MAAM,IAAIiC,KAAK,CAAC,yBAAyB,CAAC;;IAG5C,MAAMC,cAAc,GAAmB;MACrCX,MAAM;MACNO;KACD;IAED,IAAI,CAAChD,WAAW,CAACqD,OAAO,CAAC;MACvBC,WAAW,EAAE,aAAab,MAAM,OAAO;MACvCF,IAAI,EAAEI,IAAI,CAACY,SAAS,CAACH,cAAc,CAAC;MACpCI,OAAO,EAAE;QACPlC,aAAa,EAAE,UAAUJ,KAAK;;KAEjC,CAAC;EACJ;EAEOuC,sBAAsBA,CAAChB,MAAc,EAAEiB,QAAiB;IAC7D,IAAI,CAAC,IAAI,CAAC1D,WAAW,EAAEiB,SAAS,EAAE;MAChC;;IAGF,MAAMC,KAAK,GAAG,IAAI,CAACtB,WAAW,CAACuB,QAAQ,EAAE;IACzC,IAAI,CAACD,KAAK,EAAE;MACV;;IAGF,IAAI,CAAClB,WAAW,CAACqD,OAAO,CAAC;MACvBC,WAAW,EAAE,aAAab,MAAM,SAAS;MACzCF,IAAI,EAAEmB,QAAQ,GAAG,QAAQ,GAAG,SAAS;MACrCF,OAAO,EAAE;QACPlC,aAAa,EAAE,UAAUJ,KAAK;;KAEjC,CAAC;EACJ;EAEOF,UAAUA,CAAA;IACf,IAAI,IAAI,CAAChB,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC2D,UAAU,EAAE;MAC7B,IAAI,CAAC1D,uBAAuB,CAAC2B,IAAI,CAAC,KAAK,CAAC;;EAE5C;EAEA;EACOgC,eAAeA,CAACC,aAAqB;IAC1C,MAAMC,OAAO,GAAgB;MAAED;IAAa,CAAE;IAC9C,OAAO,IAAI,CAAClE,IAAI,CAACoE,IAAI,CAAO,IAAI,CAACjE,MAAM,EAAEgE,OAAO,EAAE,IAAI,CAACE,cAAc,EAAE,CAAC;EAC1E;EAEOC,YAAYA,CAAA;IACjB,OAAO,IAAI,CAACtE,IAAI,CAACuE,GAAG,CAAS,IAAI,CAACpE,MAAM,EAAE,IAAI,CAACkE,cAAc,EAAE,CAAC;EAClE;EAEOG,eAAeA,CAAC1B,MAAc,EAAE2B,IAAA,GAAe,CAAC,EAAEC,IAAA,GAAe,EAAE;IACxE,MAAMC,MAAM,GAAG;MAAEF,IAAI,EAAEA,IAAI,CAACG,QAAQ,EAAE;MAAEF,IAAI,EAAEA,IAAI,CAACE,QAAQ;IAAE,CAAE;IAC/D,OAAO,IAAI,CAAC5E,IAAI,CAACuE,GAAG,CAAY,GAAG,IAAI,CAACpE,MAAM,IAAI2C,MAAM,WAAW,EAAE;MACnE,GAAG,IAAI,CAACuB,cAAc,EAAE;MACxBM;KACD,CAAC;EACJ;EAEOE,kBAAkBA,CAAC/B,MAAc;IACtC,OAAO,IAAI,CAAC9C,IAAI,CAAC8E,GAAG,CAAO,GAAG,IAAI,CAAC3E,MAAM,IAAI2C,MAAM,OAAO,EAAE,EAAE,EAAE,IAAI,CAACuB,cAAc,EAAE,CAAC;EACxF;EAEOU,aAAaA,CAAA;IAClB,IAAI,CAACT,YAAY,EAAE,CAACnD,SAAS,CAAC;MAC5Bc,IAAI,EAAG+C,KAAK,IAAI;QACd,IAAI,CAACvE,YAAY,CAACwB,IAAI,CAAC+C,KAAK,CAAC;MAC/B,CAAC;MACDzC,KAAK,EAAGA,KAAK,IAAI;QACfT,OAAO,CAACS,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC/C;KACD,CAAC;EACJ;EAEQ8B,cAAcA,CAAA;IACpB,MAAM9C,KAAK,GAAG,IAAI,CAACtB,WAAW,CAACuB,QAAQ,EAAE;IACzC,OAAO;MACLqC,OAAO,EAAE,IAAIrE,WAAW,CAAC;QACvB,cAAc,EAAE,kBAAkB;QAClC,eAAe,EAAE,UAAU+B,KAAK;OACjC;KACF;EACH;;;uBAvNWzB,WAAW,EAAAmF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;aAAX1F,WAAW;MAAA2F,OAAA,EAAX3F,WAAW,CAAA4F,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}