{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  HtmlfileReceiver = require('./receiver/htmlfile'),\n  XHRLocalObject = require('./sender/xhr-local'),\n  AjaxBasedTransport = require('./lib/ajax-based');\nfunction HtmlFileTransport(transUrl) {\n  if (!HtmlfileReceiver.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/htmlfile', HtmlfileReceiver, XHRLocalObject);\n}\ninherits(HtmlFileTransport, AjaxBasedTransport);\nHtmlFileTransport.enabled = function (info) {\n  return HtmlfileReceiver.enabled && info.sameOrigin;\n};\nHtmlFileTransport.transportName = 'htmlfile';\nHtmlFileTransport.roundTrips = 2;\nmodule.exports = HtmlFileTransport;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}