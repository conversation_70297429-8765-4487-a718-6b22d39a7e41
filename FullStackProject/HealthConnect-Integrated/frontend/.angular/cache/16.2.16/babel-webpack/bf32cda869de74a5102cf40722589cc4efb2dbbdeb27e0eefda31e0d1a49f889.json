{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/appointment.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction AppointmentContextComponent_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"span\", 7);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"span\", 8);\n    i0.ɵɵtext(5, \"Loading appointment details...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppointmentContextComponent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"i\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.error, \" \");\n  }\n}\nfunction AppointmentContextComponent_div_0_div_3_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵelementStart(2, \"span\", 39);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.appointment.reasonForVisit);\n  }\n}\nfunction AppointmentContextComponent_div_0_div_3_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"i\", 41);\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3, \"Upcoming:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.getTimeUntilAppointment(), \" \");\n  }\n}\nfunction AppointmentContextComponent_div_0_div_3_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3, \"Completed:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Follow-up discussion \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentContextComponent_div_0_div_3_button_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function AppointmentContextComponent_div_0_div_3_button_41_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r8.openMeetingLink(ctx_r8.appointment.meetingLink));\n    });\n    i0.ɵɵelement(1, \"i\", 45);\n    i0.ɵɵtext(2, \" Join Call \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentContextComponent_div_0_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"div\", 13);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementStart(4, \"h6\", 14);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function AppointmentContextComponent_div_0_div_3_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.navigateToAppointment());\n    });\n    i0.ɵɵelement(7, \"i\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 17)(9, \"div\", 18)(10, \"div\", 19)(11, \"div\", 20);\n    i0.ɵɵelement(12, \"i\", 21);\n    i0.ɵɵelementStart(13, \"span\", 22);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 19)(17, \"div\", 20);\n    i0.ɵɵelement(18, \"i\", 23);\n    i0.ɵɵelementStart(19, \"span\", 22);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 19)(22, \"div\", 20);\n    i0.ɵɵelement(23, \"i\", 24);\n    i0.ɵɵelementStart(24, \"span\", 22);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 19)(27, \"div\", 20);\n    i0.ɵɵelement(28, \"i\", 25);\n    i0.ɵɵelementStart(29, \"span\", 26);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(31, \"div\", 27);\n    i0.ɵɵelement(32, \"i\", 28);\n    i0.ɵɵelementStart(33, \"span\", 22);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, AppointmentContextComponent_div_0_div_3_div_35_Template, 4, 1, \"div\", 29);\n    i0.ɵɵelementStart(36, \"div\", 30);\n    i0.ɵɵtemplate(37, AppointmentContextComponent_div_0_div_3_div_37_Template, 5, 1, \"div\", 31);\n    i0.ɵɵtemplate(38, AppointmentContextComponent_div_0_div_3_div_38_Template, 5, 0, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 33)(40, \"div\", 34);\n    i0.ɵɵtemplate(41, AppointmentContextComponent_div_0_div_3_button_41_Template, 3, 0, \"button\", 35);\n    i0.ɵɵelementStart(42, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function AppointmentContextComponent_div_0_div_3_Template_button_click_42_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.navigateToAppointment());\n    });\n    i0.ɵɵelement(43, \"i\", 37);\n    i0.ɵɵtext(44, \" View Details \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r3.getContextIcon() + \" me-2\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getContextTitle());\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 17, ctx_r3.appointment.date, \"mediumDate\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r3.appointment.startTime, \" - \", ctx_r3.appointment.endTime, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.appointment.doctor.fullName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(\"badge-\" + ctx_r3.appointment.status.toLowerCase());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.appointment.status, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r3.appointment.type === \"VIDEO_CALL\" ? \"fa-video\" : \"fa-user-friends\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.appointment.type === \"VIDEO_CALL\" ? \"Video Call\" : \"In-Person\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.appointment.reasonForVisit);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isBeforeAppointment());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isAfterAppointment());\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.appointment.type === \"VIDEO_CALL\" && ctx_r3.appointment.meetingLink && ctx_r3.isBeforeAppointment());\n  }\n}\nfunction AppointmentContextComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, AppointmentContextComponent_div_0_div_1_Template, 6, 0, \"div\", 2);\n    i0.ɵɵtemplate(2, AppointmentContextComponent_div_0_div_2_Template, 3, 1, \"div\", 3);\n    i0.ɵɵtemplate(3, AppointmentContextComponent_div_0_div_3_Template, 45, 20, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.error);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.appointment && !ctx_r0.loading && !ctx_r0.error);\n  }\n}\nexport class AppointmentContextComponent {\n  constructor(appointmentService, router) {\n    this.appointmentService = appointmentService;\n    this.router = router;\n    this.appointment = null;\n    this.loading = false;\n    this.error = null;\n  }\n  ngOnInit() {\n    if (this.appointmentId) {\n      this.loadAppointment();\n    }\n  }\n  loadAppointment() {\n    if (!this.appointmentId) return;\n    this.loading = true;\n    this.error = null;\n    this.appointmentService.getAppointment(this.appointmentId).subscribe({\n      next: appointment => {\n        this.appointment = appointment;\n        this.loading = false;\n      },\n      error: error => {\n        this.error = 'Failed to load appointment details';\n        this.loading = false;\n        console.error('Error loading appointment:', error);\n      }\n    });\n  }\n  navigateToAppointment() {\n    if (this.appointment) {\n      this.router.navigate(['/appointments', this.appointment.id]);\n    }\n  }\n  getContextTitle() {\n    switch (this.chatType) {\n      case 'PRE_APPOINTMENT':\n        return 'Pre-Appointment Discussion';\n      case 'POST_APPOINTMENT':\n        return 'Post-Appointment Follow-up';\n      case 'URGENT':\n        return 'Urgent Medical Consultation';\n      case 'PRESCRIPTION_INQUIRY':\n        return 'Prescription Questions';\n      case 'FOLLOW_UP':\n        return 'Follow-up Care';\n      default:\n        return 'Appointment Discussion';\n    }\n  }\n  getContextIcon() {\n    switch (this.chatType) {\n      case 'PRE_APPOINTMENT':\n        return 'fas fa-clock text-info';\n      case 'POST_APPOINTMENT':\n        return 'fas fa-check-circle text-success';\n      case 'URGENT':\n        return 'fas fa-exclamation-triangle text-danger';\n      case 'PRESCRIPTION_INQUIRY':\n        return 'fas fa-pills text-primary';\n      case 'FOLLOW_UP':\n        return 'fas fa-stethoscope text-secondary';\n      default:\n        return 'fas fa-calendar text-primary';\n    }\n  }\n  isBeforeAppointment() {\n    if (!this.appointment) return false;\n    const appointmentDateTime = new Date(`${this.appointment.date}T${this.appointment.startTime}`);\n    return appointmentDateTime > new Date();\n  }\n  isAfterAppointment() {\n    if (!this.appointment) return false;\n    const appointmentDateTime = new Date(`${this.appointment.date}T${this.appointment.endTime}`);\n    return appointmentDateTime < new Date();\n  }\n  getTimeUntilAppointment() {\n    if (!this.appointment) return '';\n    const appointmentDateTime = new Date(`${this.appointment.date}T${this.appointment.startTime}`);\n    const now = new Date();\n    const diffMs = appointmentDateTime.getTime() - now.getTime();\n    if (diffMs <= 0) return 'Appointment has passed';\n    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n    const diffHours = Math.floor(diffMs % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n    const diffMinutes = Math.floor(diffMs % (1000 * 60 * 60) / (1000 * 60));\n    if (diffDays > 0) {\n      return `${diffDays} day${diffDays > 1 ? 's' : ''} remaining`;\n    } else if (diffHours > 0) {\n      return `${diffHours} hour${diffHours > 1 ? 's' : ''} remaining`;\n    } else {\n      return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} remaining`;\n    }\n  }\n  openMeetingLink(link) {\n    window.open(link, '_blank');\n  }\n  static {\n    this.ɵfac = function AppointmentContextComponent_Factory(t) {\n      return new (t || AppointmentContextComponent)(i0.ɵɵdirectiveInject(i1.AppointmentService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppointmentContextComponent,\n      selectors: [[\"app-appointment-context\"]],\n      inputs: {\n        appointmentId: \"appointmentId\",\n        chatType: \"chatType\"\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"appointment-context-card\", 4, \"ngIf\"], [1, \"appointment-context-card\"], [\"class\", \"text-center p-3\", 4, \"ngIf\"], [\"class\", \"alert alert-warning mb-0\", 4, \"ngIf\"], [\"class\", \"appointment-context\", 4, \"ngIf\"], [1, \"text-center\", \"p-3\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\"], [1, \"visually-hidden\"], [1, \"ms-2\"], [1, \"alert\", \"alert-warning\", \"mb-0\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-2\"], [1, \"appointment-context\"], [1, \"context-header\"], [1, \"d-flex\", \"align-items-center\"], [1, \"mb-0\", \"fw-bold\"], [\"type\", \"button\", \"title\", \"View full appointment details\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"fas\", \"fa-external-link-alt\"], [1, \"appointment-summary\"], [1, \"row\", \"g-2\"], [1, \"col-md-6\"], [1, \"summary-item\"], [1, \"fas\", \"fa-calendar-alt\", \"text-muted\", \"me-2\"], [1, \"fw-medium\"], [1, \"fas\", \"fa-clock\", \"text-muted\", \"me-2\"], [1, \"fas\", \"fa-user-md\", \"text-muted\", \"me-2\"], [1, \"fas\", \"fa-tag\", \"text-muted\", \"me-2\"], [1, \"badge\"], [1, \"summary-item\", \"mt-2\"], [1, \"text-muted\", \"me-2\"], [\"class\", \"summary-item mt-2\", 4, \"ngIf\"], [1, \"time-context\", \"mt-3\"], [\"class\", \"alert alert-info py-2 mb-0\", 4, \"ngIf\"], [\"class\", \"alert alert-success py-2 mb-0\", 4, \"ngIf\"], [1, \"quick-actions\", \"mt-3\"], [\"role\", \"group\", 1, \"btn-group\", \"w-100\"], [\"type\", \"button\", \"class\", \"btn btn-sm btn-primary\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-eye\", \"me-1\"], [1, \"fas\", \"fa-notes-medical\", \"text-muted\", \"me-2\"], [1, \"text-muted\"], [1, \"alert\", \"alert-info\", \"py-2\", \"mb-0\"], [1, \"fas\", \"fa-info-circle\", \"me-2\"], [1, \"alert\", \"alert-success\", \"py-2\", \"mb-0\"], [1, \"fas\", \"fa-check-circle\", \"me-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-video\", \"me-1\"]],\n      template: function AppointmentContextComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AppointmentContextComponent_div_0_Template, 4, 3, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.appointmentId);\n        }\n      },\n      dependencies: [i3.NgIf, i3.DatePipe],\n      styles: [\".appointment-context-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border: 1px solid #dee2e6;\\n  border-radius: 12px;\\n  margin-bottom: 1rem;\\n  overflow: hidden;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n}\\n\\n.context-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1rem;\\n  background: white;\\n  border-bottom: 1px solid #dee2e6;\\n}\\n.context-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n\\n.appointment-summary[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n\\n.summary-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 0.5rem;\\n}\\n.summary-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.summary-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  width: 16px;\\n  text-align: center;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.badge.badge-pending[_ngcontent-%COMP%] {\\n  background-color: #ffc107;\\n  color: #000;\\n}\\n.badge.badge-scheduled[_ngcontent-%COMP%] {\\n  background-color: #17a2b8;\\n  color: white;\\n}\\n.badge.badge-confirmed[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n}\\n.badge.badge-completed[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: white;\\n}\\n.badge.badge-cancelled[_ngcontent-%COMP%] {\\n  background-color: #dc3545;\\n  color: white;\\n}\\n\\n.time-context[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  font-size: 0.875rem;\\n}\\n\\n.quick-actions[_ngcontent-%COMP%] {\\n  padding: 0 1rem 1rem;\\n}\\n.quick-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 6px;\\n  font-size: 0.875rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .context-header[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .appointment-summary[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .quick-actions[_ngcontent-%COMP%] {\\n    padding: 0 0.75rem 0.75rem;\\n  }\\n  .quick-actions[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .quick-actions[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    border-radius: 6px !important;\\n    margin-bottom: 0.25rem;\\n  }\\n  .quick-actions[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child {\\n    margin-bottom: 0;\\n  }\\n}\\n.appointment-context-card[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "error", "ɵɵtextInterpolate", "ctx_r4", "appointment", "reasonForVisit", "ctx_r5", "getTimeUntilAppointment", "ɵɵlistener", "AppointmentContextComponent_div_0_div_3_button_41_Template_button_click_0_listener", "ɵɵrestoreView", "_r9", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "openMeetingLink", "meetingLink", "AppointmentContextComponent_div_0_div_3_Template_button_click_6_listener", "_r11", "ctx_r10", "navigateToAppointment", "ɵɵtemplate", "AppointmentContextComponent_div_0_div_3_div_35_Template", "AppointmentContextComponent_div_0_div_3_div_37_Template", "AppointmentContextComponent_div_0_div_3_div_38_Template", "AppointmentContextComponent_div_0_div_3_button_41_Template", "AppointmentContextComponent_div_0_div_3_Template_button_click_42_listener", "ctx_r12", "ɵɵclassMap", "ctx_r3", "getContextIcon", "getContextTitle", "ɵɵpipeBind2", "date", "ɵɵtextInterpolate2", "startTime", "endTime", "doctor", "fullName", "status", "toLowerCase", "type", "ɵɵproperty", "isBeforeAppointment", "isAfterAppointment", "AppointmentContextComponent_div_0_div_1_Template", "AppointmentContextComponent_div_0_div_2_Template", "AppointmentContextComponent_div_0_div_3_Template", "ctx_r0", "loading", "AppointmentContextComponent", "constructor", "appointmentService", "router", "ngOnInit", "appointmentId", "loadAppointment", "getAppointment", "subscribe", "next", "console", "navigate", "id", "chatType", "appointmentDateTime", "Date", "now", "diffMs", "getTime", "diffDays", "Math", "floor", "diffHours", "diffMinutes", "link", "window", "open", "ɵɵdirectiveInject", "i1", "AppointmentService", "i2", "Router", "selectors", "inputs", "decls", "vars", "consts", "template", "AppointmentContextComponent_Template", "rf", "ctx", "AppointmentContextComponent_div_0_Template"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/chat/appointment-context/appointment-context.component.ts", "/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/chat/appointment-context/appointment-context.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { Appointment } from '../../core/models/appointment.model';\nimport { AppointmentService } from '../../core/services/appointment.service';\n\n@Component({\n  selector: 'app-appointment-context',\n  templateUrl: './appointment-context.component.html',\n  styleUrls: ['./appointment-context.component.scss']\n})\nexport class AppointmentContextComponent implements OnInit {\n  @Input() appointmentId?: number;\n  @Input() chatType?: string;\n  \n  appointment: Appointment | null = null;\n  loading = false;\n  error: string | null = null;\n\n  constructor(\n    private appointmentService: AppointmentService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    if (this.appointmentId) {\n      this.loadAppointment();\n    }\n  }\n\n  private loadAppointment(): void {\n    if (!this.appointmentId) return;\n    \n    this.loading = true;\n    this.error = null;\n    \n    this.appointmentService.getAppointment(this.appointmentId).subscribe({\n      next: (appointment) => {\n        this.appointment = appointment;\n        this.loading = false;\n      },\n      error: (error) => {\n        this.error = 'Failed to load appointment details';\n        this.loading = false;\n        console.error('Error loading appointment:', error);\n      }\n    });\n  }\n\n  navigateToAppointment(): void {\n    if (this.appointment) {\n      this.router.navigate(['/appointments', this.appointment.id]);\n    }\n  }\n\n  getContextTitle(): string {\n    switch (this.chatType) {\n      case 'PRE_APPOINTMENT':\n        return 'Pre-Appointment Discussion';\n      case 'POST_APPOINTMENT':\n        return 'Post-Appointment Follow-up';\n      case 'URGENT':\n        return 'Urgent Medical Consultation';\n      case 'PRESCRIPTION_INQUIRY':\n        return 'Prescription Questions';\n      case 'FOLLOW_UP':\n        return 'Follow-up Care';\n      default:\n        return 'Appointment Discussion';\n    }\n  }\n\n  getContextIcon(): string {\n    switch (this.chatType) {\n      case 'PRE_APPOINTMENT':\n        return 'fas fa-clock text-info';\n      case 'POST_APPOINTMENT':\n        return 'fas fa-check-circle text-success';\n      case 'URGENT':\n        return 'fas fa-exclamation-triangle text-danger';\n      case 'PRESCRIPTION_INQUIRY':\n        return 'fas fa-pills text-primary';\n      case 'FOLLOW_UP':\n        return 'fas fa-stethoscope text-secondary';\n      default:\n        return 'fas fa-calendar text-primary';\n    }\n  }\n\n  isBeforeAppointment(): boolean {\n    if (!this.appointment) return false;\n    const appointmentDateTime = new Date(`${this.appointment.date}T${this.appointment.startTime}`);\n    return appointmentDateTime > new Date();\n  }\n\n  isAfterAppointment(): boolean {\n    if (!this.appointment) return false;\n    const appointmentDateTime = new Date(`${this.appointment.date}T${this.appointment.endTime}`);\n    return appointmentDateTime < new Date();\n  }\n\n  getTimeUntilAppointment(): string {\n    if (!this.appointment) return '';\n\n    const appointmentDateTime = new Date(`${this.appointment.date}T${this.appointment.startTime}`);\n    const now = new Date();\n    const diffMs = appointmentDateTime.getTime() - now.getTime();\n\n    if (diffMs <= 0) return 'Appointment has passed';\n\n    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n    const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\n    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\n\n    if (diffDays > 0) {\n      return `${diffDays} day${diffDays > 1 ? 's' : ''} remaining`;\n    } else if (diffHours > 0) {\n      return `${diffHours} hour${diffHours > 1 ? 's' : ''} remaining`;\n    } else {\n      return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} remaining`;\n    }\n  }\n\n  openMeetingLink(link: string): void {\n    window.open(link, '_blank');\n  }\n}\n", "<div *ngIf=\"appointmentId\" class=\"appointment-context-card\">\n  <!-- Loading State -->\n  <div *ngIf=\"loading\" class=\"text-center p-3\">\n    <div class=\"spinner-border spinner-border-sm\" role=\"status\">\n      <span class=\"visually-hidden\">Loading...</span>\n    </div>\n    <span class=\"ms-2\">Loading appointment details...</span>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error\" class=\"alert alert-warning mb-0\">\n    <i class=\"fas fa-exclamation-triangle me-2\"></i>\n    {{ error }}\n  </div>\n\n  <!-- Appointment Context -->\n  <div *ngIf=\"appointment && !loading && !error\" class=\"appointment-context\">\n    <!-- Header -->\n    <div class=\"context-header\">\n      <div class=\"d-flex align-items-center\">\n        <i [class]=\"getContextIcon() + ' me-2'\"></i>\n        <h6 class=\"mb-0 fw-bold\">{{ getContextTitle() }}</h6>\n      </div>\n      <button \n        type=\"button\" \n        class=\"btn btn-sm btn-outline-primary\"\n        (click)=\"navigateToAppointment()\"\n        title=\"View full appointment details\">\n        <i class=\"fas fa-external-link-alt\"></i>\n      </button>\n    </div>\n\n    <!-- Appointment Summary -->\n    <div class=\"appointment-summary\">\n      <div class=\"row g-2\">\n        <div class=\"col-md-6\">\n          <div class=\"summary-item\">\n            <i class=\"fas fa-calendar-alt text-muted me-2\"></i>\n            <span class=\"fw-medium\">{{ appointment.date | date:'mediumDate' }}</span>\n          </div>\n        </div>\n        <div class=\"col-md-6\">\n          <div class=\"summary-item\">\n            <i class=\"fas fa-clock text-muted me-2\"></i>\n            <span class=\"fw-medium\">{{ appointment.startTime }} - {{ appointment.endTime }}</span>\n          </div>\n        </div>\n        <div class=\"col-md-6\">\n          <div class=\"summary-item\">\n            <i class=\"fas fa-user-md text-muted me-2\"></i>\n            <span class=\"fw-medium\">{{ appointment.doctor.fullName }}</span>\n          </div>\n        </div>\n        <div class=\"col-md-6\">\n          <div class=\"summary-item\">\n            <i class=\"fas fa-tag text-muted me-2\"></i>\n            <span class=\"badge\" \n                  [class]=\"'badge-' + appointment.status.toLowerCase()\">\n              {{ appointment.status }}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Appointment Type -->\n      <div class=\"summary-item mt-2\">\n        <i class=\"fas\" \n           [class]=\"appointment.type === 'VIDEO_CALL' ? 'fa-video' : 'fa-user-friends'\"\n           class=\"text-muted me-2\"></i>\n        <span class=\"fw-medium\">\n          {{ appointment.type === 'VIDEO_CALL' ? 'Video Call' : 'In-Person' }}\n        </span>\n      </div>\n\n      <!-- Reason for Visit -->\n      <div *ngIf=\"appointment.reasonForVisit\" class=\"summary-item mt-2\">\n        <i class=\"fas fa-notes-medical text-muted me-2\"></i>\n        <span class=\"text-muted\">{{ appointment.reasonForVisit }}</span>\n      </div>\n\n      <!-- Time Context -->\n      <div class=\"time-context mt-3\">\n        <div *ngIf=\"isBeforeAppointment()\" class=\"alert alert-info py-2 mb-0\">\n          <i class=\"fas fa-info-circle me-2\"></i>\n          <strong>Upcoming:</strong> {{ getTimeUntilAppointment() }}\n        </div>\n        <div *ngIf=\"isAfterAppointment()\" class=\"alert alert-success py-2 mb-0\">\n          <i class=\"fas fa-check-circle me-2\"></i>\n          <strong>Completed:</strong> Follow-up discussion\n        </div>\n      </div>\n    </div>\n\n    <!-- Quick Actions -->\n    <div class=\"quick-actions mt-3\">\n      <div class=\"btn-group w-100\" role=\"group\">\n        <button \n          *ngIf=\"appointment.type === 'VIDEO_CALL' && appointment.meetingLink && isBeforeAppointment()\"\n          type=\"button\" \n          class=\"btn btn-sm btn-primary\"\n          (click)=\"openMeetingLink(appointment.meetingLink)\">\n          <i class=\"fas fa-video me-1\"></i>\n          Join Call\n        </button>\n        <button \n          type=\"button\" \n          class=\"btn btn-sm btn-outline-secondary\"\n          (click)=\"navigateToAppointment()\">\n          <i class=\"fas fa-eye me-1\"></i>\n          View Details\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;ICEEA,EAAA,CAAAC,cAAA,aAA6C;IAEXD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEjDH,EAAA,CAAAC,cAAA,cAAmB;IAAAD,EAAA,CAAAE,MAAA,qCAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAI1DH,EAAA,CAAAC,cAAA,aAAoD;IAClDD,EAAA,CAAAI,SAAA,YAAgD;IAChDJ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IA8DIR,EAAA,CAAAC,cAAA,cAAkE;IAChED,EAAA,CAAAI,SAAA,YAAoD;IACpDJ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAvCH,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAC,cAAA,CAAgC;;;;;IAKzDZ,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAI,SAAA,YAAuC;IACvCJ,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAC7B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADuBH,EAAA,CAAAK,SAAA,GAC7B;IAD6BL,EAAA,CAAAM,kBAAA,MAAAO,MAAA,CAAAC,uBAAA,QAC7B;;;;;IACAd,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAI,SAAA,YAAwC;IACxCJ,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,6BAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAONH,EAAA,CAAAC,cAAA,iBAIqD;IAAnDD,EAAA,CAAAe,UAAA,mBAAAC,mFAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAF,MAAA,CAAAG,eAAA,CAAAH,MAAA,CAAAR,WAAA,CAAAY,WAAA,CAAwC;IAAA,EAAC;IAClDvB,EAAA,CAAAI,SAAA,YAAiC;IACjCJ,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAvFfH,EAAA,CAAAC,cAAA,cAA2E;IAIrED,EAAA,CAAAI,SAAA,QAA4C;IAC5CJ,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEvDH,EAAA,CAAAC,cAAA,iBAIwC;IADtCD,EAAA,CAAAe,UAAA,mBAAAS,yEAAA;MAAAxB,EAAA,CAAAiB,aAAA,CAAAQ,IAAA;MAAA,MAAAC,OAAA,GAAA1B,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAK,OAAA,CAAAC,qBAAA,EAAuB;IAAA,EAAC;IAEjC3B,EAAA,CAAAI,SAAA,YAAwC;IAC1CJ,EAAA,CAAAG,YAAA,EAAS;IAIXH,EAAA,CAAAC,cAAA,cAAiC;IAIzBD,EAAA,CAAAI,SAAA,aAAmD;IACnDJ,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG7EH,EAAA,CAAAC,cAAA,eAAsB;IAElBD,EAAA,CAAAI,SAAA,aAA4C;IAC5CJ,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAuD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG1FH,EAAA,CAAAC,cAAA,eAAsB;IAElBD,EAAA,CAAAI,SAAA,aAA8C;IAC9CJ,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGpEH,EAAA,CAAAC,cAAA,eAAsB;IAElBD,EAAA,CAAAI,SAAA,aAA0C;IAC1CJ,EAAA,CAAAC,cAAA,gBAC4D;IAC1DD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAMbH,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAAI,SAAA,aAE+B;IAC/BJ,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAA4B,UAAA,KAAAC,uDAAA,kBAGM;IAGN7B,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAA4B,UAAA,KAAAE,uDAAA,kBAGM;IACN9B,EAAA,CAAA4B,UAAA,KAAAG,uDAAA,kBAGM;IACR/B,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,eAAgC;IAE5BD,EAAA,CAAA4B,UAAA,KAAAI,0DAAA,qBAOS;IACThC,EAAA,CAAAC,cAAA,kBAGoC;IAAlCD,EAAA,CAAAe,UAAA,mBAAAkB,0EAAA;MAAAjC,EAAA,CAAAiB,aAAA,CAAAQ,IAAA;MAAA,MAAAS,OAAA,GAAAlC,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAa,OAAA,CAAAP,qBAAA,EAAuB;IAAA,EAAC;IACjC3B,EAAA,CAAAI,SAAA,aAA+B;IAC/BJ,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IA1FNH,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAmC,UAAA,CAAAC,MAAA,CAAAC,cAAA,aAAoC;IACdrC,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAS,iBAAA,CAAA2B,MAAA,CAAAE,eAAA,GAAuB;IAiBpBtC,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAAuC,WAAA,SAAAH,MAAA,CAAAzB,WAAA,CAAA6B,IAAA,gBAA0C;IAM1CxC,EAAA,CAAAK,SAAA,GAAuD;IAAvDL,EAAA,CAAAyC,kBAAA,KAAAL,MAAA,CAAAzB,WAAA,CAAA+B,SAAA,SAAAN,MAAA,CAAAzB,WAAA,CAAAgC,OAAA,KAAuD;IAMvD3C,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAS,iBAAA,CAAA2B,MAAA,CAAAzB,WAAA,CAAAiC,MAAA,CAAAC,QAAA,CAAiC;IAOnD7C,EAAA,CAAAK,SAAA,GAAqD;IAArDL,EAAA,CAAAmC,UAAA,YAAAC,MAAA,CAAAzB,WAAA,CAAAmC,MAAA,CAAAC,WAAA,GAAqD;IACzD/C,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA8B,MAAA,CAAAzB,WAAA,CAAAmC,MAAA,MACF;IAQD9C,EAAA,CAAAK,SAAA,GAA4E;IAA5EL,EAAA,CAAAmC,UAAA,CAAAC,MAAA,CAAAzB,WAAA,CAAAqC,IAAA,mDAA4E;IAG7EhD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA8B,MAAA,CAAAzB,WAAA,CAAAqC,IAAA,oDACF;IAIIhD,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAiD,UAAA,SAAAb,MAAA,CAAAzB,WAAA,CAAAC,cAAA,CAAgC;IAO9BZ,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAiD,UAAA,SAAAb,MAAA,CAAAc,mBAAA,GAA2B;IAI3BlD,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAiD,UAAA,SAAAb,MAAA,CAAAe,kBAAA,GAA0B;IAW7BnD,EAAA,CAAAK,SAAA,GAA2F;IAA3FL,EAAA,CAAAiD,UAAA,SAAAb,MAAA,CAAAzB,WAAA,CAAAqC,IAAA,qBAAAZ,MAAA,CAAAzB,WAAA,CAAAY,WAAA,IAAAa,MAAA,CAAAc,mBAAA,GAA2F;;;;;IAjGtGlD,EAAA,CAAAC,cAAA,aAA4D;IAE1DD,EAAA,CAAA4B,UAAA,IAAAwB,gDAAA,iBAKM;IAGNpD,EAAA,CAAA4B,UAAA,IAAAyB,gDAAA,iBAGM;IAGNrD,EAAA,CAAA4B,UAAA,IAAA0B,gDAAA,mBAiGM;IACRtD,EAAA,CAAAG,YAAA,EAAM;;;;IAhHEH,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAiD,UAAA,SAAAM,MAAA,CAAAC,OAAA,CAAa;IAQbxD,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAiD,UAAA,SAAAM,MAAA,CAAA/C,KAAA,CAAW;IAMXR,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAiD,UAAA,SAAAM,MAAA,CAAA5C,WAAA,KAAA4C,MAAA,CAAAC,OAAA,KAAAD,MAAA,CAAA/C,KAAA,CAAuC;;;ADN/C,OAAM,MAAOiD,2BAA2B;EAQtCC,YACUC,kBAAsC,EACtCC,MAAc;IADd,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;IANhB,KAAAjD,WAAW,GAAuB,IAAI;IACtC,KAAA6C,OAAO,GAAG,KAAK;IACf,KAAAhD,KAAK,GAAkB,IAAI;EAKxB;EAEHqD,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,IAAI,CAACC,eAAe,EAAE;;EAE1B;EAEQA,eAAeA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACD,aAAa,EAAE;IAEzB,IAAI,CAACN,OAAO,GAAG,IAAI;IACnB,IAAI,CAAChD,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACmD,kBAAkB,CAACK,cAAc,CAAC,IAAI,CAACF,aAAa,CAAC,CAACG,SAAS,CAAC;MACnEC,IAAI,EAAGvD,WAAW,IAAI;QACpB,IAAI,CAACA,WAAW,GAAGA,WAAW;QAC9B,IAAI,CAAC6C,OAAO,GAAG,KAAK;MACtB,CAAC;MACDhD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,oCAAoC;QACjD,IAAI,CAACgD,OAAO,GAAG,KAAK;QACpBW,OAAO,CAAC3D,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD;KACD,CAAC;EACJ;EAEAmB,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAAChB,WAAW,EAAE;MACpB,IAAI,CAACiD,MAAM,CAACQ,QAAQ,CAAC,CAAC,eAAe,EAAE,IAAI,CAACzD,WAAW,CAAC0D,EAAE,CAAC,CAAC;;EAEhE;EAEA/B,eAAeA,CAAA;IACb,QAAQ,IAAI,CAACgC,QAAQ;MACnB,KAAK,iBAAiB;QACpB,OAAO,4BAA4B;MACrC,KAAK,kBAAkB;QACrB,OAAO,4BAA4B;MACrC,KAAK,QAAQ;QACX,OAAO,6BAA6B;MACtC,KAAK,sBAAsB;QACzB,OAAO,wBAAwB;MACjC,KAAK,WAAW;QACd,OAAO,gBAAgB;MACzB;QACE,OAAO,wBAAwB;;EAErC;EAEAjC,cAAcA,CAAA;IACZ,QAAQ,IAAI,CAACiC,QAAQ;MACnB,KAAK,iBAAiB;QACpB,OAAO,wBAAwB;MACjC,KAAK,kBAAkB;QACrB,OAAO,kCAAkC;MAC3C,KAAK,QAAQ;QACX,OAAO,yCAAyC;MAClD,KAAK,sBAAsB;QACzB,OAAO,2BAA2B;MACpC,KAAK,WAAW;QACd,OAAO,mCAAmC;MAC5C;QACE,OAAO,8BAA8B;;EAE3C;EAEApB,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACvC,WAAW,EAAE,OAAO,KAAK;IACnC,MAAM4D,mBAAmB,GAAG,IAAIC,IAAI,CAAC,GAAG,IAAI,CAAC7D,WAAW,CAAC6B,IAAI,IAAI,IAAI,CAAC7B,WAAW,CAAC+B,SAAS,EAAE,CAAC;IAC9F,OAAO6B,mBAAmB,GAAG,IAAIC,IAAI,EAAE;EACzC;EAEArB,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACxC,WAAW,EAAE,OAAO,KAAK;IACnC,MAAM4D,mBAAmB,GAAG,IAAIC,IAAI,CAAC,GAAG,IAAI,CAAC7D,WAAW,CAAC6B,IAAI,IAAI,IAAI,CAAC7B,WAAW,CAACgC,OAAO,EAAE,CAAC;IAC5F,OAAO4B,mBAAmB,GAAG,IAAIC,IAAI,EAAE;EACzC;EAEA1D,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACH,WAAW,EAAE,OAAO,EAAE;IAEhC,MAAM4D,mBAAmB,GAAG,IAAIC,IAAI,CAAC,GAAG,IAAI,CAAC7D,WAAW,CAAC6B,IAAI,IAAI,IAAI,CAAC7B,WAAW,CAAC+B,SAAS,EAAE,CAAC;IAC9F,MAAM+B,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAME,MAAM,GAAGH,mBAAmB,CAACI,OAAO,EAAE,GAAGF,GAAG,CAACE,OAAO,EAAE;IAE5D,IAAID,MAAM,IAAI,CAAC,EAAE,OAAO,wBAAwB;IAEhD,MAAME,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC3D,MAAMK,SAAS,GAAGF,IAAI,CAACC,KAAK,CAAEJ,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACjF,MAAMM,WAAW,GAAGH,IAAI,CAACC,KAAK,CAAEJ,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEzE,IAAIE,QAAQ,GAAG,CAAC,EAAE;MAChB,OAAO,GAAGA,QAAQ,OAAOA,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,YAAY;KAC7D,MAAM,IAAIG,SAAS,GAAG,CAAC,EAAE;MACxB,OAAO,GAAGA,SAAS,QAAQA,SAAS,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,YAAY;KAChE,MAAM;MACL,OAAO,GAAGC,WAAW,UAAUA,WAAW,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,YAAY;;EAEzE;EAEA1D,eAAeA,CAAC2D,IAAY;IAC1BC,MAAM,CAACC,IAAI,CAACF,IAAI,EAAE,QAAQ,CAAC;EAC7B;;;uBAlHWxB,2BAA2B,EAAAzD,EAAA,CAAAoF,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAtF,EAAA,CAAAoF,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAA3B/B,2BAA2B;MAAAgC,SAAA;MAAAC,MAAA;QAAA5B,aAAA;QAAAQ,QAAA;MAAA;MAAAqB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVxChG,EAAA,CAAA4B,UAAA,IAAAsE,0CAAA,iBAkHM;;;UAlHAlG,EAAA,CAAAiD,UAAA,SAAAgD,GAAA,CAAAnC,aAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}