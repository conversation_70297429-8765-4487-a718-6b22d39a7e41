{"ast": null, "code": "import { augmentWebsocket } from './augment-websocket.js';\nimport { BYTE } from './byte.js';\nimport { FrameImpl } from './frame-impl.js';\nimport { Parser } from './parser.js';\nimport { Ticker } from './ticker.js';\nimport { StompSocketState } from './types.js';\nimport { Versions } from './versions.js';\n/**\n * The STOMP protocol handler\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class StompHandler {\n  get connectedVersion() {\n    return this._connectedVersion;\n  }\n  get connected() {\n    return this._connected;\n  }\n  constructor(_client, _webSocket, config) {\n    this._client = _client;\n    this._webSocket = _webSocket;\n    this._connected = false;\n    this._serverFrameHandlers = {\n      // [CONNECTED Frame](https://stomp.github.com/stomp-specification-1.2.html#CONNECTED_Frame)\n      CONNECTED: frame => {\n        this.debug(`connected to server ${frame.headers.server}`);\n        this._connected = true;\n        this._connectedVersion = frame.headers.version;\n        // STOMP version 1.2 needs header values to be escaped\n        if (this._connectedVersion === Versions.V1_2) {\n          this._escapeHeaderValues = true;\n        }\n        this._setupHeartbeat(frame.headers);\n        this.onConnect(frame);\n      },\n      // [MESSAGE Frame](https://stomp.github.com/stomp-specification-1.2.html#MESSAGE)\n      MESSAGE: frame => {\n        // the callback is registered when the client calls\n        // `subscribe()`.\n        // If there is no registered subscription for the received message,\n        // the default `onUnhandledMessage` callback is used that the client can set.\n        // This is useful for subscriptions that are automatically created\n        // on the browser side (e.g. [RabbitMQ's temporary\n        // queues](https://www.rabbitmq.com/stomp.html)).\n        const subscription = frame.headers.subscription;\n        const onReceive = this._subscriptions[subscription] || this.onUnhandledMessage;\n        // bless the frame to be a Message\n        const message = frame;\n        const client = this;\n        const messageId = this._connectedVersion === Versions.V1_2 ? message.headers.ack : message.headers['message-id'];\n        // add `ack()` and `nack()` methods directly to the returned frame\n        // so that a simple call to `message.ack()` can acknowledge the message.\n        message.ack = (headers = {}) => {\n          return client.ack(messageId, subscription, headers);\n        };\n        message.nack = (headers = {}) => {\n          return client.nack(messageId, subscription, headers);\n        };\n        onReceive(message);\n      },\n      // [RECEIPT Frame](https://stomp.github.com/stomp-specification-1.2.html#RECEIPT)\n      RECEIPT: frame => {\n        const callback = this._receiptWatchers[frame.headers['receipt-id']];\n        if (callback) {\n          callback(frame);\n          // Server will acknowledge only once, remove the callback\n          delete this._receiptWatchers[frame.headers['receipt-id']];\n        } else {\n          this.onUnhandledReceipt(frame);\n        }\n      },\n      // [ERROR Frame](https://stomp.github.com/stomp-specification-1.2.html#ERROR)\n      ERROR: frame => {\n        this.onStompError(frame);\n      }\n    };\n    // used to index subscribers\n    this._counter = 0;\n    // subscription callbacks indexed by subscriber's ID\n    this._subscriptions = {};\n    // receipt-watchers indexed by receipts-ids\n    this._receiptWatchers = {};\n    this._partialData = '';\n    this._escapeHeaderValues = false;\n    this._lastServerActivityTS = Date.now();\n    this.debug = config.debug;\n    this.stompVersions = config.stompVersions;\n    this.connectHeaders = config.connectHeaders;\n    this.disconnectHeaders = config.disconnectHeaders;\n    this.heartbeatIncoming = config.heartbeatIncoming;\n    this.heartbeatOutgoing = config.heartbeatOutgoing;\n    this.splitLargeFrames = config.splitLargeFrames;\n    this.maxWebSocketChunkSize = config.maxWebSocketChunkSize;\n    this.forceBinaryWSFrames = config.forceBinaryWSFrames;\n    this.logRawCommunication = config.logRawCommunication;\n    this.appendMissingNULLonIncoming = config.appendMissingNULLonIncoming;\n    this.discardWebsocketOnCommFailure = config.discardWebsocketOnCommFailure;\n    this.onConnect = config.onConnect;\n    this.onDisconnect = config.onDisconnect;\n    this.onStompError = config.onStompError;\n    this.onWebSocketClose = config.onWebSocketClose;\n    this.onWebSocketError = config.onWebSocketError;\n    this.onUnhandledMessage = config.onUnhandledMessage;\n    this.onUnhandledReceipt = config.onUnhandledReceipt;\n    this.onUnhandledFrame = config.onUnhandledFrame;\n  }\n  start() {\n    const parser = new Parser(\n    // On Frame\n    rawFrame => {\n      const frame = FrameImpl.fromRawFrame(rawFrame, this._escapeHeaderValues);\n      // if this.logRawCommunication is set, the rawChunk is logged at this._webSocket.onmessage\n      if (!this.logRawCommunication) {\n        this.debug(`<<< ${frame}`);\n      }\n      const serverFrameHandler = this._serverFrameHandlers[frame.command] || this.onUnhandledFrame;\n      serverFrameHandler(frame);\n    },\n    // On Incoming Ping\n    () => {\n      this.debug('<<< PONG');\n    });\n    this._webSocket.onmessage = evt => {\n      this.debug('Received data');\n      this._lastServerActivityTS = Date.now();\n      if (this.logRawCommunication) {\n        const rawChunkAsString = evt.data instanceof ArrayBuffer ? new TextDecoder().decode(evt.data) : evt.data;\n        this.debug(`<<< ${rawChunkAsString}`);\n      }\n      parser.parseChunk(evt.data, this.appendMissingNULLonIncoming);\n    };\n    this._webSocket.onclose = closeEvent => {\n      this.debug(`Connection closed to ${this._webSocket.url}`);\n      this._cleanUp();\n      this.onWebSocketClose(closeEvent);\n    };\n    this._webSocket.onerror = errorEvent => {\n      this.onWebSocketError(errorEvent);\n    };\n    this._webSocket.onopen = () => {\n      // Clone before updating\n      const connectHeaders = Object.assign({}, this.connectHeaders);\n      this.debug('Web Socket Opened...');\n      connectHeaders['accept-version'] = this.stompVersions.supportedVersions();\n      connectHeaders['heart-beat'] = [this.heartbeatOutgoing, this.heartbeatIncoming].join(',');\n      this._transmit({\n        command: 'CONNECT',\n        headers: connectHeaders\n      });\n    };\n  }\n  _setupHeartbeat(headers) {\n    if (headers.version !== Versions.V1_1 && headers.version !== Versions.V1_2) {\n      return;\n    }\n    // It is valid for the server to not send this header\n    // https://stomp.github.io/stomp-specification-1.2.html#Heart-beating\n    if (!headers['heart-beat']) {\n      return;\n    }\n    // heart-beat header received from the server looks like:\n    //\n    //     heart-beat: sx, sy\n    const [serverOutgoing, serverIncoming] = headers['heart-beat'].split(',').map(v => parseInt(v, 10));\n    if (this.heartbeatOutgoing !== 0 && serverIncoming !== 0) {\n      const ttl = Math.max(this.heartbeatOutgoing, serverIncoming);\n      this.debug(`send PING every ${ttl}ms`);\n      this._pinger = new Ticker(ttl, this._client.heartbeatStrategy, this.debug);\n      this._pinger.start(() => {\n        if (this._webSocket.readyState === StompSocketState.OPEN) {\n          this._webSocket.send(BYTE.LF);\n          this.debug('>>> PING');\n        }\n      });\n    }\n    if (this.heartbeatIncoming !== 0 && serverOutgoing !== 0) {\n      const ttl = Math.max(this.heartbeatIncoming, serverOutgoing);\n      this.debug(`check PONG every ${ttl}ms`);\n      this._ponger = setInterval(() => {\n        const delta = Date.now() - this._lastServerActivityTS;\n        // We wait twice the TTL to be flexible on window's setInterval calls\n        if (delta > ttl * 2) {\n          this.debug(`did not receive server activity for the last ${delta}ms`);\n          this._closeOrDiscardWebsocket();\n        }\n      }, ttl);\n    }\n  }\n  _closeOrDiscardWebsocket() {\n    if (this.discardWebsocketOnCommFailure) {\n      this.debug('Discarding websocket, the underlying socket may linger for a while');\n      this.discardWebsocket();\n    } else {\n      this.debug('Issuing close on the websocket');\n      this._closeWebsocket();\n    }\n  }\n  forceDisconnect() {\n    if (this._webSocket) {\n      if (this._webSocket.readyState === StompSocketState.CONNECTING || this._webSocket.readyState === StompSocketState.OPEN) {\n        this._closeOrDiscardWebsocket();\n      }\n    }\n  }\n  _closeWebsocket() {\n    this._webSocket.onmessage = () => {}; // ignore messages\n    this._webSocket.close();\n  }\n  discardWebsocket() {\n    if (typeof this._webSocket.terminate !== 'function') {\n      augmentWebsocket(this._webSocket, msg => this.debug(msg));\n    }\n    // @ts-ignore - this method will be there at this stage\n    this._webSocket.terminate();\n  }\n  _transmit(params) {\n    const {\n      command,\n      headers,\n      body,\n      binaryBody,\n      skipContentLengthHeader\n    } = params;\n    const frame = new FrameImpl({\n      command,\n      headers,\n      body,\n      binaryBody,\n      escapeHeaderValues: this._escapeHeaderValues,\n      skipContentLengthHeader\n    });\n    let rawChunk = frame.serialize();\n    if (this.logRawCommunication) {\n      this.debug(`>>> ${rawChunk}`);\n    } else {\n      this.debug(`>>> ${frame}`);\n    }\n    if (this.forceBinaryWSFrames && typeof rawChunk === 'string') {\n      rawChunk = new TextEncoder().encode(rawChunk);\n    }\n    if (typeof rawChunk !== 'string' || !this.splitLargeFrames) {\n      this._webSocket.send(rawChunk);\n    } else {\n      let out = rawChunk;\n      while (out.length > 0) {\n        const chunk = out.substring(0, this.maxWebSocketChunkSize);\n        out = out.substring(this.maxWebSocketChunkSize);\n        this._webSocket.send(chunk);\n        this.debug(`chunk sent = ${chunk.length}, remaining = ${out.length}`);\n      }\n    }\n  }\n  dispose() {\n    if (this.connected) {\n      try {\n        // clone before updating\n        const disconnectHeaders = Object.assign({}, this.disconnectHeaders);\n        if (!disconnectHeaders.receipt) {\n          disconnectHeaders.receipt = `close-${this._counter++}`;\n        }\n        this.watchForReceipt(disconnectHeaders.receipt, frame => {\n          this._closeWebsocket();\n          this._cleanUp();\n          this.onDisconnect(frame);\n        });\n        this._transmit({\n          command: 'DISCONNECT',\n          headers: disconnectHeaders\n        });\n      } catch (error) {\n        this.debug(`Ignoring error during disconnect ${error}`);\n      }\n    } else {\n      if (this._webSocket.readyState === StompSocketState.CONNECTING || this._webSocket.readyState === StompSocketState.OPEN) {\n        this._closeWebsocket();\n      }\n    }\n  }\n  _cleanUp() {\n    this._connected = false;\n    if (this._pinger) {\n      this._pinger.stop();\n      this._pinger = undefined;\n    }\n    if (this._ponger) {\n      clearInterval(this._ponger);\n      this._ponger = undefined;\n    }\n  }\n  publish(params) {\n    const {\n      destination,\n      headers,\n      body,\n      binaryBody,\n      skipContentLengthHeader\n    } = params;\n    const hdrs = Object.assign({\n      destination\n    }, headers);\n    this._transmit({\n      command: 'SEND',\n      headers: hdrs,\n      body,\n      binaryBody,\n      skipContentLengthHeader\n    });\n  }\n  watchForReceipt(receiptId, callback) {\n    this._receiptWatchers[receiptId] = callback;\n  }\n  subscribe(destination, callback, headers = {}) {\n    headers = Object.assign({}, headers);\n    if (!headers.id) {\n      headers.id = `sub-${this._counter++}`;\n    }\n    headers.destination = destination;\n    this._subscriptions[headers.id] = callback;\n    this._transmit({\n      command: 'SUBSCRIBE',\n      headers\n    });\n    const client = this;\n    return {\n      id: headers.id,\n      unsubscribe(hdrs) {\n        return client.unsubscribe(headers.id, hdrs);\n      }\n    };\n  }\n  unsubscribe(id, headers = {}) {\n    headers = Object.assign({}, headers);\n    delete this._subscriptions[id];\n    headers.id = id;\n    this._transmit({\n      command: 'UNSUBSCRIBE',\n      headers\n    });\n  }\n  begin(transactionId) {\n    const txId = transactionId || `tx-${this._counter++}`;\n    this._transmit({\n      command: 'BEGIN',\n      headers: {\n        transaction: txId\n      }\n    });\n    const client = this;\n    return {\n      id: txId,\n      commit() {\n        client.commit(txId);\n      },\n      abort() {\n        client.abort(txId);\n      }\n    };\n  }\n  commit(transactionId) {\n    this._transmit({\n      command: 'COMMIT',\n      headers: {\n        transaction: transactionId\n      }\n    });\n  }\n  abort(transactionId) {\n    this._transmit({\n      command: 'ABORT',\n      headers: {\n        transaction: transactionId\n      }\n    });\n  }\n  ack(messageId, subscriptionId, headers = {}) {\n    headers = Object.assign({}, headers);\n    if (this._connectedVersion === Versions.V1_2) {\n      headers.id = messageId;\n    } else {\n      headers['message-id'] = messageId;\n    }\n    headers.subscription = subscriptionId;\n    this._transmit({\n      command: 'ACK',\n      headers\n    });\n  }\n  nack(messageId, subscriptionId, headers = {}) {\n    headers = Object.assign({}, headers);\n    if (this._connectedVersion === Versions.V1_2) {\n      headers.id = messageId;\n    } else {\n      headers['message-id'] = messageId;\n    }\n    headers.subscription = subscriptionId;\n    return this._transmit({\n      command: 'NACK',\n      headers\n    });\n  }\n}", "map": {"version": 3, "names": ["augmentWebsocket", "BYTE", "FrameImpl", "<PERSON><PERSON><PERSON>", "Ticker", "StompSocketState", "Versions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connectedVersion", "_connectedVersion", "connected", "_connected", "constructor", "_client", "_webSocket", "config", "_serverFrameHandlers", "CONNECTED", "frame", "debug", "headers", "server", "version", "V1_2", "_escapeHeaderV<PERSON>ues", "_setupHeartbeat", "onConnect", "MESSAGE", "subscription", "onReceive", "_subscriptions", "onUnhandledMessage", "message", "client", "messageId", "ack", "nack", "RECEIPT", "callback", "_receiptWatchers", "onUnhandledReceipt", "ERROR", "onStompError", "_counter", "_partialData", "_lastServerActivityTS", "Date", "now", "stompV<PERSON><PERSON>", "connectHeaders", "disconnectHeaders", "heartbeatIncoming", "heartbeatOutgoing", "splitLargeFrames", "maxWebSocketChunkSize", "forceBinaryWSFrames", "logRawCommunication", "appendMissingNULLonIncoming", "discardWebsocketOnCommFailure", "onDisconnect", "onWebSocketClose", "onWebSocketError", "onUnhandledFrame", "start", "parser", "rawFrame", "fromRawFrame", "serverFrameHandler", "command", "onmessage", "evt", "rawChunkAsString", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TextDecoder", "decode", "parseChunk", "onclose", "closeEvent", "url", "_cleanUp", "onerror", "errorEvent", "onopen", "Object", "assign", "supportedVersions", "join", "_transmit", "V1_1", "serverOutgoing", "serverIncoming", "split", "map", "v", "parseInt", "ttl", "Math", "max", "_pinger", "heartbeatStrategy", "readyState", "OPEN", "send", "LF", "_ponger", "setInterval", "delta", "_closeOrDiscardWebsocket", "discardWeb<PERSON>cket", "_closeWebsocket", "forceDisconnect", "CONNECTING", "close", "terminate", "msg", "params", "body", "binaryBody", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapeHeader<PERSON><PERSON>ues", "rawChunk", "serialize", "TextEncoder", "encode", "out", "length", "chunk", "substring", "dispose", "receipt", "watchForReceipt", "error", "stop", "undefined", "clearInterval", "publish", "destination", "hdrs", "receiptId", "subscribe", "id", "unsubscribe", "begin", "transactionId", "txId", "transaction", "commit", "abort", "subscriptionId"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/@stomp/stompjs/esm6/stomp-handler.js"], "sourcesContent": ["import { augmentWebsocket } from './augment-websocket.js';\nimport { BYTE } from './byte.js';\nimport { FrameImpl } from './frame-impl.js';\nimport { Parser } from './parser.js';\nimport { Ticker } from './ticker.js';\nimport { StompSocketState, } from './types.js';\nimport { Versions } from './versions.js';\n/**\n * The STOMP protocol handler\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class StompHandler {\n    get connectedVersion() {\n        return this._connectedVersion;\n    }\n    get connected() {\n        return this._connected;\n    }\n    constructor(_client, _webSocket, config) {\n        this._client = _client;\n        this._webSocket = _webSocket;\n        this._connected = false;\n        this._serverFrameHandlers = {\n            // [CONNECTED Frame](https://stomp.github.com/stomp-specification-1.2.html#CONNECTED_Frame)\n            CONNECTED: frame => {\n                this.debug(`connected to server ${frame.headers.server}`);\n                this._connected = true;\n                this._connectedVersion = frame.headers.version;\n                // STOMP version 1.2 needs header values to be escaped\n                if (this._connectedVersion === Versions.V1_2) {\n                    this._escapeHeaderValues = true;\n                }\n                this._setupHeartbeat(frame.headers);\n                this.onConnect(frame);\n            },\n            // [MESSAGE Frame](https://stomp.github.com/stomp-specification-1.2.html#MESSAGE)\n            MESSAGE: frame => {\n                // the callback is registered when the client calls\n                // `subscribe()`.\n                // If there is no registered subscription for the received message,\n                // the default `onUnhandledMessage` callback is used that the client can set.\n                // This is useful for subscriptions that are automatically created\n                // on the browser side (e.g. [RabbitMQ's temporary\n                // queues](https://www.rabbitmq.com/stomp.html)).\n                const subscription = frame.headers.subscription;\n                const onReceive = this._subscriptions[subscription] || this.onUnhandledMessage;\n                // bless the frame to be a Message\n                const message = frame;\n                const client = this;\n                const messageId = this._connectedVersion === Versions.V1_2\n                    ? message.headers.ack\n                    : message.headers['message-id'];\n                // add `ack()` and `nack()` methods directly to the returned frame\n                // so that a simple call to `message.ack()` can acknowledge the message.\n                message.ack = (headers = {}) => {\n                    return client.ack(messageId, subscription, headers);\n                };\n                message.nack = (headers = {}) => {\n                    return client.nack(messageId, subscription, headers);\n                };\n                onReceive(message);\n            },\n            // [RECEIPT Frame](https://stomp.github.com/stomp-specification-1.2.html#RECEIPT)\n            RECEIPT: frame => {\n                const callback = this._receiptWatchers[frame.headers['receipt-id']];\n                if (callback) {\n                    callback(frame);\n                    // Server will acknowledge only once, remove the callback\n                    delete this._receiptWatchers[frame.headers['receipt-id']];\n                }\n                else {\n                    this.onUnhandledReceipt(frame);\n                }\n            },\n            // [ERROR Frame](https://stomp.github.com/stomp-specification-1.2.html#ERROR)\n            ERROR: frame => {\n                this.onStompError(frame);\n            },\n        };\n        // used to index subscribers\n        this._counter = 0;\n        // subscription callbacks indexed by subscriber's ID\n        this._subscriptions = {};\n        // receipt-watchers indexed by receipts-ids\n        this._receiptWatchers = {};\n        this._partialData = '';\n        this._escapeHeaderValues = false;\n        this._lastServerActivityTS = Date.now();\n        this.debug = config.debug;\n        this.stompVersions = config.stompVersions;\n        this.connectHeaders = config.connectHeaders;\n        this.disconnectHeaders = config.disconnectHeaders;\n        this.heartbeatIncoming = config.heartbeatIncoming;\n        this.heartbeatOutgoing = config.heartbeatOutgoing;\n        this.splitLargeFrames = config.splitLargeFrames;\n        this.maxWebSocketChunkSize = config.maxWebSocketChunkSize;\n        this.forceBinaryWSFrames = config.forceBinaryWSFrames;\n        this.logRawCommunication = config.logRawCommunication;\n        this.appendMissingNULLonIncoming = config.appendMissingNULLonIncoming;\n        this.discardWebsocketOnCommFailure = config.discardWebsocketOnCommFailure;\n        this.onConnect = config.onConnect;\n        this.onDisconnect = config.onDisconnect;\n        this.onStompError = config.onStompError;\n        this.onWebSocketClose = config.onWebSocketClose;\n        this.onWebSocketError = config.onWebSocketError;\n        this.onUnhandledMessage = config.onUnhandledMessage;\n        this.onUnhandledReceipt = config.onUnhandledReceipt;\n        this.onUnhandledFrame = config.onUnhandledFrame;\n    }\n    start() {\n        const parser = new Parser(\n        // On Frame\n        rawFrame => {\n            const frame = FrameImpl.fromRawFrame(rawFrame, this._escapeHeaderValues);\n            // if this.logRawCommunication is set, the rawChunk is logged at this._webSocket.onmessage\n            if (!this.logRawCommunication) {\n                this.debug(`<<< ${frame}`);\n            }\n            const serverFrameHandler = this._serverFrameHandlers[frame.command] || this.onUnhandledFrame;\n            serverFrameHandler(frame);\n        }, \n        // On Incoming Ping\n        () => {\n            this.debug('<<< PONG');\n        });\n        this._webSocket.onmessage = (evt) => {\n            this.debug('Received data');\n            this._lastServerActivityTS = Date.now();\n            if (this.logRawCommunication) {\n                const rawChunkAsString = evt.data instanceof ArrayBuffer\n                    ? new TextDecoder().decode(evt.data)\n                    : evt.data;\n                this.debug(`<<< ${rawChunkAsString}`);\n            }\n            parser.parseChunk(evt.data, this.appendMissingNULLonIncoming);\n        };\n        this._webSocket.onclose = (closeEvent) => {\n            this.debug(`Connection closed to ${this._webSocket.url}`);\n            this._cleanUp();\n            this.onWebSocketClose(closeEvent);\n        };\n        this._webSocket.onerror = (errorEvent) => {\n            this.onWebSocketError(errorEvent);\n        };\n        this._webSocket.onopen = () => {\n            // Clone before updating\n            const connectHeaders = Object.assign({}, this.connectHeaders);\n            this.debug('Web Socket Opened...');\n            connectHeaders['accept-version'] = this.stompVersions.supportedVersions();\n            connectHeaders['heart-beat'] = [\n                this.heartbeatOutgoing,\n                this.heartbeatIncoming,\n            ].join(',');\n            this._transmit({ command: 'CONNECT', headers: connectHeaders });\n        };\n    }\n    _setupHeartbeat(headers) {\n        if (headers.version !== Versions.V1_1 &&\n            headers.version !== Versions.V1_2) {\n            return;\n        }\n        // It is valid for the server to not send this header\n        // https://stomp.github.io/stomp-specification-1.2.html#Heart-beating\n        if (!headers['heart-beat']) {\n            return;\n        }\n        // heart-beat header received from the server looks like:\n        //\n        //     heart-beat: sx, sy\n        const [serverOutgoing, serverIncoming] = headers['heart-beat']\n            .split(',')\n            .map((v) => parseInt(v, 10));\n        if (this.heartbeatOutgoing !== 0 && serverIncoming !== 0) {\n            const ttl = Math.max(this.heartbeatOutgoing, serverIncoming);\n            this.debug(`send PING every ${ttl}ms`);\n            this._pinger = new Ticker(ttl, this._client.heartbeatStrategy, this.debug);\n            this._pinger.start(() => {\n                if (this._webSocket.readyState === StompSocketState.OPEN) {\n                    this._webSocket.send(BYTE.LF);\n                    this.debug('>>> PING');\n                }\n            });\n        }\n        if (this.heartbeatIncoming !== 0 && serverOutgoing !== 0) {\n            const ttl = Math.max(this.heartbeatIncoming, serverOutgoing);\n            this.debug(`check PONG every ${ttl}ms`);\n            this._ponger = setInterval(() => {\n                const delta = Date.now() - this._lastServerActivityTS;\n                // We wait twice the TTL to be flexible on window's setInterval calls\n                if (delta > ttl * 2) {\n                    this.debug(`did not receive server activity for the last ${delta}ms`);\n                    this._closeOrDiscardWebsocket();\n                }\n            }, ttl);\n        }\n    }\n    _closeOrDiscardWebsocket() {\n        if (this.discardWebsocketOnCommFailure) {\n            this.debug('Discarding websocket, the underlying socket may linger for a while');\n            this.discardWebsocket();\n        }\n        else {\n            this.debug('Issuing close on the websocket');\n            this._closeWebsocket();\n        }\n    }\n    forceDisconnect() {\n        if (this._webSocket) {\n            if (this._webSocket.readyState === StompSocketState.CONNECTING ||\n                this._webSocket.readyState === StompSocketState.OPEN) {\n                this._closeOrDiscardWebsocket();\n            }\n        }\n    }\n    _closeWebsocket() {\n        this._webSocket.onmessage = () => { }; // ignore messages\n        this._webSocket.close();\n    }\n    discardWebsocket() {\n        if (typeof this._webSocket.terminate !== 'function') {\n            augmentWebsocket(this._webSocket, (msg) => this.debug(msg));\n        }\n        // @ts-ignore - this method will be there at this stage\n        this._webSocket.terminate();\n    }\n    _transmit(params) {\n        const { command, headers, body, binaryBody, skipContentLengthHeader } = params;\n        const frame = new FrameImpl({\n            command,\n            headers,\n            body,\n            binaryBody,\n            escapeHeaderValues: this._escapeHeaderValues,\n            skipContentLengthHeader,\n        });\n        let rawChunk = frame.serialize();\n        if (this.logRawCommunication) {\n            this.debug(`>>> ${rawChunk}`);\n        }\n        else {\n            this.debug(`>>> ${frame}`);\n        }\n        if (this.forceBinaryWSFrames && typeof rawChunk === 'string') {\n            rawChunk = new TextEncoder().encode(rawChunk);\n        }\n        if (typeof rawChunk !== 'string' || !this.splitLargeFrames) {\n            this._webSocket.send(rawChunk);\n        }\n        else {\n            let out = rawChunk;\n            while (out.length > 0) {\n                const chunk = out.substring(0, this.maxWebSocketChunkSize);\n                out = out.substring(this.maxWebSocketChunkSize);\n                this._webSocket.send(chunk);\n                this.debug(`chunk sent = ${chunk.length}, remaining = ${out.length}`);\n            }\n        }\n    }\n    dispose() {\n        if (this.connected) {\n            try {\n                // clone before updating\n                const disconnectHeaders = Object.assign({}, this.disconnectHeaders);\n                if (!disconnectHeaders.receipt) {\n                    disconnectHeaders.receipt = `close-${this._counter++}`;\n                }\n                this.watchForReceipt(disconnectHeaders.receipt, frame => {\n                    this._closeWebsocket();\n                    this._cleanUp();\n                    this.onDisconnect(frame);\n                });\n                this._transmit({ command: 'DISCONNECT', headers: disconnectHeaders });\n            }\n            catch (error) {\n                this.debug(`Ignoring error during disconnect ${error}`);\n            }\n        }\n        else {\n            if (this._webSocket.readyState === StompSocketState.CONNECTING ||\n                this._webSocket.readyState === StompSocketState.OPEN) {\n                this._closeWebsocket();\n            }\n        }\n    }\n    _cleanUp() {\n        this._connected = false;\n        if (this._pinger) {\n            this._pinger.stop();\n            this._pinger = undefined;\n        }\n        if (this._ponger) {\n            clearInterval(this._ponger);\n            this._ponger = undefined;\n        }\n    }\n    publish(params) {\n        const { destination, headers, body, binaryBody, skipContentLengthHeader } = params;\n        const hdrs = Object.assign({ destination }, headers);\n        this._transmit({\n            command: 'SEND',\n            headers: hdrs,\n            body,\n            binaryBody,\n            skipContentLengthHeader,\n        });\n    }\n    watchForReceipt(receiptId, callback) {\n        this._receiptWatchers[receiptId] = callback;\n    }\n    subscribe(destination, callback, headers = {}) {\n        headers = Object.assign({}, headers);\n        if (!headers.id) {\n            headers.id = `sub-${this._counter++}`;\n        }\n        headers.destination = destination;\n        this._subscriptions[headers.id] = callback;\n        this._transmit({ command: 'SUBSCRIBE', headers });\n        const client = this;\n        return {\n            id: headers.id,\n            unsubscribe(hdrs) {\n                return client.unsubscribe(headers.id, hdrs);\n            },\n        };\n    }\n    unsubscribe(id, headers = {}) {\n        headers = Object.assign({}, headers);\n        delete this._subscriptions[id];\n        headers.id = id;\n        this._transmit({ command: 'UNSUBSCRIBE', headers });\n    }\n    begin(transactionId) {\n        const txId = transactionId || `tx-${this._counter++}`;\n        this._transmit({\n            command: 'BEGIN',\n            headers: {\n                transaction: txId,\n            },\n        });\n        const client = this;\n        return {\n            id: txId,\n            commit() {\n                client.commit(txId);\n            },\n            abort() {\n                client.abort(txId);\n            },\n        };\n    }\n    commit(transactionId) {\n        this._transmit({\n            command: 'COMMIT',\n            headers: {\n                transaction: transactionId,\n            },\n        });\n    }\n    abort(transactionId) {\n        this._transmit({\n            command: 'ABORT',\n            headers: {\n                transaction: transactionId,\n            },\n        });\n    }\n    ack(messageId, subscriptionId, headers = {}) {\n        headers = Object.assign({}, headers);\n        if (this._connectedVersion === Versions.V1_2) {\n            headers.id = messageId;\n        }\n        else {\n            headers['message-id'] = messageId;\n        }\n        headers.subscription = subscriptionId;\n        this._transmit({ command: 'ACK', headers });\n    }\n    nack(messageId, subscriptionId, headers = {}) {\n        headers = Object.assign({}, headers);\n        if (this._connectedVersion === Versions.V1_2) {\n            headers.id = messageId;\n        }\n        else {\n            headers['message-id'] = messageId;\n        }\n        headers.subscription = subscriptionId;\n        return this._transmit({ command: 'NACK', headers });\n    }\n}\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,gBAAgB,QAAS,YAAY;AAC9C,SAASC,QAAQ,QAAQ,eAAe;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,CAAC;EACtB,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACC,iBAAiB;EACjC;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACAC,WAAWA,CAACC,OAAO,EAAEC,UAAU,EAAEC,MAAM,EAAE;IACrC,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACH,UAAU,GAAG,KAAK;IACvB,IAAI,CAACK,oBAAoB,GAAG;MACxB;MACAC,SAAS,EAAEC,KAAK,IAAI;QAChB,IAAI,CAACC,KAAK,CAAE,uBAAsBD,KAAK,CAACE,OAAO,CAACC,MAAO,EAAC,CAAC;QACzD,IAAI,CAACV,UAAU,GAAG,IAAI;QACtB,IAAI,CAACF,iBAAiB,GAAGS,KAAK,CAACE,OAAO,CAACE,OAAO;QAC9C;QACA,IAAI,IAAI,CAACb,iBAAiB,KAAKH,QAAQ,CAACiB,IAAI,EAAE;UAC1C,IAAI,CAACC,mBAAmB,GAAG,IAAI;QACnC;QACA,IAAI,CAACC,eAAe,CAACP,KAAK,CAACE,OAAO,CAAC;QACnC,IAAI,CAACM,SAAS,CAACR,KAAK,CAAC;MACzB,CAAC;MACD;MACAS,OAAO,EAAET,KAAK,IAAI;QACd;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAMU,YAAY,GAAGV,KAAK,CAACE,OAAO,CAACQ,YAAY;QAC/C,MAAMC,SAAS,GAAG,IAAI,CAACC,cAAc,CAACF,YAAY,CAAC,IAAI,IAAI,CAACG,kBAAkB;QAC9E;QACA,MAAMC,OAAO,GAAGd,KAAK;QACrB,MAAMe,MAAM,GAAG,IAAI;QACnB,MAAMC,SAAS,GAAG,IAAI,CAACzB,iBAAiB,KAAKH,QAAQ,CAACiB,IAAI,GACpDS,OAAO,CAACZ,OAAO,CAACe,GAAG,GACnBH,OAAO,CAACZ,OAAO,CAAC,YAAY,CAAC;QACnC;QACA;QACAY,OAAO,CAACG,GAAG,GAAG,CAACf,OAAO,GAAG,CAAC,CAAC,KAAK;UAC5B,OAAOa,MAAM,CAACE,GAAG,CAACD,SAAS,EAAEN,YAAY,EAAER,OAAO,CAAC;QACvD,CAAC;QACDY,OAAO,CAACI,IAAI,GAAG,CAAChB,OAAO,GAAG,CAAC,CAAC,KAAK;UAC7B,OAAOa,MAAM,CAACG,IAAI,CAACF,SAAS,EAAEN,YAAY,EAAER,OAAO,CAAC;QACxD,CAAC;QACDS,SAAS,CAACG,OAAO,CAAC;MACtB,CAAC;MACD;MACAK,OAAO,EAAEnB,KAAK,IAAI;QACd,MAAMoB,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAACrB,KAAK,CAACE,OAAO,CAAC,YAAY,CAAC,CAAC;QACnE,IAAIkB,QAAQ,EAAE;UACVA,QAAQ,CAACpB,KAAK,CAAC;UACf;UACA,OAAO,IAAI,CAACqB,gBAAgB,CAACrB,KAAK,CAACE,OAAO,CAAC,YAAY,CAAC,CAAC;QAC7D,CAAC,MACI;UACD,IAAI,CAACoB,kBAAkB,CAACtB,KAAK,CAAC;QAClC;MACJ,CAAC;MACD;MACAuB,KAAK,EAAEvB,KAAK,IAAI;QACZ,IAAI,CAACwB,YAAY,CAACxB,KAAK,CAAC;MAC5B;IACJ,CAAC;IACD;IACA,IAAI,CAACyB,QAAQ,GAAG,CAAC;IACjB;IACA,IAAI,CAACb,cAAc,GAAG,CAAC,CAAC;IACxB;IACA,IAAI,CAACS,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACK,YAAY,GAAG,EAAE;IACtB,IAAI,CAACpB,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACqB,qBAAqB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IACvC,IAAI,CAAC5B,KAAK,GAAGJ,MAAM,CAACI,KAAK;IACzB,IAAI,CAAC6B,aAAa,GAAGjC,MAAM,CAACiC,aAAa;IACzC,IAAI,CAACC,cAAc,GAAGlC,MAAM,CAACkC,cAAc;IAC3C,IAAI,CAACC,iBAAiB,GAAGnC,MAAM,CAACmC,iBAAiB;IACjD,IAAI,CAACC,iBAAiB,GAAGpC,MAAM,CAACoC,iBAAiB;IACjD,IAAI,CAACC,iBAAiB,GAAGrC,MAAM,CAACqC,iBAAiB;IACjD,IAAI,CAACC,gBAAgB,GAAGtC,MAAM,CAACsC,gBAAgB;IAC/C,IAAI,CAACC,qBAAqB,GAAGvC,MAAM,CAACuC,qBAAqB;IACzD,IAAI,CAACC,mBAAmB,GAAGxC,MAAM,CAACwC,mBAAmB;IACrD,IAAI,CAACC,mBAAmB,GAAGzC,MAAM,CAACyC,mBAAmB;IACrD,IAAI,CAACC,2BAA2B,GAAG1C,MAAM,CAAC0C,2BAA2B;IACrE,IAAI,CAACC,6BAA6B,GAAG3C,MAAM,CAAC2C,6BAA6B;IACzE,IAAI,CAAChC,SAAS,GAAGX,MAAM,CAACW,SAAS;IACjC,IAAI,CAACiC,YAAY,GAAG5C,MAAM,CAAC4C,YAAY;IACvC,IAAI,CAACjB,YAAY,GAAG3B,MAAM,CAAC2B,YAAY;IACvC,IAAI,CAACkB,gBAAgB,GAAG7C,MAAM,CAAC6C,gBAAgB;IAC/C,IAAI,CAACC,gBAAgB,GAAG9C,MAAM,CAAC8C,gBAAgB;IAC/C,IAAI,CAAC9B,kBAAkB,GAAGhB,MAAM,CAACgB,kBAAkB;IACnD,IAAI,CAACS,kBAAkB,GAAGzB,MAAM,CAACyB,kBAAkB;IACnD,IAAI,CAACsB,gBAAgB,GAAG/C,MAAM,CAAC+C,gBAAgB;EACnD;EACAC,KAAKA,CAAA,EAAG;IACJ,MAAMC,MAAM,GAAG,IAAI7D,MAAM;IACzB;IACA8D,QAAQ,IAAI;MACR,MAAM/C,KAAK,GAAGhB,SAAS,CAACgE,YAAY,CAACD,QAAQ,EAAE,IAAI,CAACzC,mBAAmB,CAAC;MACxE;MACA,IAAI,CAAC,IAAI,CAACgC,mBAAmB,EAAE;QAC3B,IAAI,CAACrC,KAAK,CAAE,OAAMD,KAAM,EAAC,CAAC;MAC9B;MACA,MAAMiD,kBAAkB,GAAG,IAAI,CAACnD,oBAAoB,CAACE,KAAK,CAACkD,OAAO,CAAC,IAAI,IAAI,CAACN,gBAAgB;MAC5FK,kBAAkB,CAACjD,KAAK,CAAC;IAC7B,CAAC;IACD;IACA,MAAM;MACF,IAAI,CAACC,KAAK,CAAC,UAAU,CAAC;IAC1B,CAAC,CAAC;IACF,IAAI,CAACL,UAAU,CAACuD,SAAS,GAAIC,GAAG,IAAK;MACjC,IAAI,CAACnD,KAAK,CAAC,eAAe,CAAC;MAC3B,IAAI,CAAC0B,qBAAqB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MACvC,IAAI,IAAI,CAACS,mBAAmB,EAAE;QAC1B,MAAMe,gBAAgB,GAAGD,GAAG,CAACE,IAAI,YAAYC,WAAW,GAClD,IAAIC,WAAW,CAAC,CAAC,CAACC,MAAM,CAACL,GAAG,CAACE,IAAI,CAAC,GAClCF,GAAG,CAACE,IAAI;QACd,IAAI,CAACrD,KAAK,CAAE,OAAMoD,gBAAiB,EAAC,CAAC;MACzC;MACAP,MAAM,CAACY,UAAU,CAACN,GAAG,CAACE,IAAI,EAAE,IAAI,CAACf,2BAA2B,CAAC;IACjE,CAAC;IACD,IAAI,CAAC3C,UAAU,CAAC+D,OAAO,GAAIC,UAAU,IAAK;MACtC,IAAI,CAAC3D,KAAK,CAAE,wBAAuB,IAAI,CAACL,UAAU,CAACiE,GAAI,EAAC,CAAC;MACzD,IAAI,CAACC,QAAQ,CAAC,CAAC;MACf,IAAI,CAACpB,gBAAgB,CAACkB,UAAU,CAAC;IACrC,CAAC;IACD,IAAI,CAAChE,UAAU,CAACmE,OAAO,GAAIC,UAAU,IAAK;MACtC,IAAI,CAACrB,gBAAgB,CAACqB,UAAU,CAAC;IACrC,CAAC;IACD,IAAI,CAACpE,UAAU,CAACqE,MAAM,GAAG,MAAM;MAC3B;MACA,MAAMlC,cAAc,GAAGmC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACpC,cAAc,CAAC;MAC7D,IAAI,CAAC9B,KAAK,CAAC,sBAAsB,CAAC;MAClC8B,cAAc,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAACD,aAAa,CAACsC,iBAAiB,CAAC,CAAC;MACzErC,cAAc,CAAC,YAAY,CAAC,GAAG,CAC3B,IAAI,CAACG,iBAAiB,EACtB,IAAI,CAACD,iBAAiB,CACzB,CAACoC,IAAI,CAAC,GAAG,CAAC;MACX,IAAI,CAACC,SAAS,CAAC;QAAEpB,OAAO,EAAE,SAAS;QAAEhD,OAAO,EAAE6B;MAAe,CAAC,CAAC;IACnE,CAAC;EACL;EACAxB,eAAeA,CAACL,OAAO,EAAE;IACrB,IAAIA,OAAO,CAACE,OAAO,KAAKhB,QAAQ,CAACmF,IAAI,IACjCrE,OAAO,CAACE,OAAO,KAAKhB,QAAQ,CAACiB,IAAI,EAAE;MACnC;IACJ;IACA;IACA;IACA,IAAI,CAACH,OAAO,CAAC,YAAY,CAAC,EAAE;MACxB;IACJ;IACA;IACA;IACA;IACA,MAAM,CAACsE,cAAc,EAAEC,cAAc,CAAC,GAAGvE,OAAO,CAAC,YAAY,CAAC,CACzDwE,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAEC,CAAC,IAAKC,QAAQ,CAACD,CAAC,EAAE,EAAE,CAAC,CAAC;IAChC,IAAI,IAAI,CAAC1C,iBAAiB,KAAK,CAAC,IAAIuC,cAAc,KAAK,CAAC,EAAE;MACtD,MAAMK,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC9C,iBAAiB,EAAEuC,cAAc,CAAC;MAC5D,IAAI,CAACxE,KAAK,CAAE,mBAAkB6E,GAAI,IAAG,CAAC;MACtC,IAAI,CAACG,OAAO,GAAG,IAAI/F,MAAM,CAAC4F,GAAG,EAAE,IAAI,CAACnF,OAAO,CAACuF,iBAAiB,EAAE,IAAI,CAACjF,KAAK,CAAC;MAC1E,IAAI,CAACgF,OAAO,CAACpC,KAAK,CAAC,MAAM;QACrB,IAAI,IAAI,CAACjD,UAAU,CAACuF,UAAU,KAAKhG,gBAAgB,CAACiG,IAAI,EAAE;UACtD,IAAI,CAACxF,UAAU,CAACyF,IAAI,CAACtG,IAAI,CAACuG,EAAE,CAAC;UAC7B,IAAI,CAACrF,KAAK,CAAC,UAAU,CAAC;QAC1B;MACJ,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAACgC,iBAAiB,KAAK,CAAC,IAAIuC,cAAc,KAAK,CAAC,EAAE;MACtD,MAAMM,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/C,iBAAiB,EAAEuC,cAAc,CAAC;MAC5D,IAAI,CAACvE,KAAK,CAAE,oBAAmB6E,GAAI,IAAG,CAAC;MACvC,IAAI,CAACS,OAAO,GAAGC,WAAW,CAAC,MAAM;QAC7B,MAAMC,KAAK,GAAG7D,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACF,qBAAqB;QACrD;QACA,IAAI8D,KAAK,GAAGX,GAAG,GAAG,CAAC,EAAE;UACjB,IAAI,CAAC7E,KAAK,CAAE,gDAA+CwF,KAAM,IAAG,CAAC;UACrE,IAAI,CAACC,wBAAwB,CAAC,CAAC;QACnC;MACJ,CAAC,EAAEZ,GAAG,CAAC;IACX;EACJ;EACAY,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAAClD,6BAA6B,EAAE;MACpC,IAAI,CAACvC,KAAK,CAAC,oEAAoE,CAAC;MAChF,IAAI,CAAC0F,gBAAgB,CAAC,CAAC;IAC3B,CAAC,MACI;MACD,IAAI,CAAC1F,KAAK,CAAC,gCAAgC,CAAC;MAC5C,IAAI,CAAC2F,eAAe,CAAC,CAAC;IAC1B;EACJ;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACjG,UAAU,EAAE;MACjB,IAAI,IAAI,CAACA,UAAU,CAACuF,UAAU,KAAKhG,gBAAgB,CAAC2G,UAAU,IAC1D,IAAI,CAAClG,UAAU,CAACuF,UAAU,KAAKhG,gBAAgB,CAACiG,IAAI,EAAE;QACtD,IAAI,CAACM,wBAAwB,CAAC,CAAC;MACnC;IACJ;EACJ;EACAE,eAAeA,CAAA,EAAG;IACd,IAAI,CAAChG,UAAU,CAACuD,SAAS,GAAG,MAAM,CAAE,CAAC,CAAC,CAAC;IACvC,IAAI,CAACvD,UAAU,CAACmG,KAAK,CAAC,CAAC;EAC3B;EACAJ,gBAAgBA,CAAA,EAAG;IACf,IAAI,OAAO,IAAI,CAAC/F,UAAU,CAACoG,SAAS,KAAK,UAAU,EAAE;MACjDlH,gBAAgB,CAAC,IAAI,CAACc,UAAU,EAAGqG,GAAG,IAAK,IAAI,CAAChG,KAAK,CAACgG,GAAG,CAAC,CAAC;IAC/D;IACA;IACA,IAAI,CAACrG,UAAU,CAACoG,SAAS,CAAC,CAAC;EAC/B;EACA1B,SAASA,CAAC4B,MAAM,EAAE;IACd,MAAM;MAAEhD,OAAO;MAAEhD,OAAO;MAAEiG,IAAI;MAAEC,UAAU;MAAEC;IAAwB,CAAC,GAAGH,MAAM;IAC9E,MAAMlG,KAAK,GAAG,IAAIhB,SAAS,CAAC;MACxBkE,OAAO;MACPhD,OAAO;MACPiG,IAAI;MACJC,UAAU;MACVE,kBAAkB,EAAE,IAAI,CAAChG,mBAAmB;MAC5C+F;IACJ,CAAC,CAAC;IACF,IAAIE,QAAQ,GAAGvG,KAAK,CAACwG,SAAS,CAAC,CAAC;IAChC,IAAI,IAAI,CAAClE,mBAAmB,EAAE;MAC1B,IAAI,CAACrC,KAAK,CAAE,OAAMsG,QAAS,EAAC,CAAC;IACjC,CAAC,MACI;MACD,IAAI,CAACtG,KAAK,CAAE,OAAMD,KAAM,EAAC,CAAC;IAC9B;IACA,IAAI,IAAI,CAACqC,mBAAmB,IAAI,OAAOkE,QAAQ,KAAK,QAAQ,EAAE;MAC1DA,QAAQ,GAAG,IAAIE,WAAW,CAAC,CAAC,CAACC,MAAM,CAACH,QAAQ,CAAC;IACjD;IACA,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAI,CAAC,IAAI,CAACpE,gBAAgB,EAAE;MACxD,IAAI,CAACvC,UAAU,CAACyF,IAAI,CAACkB,QAAQ,CAAC;IAClC,CAAC,MACI;MACD,IAAII,GAAG,GAAGJ,QAAQ;MAClB,OAAOI,GAAG,CAACC,MAAM,GAAG,CAAC,EAAE;QACnB,MAAMC,KAAK,GAAGF,GAAG,CAACG,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC1E,qBAAqB,CAAC;QAC1DuE,GAAG,GAAGA,GAAG,CAACG,SAAS,CAAC,IAAI,CAAC1E,qBAAqB,CAAC;QAC/C,IAAI,CAACxC,UAAU,CAACyF,IAAI,CAACwB,KAAK,CAAC;QAC3B,IAAI,CAAC5G,KAAK,CAAE,gBAAe4G,KAAK,CAACD,MAAO,iBAAgBD,GAAG,CAACC,MAAO,EAAC,CAAC;MACzE;IACJ;EACJ;EACAG,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACvH,SAAS,EAAE;MAChB,IAAI;QACA;QACA,MAAMwC,iBAAiB,GAAGkC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACnC,iBAAiB,CAAC;QACnE,IAAI,CAACA,iBAAiB,CAACgF,OAAO,EAAE;UAC5BhF,iBAAiB,CAACgF,OAAO,GAAI,SAAQ,IAAI,CAACvF,QAAQ,EAAG,EAAC;QAC1D;QACA,IAAI,CAACwF,eAAe,CAACjF,iBAAiB,CAACgF,OAAO,EAAEhH,KAAK,IAAI;UACrD,IAAI,CAAC4F,eAAe,CAAC,CAAC;UACtB,IAAI,CAAC9B,QAAQ,CAAC,CAAC;UACf,IAAI,CAACrB,YAAY,CAACzC,KAAK,CAAC;QAC5B,CAAC,CAAC;QACF,IAAI,CAACsE,SAAS,CAAC;UAAEpB,OAAO,EAAE,YAAY;UAAEhD,OAAO,EAAE8B;QAAkB,CAAC,CAAC;MACzE,CAAC,CACD,OAAOkF,KAAK,EAAE;QACV,IAAI,CAACjH,KAAK,CAAE,oCAAmCiH,KAAM,EAAC,CAAC;MAC3D;IACJ,CAAC,MACI;MACD,IAAI,IAAI,CAACtH,UAAU,CAACuF,UAAU,KAAKhG,gBAAgB,CAAC2G,UAAU,IAC1D,IAAI,CAAClG,UAAU,CAACuF,UAAU,KAAKhG,gBAAgB,CAACiG,IAAI,EAAE;QACtD,IAAI,CAACQ,eAAe,CAAC,CAAC;MAC1B;IACJ;EACJ;EACA9B,QAAQA,CAAA,EAAG;IACP,IAAI,CAACrE,UAAU,GAAG,KAAK;IACvB,IAAI,IAAI,CAACwF,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACkC,IAAI,CAAC,CAAC;MACnB,IAAI,CAAClC,OAAO,GAAGmC,SAAS;IAC5B;IACA,IAAI,IAAI,CAAC7B,OAAO,EAAE;MACd8B,aAAa,CAAC,IAAI,CAAC9B,OAAO,CAAC;MAC3B,IAAI,CAACA,OAAO,GAAG6B,SAAS;IAC5B;EACJ;EACAE,OAAOA,CAACpB,MAAM,EAAE;IACZ,MAAM;MAAEqB,WAAW;MAAErH,OAAO;MAAEiG,IAAI;MAAEC,UAAU;MAAEC;IAAwB,CAAC,GAAGH,MAAM;IAClF,MAAMsB,IAAI,GAAGtD,MAAM,CAACC,MAAM,CAAC;MAAEoD;IAAY,CAAC,EAAErH,OAAO,CAAC;IACpD,IAAI,CAACoE,SAAS,CAAC;MACXpB,OAAO,EAAE,MAAM;MACfhD,OAAO,EAAEsH,IAAI;MACbrB,IAAI;MACJC,UAAU;MACVC;IACJ,CAAC,CAAC;EACN;EACAY,eAAeA,CAACQ,SAAS,EAAErG,QAAQ,EAAE;IACjC,IAAI,CAACC,gBAAgB,CAACoG,SAAS,CAAC,GAAGrG,QAAQ;EAC/C;EACAsG,SAASA,CAACH,WAAW,EAAEnG,QAAQ,EAAElB,OAAO,GAAG,CAAC,CAAC,EAAE;IAC3CA,OAAO,GAAGgE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjE,OAAO,CAAC;IACpC,IAAI,CAACA,OAAO,CAACyH,EAAE,EAAE;MACbzH,OAAO,CAACyH,EAAE,GAAI,OAAM,IAAI,CAAClG,QAAQ,EAAG,EAAC;IACzC;IACAvB,OAAO,CAACqH,WAAW,GAAGA,WAAW;IACjC,IAAI,CAAC3G,cAAc,CAACV,OAAO,CAACyH,EAAE,CAAC,GAAGvG,QAAQ;IAC1C,IAAI,CAACkD,SAAS,CAAC;MAAEpB,OAAO,EAAE,WAAW;MAAEhD;IAAQ,CAAC,CAAC;IACjD,MAAMa,MAAM,GAAG,IAAI;IACnB,OAAO;MACH4G,EAAE,EAAEzH,OAAO,CAACyH,EAAE;MACdC,WAAWA,CAACJ,IAAI,EAAE;QACd,OAAOzG,MAAM,CAAC6G,WAAW,CAAC1H,OAAO,CAACyH,EAAE,EAAEH,IAAI,CAAC;MAC/C;IACJ,CAAC;EACL;EACAI,WAAWA,CAACD,EAAE,EAAEzH,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1BA,OAAO,GAAGgE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjE,OAAO,CAAC;IACpC,OAAO,IAAI,CAACU,cAAc,CAAC+G,EAAE,CAAC;IAC9BzH,OAAO,CAACyH,EAAE,GAAGA,EAAE;IACf,IAAI,CAACrD,SAAS,CAAC;MAAEpB,OAAO,EAAE,aAAa;MAAEhD;IAAQ,CAAC,CAAC;EACvD;EACA2H,KAAKA,CAACC,aAAa,EAAE;IACjB,MAAMC,IAAI,GAAGD,aAAa,IAAK,MAAK,IAAI,CAACrG,QAAQ,EAAG,EAAC;IACrD,IAAI,CAAC6C,SAAS,CAAC;MACXpB,OAAO,EAAE,OAAO;MAChBhD,OAAO,EAAE;QACL8H,WAAW,EAAED;MACjB;IACJ,CAAC,CAAC;IACF,MAAMhH,MAAM,GAAG,IAAI;IACnB,OAAO;MACH4G,EAAE,EAAEI,IAAI;MACRE,MAAMA,CAAA,EAAG;QACLlH,MAAM,CAACkH,MAAM,CAACF,IAAI,CAAC;MACvB,CAAC;MACDG,KAAKA,CAAA,EAAG;QACJnH,MAAM,CAACmH,KAAK,CAACH,IAAI,CAAC;MACtB;IACJ,CAAC;EACL;EACAE,MAAMA,CAACH,aAAa,EAAE;IAClB,IAAI,CAACxD,SAAS,CAAC;MACXpB,OAAO,EAAE,QAAQ;MACjBhD,OAAO,EAAE;QACL8H,WAAW,EAAEF;MACjB;IACJ,CAAC,CAAC;EACN;EACAI,KAAKA,CAACJ,aAAa,EAAE;IACjB,IAAI,CAACxD,SAAS,CAAC;MACXpB,OAAO,EAAE,OAAO;MAChBhD,OAAO,EAAE;QACL8H,WAAW,EAAEF;MACjB;IACJ,CAAC,CAAC;EACN;EACA7G,GAAGA,CAACD,SAAS,EAAEmH,cAAc,EAAEjI,OAAO,GAAG,CAAC,CAAC,EAAE;IACzCA,OAAO,GAAGgE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjE,OAAO,CAAC;IACpC,IAAI,IAAI,CAACX,iBAAiB,KAAKH,QAAQ,CAACiB,IAAI,EAAE;MAC1CH,OAAO,CAACyH,EAAE,GAAG3G,SAAS;IAC1B,CAAC,MACI;MACDd,OAAO,CAAC,YAAY,CAAC,GAAGc,SAAS;IACrC;IACAd,OAAO,CAACQ,YAAY,GAAGyH,cAAc;IACrC,IAAI,CAAC7D,SAAS,CAAC;MAAEpB,OAAO,EAAE,KAAK;MAAEhD;IAAQ,CAAC,CAAC;EAC/C;EACAgB,IAAIA,CAACF,SAAS,EAAEmH,cAAc,EAAEjI,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1CA,OAAO,GAAGgE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjE,OAAO,CAAC;IACpC,IAAI,IAAI,CAACX,iBAAiB,KAAKH,QAAQ,CAACiB,IAAI,EAAE;MAC1CH,OAAO,CAACyH,EAAE,GAAG3G,SAAS;IAC1B,CAAC,MACI;MACDd,OAAO,CAAC,YAAY,CAAC,GAAGc,SAAS;IACrC;IACAd,OAAO,CAACQ,YAAY,GAAGyH,cAAc;IACrC,OAAO,IAAI,CAAC7D,SAAS,CAAC;MAAEpB,OAAO,EAAE,MAAM;MAAEhD;IAAQ,CAAC,CAAC;EACvD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}