{"ast": null, "code": "'use strict';\n\nvar logObject = {};\n['log', 'debug', 'warn'].forEach(function (level) {\n  var levelExists;\n  try {\n    levelExists = global.console && global.console[level] && global.console[level].apply;\n  } catch (e) {\n    // do nothing\n  }\n  logObject[level] = levelExists ? function () {\n    return global.console[level].apply(global.console, arguments);\n  } : level === 'log' ? function () {} : logObject.log;\n});\nmodule.exports = logObject;", "map": {"version": 3, "names": ["logObject", "for<PERSON>ach", "level", "levelExists", "global", "console", "apply", "e", "arguments", "log", "module", "exports"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/sockjs-client/lib/utils/log.js"], "sourcesContent": ["'use strict';\n\nvar logObject = {};\n['log', 'debug', 'warn'].forEach(function (level) {\n  var levelExists;\n\n  try {\n    levelExists = global.console && global.console[level] && global.console[level].apply;\n  } catch(e) {\n    // do nothing\n  }\n\n  logObject[level] = levelExists ? function () {\n    return global.console[level].apply(global.console, arguments);\n  } : (level === 'log' ? function () {} : logObject.log);\n});\n\nmodule.exports = logObject;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,SAAS,GAAG,CAAC,CAAC;AAClB,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAACC,OAAO,CAAC,UAAUC,KAAK,EAAE;EAChD,IAAIC,WAAW;EAEf,IAAI;IACFA,WAAW,GAAGC,MAAM,CAACC,OAAO,IAAID,MAAM,CAACC,OAAO,CAACH,KAAK,CAAC,IAAIE,MAAM,CAACC,OAAO,CAACH,KAAK,CAAC,CAACI,KAAK;EACtF,CAAC,CAAC,OAAMC,CAAC,EAAE;IACT;EAAA;EAGFP,SAAS,CAACE,KAAK,CAAC,GAAGC,WAAW,GAAG,YAAY;IAC3C,OAAOC,MAAM,CAACC,OAAO,CAACH,KAAK,CAAC,CAACI,KAAK,CAACF,MAAM,CAACC,OAAO,EAAEG,SAAS,CAAC;EAC/D,CAAC,GAAIN,KAAK,KAAK,KAAK,GAAG,YAAY,CAAC,CAAC,GAAGF,SAAS,CAACS,GAAI;AACxD,CAAC,CAAC;AAEFC,MAAM,CAACC,OAAO,GAAGX,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}