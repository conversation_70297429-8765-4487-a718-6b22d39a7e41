{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../core/services/chat.service\";\nimport * as i3 from \"../../core/services/auth.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../message-item/message-item.component\";\nimport * as i6 from \"../appointment-context/appointment-context.component\";\nconst _c0 = [\"messagesContainer\"];\nfunction ChatWindowComponent_div_0_app_doctor_availability_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-doctor-availability\", 26);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵproperty(\"doctorId\", (tmp_0_0 = ctx_r2.getOtherParticipant()) == null ? null : tmp_0_0.id)(\"showDetails\", false);\n  }\n}\nfunction ChatWindowComponent_div_0_app_appointment_context_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-appointment-context\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"appointmentId\", ctx_r3.chat.relatedAppointment.id)(\"chatType\", ctx_r3.chat.type);\n  }\n}\nfunction ChatWindowComponent_div_0_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"span\", 30);\n    i0.ɵɵtext(3, \"Loading messages...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ChatWindowComponent_div_0_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"i\", 32);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No messages yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"small\");\n    i0.ɵɵtext(5, \"Start the conversation!\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatWindowComponent_div_0_div_18_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"span\", 39);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r13.formatMessageDate(message_r11.createdAt));\n  }\n}\nfunction ChatWindowComponent_div_0_div_18_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ChatWindowComponent_div_0_div_18_ng_container_1_div_1_Template, 3, 1, \"div\", 36);\n    i0.ɵɵelement(2, \"app-message-item\", 37);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r11 = ctx.$implicit;\n    const i_r12 = ctx.index;\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.shouldShowDateSeparator(i_r12));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"message\", message_r11)(\"isOwn\", message_r11.sender.id === (ctx_r9.currentUser == null ? null : ctx_r9.currentUser.id));\n  }\n}\nfunction ChatWindowComponent_div_0_div_18_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41);\n    i0.ɵɵelement(2, \"span\")(3, \"span\")(4, \"span\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"small\", 42);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    let tmp_0_0;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", (tmp_0_0 = ctx_r10.getOtherParticipant()) == null ? null : tmp_0_0.fullName, \" is typing...\");\n  }\n}\nfunction ChatWindowComponent_div_0_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtemplate(1, ChatWindowComponent_div_0_div_18_ng_container_1_Template, 3, 3, \"ng-container\", 34);\n    i0.ɵɵtemplate(2, ChatWindowComponent_div_0_div_18_div_2_Template, 7, 1, \"div\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.messages);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.isTyping());\n  }\n}\nfunction ChatWindowComponent_div_0_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"small\", 44);\n    i0.ɵɵelement(2, \"i\", 45);\n    i0.ɵɵtext(3, \" Connection lost. Trying to reconnect... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatWindowComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n    i0.ɵɵelement(3, \"img\", 5);\n    i0.ɵɵelementStart(4, \"div\", 6)(5, \"h6\", 7);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"small\", 8);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, ChatWindowComponent_div_0_app_doctor_availability_9_Template, 1, 2, \"app-doctor-availability\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 10)(11, \"span\", 11);\n    i0.ɵɵelement(12, \"i\", 12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(13, ChatWindowComponent_div_0_app_appointment_context_13_Template, 1, 2, \"app-appointment-context\", 13);\n    i0.ɵɵelementStart(14, \"div\", 14, 15);\n    i0.ɵɵtemplate(16, ChatWindowComponent_div_0_div_16_Template, 4, 0, \"div\", 16);\n    i0.ɵɵtemplate(17, ChatWindowComponent_div_0_div_17_Template, 6, 0, \"div\", 17);\n    i0.ɵɵtemplate(18, ChatWindowComponent_div_0_div_18_Template, 3, 2, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 19)(20, \"form\", 20);\n    i0.ɵɵlistener(\"ngSubmit\", function ChatWindowComponent_div_0_Template_form_ngSubmit_20_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.sendMessage());\n    });\n    i0.ɵɵelementStart(21, \"div\", 21)(22, \"input\", 22);\n    i0.ɵɵlistener(\"input\", function ChatWindowComponent_div_0_Template_input_input_22_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.onTyping());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 23);\n    i0.ɵɵelement(24, \"i\", 24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(25, ChatWindowComponent_div_0_div_25_Template, 4, 0, \"div\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ((tmp_0_0 = ctx_r0.getOtherParticipant()) == null ? null : tmp_0_0.avatar) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", (tmp_1_0 = ctx_r0.getOtherParticipant()) == null ? null : tmp_1_0.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((tmp_2_0 = ctx_r0.getOtherParticipant()) == null ? null : tmp_2_0.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ((tmp_3_0 = ctx_r0.getOtherParticipant()) == null ? null : tmp_3_0.role) === \"DOCTOR\" ? \"Dr. \" + ((tmp_3_0 = ctx_r0.getOtherParticipant()) == null ? null : tmp_3_0.specialization) : \"Patient\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r0.getOtherParticipant()) == null ? null : tmp_4_0.role) === \"DOCTOR\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"connected\", ctx_r0.connectionStatus)(\"disconnected\", !ctx_r0.connectionStatus);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"bi-wifi\", ctx_r0.connectionStatus)(\"bi-wifi-off\", !ctx_r0.connectionStatus);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.chat == null ? null : ctx_r0.chat.relatedAppointment);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading && ctx_r0.messages.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading && ctx_r0.messages.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.messageForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.connectionStatus);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.messageForm.valid || !ctx_r0.connectionStatus);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.connectionStatus);\n  }\n}\nfunction ChatWindowComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47);\n    i0.ɵɵelement(2, \"i\", 32);\n    i0.ɵɵelementStart(3, \"h5\");\n    i0.ɵɵtext(4, \"Select a conversation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Choose a conversation from the list to start messaging\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class ChatWindowComponent {\n  constructor(fb, chatService, authService) {\n    this.fb = fb;\n    this.chatService = chatService;\n    this.authService = authService;\n    this.chat = null;\n    this.messages = [];\n    this.loading = false;\n    this.typingUsers = new Set();\n    this.connectionStatus = false;\n    this.subscriptions = [];\n    this.shouldScrollToBottom = false;\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]]\n    });\n  }\n  ngOnInit() {\n    this.currentUser = this.authService.getCurrentUser();\n    this.subscribeToServices();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n  }\n  ngAfterViewChecked() {\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n  subscribeToServices() {\n    // Subscribe to connection status\n    const connectionSub = this.chatService.connectionStatus$.subscribe(status => {\n      this.connectionStatus = status;\n    });\n    this.subscriptions.push(connectionSub);\n    // Subscribe to new messages\n    const messagesSub = this.chatService.messages$.subscribe(message => {\n      if (this.chat && message.chatId === this.chat.id) {\n        this.messages.push(message);\n        this.shouldScrollToBottom = true;\n        // Mark as read if not from current user\n        if (message.sender.id !== this.currentUser?.id) {\n          this.chatService.markMessagesAsRead(this.chat.id).subscribe();\n        }\n      }\n    });\n    this.subscriptions.push(messagesSub);\n    // Subscribe to typing notifications\n    const typingSub = this.chatService.typing$.subscribe(notification => {\n      this.handleTypingNotification(notification);\n    });\n    this.subscriptions.push(typingSub);\n  }\n  loadChat(chat) {\n    this.chat = chat;\n    this.messages = [];\n    this.typingUsers.clear();\n    if (chat) {\n      this.loadMessages();\n      this.chatService.subscribeToChatMessages(chat.id);\n    }\n  }\n  loadMessages() {\n    if (!this.chat) return;\n    this.loading = true;\n    this.chatService.getChatMessages(this.chat.id).subscribe({\n      next: messages => {\n        this.messages = messages.reverse(); // Reverse to show oldest first\n        this.loading = false;\n        this.shouldScrollToBottom = true;\n      },\n      error: error => {\n        console.error('Failed to load messages:', error);\n        this.loading = false;\n      }\n    });\n  }\n  sendMessage() {\n    if (!this.messageForm.valid || !this.chat || !this.connectionStatus) {\n      return;\n    }\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) {\n      return;\n    }\n    try {\n      this.chatService.sendMessage(this.chat.id, content);\n      this.messageForm.reset();\n      this.stopTyping();\n    } catch (error) {\n      console.error('Failed to send message:', error);\n    }\n  }\n  onTyping() {\n    if (!this.chat) return;\n    this.chatService.sendTypingNotification(this.chat.id, true);\n    // Clear existing timeout\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    // Set timeout to stop typing after 3 seconds\n    this.typingTimeout = setTimeout(() => {\n      this.stopTyping();\n    }, 3000);\n  }\n  stopTyping() {\n    if (!this.chat) return;\n    this.chatService.sendTypingNotification(this.chat.id, false);\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n      this.typingTimeout = null;\n    }\n  }\n  handleTypingNotification(notification) {\n    if (notification.userId === this.currentUser?.id) {\n      return; // Ignore own typing notifications\n    }\n\n    if (notification.status === 'typing') {\n      this.typingUsers.add(notification.userId);\n    } else {\n      this.typingUsers.delete(notification.userId);\n    }\n  }\n  scrollToBottom() {\n    try {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    } catch (err) {\n      console.error('Error scrolling to bottom:', err);\n    }\n  }\n  getOtherParticipant() {\n    if (!this.chat) return null;\n    return this.currentUser?.role === 'PATIENT' ? this.chat.doctor : this.chat.patient;\n  }\n  isTyping() {\n    return this.typingUsers.size > 0;\n  }\n  formatMessageTime(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString([], {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  formatMessageDate(dateString) {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return 'Today';\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Yesterday';\n    } else {\n      return date.toLocaleDateString();\n    }\n  }\n  shouldShowDateSeparator(index) {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    const currentDate = new Date(currentMessage.createdAt).toDateString();\n    const previousDate = new Date(previousMessage.createdAt).toDateString();\n    return currentDate !== previousDate;\n  }\n  static {\n    this.ɵfac = function ChatWindowComponent_Factory(t) {\n      return new (t || ChatWindowComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ChatService), i0.ɵɵdirectiveInject(i3.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatWindowComponent,\n      selectors: [[\"app-chat-window\"]],\n      viewQuery: function ChatWindowComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n        }\n      },\n      inputs: {\n        chat: \"chat\"\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"chat-window\", 4, \"ngIf\"], [\"class\", \"chat-placeholder\", 4, \"ngIf\"], [1, \"chat-window\"], [1, \"chat-header\"], [1, \"participant-info\"], [1, \"participant-avatar\", 3, \"src\", \"alt\"], [1, \"participant-details\"], [1, \"mb-0\"], [1, \"text-muted\"], [\"size\", \"sm\", 3, \"doctorId\", \"showDetails\", 4, \"ngIf\"], [1, \"connection-status\"], [1, \"status-indicator\"], [1, \"bi\"], [3, \"appointmentId\", \"chatType\", 4, \"ngIf\"], [1, \"messages-container\"], [\"messagesContainer\", \"\"], [\"class\", \"text-center p-3\", 4, \"ngIf\"], [\"class\", \"text-center p-4 text-muted\", 4, \"ngIf\"], [\"class\", \"messages-list\", 4, \"ngIf\"], [1, \"message-input-container\"], [1, \"message-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"input-group\"], [\"type\", \"text\", \"formControlName\", \"content\", \"placeholder\", \"Type a message...\", \"autocomplete\", \"off\", 1, \"form-control\", 3, \"disabled\", \"input\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [1, \"bi\", \"bi-send\"], [\"class\", \"connection-warning\", 4, \"ngIf\"], [\"size\", \"sm\", 3, \"doctorId\", \"showDetails\"], [3, \"appointmentId\", \"chatType\"], [1, \"text-center\", \"p-3\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\"], [1, \"visually-hidden\"], [1, \"text-center\", \"p-4\", \"text-muted\"], [1, \"bi\", \"bi-chat-square-text\", \"fs-1\", \"mb-3\", \"d-block\"], [1, \"messages-list\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"typing-indicator\", 4, \"ngIf\"], [\"class\", \"date-separator\", 4, \"ngIf\"], [3, \"message\", \"isOwn\"], [1, \"date-separator\"], [1, \"date-label\"], [1, \"typing-indicator\"], [1, \"typing-dots\"], [1, \"text-muted\", \"ms-2\"], [1, \"connection-warning\"], [1, \"text-warning\"], [1, \"bi\", \"bi-exclamation-triangle\", \"me-1\"], [1, \"chat-placeholder\"], [1, \"text-center\", \"text-muted\"]],\n      template: function ChatWindowComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ChatWindowComponent_div_0_Template, 26, 21, \"div\", 0);\n          i0.ɵɵtemplate(1, ChatWindowComponent_div_1_Template, 7, 0, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.chat);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.chat);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.MessageItemComponent, i6.AppointmentContextComponent],\n      styles: [\".chat-window[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1rem;\\n  border-bottom: 1px solid #e9ecef;\\n  background-color: #f8f9fa;\\n}\\n.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .participant-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  margin-right: 0.75rem;\\n  border: 2px solid #e9ecef;\\n}\\n.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .participant-details[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n}\\n.chat-header[_ngcontent-%COMP%]   .connection-status[_ngcontent-%COMP%]   .status-indicator.connected[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.chat-header[_ngcontent-%COMP%]   .connection-status[_ngcontent-%COMP%]   .status-indicator.disconnected[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n\\n.messages-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 1rem;\\n  background-color: #f8f9fa;\\n}\\n\\n.messages-list[_ngcontent-%COMP%]   .date-separator[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 1rem 0;\\n}\\n.messages-list[_ngcontent-%COMP%]   .date-separator[_ngcontent-%COMP%]   .date-label[_ngcontent-%COMP%] {\\n  background-color: #e9ecef;\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 1rem;\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n}\\n.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin: 0.5rem 0;\\n}\\n.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #e9ecef;\\n  padding: 0.5rem 0.75rem;\\n  border-radius: 1rem;\\n}\\n.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  width: 6px;\\n  height: 6px;\\n  background-color: #6c757d;\\n  border-radius: 50%;\\n  margin: 0 2px;\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n}\\n.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: -0.32s;\\n}\\n.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: -0.16s;\\n}\\n\\n.message-input-container[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  border-top: 1px solid #e9ecef;\\n  background-color: white;\\n}\\n.message-input-container[_ngcontent-%COMP%]   .message-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border-right: none;\\n}\\n.message-input-container[_ngcontent-%COMP%]   .message-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  border-color: #80bdff;\\n}\\n.message-input-container[_ngcontent-%COMP%]   .message-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-left: none;\\n  padding: 0.5rem 1rem;\\n}\\n.message-input-container[_ngcontent-%COMP%]   .message-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n}\\n.message-input-container[_ngcontent-%COMP%]   .connection-warning[_ngcontent-%COMP%] {\\n  margin-top: 0.5rem;\\n  text-align: center;\\n}\\n\\n.chat-placeholder[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: #f8f9fa;\\n}\\n.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%] {\\n  max-width: 300px;\\n}\\n.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0%, 60%, 100% {\\n    transform: translateY(0);\\n  }\\n  30% {\\n    transform: translateY(-10px);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .chat-header[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .participant-avatar[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .messages-container[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .message-input-container[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelement", "ɵɵproperty", "tmp_0_0", "ctx_r2", "getOtherParticipant", "id", "ctx_r3", "chat", "relatedAppointment", "type", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r13", "formatMessageDate", "message_r11", "createdAt", "ɵɵelementContainerStart", "ɵɵtemplate", "ChatWindowComponent_div_0_div_18_ng_container_1_div_1_Template", "ɵɵelementContainerEnd", "ctx_r9", "shouldShowDateSeparator", "i_r12", "sender", "currentUser", "ɵɵtextInterpolate1", "ctx_r10", "fullName", "ChatWindowComponent_div_0_div_18_ng_container_1_Template", "ChatWindowComponent_div_0_div_18_div_2_Template", "ctx_r7", "messages", "isTyping", "ChatWindowComponent_div_0_app_doctor_availability_9_Template", "ChatWindowComponent_div_0_app_appointment_context_13_Template", "ChatWindowComponent_div_0_div_16_Template", "ChatWindowComponent_div_0_div_17_Template", "ChatWindowComponent_div_0_div_18_Template", "ɵɵlistener", "ChatWindowComponent_div_0_Template_form_ngSubmit_20_listener", "ɵɵrestoreView", "_r16", "ctx_r15", "ɵɵnextContext", "ɵɵresetView", "sendMessage", "ChatWindowComponent_div_0_Template_input_input_22_listener", "ctx_r17", "onTyping", "ChatWindowComponent_div_0_div_25_Template", "ctx_r0", "avatar", "ɵɵsanitizeUrl", "tmp_1_0", "tmp_2_0", "tmp_3_0", "role", "specialization", "tmp_4_0", "ɵɵclassProp", "connectionStatus", "loading", "length", "messageForm", "valid", "ChatWindowComponent", "constructor", "fb", "chatService", "authService", "typingUsers", "Set", "subscriptions", "shouldScrollToBottom", "group", "content", "required", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "getCurrentUser", "subscribeToServices", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "typingTimeout", "clearTimeout", "ngAfterViewChecked", "scrollToBottom", "connectionSub", "connectionStatus$", "subscribe", "status", "push", "messagesSub", "messages$", "message", "chatId", "markMessagesAsRead", "typingSub", "typing$", "notification", "handleTypingNotification", "loadChat", "clear", "loadMessages", "subscribeToChatMessages", "getChatMessages", "next", "reverse", "error", "console", "get", "value", "trim", "reset", "stopTyping", "sendTypingNotification", "setTimeout", "userId", "add", "delete", "messagesContainer", "element", "nativeElement", "scrollTop", "scrollHeight", "err", "doctor", "patient", "size", "formatMessageTime", "dateString", "date", "Date", "toLocaleTimeString", "hour", "minute", "today", "yesterday", "setDate", "getDate", "toDateString", "toLocaleDateString", "index", "currentMessage", "previousMessage", "currentDate", "previousDate", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ChatService", "i3", "AuthService", "selectors", "viewQuery", "ChatWindowComponent_Query", "rf", "ctx", "ChatWindowComponent_div_0_Template", "ChatWindowComponent_div_1_Template"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/chat/chat-window/chat-window.component.ts", "/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/chat/chat-window/chat-window.component.html"], "sourcesContent": ["import { Component, Input, OnInit, OnDestroy, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { Chat, Message, TypingNotification } from '../../core/models/chat.model';\nimport { ChatService } from '../../core/services/chat.service';\nimport { AuthService } from '../../core/services/auth.service';\n\n@Component({\n  selector: 'app-chat-window',\n  templateUrl: './chat-window.component.html',\n  styleUrls: ['./chat-window.component.scss']\n})\nexport class ChatWindowComponent implements OnInit, OnDestroy, AfterViewChecked {\n  @Input() chat: Chat | null = null;\n  @ViewChild('messagesContainer') messagesContainer!: ElementRef;\n  \n  messages: Message[] = [];\n  messageForm: FormGroup;\n  currentUser: any;\n  loading = false;\n  typingUsers: Set<number> = new Set();\n  connectionStatus = false;\n  \n  private subscriptions: Subscription[] = [];\n  private typingTimeout: any;\n  private shouldScrollToBottom = false;\n\n  constructor(\n    private fb: FormBuilder,\n    private chatService: ChatService,\n    private authService: AuthService\n  ) {\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]]\n    });\n  }\n\n  ngOnInit(): void {\n    this.currentUser = this.authService.getCurrentUser();\n    this.subscribeToServices();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n  }\n\n  ngAfterViewChecked(): void {\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n\n  private subscribeToServices(): void {\n    // Subscribe to connection status\n    const connectionSub = this.chatService.connectionStatus$.subscribe(status => {\n      this.connectionStatus = status;\n    });\n    this.subscriptions.push(connectionSub);\n\n    // Subscribe to new messages\n    const messagesSub = this.chatService.messages$.subscribe(message => {\n      if (this.chat && message.chatId === this.chat.id) {\n        this.messages.push(message);\n        this.shouldScrollToBottom = true;\n        \n        // Mark as read if not from current user\n        if (message.sender.id !== this.currentUser?.id) {\n          this.chatService.markMessagesAsRead(this.chat.id).subscribe();\n        }\n      }\n    });\n    this.subscriptions.push(messagesSub);\n\n    // Subscribe to typing notifications\n    const typingSub = this.chatService.typing$.subscribe(notification => {\n      this.handleTypingNotification(notification);\n    });\n    this.subscriptions.push(typingSub);\n  }\n\n  loadChat(chat: Chat): void {\n    this.chat = chat;\n    this.messages = [];\n    this.typingUsers.clear();\n    \n    if (chat) {\n      this.loadMessages();\n      this.chatService.subscribeToChatMessages(chat.id);\n    }\n  }\n\n  private loadMessages(): void {\n    if (!this.chat) return;\n    \n    this.loading = true;\n    this.chatService.getChatMessages(this.chat.id).subscribe({\n      next: (messages) => {\n        this.messages = messages.reverse(); // Reverse to show oldest first\n        this.loading = false;\n        this.shouldScrollToBottom = true;\n      },\n      error: (error) => {\n        console.error('Failed to load messages:', error);\n        this.loading = false;\n      }\n    });\n  }\n\n  sendMessage(): void {\n    if (!this.messageForm.valid || !this.chat || !this.connectionStatus) {\n      return;\n    }\n\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) {\n      return;\n    }\n\n    try {\n      this.chatService.sendMessage(this.chat.id, content);\n      this.messageForm.reset();\n      this.stopTyping();\n    } catch (error) {\n      console.error('Failed to send message:', error);\n    }\n  }\n\n  onTyping(): void {\n    if (!this.chat) return;\n\n    this.chatService.sendTypingNotification(this.chat.id, true);\n    \n    // Clear existing timeout\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    \n    // Set timeout to stop typing after 3 seconds\n    this.typingTimeout = setTimeout(() => {\n      this.stopTyping();\n    }, 3000);\n  }\n\n  private stopTyping(): void {\n    if (!this.chat) return;\n    \n    this.chatService.sendTypingNotification(this.chat.id, false);\n    \n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n      this.typingTimeout = null;\n    }\n  }\n\n  private handleTypingNotification(notification: TypingNotification): void {\n    if (notification.userId === this.currentUser?.id) {\n      return; // Ignore own typing notifications\n    }\n\n    if (notification.status === 'typing') {\n      this.typingUsers.add(notification.userId);\n    } else {\n      this.typingUsers.delete(notification.userId);\n    }\n  }\n\n  private scrollToBottom(): void {\n    try {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    } catch (err) {\n      console.error('Error scrolling to bottom:', err);\n    }\n  }\n\n  getOtherParticipant(): any {\n    if (!this.chat) return null;\n    return this.currentUser?.role === 'PATIENT' ? this.chat.doctor : this.chat.patient;\n  }\n\n  isTyping(): boolean {\n    return this.typingUsers.size > 0;\n  }\n\n  formatMessageTime(dateString: string): string {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  }\n\n  formatMessageDate(dateString: string): string {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n\n    if (date.toDateString() === today.toDateString()) {\n      return 'Today';\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Yesterday';\n    } else {\n      return date.toLocaleDateString();\n    }\n  }\n\n  shouldShowDateSeparator(index: number): boolean {\n    if (index === 0) return true;\n    \n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    \n    const currentDate = new Date(currentMessage.createdAt).toDateString();\n    const previousDate = new Date(previousMessage.createdAt).toDateString();\n    \n    return currentDate !== previousDate;\n  }\n}\n", "<div class=\"chat-window\" *ngIf=\"chat\">\n  <!-- Cha<PERSON> -->\n  <div class=\"chat-header\">\n    <div class=\"participant-info\">\n      <img \n        [src]=\"getOtherParticipant()?.avatar || '/assets/images/default-avatar.png'\" \n        [alt]=\"getOtherParticipant()?.fullName\"\n        class=\"participant-avatar\">\n      <div class=\"participant-details\">\n        <h6 class=\"mb-0\">{{ getOtherParticipant()?.fullName }}</h6>\n        <small class=\"text-muted\">\n          {{ getOtherParticipant()?.role === 'DOCTOR' ? 'Dr. ' + getOtherParticipant()?.specialization : 'Patient' }}\n        </small>\n        <!-- Doctor Availability -->\n        <app-doctor-availability\n          *ngIf=\"getOtherParticipant()?.role === 'DOCTOR'\"\n          [doctorId]=\"getOtherParticipant()?.id\"\n          [showDetails]=\"false\"\n          size=\"sm\">\n        </app-doctor-availability>\n      </div>\n    </div>\n    \n    <div class=\"connection-status\">\n      <span \n        class=\"status-indicator\"\n        [class.connected]=\"connectionStatus\"\n        [class.disconnected]=\"!connectionStatus\">\n        <i class=\"bi\" [class.bi-wifi]=\"connectionStatus\" [class.bi-wifi-off]=\"!connectionStatus\"></i>\n      </span>\n    </div>\n  </div>\n\n  <!-- Appointment Context -->\n  <app-appointment-context\n    *ngIf=\"chat?.relatedAppointment\"\n    [appointmentId]=\"chat.relatedAppointment.id\"\n    [chatType]=\"chat.type\">\n  </app-appointment-context>\n\n  <!-- Messages Area -->\n  <div class=\"messages-container\" #messagesContainer>\n    <div *ngIf=\"loading\" class=\"text-center p-3\">\n      <div class=\"spinner-border spinner-border-sm\" role=\"status\">\n        <span class=\"visually-hidden\">Loading messages...</span>\n      </div>\n    </div>\n\n    <div *ngIf=\"!loading && messages.length === 0\" class=\"text-center p-4 text-muted\">\n      <i class=\"bi bi-chat-square-text fs-1 mb-3 d-block\"></i>\n      <p>No messages yet</p>\n      <small>Start the conversation!</small>\n    </div>\n\n    <div *ngIf=\"!loading && messages.length > 0\" class=\"messages-list\">\n      <ng-container *ngFor=\"let message of messages; let i = index\">\n        <!-- Date Separator -->\n        <div *ngIf=\"shouldShowDateSeparator(i)\" class=\"date-separator\">\n          <span class=\"date-label\">{{ formatMessageDate(message.createdAt) }}</span>\n        </div>\n\n        <!-- Message Item -->\n        <app-message-item \n          [message]=\"message\" \n          [isOwn]=\"message.sender.id === currentUser?.id\">\n        </app-message-item>\n      </ng-container>\n\n      <!-- Typing Indicator -->\n      <div *ngIf=\"isTyping()\" class=\"typing-indicator\">\n        <div class=\"typing-dots\">\n          <span></span>\n          <span></span>\n          <span></span>\n        </div>\n        <small class=\"text-muted ms-2\">{{ getOtherParticipant()?.fullName }} is typing...</small>\n      </div>\n    </div>\n  </div>\n\n  <!-- Message Input -->\n  <div class=\"message-input-container\">\n    <form [formGroup]=\"messageForm\" (ngSubmit)=\"sendMessage()\" class=\"message-form\">\n      <div class=\"input-group\">\n        <input \n          type=\"text\" \n          class=\"form-control\" \n          formControlName=\"content\"\n          placeholder=\"Type a message...\"\n          (input)=\"onTyping()\"\n          [disabled]=\"!connectionStatus\"\n          autocomplete=\"off\">\n        \n        <button \n          type=\"submit\" \n          class=\"btn btn-primary\"\n          [disabled]=\"!messageForm.valid || !connectionStatus\">\n          <i class=\"bi bi-send\"></i>\n        </button>\n      </div>\n    </form>\n    \n    <div *ngIf=\"!connectionStatus\" class=\"connection-warning\">\n      <small class=\"text-warning\">\n        <i class=\"bi bi-exclamation-triangle me-1\"></i>\n        Connection lost. Trying to reconnect...\n      </small>\n    </div>\n  </div>\n</div>\n\n<div class=\"chat-placeholder\" *ngIf=\"!chat\">\n  <div class=\"text-center text-muted\">\n    <i class=\"bi bi-chat-square-text fs-1 mb-3 d-block\"></i>\n    <h5>Select a conversation</h5>\n    <p>Choose a conversation from the list to start messaging</p>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;ICa3DC,EAAA,CAAAC,SAAA,kCAK0B;;;;;IAHxBD,EAAA,CAAAE,UAAA,cAAAC,OAAA,GAAAC,MAAA,CAAAC,mBAAA,qBAAAF,OAAA,CAAAG,EAAA,CAAsC;;;;;IAkB9CN,EAAA,CAAAC,SAAA,kCAI0B;;;;IAFxBD,EAAA,CAAAE,UAAA,kBAAAK,MAAA,CAAAC,IAAA,CAAAC,kBAAA,CAAAH,EAAA,CAA4C,aAAAC,MAAA,CAAAC,IAAA,CAAAE,IAAA;;;;;IAM5CV,EAAA,CAAAW,cAAA,cAA6C;IAEXX,EAAA,CAAAY,MAAA,0BAAmB;IAAAZ,EAAA,CAAAa,YAAA,EAAO;;;;;IAI5Db,EAAA,CAAAW,cAAA,cAAkF;IAChFX,EAAA,CAAAC,SAAA,YAAwD;IACxDD,EAAA,CAAAW,cAAA,QAAG;IAAAX,EAAA,CAAAY,MAAA,sBAAe;IAAAZ,EAAA,CAAAa,YAAA,EAAI;IACtBb,EAAA,CAAAW,cAAA,YAAO;IAAAX,EAAA,CAAAY,MAAA,8BAAuB;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;;;;;IAMpCb,EAAA,CAAAW,cAAA,cAA+D;IACpCX,EAAA,CAAAY,MAAA,GAA0C;IAAAZ,EAAA,CAAAa,YAAA,EAAO;;;;;IAAjDb,EAAA,CAAAc,SAAA,GAA0C;IAA1Cd,EAAA,CAAAe,iBAAA,CAAAC,OAAA,CAAAC,iBAAA,CAAAC,WAAA,CAAAC,SAAA,EAA0C;;;;;IAHvEnB,EAAA,CAAAoB,uBAAA,GAA8D;IAE5DpB,EAAA,CAAAqB,UAAA,IAAAC,8DAAA,kBAEM;IAGNtB,EAAA,CAAAC,SAAA,2BAGmB;IACrBD,EAAA,CAAAuB,qBAAA,EAAe;;;;;;IATPvB,EAAA,CAAAc,SAAA,GAAgC;IAAhCd,EAAA,CAAAE,UAAA,SAAAsB,MAAA,CAAAC,uBAAA,CAAAC,KAAA,EAAgC;IAMpC1B,EAAA,CAAAc,SAAA,GAAmB;IAAnBd,EAAA,CAAAE,UAAA,YAAAgB,WAAA,CAAmB,UAAAA,WAAA,CAAAS,MAAA,CAAArB,EAAA,MAAAkB,MAAA,CAAAI,WAAA,kBAAAJ,MAAA,CAAAI,WAAA,CAAAtB,EAAA;;;;;IAMvBN,EAAA,CAAAW,cAAA,cAAiD;IAE7CX,EAAA,CAAAC,SAAA,WAAa;IAGfD,EAAA,CAAAa,YAAA,EAAM;IACNb,EAAA,CAAAW,cAAA,gBAA+B;IAAAX,EAAA,CAAAY,MAAA,GAAkD;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;;;;;IAA1Db,EAAA,CAAAc,SAAA,GAAkD;IAAlDd,EAAA,CAAA6B,kBAAA,MAAA1B,OAAA,GAAA2B,OAAA,CAAAzB,mBAAA,qBAAAF,OAAA,CAAA4B,QAAA,kBAAkD;;;;;IArBrF/B,EAAA,CAAAW,cAAA,cAAmE;IACjEX,EAAA,CAAAqB,UAAA,IAAAW,wDAAA,2BAWe;IAGfhC,EAAA,CAAAqB,UAAA,IAAAY,+CAAA,kBAOM;IACRjC,EAAA,CAAAa,YAAA,EAAM;;;;IAtB8Bb,EAAA,CAAAc,SAAA,GAAa;IAAbd,EAAA,CAAAE,UAAA,YAAAgC,MAAA,CAAAC,QAAA,CAAa;IAczCnC,EAAA,CAAAc,SAAA,GAAgB;IAAhBd,EAAA,CAAAE,UAAA,SAAAgC,MAAA,CAAAE,QAAA,GAAgB;;;;;IAiCxBpC,EAAA,CAAAW,cAAA,cAA0D;IAEtDX,EAAA,CAAAC,SAAA,YAA+C;IAC/CD,EAAA,CAAAY,MAAA,gDACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;;;;;;IA1Gdb,EAAA,CAAAW,cAAA,aAAsC;IAIhCX,EAAA,CAAAC,SAAA,aAG6B;IAC7BD,EAAA,CAAAW,cAAA,aAAiC;IACdX,EAAA,CAAAY,MAAA,GAAqC;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAC3Db,EAAA,CAAAW,cAAA,eAA0B;IACxBX,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IAERb,EAAA,CAAAqB,UAAA,IAAAgB,4DAAA,qCAK0B;IAC5BrC,EAAA,CAAAa,YAAA,EAAM;IAGRb,EAAA,CAAAW,cAAA,eAA+B;IAK3BX,EAAA,CAAAC,SAAA,aAA6F;IAC/FD,EAAA,CAAAa,YAAA,EAAO;IAKXb,EAAA,CAAAqB,UAAA,KAAAiB,6DAAA,sCAI0B;IAG1BtC,EAAA,CAAAW,cAAA,mBAAmD;IACjDX,EAAA,CAAAqB,UAAA,KAAAkB,yCAAA,kBAIM;IAENvC,EAAA,CAAAqB,UAAA,KAAAmB,yCAAA,kBAIM;IAENxC,EAAA,CAAAqB,UAAA,KAAAoB,yCAAA,kBAuBM;IACRzC,EAAA,CAAAa,YAAA,EAAM;IAGNb,EAAA,CAAAW,cAAA,eAAqC;IACHX,EAAA,CAAA0C,UAAA,sBAAAC,6DAAA;MAAA3C,EAAA,CAAA4C,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA9C,EAAA,CAAA+C,aAAA;MAAA,OAAY/C,EAAA,CAAAgD,WAAA,CAAAF,OAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IACxDjD,EAAA,CAAAW,cAAA,eAAyB;IAMrBX,EAAA,CAAA0C,UAAA,mBAAAQ,2DAAA;MAAAlD,EAAA,CAAA4C,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAAnD,EAAA,CAAA+C,aAAA;MAAA,OAAS/C,EAAA,CAAAgD,WAAA,CAAAG,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IALtBpD,EAAA,CAAAa,YAAA,EAOqB;IAErBb,EAAA,CAAAW,cAAA,kBAGuD;IACrDX,EAAA,CAAAC,SAAA,aAA0B;IAC5BD,EAAA,CAAAa,YAAA,EAAS;IAIbb,EAAA,CAAAqB,UAAA,KAAAgC,yCAAA,kBAKM;IACRrD,EAAA,CAAAa,YAAA,EAAM;;;;;;;;;IAvGAb,EAAA,CAAAc,SAAA,GAA4E;IAA5Ed,EAAA,CAAAE,UAAA,UAAAC,OAAA,GAAAmD,MAAA,CAAAjD,mBAAA,qBAAAF,OAAA,CAAAoD,MAAA,0CAAAvD,EAAA,CAAAwD,aAAA,CAA4E,SAAAC,OAAA,GAAAH,MAAA,CAAAjD,mBAAA,qBAAAoD,OAAA,CAAA1B,QAAA;IAI3D/B,EAAA,CAAAc,SAAA,GAAqC;IAArCd,EAAA,CAAAe,iBAAA,EAAA2C,OAAA,GAAAJ,MAAA,CAAAjD,mBAAA,qBAAAqD,OAAA,CAAA3B,QAAA,CAAqC;IAEpD/B,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAA6B,kBAAA,QAAA8B,OAAA,GAAAL,MAAA,CAAAjD,mBAAA,qBAAAsD,OAAA,CAAAC,IAAA,4BAAAD,OAAA,GAAAL,MAAA,CAAAjD,mBAAA,qBAAAsD,OAAA,CAAAE,cAAA,mBACF;IAGG7D,EAAA,CAAAc,SAAA,GAA8C;IAA9Cd,EAAA,CAAAE,UAAA,WAAA4D,OAAA,GAAAR,MAAA,CAAAjD,mBAAA,qBAAAyD,OAAA,CAAAF,IAAA,eAA8C;IAWjD5D,EAAA,CAAAc,SAAA,GAAoC;IAApCd,EAAA,CAAA+D,WAAA,cAAAT,MAAA,CAAAU,gBAAA,CAAoC,kBAAAV,MAAA,CAAAU,gBAAA;IAEtBhE,EAAA,CAAAc,SAAA,GAAkC;IAAlCd,EAAA,CAAA+D,WAAA,YAAAT,MAAA,CAAAU,gBAAA,CAAkC,iBAAAV,MAAA,CAAAU,gBAAA;IAOnDhE,EAAA,CAAAc,SAAA,GAA8B;IAA9Bd,EAAA,CAAAE,UAAA,SAAAoD,MAAA,CAAA9C,IAAA,kBAAA8C,MAAA,CAAA9C,IAAA,CAAAC,kBAAA,CAA8B;IAOzBT,EAAA,CAAAc,SAAA,GAAa;IAAbd,EAAA,CAAAE,UAAA,SAAAoD,MAAA,CAAAW,OAAA,CAAa;IAMbjE,EAAA,CAAAc,SAAA,GAAuC;IAAvCd,EAAA,CAAAE,UAAA,UAAAoD,MAAA,CAAAW,OAAA,IAAAX,MAAA,CAAAnB,QAAA,CAAA+B,MAAA,OAAuC;IAMvClE,EAAA,CAAAc,SAAA,GAAqC;IAArCd,EAAA,CAAAE,UAAA,UAAAoD,MAAA,CAAAW,OAAA,IAAAX,MAAA,CAAAnB,QAAA,CAAA+B,MAAA,KAAqC;IA4BrClE,EAAA,CAAAc,SAAA,GAAyB;IAAzBd,EAAA,CAAAE,UAAA,cAAAoD,MAAA,CAAAa,WAAA,CAAyB;IAQzBnE,EAAA,CAAAc,SAAA,GAA8B;IAA9Bd,EAAA,CAAAE,UAAA,cAAAoD,MAAA,CAAAU,gBAAA,CAA8B;IAM9BhE,EAAA,CAAAc,SAAA,GAAoD;IAApDd,EAAA,CAAAE,UAAA,cAAAoD,MAAA,CAAAa,WAAA,CAAAC,KAAA,KAAAd,MAAA,CAAAU,gBAAA,CAAoD;IAMpDhE,EAAA,CAAAc,SAAA,GAAuB;IAAvBd,EAAA,CAAAE,UAAA,UAAAoD,MAAA,CAAAU,gBAAA,CAAuB;;;;;IASjChE,EAAA,CAAAW,cAAA,cAA4C;IAExCX,EAAA,CAAAC,SAAA,YAAwD;IACxDD,EAAA,CAAAW,cAAA,SAAI;IAAAX,EAAA,CAAAY,MAAA,4BAAqB;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAC9Bb,EAAA,CAAAW,cAAA,QAAG;IAAAX,EAAA,CAAAY,MAAA,6DAAsD;IAAAZ,EAAA,CAAAa,YAAA,EAAI;;;ADvGjE,OAAM,MAAOwD,mBAAmB;EAe9BC,YACUC,EAAe,EACfC,WAAwB,EACxBC,WAAwB;IAFxB,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IAjBZ,KAAAjE,IAAI,GAAgB,IAAI;IAGjC,KAAA2B,QAAQ,GAAc,EAAE;IAGxB,KAAA8B,OAAO,GAAG,KAAK;IACf,KAAAS,WAAW,GAAgB,IAAIC,GAAG,EAAE;IACpC,KAAAX,gBAAgB,GAAG,KAAK;IAEhB,KAAAY,aAAa,GAAmB,EAAE;IAElC,KAAAC,oBAAoB,GAAG,KAAK;IAOlC,IAAI,CAACV,WAAW,GAAG,IAAI,CAACI,EAAE,CAACO,KAAK,CAAC;MAC/BC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAChF,UAAU,CAACiF,QAAQ,EAAEjF,UAAU,CAACkF,SAAS,CAAC,CAAC,CAAC,CAAC;KAC7D,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACtD,WAAW,GAAG,IAAI,CAAC6C,WAAW,CAACU,cAAc,EAAE;IACpD,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACT,aAAa,CAACU,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,IAAI,CAACC,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;EAEpC;EAEAE,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACd,oBAAoB,EAAE;MAC7B,IAAI,CAACe,cAAc,EAAE;MACrB,IAAI,CAACf,oBAAoB,GAAG,KAAK;;EAErC;EAEQO,mBAAmBA,CAAA;IACzB;IACA,MAAMS,aAAa,GAAG,IAAI,CAACrB,WAAW,CAACsB,iBAAiB,CAACC,SAAS,CAACC,MAAM,IAAG;MAC1E,IAAI,CAAChC,gBAAgB,GAAGgC,MAAM;IAChC,CAAC,CAAC;IACF,IAAI,CAACpB,aAAa,CAACqB,IAAI,CAACJ,aAAa,CAAC;IAEtC;IACA,MAAMK,WAAW,GAAG,IAAI,CAAC1B,WAAW,CAAC2B,SAAS,CAACJ,SAAS,CAACK,OAAO,IAAG;MACjE,IAAI,IAAI,CAAC5F,IAAI,IAAI4F,OAAO,CAACC,MAAM,KAAK,IAAI,CAAC7F,IAAI,CAACF,EAAE,EAAE;QAChD,IAAI,CAAC6B,QAAQ,CAAC8D,IAAI,CAACG,OAAO,CAAC;QAC3B,IAAI,CAACvB,oBAAoB,GAAG,IAAI;QAEhC;QACA,IAAIuB,OAAO,CAACzE,MAAM,CAACrB,EAAE,KAAK,IAAI,CAACsB,WAAW,EAAEtB,EAAE,EAAE;UAC9C,IAAI,CAACkE,WAAW,CAAC8B,kBAAkB,CAAC,IAAI,CAAC9F,IAAI,CAACF,EAAE,CAAC,CAACyF,SAAS,EAAE;;;IAGnE,CAAC,CAAC;IACF,IAAI,CAACnB,aAAa,CAACqB,IAAI,CAACC,WAAW,CAAC;IAEpC;IACA,MAAMK,SAAS,GAAG,IAAI,CAAC/B,WAAW,CAACgC,OAAO,CAACT,SAAS,CAACU,YAAY,IAAG;MAClE,IAAI,CAACC,wBAAwB,CAACD,YAAY,CAAC;IAC7C,CAAC,CAAC;IACF,IAAI,CAAC7B,aAAa,CAACqB,IAAI,CAACM,SAAS,CAAC;EACpC;EAEAI,QAAQA,CAACnG,IAAU;IACjB,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC2B,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACuC,WAAW,CAACkC,KAAK,EAAE;IAExB,IAAIpG,IAAI,EAAE;MACR,IAAI,CAACqG,YAAY,EAAE;MACnB,IAAI,CAACrC,WAAW,CAACsC,uBAAuB,CAACtG,IAAI,CAACF,EAAE,CAAC;;EAErD;EAEQuG,YAAYA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACrG,IAAI,EAAE;IAEhB,IAAI,CAACyD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACO,WAAW,CAACuC,eAAe,CAAC,IAAI,CAACvG,IAAI,CAACF,EAAE,CAAC,CAACyF,SAAS,CAAC;MACvDiB,IAAI,EAAG7E,QAAQ,IAAI;QACjB,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAAC8E,OAAO,EAAE,CAAC,CAAC;QACpC,IAAI,CAAChD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACY,oBAAoB,GAAG,IAAI;MAClC,CAAC;MACDqC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACjD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAhB,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACkB,WAAW,CAACC,KAAK,IAAI,CAAC,IAAI,CAAC5D,IAAI,IAAI,CAAC,IAAI,CAACwD,gBAAgB,EAAE;MACnE;;IAGF,MAAMe,OAAO,GAAG,IAAI,CAACZ,WAAW,CAACiD,GAAG,CAAC,SAAS,CAAC,EAAEC,KAAK,EAAEC,IAAI,EAAE;IAC9D,IAAI,CAACvC,OAAO,EAAE;MACZ;;IAGF,IAAI;MACF,IAAI,CAACP,WAAW,CAACvB,WAAW,CAAC,IAAI,CAACzC,IAAI,CAACF,EAAE,EAAEyE,OAAO,CAAC;MACnD,IAAI,CAACZ,WAAW,CAACoD,KAAK,EAAE;MACxB,IAAI,CAACC,UAAU,EAAE;KAClB,CAAC,OAAON,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;;EAEnD;EAEA9D,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAAC5C,IAAI,EAAE;IAEhB,IAAI,CAACgE,WAAW,CAACiD,sBAAsB,CAAC,IAAI,CAACjH,IAAI,CAACF,EAAE,EAAE,IAAI,CAAC;IAE3D;IACA,IAAI,IAAI,CAACmF,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAGlC;IACA,IAAI,CAACA,aAAa,GAAGiC,UAAU,CAAC,MAAK;MACnC,IAAI,CAACF,UAAU,EAAE;IACnB,CAAC,EAAE,IAAI,CAAC;EACV;EAEQA,UAAUA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAChH,IAAI,EAAE;IAEhB,IAAI,CAACgE,WAAW,CAACiD,sBAAsB,CAAC,IAAI,CAACjH,IAAI,CAACF,EAAE,EAAE,KAAK,CAAC;IAE5D,IAAI,IAAI,CAACmF,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;MAChC,IAAI,CAACA,aAAa,GAAG,IAAI;;EAE7B;EAEQiB,wBAAwBA,CAACD,YAAgC;IAC/D,IAAIA,YAAY,CAACkB,MAAM,KAAK,IAAI,CAAC/F,WAAW,EAAEtB,EAAE,EAAE;MAChD,OAAO,CAAC;;;IAGV,IAAImG,YAAY,CAACT,MAAM,KAAK,QAAQ,EAAE;MACpC,IAAI,CAACtB,WAAW,CAACkD,GAAG,CAACnB,YAAY,CAACkB,MAAM,CAAC;KAC1C,MAAM;MACL,IAAI,CAACjD,WAAW,CAACmD,MAAM,CAACpB,YAAY,CAACkB,MAAM,CAAC;;EAEhD;EAEQ/B,cAAcA,CAAA;IACpB,IAAI;MACF,IAAI,IAAI,CAACkC,iBAAiB,EAAE;QAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAACE,aAAa;QACpDD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;KAE3C,CAAC,OAAOC,GAAG,EAAE;MACZhB,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEiB,GAAG,CAAC;;EAEpD;EAEA9H,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACG,IAAI,EAAE,OAAO,IAAI;IAC3B,OAAO,IAAI,CAACoB,WAAW,EAAEgC,IAAI,KAAK,SAAS,GAAG,IAAI,CAACpD,IAAI,CAAC4H,MAAM,GAAG,IAAI,CAAC5H,IAAI,CAAC6H,OAAO;EACpF;EAEAjG,QAAQA,CAAA;IACN,OAAO,IAAI,CAACsC,WAAW,CAAC4D,IAAI,GAAG,CAAC;EAClC;EAEAC,iBAAiBA,CAACC,UAAkB;IAClC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,EAAE,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAS,CAAE,CAAC;EAC5E;EAEA5H,iBAAiBA,CAACuH,UAAkB;IAClC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMM,KAAK,GAAG,IAAIJ,IAAI,EAAE;IACxB,MAAMK,SAAS,GAAG,IAAIL,IAAI,CAACI,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,IAAIR,IAAI,CAACS,YAAY,EAAE,KAAKJ,KAAK,CAACI,YAAY,EAAE,EAAE;MAChD,OAAO,OAAO;KACf,MAAM,IAAIT,IAAI,CAACS,YAAY,EAAE,KAAKH,SAAS,CAACG,YAAY,EAAE,EAAE;MAC3D,OAAO,WAAW;KACnB,MAAM;MACL,OAAOT,IAAI,CAACU,kBAAkB,EAAE;;EAEpC;EAEA1H,uBAAuBA,CAAC2H,KAAa;IACnC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,MAAMC,cAAc,GAAG,IAAI,CAAClH,QAAQ,CAACiH,KAAK,CAAC;IAC3C,MAAME,eAAe,GAAG,IAAI,CAACnH,QAAQ,CAACiH,KAAK,GAAG,CAAC,CAAC;IAEhD,MAAMG,WAAW,GAAG,IAAIb,IAAI,CAACW,cAAc,CAAClI,SAAS,CAAC,CAAC+H,YAAY,EAAE;IACrE,MAAMM,YAAY,GAAG,IAAId,IAAI,CAACY,eAAe,CAACnI,SAAS,CAAC,CAAC+H,YAAY,EAAE;IAEvE,OAAOK,WAAW,KAAKC,YAAY;EACrC;;;uBAhNWnF,mBAAmB,EAAArE,EAAA,CAAAyJ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3J,EAAA,CAAAyJ,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA7J,EAAA,CAAAyJ,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAnB1F,mBAAmB;MAAA2F,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UCZhCnK,EAAA,CAAAqB,UAAA,IAAAgJ,kCAAA,mBA6GM;UAENrK,EAAA,CAAAqB,UAAA,IAAAiJ,kCAAA,iBAMM;;;UArHoBtK,EAAA,CAAAE,UAAA,SAAAkK,GAAA,CAAA5J,IAAA,CAAU;UA+GLR,EAAA,CAAAc,SAAA,GAAW;UAAXd,EAAA,CAAAE,UAAA,UAAAkK,GAAA,CAAA5J,IAAA,CAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}