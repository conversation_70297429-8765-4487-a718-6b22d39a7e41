{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction RegisterComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelement(1, \"i\", 16);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.successMessage, \" \");\n  }\n}\nfunction RegisterComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵelement(1, \"i\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.errorMessage, \" \");\n  }\n}\nfunction RegisterComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20)(2, \"span\", 21);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 22);\n    i0.ɵɵtext(5, \"Initializing form...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RegisterComponent_form_14_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getFieldError(\"fullName\"), \" \");\n  }\n}\nfunction RegisterComponent_form_14_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.getFieldError(\"email\"), \" \");\n  }\n}\nfunction RegisterComponent_form_14_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getFieldError(\"password\"), \" \");\n  }\n}\nfunction RegisterComponent_form_14_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.getFieldError(\"confirmPassword\"), \" \");\n  }\n}\nfunction RegisterComponent_form_14_div_38_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.getFieldError(\"specialization\"), \" \");\n  }\n}\nfunction RegisterComponent_form_14_div_38_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.getFieldError(\"licenseNumber\"), \" \");\n  }\n}\nfunction RegisterComponent_form_14_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"h6\", 59);\n    i0.ɵɵelement(2, \"i\", 34);\n    i0.ɵɵtext(3, \"Doctor Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 26)(5, \"div\", 41)(6, \"label\", 60);\n    i0.ɵɵtext(7, \"Specialization *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"input\", 61);\n    i0.ɵɵtemplate(9, RegisterComponent_form_14_div_38_div_9_Template, 2, 1, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 41)(11, \"label\", 62);\n    i0.ɵɵtext(12, \"License Number *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 63);\n    i0.ɵɵtemplate(14, RegisterComponent_form_14_div_38_div_14_Template, 2, 1, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 41)(16, \"label\", 64);\n    i0.ɵɵtext(17, \"Hospital/Clinic\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"input\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 41)(20, \"label\", 66);\n    i0.ɵɵtext(21, \"Years of Experience\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"input\", 67);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r8.isFieldInvalid(\"specialization\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.isFieldInvalid(\"specialization\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r8.isFieldInvalid(\"licenseNumber\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.isFieldInvalid(\"licenseNumber\"));\n  }\n}\nfunction RegisterComponent_form_14_span_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 68);\n  }\n}\nfunction RegisterComponent_form_14_span_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Creating Account...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_form_14_span_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Create Account\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_form_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 23);\n    i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_form_14_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 24)(2, \"label\", 25);\n    i0.ɵɵtext(3, \"I am a:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 26)(5, \"div\", 27)(6, \"div\", 28);\n    i0.ɵɵelement(7, \"input\", 29);\n    i0.ɵɵelementStart(8, \"label\", 30);\n    i0.ɵɵelement(9, \"i\", 31);\n    i0.ɵɵtext(10, \"Patient \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 27)(12, \"div\", 28);\n    i0.ɵɵelement(13, \"input\", 32);\n    i0.ɵɵelementStart(14, \"label\", 33);\n    i0.ɵɵelement(15, \"i\", 34);\n    i0.ɵɵtext(16, \"Doctor \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(17, \"div\", 26)(18, \"div\", 35)(19, \"label\", 36);\n    i0.ɵɵtext(20, \"Full Name *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"input\", 37);\n    i0.ɵɵtemplate(22, RegisterComponent_form_14_div_22_Template, 2, 1, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 35)(24, \"label\", 39);\n    i0.ɵɵtext(25, \"Email Address *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"input\", 40);\n    i0.ɵɵtemplate(27, RegisterComponent_form_14_div_27_Template, 2, 1, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 41)(29, \"label\", 42);\n    i0.ɵɵtext(30, \"Password *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"input\", 43);\n    i0.ɵɵtemplate(32, RegisterComponent_form_14_div_32_Template, 2, 1, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 41)(34, \"label\", 44);\n    i0.ɵɵtext(35, \"Confirm Password *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(36, \"input\", 45);\n    i0.ɵɵtemplate(37, RegisterComponent_form_14_div_37_Template, 2, 1, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(38, RegisterComponent_form_14_div_38_Template, 23, 6, \"div\", 46);\n    i0.ɵɵelementStart(39, \"div\", 47)(40, \"h6\", 48);\n    i0.ɵɵelement(41, \"i\", 49);\n    i0.ɵɵtext(42, \"Contact Information (Optional) \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 26)(44, \"div\", 41)(45, \"label\", 50);\n    i0.ɵɵtext(46, \"Phone Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(47, \"input\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 41)(49, \"label\", 52);\n    i0.ɵɵtext(50, \"Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(51, \"input\", 53);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(52, \"button\", 54);\n    i0.ɵɵtemplate(53, RegisterComponent_form_14_span_53_Template, 1, 0, \"span\", 55);\n    i0.ɵɵtemplate(54, RegisterComponent_form_14_span_54_Template, 2, 0, \"span\", 56);\n    i0.ɵɵtemplate(55, RegisterComponent_form_14_span_55_Template, 2, 0, \"span\", 56);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.registerForm);\n    i0.ɵɵadvance(21);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r3.isFieldInvalid(\"fullName\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isFieldInvalid(\"fullName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r3.isFieldInvalid(\"email\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isFieldInvalid(\"email\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r3.isFieldInvalid(\"password\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isFieldInvalid(\"password\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r3.isFieldInvalid(\"confirmPassword\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isFieldInvalid(\"confirmPassword\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedRole === \"DOCTOR\");\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isLoading);\n  }\n}\nexport class RegisterComponent {\n  constructor(formBuilder, authService, router) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.isLoading = false;\n    this.errorMessage = '';\n    this.successMessage = '';\n    this.selectedRole = 'PATIENT';\n  }\n  ngOnInit() {\n    // Redirect if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.redirectToDashboard();\n      return;\n    }\n    this.initializeForm();\n  }\n  initializeForm() {\n    this.registerForm = this.formBuilder.group({\n      fullName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      confirmPassword: ['', [Validators.required]],\n      role: ['PATIENT', [Validators.required]],\n      phoneNumber: [''],\n      address: [''],\n      // Doctor-specific fields\n      specialization: [''],\n      licenseNumber: [''],\n      affiliation: [''],\n      yearsOfExperience: ['']\n    }, {\n      validators: this.passwordMatchValidator\n    });\n    // Watch for role changes\n    this.registerForm.get('role')?.valueChanges.subscribe(role => {\n      this.selectedRole = role;\n      this.updateValidators();\n    });\n  }\n  passwordMatchValidator(form) {\n    const password = form.get('password');\n    const confirmPassword = form.get('confirmPassword');\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({\n        passwordMismatch: true\n      });\n    } else {\n      if (confirmPassword?.errors?.['passwordMismatch']) {\n        delete confirmPassword.errors['passwordMismatch'];\n        if (Object.keys(confirmPassword.errors).length === 0) {\n          confirmPassword.setErrors(null);\n        }\n      }\n    }\n    return null;\n  }\n  updateValidators() {\n    const specialization = this.registerForm.get('specialization');\n    const licenseNumber = this.registerForm.get('licenseNumber');\n    if (this.selectedRole === 'DOCTOR') {\n      specialization?.setValidators([Validators.required]);\n      licenseNumber?.setValidators([Validators.required]);\n    } else {\n      specialization?.clearValidators();\n      licenseNumber?.clearValidators();\n    }\n    specialization?.updateValueAndValidity();\n    licenseNumber?.updateValueAndValidity();\n  }\n  onSubmit() {\n    if (!this.registerForm || this.registerForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.successMessage = '';\n    const registerRequest = this.registerForm.value;\n    this.authService.register(registerRequest).subscribe({\n      next: response => {\n        this.isLoading = false;\n        this.successMessage = 'Registration successful! Redirecting to dashboard...';\n        // Redirect after a short delay\n        setTimeout(() => {\n          this.redirectToDashboard();\n        }, 2000);\n      },\n      error: error => {\n        this.isLoading = false;\n        this.errorMessage = error.message || 'Registration failed. Please try again.';\n      }\n    });\n  }\n  redirectToDashboard() {\n    const user = this.authService.getCurrentUser();\n    if (user) {\n      if (user.role === 'DOCTOR') {\n        this.router.navigate(['/doctor/dashboard']);\n      } else if (user.role === 'PATIENT') {\n        this.router.navigate(['/patient/dashboard']);\n      } else {\n        this.router.navigate(['/']);\n      }\n    }\n  }\n  markFormGroupTouched() {\n    if (!this.registerForm) {\n      return;\n    }\n    Object.keys(this.registerForm.controls).forEach(key => {\n      const control = this.registerForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  isFieldInvalid(fieldName) {\n    if (!this.registerForm) {\n      return false;\n    }\n    const field = this.registerForm.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n  getFieldError(fieldName) {\n    if (!this.registerForm) {\n      return '';\n    }\n    const field = this.registerForm.get(fieldName);\n    if (field?.errors) {\n      if (field.errors['required']) {\n        return `${this.getFieldDisplayName(fieldName)} is required`;\n      }\n      if (field.errors['email']) {\n        return 'Please enter a valid email address';\n      }\n      if (field.errors['minlength']) {\n        return `${this.getFieldDisplayName(fieldName)} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      }\n      if (field.errors['maxlength']) {\n        return `${this.getFieldDisplayName(fieldName)} must not exceed ${field.errors['maxlength'].requiredLength} characters`;\n      }\n      if (field.errors['passwordMismatch']) {\n        return 'Passwords do not match';\n      }\n    }\n    return '';\n  }\n  getFieldDisplayName(fieldName) {\n    const displayNames = {\n      fullName: 'Full Name',\n      email: 'Email',\n      password: 'Password',\n      confirmPassword: 'Confirm Password',\n      phoneNumber: 'Phone Number',\n      specialization: 'Specialization',\n      licenseNumber: 'License Number',\n      affiliation: 'Affiliation',\n      yearsOfExperience: 'Years of Experience'\n    };\n    return displayNames[fieldName] || fieldName;\n  }\n  static {\n    this.ɵfac = function RegisterComponent_Factory(t) {\n      return new (t || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterComponent,\n      selectors: [[\"app-register\"]],\n      decls: 20,\n      vars: 4,\n      consts: [[1, \"container-fluid\", \"min-vh-100\", \"py-4\"], [1, \"row\", \"justify-content-center\"], [1, \"col-md-8\", \"col-lg-6\"], [1, \"card\", \"shadow\"], [1, \"card-body\", \"p-4\"], [1, \"text-center\", \"mb-4\"], [1, \"bi\", \"bi-heart-pulse\", \"text-primary\", \"display-4\", \"mb-3\"], [1, \"fw-bold\", \"text-dark\"], [1, \"text-muted\"], [\"class\", \"alert alert-success\", \"role\", \"alert\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"novalidate\", \"\", 3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [1, \"text-center\", \"mt-4\"], [\"routerLink\", \"/auth/login\", 1, \"text-primary\", \"text-decoration-none\", \"fw-semibold\"], [\"role\", \"alert\", 1, \"alert\", \"alert-success\"], [1, \"bi\", \"bi-check-circle\", \"me-2\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"bi\", \"bi-exclamation-triangle\", \"me-2\"], [1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\", \"text-muted\"], [\"novalidate\", \"\", 3, \"formGroup\", \"ngSubmit\"], [1, \"mb-4\"], [1, \"form-label\", \"fw-semibold\"], [1, \"row\"], [1, \"col-6\"], [1, \"form-check\"], [\"type\", \"radio\", \"name\", \"role\", \"id\", \"rolePatient\", \"value\", \"PATIENT\", \"formControlName\", \"role\", 1, \"form-check-input\"], [\"for\", \"rolePatient\", 1, \"form-check-label\"], [1, \"bi\", \"bi-person\", \"me-2\"], [\"type\", \"radio\", \"name\", \"role\", \"id\", \"roleDoctor\", \"value\", \"DOCTOR\", \"formControlName\", \"role\", 1, \"form-check-input\"], [\"for\", \"roleDoctor\", 1, \"form-check-label\"], [1, \"bi\", \"bi-person-badge\", \"me-2\"], [1, \"col-12\", \"mb-3\"], [\"for\", \"fullName\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"fullName\", \"formControlName\", \"fullName\", \"placeholder\", \"Enter your full name\", 1, \"form-control\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", 1, \"form-control\"], [1, \"col-md-6\", \"mb-3\"], [\"for\", \"password\", 1, \"form-label\"], [\"type\", \"password\", \"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Create a password\", 1, \"form-control\"], [\"for\", \"confirmPassword\", 1, \"form-label\"], [\"type\", \"password\", \"id\", \"confirmPassword\", \"formControlName\", \"confirmPassword\", \"placeholder\", \"Confirm your password\", 1, \"form-control\"], [\"class\", \"border-top pt-3 mb-3\", 4, \"ngIf\"], [1, \"border-top\", \"pt-3\", \"mb-4\"], [1, \"fw-semibold\", \"mb-3\"], [1, \"bi\", \"bi-telephone\", \"me-2\"], [\"for\", \"phoneNumber\", 1, \"form-label\"], [\"type\", \"tel\", \"id\", \"phoneNumber\", \"formControlName\", \"phoneNumber\", \"placeholder\", \"Your phone number\", 1, \"form-control\"], [\"for\", \"address\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"address\", \"formControlName\", \"address\", \"placeholder\", \"Your address\", 1, \"form-control\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"w-100\", \"py-2\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"invalid-feedback\"], [1, \"border-top\", \"pt-3\", \"mb-3\"], [1, \"fw-semibold\", \"text-primary\", \"mb-3\"], [\"for\", \"specialization\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"specialization\", \"formControlName\", \"specialization\", \"placeholder\", \"e.g., Cardiology\", 1, \"form-control\"], [\"for\", \"licenseNumber\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"licenseNumber\", \"formControlName\", \"licenseNumber\", \"placeholder\", \"Medical license number\", 1, \"form-control\"], [\"for\", \"affiliation\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"affiliation\", \"formControlName\", \"affiliation\", \"placeholder\", \"Your workplace\", 1, \"form-control\"], [\"for\", \"yearsOfExperience\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"yearsOfExperience\", \"formControlName\", \"yearsOfExperience\", \"placeholder\", \"0\", \"min\", \"0\", \"max\", \"50\", 1, \"form-control\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n      template: function RegisterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"i\", 6);\n          i0.ɵɵelementStart(7, \"h2\", 7);\n          i0.ɵɵtext(8, \"Join HealthConnect\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\", 8);\n          i0.ɵɵtext(10, \"Create your account to get started\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(11, RegisterComponent_div_11_Template, 3, 1, \"div\", 9);\n          i0.ɵɵtemplate(12, RegisterComponent_div_12_Template, 3, 1, \"div\", 10);\n          i0.ɵɵtemplate(13, RegisterComponent_div_13_Template, 6, 0, \"div\", 11);\n          i0.ɵɵtemplate(14, RegisterComponent_form_14_Template, 56, 18, \"form\", 12);\n          i0.ɵɵelementStart(15, \"div\", 13)(16, \"p\", 8);\n          i0.ɵɵtext(17, \" Already have an account? \");\n          i0.ɵɵelementStart(18, \"a\", 14);\n          i0.ɵɵtext(19, \" Sign in here \");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.successMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.registerForm);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.registerForm);\n        }\n      },\n      dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.RadioControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink],\n      styles: [\".min-vh-100[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n}\\n\\n.form-check-input[_ngcontent-%COMP%]:checked {\\n  background-color: #0d6efd;\\n  border-color: #0d6efd;\\n}\\n\\n.form-check-label[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  font-weight: 500;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);\\n  border: none;\\n  font-weight: 500;\\n  border-radius: 8px;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #0b5ed7 0%, #0a58ca 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(13, 110, 253, 0.3);\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:disabled {\\n  background: #6c757d;\\n  transform: none;\\n  box-shadow: none;\\n}\\n\\n.border-top[_ngcontent-%COMP%] {\\n  border-color: #e9ecef !important;\\n}\\n\\n.text-primary[_ngcontent-%COMP%] {\\n  color: #0d6efd !important;\\n}\\n\\n.alert[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 8px;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  border-radius: 6px;\\n  border: 1px solid #ced4da;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #86b7fe;\\n  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);\\n}\\n\\n@media (max-width: 768px) {\\n  .container-fluid[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .card-body[_ngcontent-%COMP%] {\\n    padding: 2rem 1.5rem !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "successMessage", "ctx_r1", "errorMessage", "ctx_r4", "getFieldError", "ctx_r5", "ctx_r6", "ctx_r7", "ctx_r12", "ctx_r13", "ɵɵtemplate", "RegisterComponent_form_14_div_38_div_9_Template", "RegisterComponent_form_14_div_38_div_14_Template", "ɵɵclassProp", "ctx_r8", "isFieldInvalid", "ɵɵproperty", "ɵɵlistener", "RegisterComponent_form_14_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r15", "ctx_r14", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "RegisterComponent_form_14_div_22_Template", "RegisterComponent_form_14_div_27_Template", "RegisterComponent_form_14_div_32_Template", "RegisterComponent_form_14_div_37_Template", "RegisterComponent_form_14_div_38_Template", "RegisterComponent_form_14_span_53_Template", "RegisterComponent_form_14_span_54_Template", "RegisterComponent_form_14_span_55_Template", "ctx_r3", "registerForm", "selectedR<PERSON>", "isLoading", "RegisterComponent", "constructor", "formBuilder", "authService", "router", "ngOnInit", "isAuthenticated", "redirectToDashboard", "initializeForm", "group", "fullName", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "email", "password", "confirmPassword", "role", "phoneNumber", "address", "specialization", "licenseNumber", "affiliation", "yearsOfExperience", "validators", "passwordMatchValidator", "get", "valueChanges", "subscribe", "updateValidators", "form", "value", "setErrors", "passwordMismatch", "errors", "Object", "keys", "length", "setValidators", "clearValidators", "updateValueAndValidity", "invalid", "markFormGroupTouched", "registerRequest", "register", "next", "response", "setTimeout", "error", "message", "user", "getCurrentUser", "navigate", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "fieldName", "field", "dirty", "touched", "getFieldDisplayName", "<PERSON><PERSON><PERSON><PERSON>", "displayNames", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "RegisterComponent_Template", "rf", "ctx", "RegisterComponent_div_11_Template", "RegisterComponent_div_12_Template", "RegisterComponent_div_13_Template", "RegisterComponent_form_14_Template"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/auth/register/register.component.ts", "/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/auth/register/register.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../core/services/auth.service';\nimport { RegisterRequest } from '../../core/models/user.model';\n\n@Component({\n  selector: 'app-register',\n  templateUrl: './register.component.html',\n  styleUrls: ['./register.component.scss']\n})\nexport class RegisterComponent implements OnInit {\n  registerForm!: FormGroup;\n  isLoading = false;\n  errorMessage = '';\n  successMessage = '';\n  selectedRole: 'PATIENT' | 'DOCTOR' = 'PATIENT';\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Redirect if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.redirectToDashboard();\n      return;\n    }\n\n    this.initializeForm();\n  }\n\n  private initializeForm(): void {\n    this.registerForm = this.formBuilder.group({\n      fullName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      confirmPassword: ['', [Validators.required]],\n      role: ['PATIENT', [Validators.required]],\n      phoneNumber: [''],\n      address: [''],\n      \n      // Doctor-specific fields\n      specialization: [''],\n      licenseNumber: [''],\n      affiliation: [''],\n      yearsOfExperience: ['']\n    }, { validators: this.passwordMatchValidator });\n\n    // Watch for role changes\n    this.registerForm.get('role')?.valueChanges.subscribe(role => {\n      this.selectedRole = role;\n      this.updateValidators();\n    });\n  }\n\n  private passwordMatchValidator(form: FormGroup) {\n    const password = form.get('password');\n    const confirmPassword = form.get('confirmPassword');\n    \n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({ passwordMismatch: true });\n    } else {\n      if (confirmPassword?.errors?.['passwordMismatch']) {\n        delete confirmPassword.errors['passwordMismatch'];\n        if (Object.keys(confirmPassword.errors).length === 0) {\n          confirmPassword.setErrors(null);\n        }\n      }\n    }\n    return null;\n  }\n\n  private updateValidators(): void {\n    const specialization = this.registerForm.get('specialization');\n    const licenseNumber = this.registerForm.get('licenseNumber');\n\n    if (this.selectedRole === 'DOCTOR') {\n      specialization?.setValidators([Validators.required]);\n      licenseNumber?.setValidators([Validators.required]);\n    } else {\n      specialization?.clearValidators();\n      licenseNumber?.clearValidators();\n    }\n\n    specialization?.updateValueAndValidity();\n    licenseNumber?.updateValueAndValidity();\n  }\n\n  onSubmit(): void {\n    if (!this.registerForm || this.registerForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.successMessage = '';\n\n    const registerRequest: RegisterRequest = this.registerForm.value;\n\n    this.authService.register(registerRequest).subscribe({\n      next: (response) => {\n        this.isLoading = false;\n        this.successMessage = 'Registration successful! Redirecting to dashboard...';\n        \n        // Redirect after a short delay\n        setTimeout(() => {\n          this.redirectToDashboard();\n        }, 2000);\n      },\n      error: (error) => {\n        this.isLoading = false;\n        this.errorMessage = error.message || 'Registration failed. Please try again.';\n      }\n    });\n  }\n\n  private redirectToDashboard(): void {\n    const user = this.authService.getCurrentUser();\n    if (user) {\n      if (user.role === 'DOCTOR') {\n        this.router.navigate(['/doctor/dashboard']);\n      } else if (user.role === 'PATIENT') {\n        this.router.navigate(['/patient/dashboard']);\n      } else {\n        this.router.navigate(['/']);\n      }\n    }\n  }\n\n  private markFormGroupTouched(): void {\n    if (!this.registerForm) {\n      return;\n    }\n    Object.keys(this.registerForm.controls).forEach(key => {\n      const control = this.registerForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  isFieldInvalid(fieldName: string): boolean {\n    if (!this.registerForm) {\n      return false;\n    }\n    const field = this.registerForm.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n\n  getFieldError(fieldName: string): string {\n    if (!this.registerForm) {\n      return '';\n    }\n    const field = this.registerForm.get(fieldName);\n    if (field?.errors) {\n      if (field.errors['required']) {\n        return `${this.getFieldDisplayName(fieldName)} is required`;\n      }\n      if (field.errors['email']) {\n        return 'Please enter a valid email address';\n      }\n      if (field.errors['minlength']) {\n        return `${this.getFieldDisplayName(fieldName)} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      }\n      if (field.errors['maxlength']) {\n        return `${this.getFieldDisplayName(fieldName)} must not exceed ${field.errors['maxlength'].requiredLength} characters`;\n      }\n      if (field.errors['passwordMismatch']) {\n        return 'Passwords do not match';\n      }\n    }\n    return '';\n  }\n\n  private getFieldDisplayName(fieldName: string): string {\n    const displayNames: { [key: string]: string } = {\n      fullName: 'Full Name',\n      email: 'Email',\n      password: 'Password',\n      confirmPassword: 'Confirm Password',\n      phoneNumber: 'Phone Number',\n      specialization: 'Specialization',\n      licenseNumber: 'License Number',\n      affiliation: 'Affiliation',\n      yearsOfExperience: 'Years of Experience'\n    };\n    return displayNames[fieldName] || fieldName;\n  }\n}\n", "<div class=\"container-fluid min-vh-100 py-4\">\n  <div class=\"row justify-content-center\">\n    <div class=\"col-md-8 col-lg-6\">\n      <div class=\"card shadow\">\n        <div class=\"card-body p-4\">\n          <!-- Header -->\n          <div class=\"text-center mb-4\">\n            <i class=\"bi bi-heart-pulse text-primary display-4 mb-3\"></i>\n            <h2 class=\"fw-bold text-dark\">Join HealthConnect</h2>\n            <p class=\"text-muted\">Create your account to get started</p>\n          </div>\n\n          <!-- Success Alert -->\n          <div *ngIf=\"successMessage\" class=\"alert alert-success\" role=\"alert\">\n            <i class=\"bi bi-check-circle me-2\"></i>\n            {{ successMessage }}\n          </div>\n\n          <!-- Error <PERSON> -->\n          <div *ngIf=\"errorMessage\" class=\"alert alert-danger\" role=\"alert\">\n            <i class=\"bi bi-exclamation-triangle me-2\"></i>\n            {{ errorMessage }}\n          </div>\n\n          <!-- Loading State -->\n          <div *ngIf=\"!registerForm\" class=\"text-center py-4\">\n            <div class=\"spinner-border text-primary\" role=\"status\">\n              <span class=\"visually-hidden\">Loading...</span>\n            </div>\n            <p class=\"mt-2 text-muted\">Initializing form...</p>\n          </div>\n\n          <!-- Registration Form -->\n          <form *ngIf=\"registerForm\" [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\" novalidate>\n\n            <!-- Role Selection -->\n            <div class=\"mb-4\">\n              <label class=\"form-label fw-semibold\">I am a:</label>\n              <div class=\"row\">\n                <div class=\"col-6\">\n                  <div class=\"form-check\">\n                    <input\n                      class=\"form-check-input\"\n                      type=\"radio\"\n                      name=\"role\"\n                      id=\"rolePatient\"\n                      value=\"PATIENT\"\n                      formControlName=\"role\"\n                    >\n                    <label class=\"form-check-label\" for=\"rolePatient\">\n                      <i class=\"bi bi-person me-2\"></i>Patient\n                    </label>\n                  </div>\n                </div>\n                <div class=\"col-6\">\n                  <div class=\"form-check\">\n                    <input\n                      class=\"form-check-input\"\n                      type=\"radio\"\n                      name=\"role\"\n                      id=\"roleDoctor\"\n                      value=\"DOCTOR\"\n                      formControlName=\"role\"\n                    >\n                    <label class=\"form-check-label\" for=\"roleDoctor\">\n                      <i class=\"bi bi-person-badge me-2\"></i>Doctor\n                    </label>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Basic Information -->\n            <div class=\"row\">\n              <div class=\"col-12 mb-3\">\n                <label for=\"fullName\" class=\"form-label\">Full Name *</label>\n                <input\n                  type=\"text\"\n                  class=\"form-control\"\n                  id=\"fullName\"\n                  formControlName=\"fullName\"\n                  placeholder=\"Enter your full name\"\n                  [class.is-invalid]=\"isFieldInvalid('fullName')\"\n                >\n                <div *ngIf=\"isFieldInvalid('fullName')\" class=\"invalid-feedback\">\n                  {{ getFieldError('fullName') }}\n                </div>\n              </div>\n\n              <div class=\"col-12 mb-3\">\n                <label for=\"email\" class=\"form-label\">Email Address *</label>\n                <input\n                  type=\"email\"\n                  class=\"form-control\"\n                  id=\"email\"\n                  formControlName=\"email\"\n                  placeholder=\"Enter your email\"\n                  [class.is-invalid]=\"isFieldInvalid('email')\"\n                >\n                <div *ngIf=\"isFieldInvalid('email')\" class=\"invalid-feedback\">\n                  {{ getFieldError('email') }}\n                </div>\n              </div>\n\n              <div class=\"col-md-6 mb-3\">\n                <label for=\"password\" class=\"form-label\">Password *</label>\n                <input\n                  type=\"password\"\n                  class=\"form-control\"\n                  id=\"password\"\n                  formControlName=\"password\"\n                  placeholder=\"Create a password\"\n                  [class.is-invalid]=\"isFieldInvalid('password')\"\n                >\n                <div *ngIf=\"isFieldInvalid('password')\" class=\"invalid-feedback\">\n                  {{ getFieldError('password') }}\n                </div>\n              </div>\n\n              <div class=\"col-md-6 mb-3\">\n                <label for=\"confirmPassword\" class=\"form-label\">Confirm Password *</label>\n                <input\n                  type=\"password\"\n                  class=\"form-control\"\n                  id=\"confirmPassword\"\n                  formControlName=\"confirmPassword\"\n                  placeholder=\"Confirm your password\"\n                  [class.is-invalid]=\"isFieldInvalid('confirmPassword')\"\n                >\n                <div *ngIf=\"isFieldInvalid('confirmPassword')\" class=\"invalid-feedback\">\n                  {{ getFieldError('confirmPassword') }}\n                </div>\n              </div>\n            </div>\n\n            <!-- Doctor-specific fields -->\n            <div *ngIf=\"selectedRole === 'DOCTOR'\" class=\"border-top pt-3 mb-3\">\n              <h6 class=\"fw-semibold text-primary mb-3\">\n                <i class=\"bi bi-person-badge me-2\"></i>Doctor Information\n              </h6>\n              \n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"specialization\" class=\"form-label\">Specialization *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"specialization\"\n                    formControlName=\"specialization\"\n                    placeholder=\"e.g., Cardiology\"\n                    [class.is-invalid]=\"isFieldInvalid('specialization')\"\n                  >\n                  <div *ngIf=\"isFieldInvalid('specialization')\" class=\"invalid-feedback\">\n                    {{ getFieldError('specialization') }}\n                  </div>\n                </div>\n\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"licenseNumber\" class=\"form-label\">License Number *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"licenseNumber\"\n                    formControlName=\"licenseNumber\"\n                    placeholder=\"Medical license number\"\n                    [class.is-invalid]=\"isFieldInvalid('licenseNumber')\"\n                  >\n                  <div *ngIf=\"isFieldInvalid('licenseNumber')\" class=\"invalid-feedback\">\n                    {{ getFieldError('licenseNumber') }}\n                  </div>\n                </div>\n\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"affiliation\" class=\"form-label\">Hospital/Clinic</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"affiliation\"\n                    formControlName=\"affiliation\"\n                    placeholder=\"Your workplace\"\n                  >\n                </div>\n\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"yearsOfExperience\" class=\"form-label\">Years of Experience</label>\n                  <input\n                    type=\"number\"\n                    class=\"form-control\"\n                    id=\"yearsOfExperience\"\n                    formControlName=\"yearsOfExperience\"\n                    placeholder=\"0\"\n                    min=\"0\"\n                    max=\"50\"\n                  >\n                </div>\n              </div>\n            </div>\n\n            <!-- Optional Contact Information -->\n            <div class=\"border-top pt-3 mb-4\">\n              <h6 class=\"fw-semibold mb-3\">\n                <i class=\"bi bi-telephone me-2\"></i>Contact Information (Optional)\n              </h6>\n              \n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"phoneNumber\" class=\"form-label\">Phone Number</label>\n                  <input\n                    type=\"tel\"\n                    class=\"form-control\"\n                    id=\"phoneNumber\"\n                    formControlName=\"phoneNumber\"\n                    placeholder=\"Your phone number\"\n                  >\n                </div>\n\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"address\" class=\"form-label\">Address</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"address\"\n                    formControlName=\"address\"\n                    placeholder=\"Your address\"\n                  >\n                </div>\n              </div>\n            </div>\n\n            <!-- Submit Button -->\n            <button\n              type=\"submit\"\n              class=\"btn btn-primary w-100 py-2\"\n              [disabled]=\"isLoading\"\n            >\n              <span *ngIf=\"isLoading\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\n              <span *ngIf=\"isLoading\">Creating Account...</span>\n              <span *ngIf=\"!isLoading\">Create Account</span>\n            </button>\n          </form>\n\n          <!-- Login Link -->\n          <div class=\"text-center mt-4\">\n            <p class=\"text-muted\">\n              Already have an account?\n              <a routerLink=\"/auth/login\" class=\"text-primary text-decoration-none fw-semibold\">\n                Sign in here\n              </a>\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;ICYzDC,EAAA,CAAAC,cAAA,cAAqE;IACnED,EAAA,CAAAE,SAAA,YAAuC;IACvCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,cAAA,MACF;;;;;IAGAR,EAAA,CAAAC,cAAA,cAAkE;IAChED,EAAA,CAAAE,SAAA,YAA+C;IAC/CF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAG,MAAA,CAAAC,YAAA,MACF;;;;;IAGAV,EAAA,CAAAC,cAAA,cAAoD;IAElBD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEjDJ,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAG,MAAA,2BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAuD/CJ,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAK,MAAA,CAAAC,aAAA,kBACF;;;;;IAaAZ,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAO,MAAA,CAAAD,aAAA,eACF;;;;;IAaAZ,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAQ,MAAA,CAAAF,aAAA,kBACF;;;;;IAaAZ,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAS,MAAA,CAAAH,aAAA,yBACF;;;;;IAqBEZ,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAU,OAAA,CAAAJ,aAAA,wBACF;;;;;IAaAZ,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAW,OAAA,CAAAL,aAAA,uBACF;;;;;IAjCNZ,EAAA,CAAAC,cAAA,cAAoE;IAEhED,EAAA,CAAAE,SAAA,YAAuC;IAAAF,EAAA,CAAAG,MAAA,0BACzC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAELJ,EAAA,CAAAC,cAAA,cAAiB;IAEkCD,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvEJ,EAAA,CAAAE,SAAA,gBAOC;IACDF,EAAA,CAAAkB,UAAA,IAAAC,+CAAA,kBAEM;IACRnB,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,eAA2B;IACqBD,EAAA,CAAAG,MAAA,wBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACtEJ,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAkB,UAAA,KAAAE,gDAAA,kBAEM;IACRpB,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,eAA2B;IACmBD,EAAA,CAAAG,MAAA,uBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACnEJ,EAAA,CAAAE,SAAA,iBAMC;IACHF,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,eAA2B;IACyBD,EAAA,CAAAG,MAAA,2BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC7EJ,EAAA,CAAAE,SAAA,iBAQC;IACHF,EAAA,CAAAI,YAAA,EAAM;;;;IA5CFJ,EAAA,CAAAK,SAAA,GAAqD;IAArDL,EAAA,CAAAqB,WAAA,eAAAC,MAAA,CAAAC,cAAA,mBAAqD;IAEjDvB,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAwB,UAAA,SAAAF,MAAA,CAAAC,cAAA,mBAAsC;IAa1CvB,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAAqB,WAAA,eAAAC,MAAA,CAAAC,cAAA,kBAAoD;IAEhDvB,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAwB,UAAA,SAAAF,MAAA,CAAAC,cAAA,kBAAqC;;;;;IAoE/CvB,EAAA,CAAAE,SAAA,eAA2F;;;;;IAC3FF,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAClDJ,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IA5MlDJ,EAAA,CAAAC,cAAA,eAAyF;IAAnCD,EAAA,CAAAyB,UAAA,sBAAAC,4DAAA;MAAA1B,EAAA,CAAA2B,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA7B,EAAA,CAAA8B,aAAA;MAAA,OAAY9B,EAAA,CAAA+B,WAAA,CAAAF,OAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAG3EhC,EAAA,CAAAC,cAAA,cAAkB;IACsBD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACrDJ,EAAA,CAAAC,cAAA,cAAiB;IAGXD,EAAA,CAAAE,SAAA,gBAOC;IACDF,EAAA,CAAAC,cAAA,gBAAkD;IAChDD,EAAA,CAAAE,SAAA,YAAiC;IAAAF,EAAA,CAAAG,MAAA,gBACnC;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAGZJ,EAAA,CAAAC,cAAA,eAAmB;IAEfD,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAC,cAAA,iBAAiD;IAC/CD,EAAA,CAAAE,SAAA,aAAuC;IAAAF,EAAA,CAAAG,MAAA,eACzC;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAOhBJ,EAAA,CAAAC,cAAA,eAAiB;IAE4BD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC5DJ,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAkB,UAAA,KAAAe,yCAAA,kBAEM;IACRjC,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,eAAyB;IACeD,EAAA,CAAAG,MAAA,uBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC7DJ,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAkB,UAAA,KAAAgB,yCAAA,kBAEM;IACRlC,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,eAA2B;IACgBD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC3DJ,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAkB,UAAA,KAAAiB,yCAAA,kBAEM;IACRnC,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,eAA2B;IACuBD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1EJ,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAkB,UAAA,KAAAkB,yCAAA,kBAEM;IACRpC,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAkB,UAAA,KAAAmB,yCAAA,mBA4DM;IAGNrC,EAAA,CAAAC,cAAA,eAAkC;IAE9BD,EAAA,CAAAE,SAAA,aAAoC;IAAAF,EAAA,CAAAG,MAAA,uCACtC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAELJ,EAAA,CAAAC,cAAA,eAAiB;IAE+BD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAChEJ,EAAA,CAAAE,SAAA,iBAMC;IACHF,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,eAA2B;IACeD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvDJ,EAAA,CAAAE,SAAA,iBAMC;IACHF,EAAA,CAAAI,YAAA,EAAM;IAKVJ,EAAA,CAAAC,cAAA,kBAIC;IACCD,EAAA,CAAAkB,UAAA,KAAAoB,0CAAA,mBAA2F;IAC3FtC,EAAA,CAAAkB,UAAA,KAAAqB,0CAAA,mBAAkD;IAClDvC,EAAA,CAAAkB,UAAA,KAAAsB,0CAAA,mBAA8C;IAChDxC,EAAA,CAAAI,YAAA,EAAS;;;;IA7MgBJ,EAAA,CAAAwB,UAAA,cAAAiB,MAAA,CAAAC,YAAA,CAA0B;IAiD7C1C,EAAA,CAAAK,SAAA,IAA+C;IAA/CL,EAAA,CAAAqB,WAAA,eAAAoB,MAAA,CAAAlB,cAAA,aAA+C;IAE3CvB,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAwB,UAAA,SAAAiB,MAAA,CAAAlB,cAAA,aAAgC;IAapCvB,EAAA,CAAAK,SAAA,GAA4C;IAA5CL,EAAA,CAAAqB,WAAA,eAAAoB,MAAA,CAAAlB,cAAA,UAA4C;IAExCvB,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAwB,UAAA,SAAAiB,MAAA,CAAAlB,cAAA,UAA6B;IAajCvB,EAAA,CAAAK,SAAA,GAA+C;IAA/CL,EAAA,CAAAqB,WAAA,eAAAoB,MAAA,CAAAlB,cAAA,aAA+C;IAE3CvB,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAwB,UAAA,SAAAiB,MAAA,CAAAlB,cAAA,aAAgC;IAapCvB,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAAqB,WAAA,eAAAoB,MAAA,CAAAlB,cAAA,oBAAsD;IAElDvB,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAwB,UAAA,SAAAiB,MAAA,CAAAlB,cAAA,oBAAuC;IAO3CvB,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAwB,UAAA,SAAAiB,MAAA,CAAAE,YAAA,cAA+B;IAiGnC3C,EAAA,CAAAK,SAAA,IAAsB;IAAtBL,EAAA,CAAAwB,UAAA,aAAAiB,MAAA,CAAAG,SAAA,CAAsB;IAEf5C,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAwB,UAAA,SAAAiB,MAAA,CAAAG,SAAA,CAAe;IACf5C,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAwB,UAAA,SAAAiB,MAAA,CAAAG,SAAA,CAAe;IACf5C,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAwB,UAAA,UAAAiB,MAAA,CAAAG,SAAA,CAAgB;;;ADlOrC,OAAM,MAAOC,iBAAiB;EAO5BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IARhB,KAAAL,SAAS,GAAG,KAAK;IACjB,KAAAlC,YAAY,GAAG,EAAE;IACjB,KAAAF,cAAc,GAAG,EAAE;IACnB,KAAAmC,YAAY,GAAyB,SAAS;EAM3C;EAEHO,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACF,WAAW,CAACG,eAAe,EAAE,EAAE;MACtC,IAAI,CAACC,mBAAmB,EAAE;MAC1B;;IAGF,IAAI,CAACC,cAAc,EAAE;EACvB;EAEQA,cAAcA,CAAA;IACpB,IAAI,CAACX,YAAY,GAAG,IAAI,CAACK,WAAW,CAACO,KAAK,CAAC;MACzCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACxD,UAAU,CAACyD,QAAQ,EAAEzD,UAAU,CAAC0D,SAAS,CAAC,CAAC,CAAC,EAAE1D,UAAU,CAAC2D,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MACzFC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC5D,UAAU,CAACyD,QAAQ,EAAEzD,UAAU,CAAC4D,KAAK,CAAC,CAAC;MACpDC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC7D,UAAU,CAACyD,QAAQ,EAAEzD,UAAU,CAAC0D,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DI,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC9D,UAAU,CAACyD,QAAQ,CAAC,CAAC;MAC5CM,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC/D,UAAU,CAACyD,QAAQ,CAAC,CAAC;MACxCO,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,OAAO,EAAE,CAAC,EAAE,CAAC;MAEb;MACAC,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,aAAa,EAAE,CAAC,EAAE,CAAC;MACnBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,iBAAiB,EAAE,CAAC,EAAE;KACvB,EAAE;MAAEC,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;IAE/C;IACA,IAAI,CAAC5B,YAAY,CAAC6B,GAAG,CAAC,MAAM,CAAC,EAAEC,YAAY,CAACC,SAAS,CAACX,IAAI,IAAG;MAC3D,IAAI,CAACnB,YAAY,GAAGmB,IAAI;MACxB,IAAI,CAACY,gBAAgB,EAAE;IACzB,CAAC,CAAC;EACJ;EAEQJ,sBAAsBA,CAACK,IAAe;IAC5C,MAAMf,QAAQ,GAAGe,IAAI,CAACJ,GAAG,CAAC,UAAU,CAAC;IACrC,MAAMV,eAAe,GAAGc,IAAI,CAACJ,GAAG,CAAC,iBAAiB,CAAC;IAEnD,IAAIX,QAAQ,IAAIC,eAAe,IAAID,QAAQ,CAACgB,KAAK,KAAKf,eAAe,CAACe,KAAK,EAAE;MAC3Ef,eAAe,CAACgB,SAAS,CAAC;QAAEC,gBAAgB,EAAE;MAAI,CAAE,CAAC;KACtD,MAAM;MACL,IAAIjB,eAAe,EAAEkB,MAAM,GAAG,kBAAkB,CAAC,EAAE;QACjD,OAAOlB,eAAe,CAACkB,MAAM,CAAC,kBAAkB,CAAC;QACjD,IAAIC,MAAM,CAACC,IAAI,CAACpB,eAAe,CAACkB,MAAM,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;UACpDrB,eAAe,CAACgB,SAAS,CAAC,IAAI,CAAC;;;;IAIrC,OAAO,IAAI;EACb;EAEQH,gBAAgBA,CAAA;IACtB,MAAMT,cAAc,GAAG,IAAI,CAACvB,YAAY,CAAC6B,GAAG,CAAC,gBAAgB,CAAC;IAC9D,MAAML,aAAa,GAAG,IAAI,CAACxB,YAAY,CAAC6B,GAAG,CAAC,eAAe,CAAC;IAE5D,IAAI,IAAI,CAAC5B,YAAY,KAAK,QAAQ,EAAE;MAClCsB,cAAc,EAAEkB,aAAa,CAAC,CAACpF,UAAU,CAACyD,QAAQ,CAAC,CAAC;MACpDU,aAAa,EAAEiB,aAAa,CAAC,CAACpF,UAAU,CAACyD,QAAQ,CAAC,CAAC;KACpD,MAAM;MACLS,cAAc,EAAEmB,eAAe,EAAE;MACjClB,aAAa,EAAEkB,eAAe,EAAE;;IAGlCnB,cAAc,EAAEoB,sBAAsB,EAAE;IACxCnB,aAAa,EAAEmB,sBAAsB,EAAE;EACzC;EAEArD,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACU,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC4C,OAAO,EAAE;MACnD,IAAI,CAACC,oBAAoB,EAAE;MAC3B;;IAGF,IAAI,CAAC3C,SAAS,GAAG,IAAI;IACrB,IAAI,CAAClC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACF,cAAc,GAAG,EAAE;IAExB,MAAMgF,eAAe,GAAoB,IAAI,CAAC9C,YAAY,CAACkC,KAAK;IAEhE,IAAI,CAAC5B,WAAW,CAACyC,QAAQ,CAACD,eAAe,CAAC,CAACf,SAAS,CAAC;MACnDiB,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC/C,SAAS,GAAG,KAAK;QACtB,IAAI,CAACpC,cAAc,GAAG,sDAAsD;QAE5E;QACAoF,UAAU,CAAC,MAAK;UACd,IAAI,CAACxC,mBAAmB,EAAE;QAC5B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDyC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACjD,SAAS,GAAG,KAAK;QACtB,IAAI,CAAClC,YAAY,GAAGmF,KAAK,CAACC,OAAO,IAAI,wCAAwC;MAC/E;KACD,CAAC;EACJ;EAEQ1C,mBAAmBA,CAAA;IACzB,MAAM2C,IAAI,GAAG,IAAI,CAAC/C,WAAW,CAACgD,cAAc,EAAE;IAC9C,IAAID,IAAI,EAAE;MACR,IAAIA,IAAI,CAACjC,IAAI,KAAK,QAAQ,EAAE;QAC1B,IAAI,CAACb,MAAM,CAACgD,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;OAC5C,MAAM,IAAIF,IAAI,CAACjC,IAAI,KAAK,SAAS,EAAE;QAClC,IAAI,CAACb,MAAM,CAACgD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;OAC7C,MAAM;QACL,IAAI,CAAChD,MAAM,CAACgD,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;;;EAGjC;EAEQV,oBAAoBA,CAAA;IAC1B,IAAI,CAAC,IAAI,CAAC7C,YAAY,EAAE;MACtB;;IAEFsC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvC,YAAY,CAACwD,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACpD,MAAMC,OAAO,GAAG,IAAI,CAAC3D,YAAY,CAAC6B,GAAG,CAAC6B,GAAG,CAAC;MAC1CC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA/E,cAAcA,CAACgF,SAAiB;IAC9B,IAAI,CAAC,IAAI,CAAC7D,YAAY,EAAE;MACtB,OAAO,KAAK;;IAEd,MAAM8D,KAAK,GAAG,IAAI,CAAC9D,YAAY,CAAC6B,GAAG,CAACgC,SAAS,CAAC;IAC9C,OAAO,CAAC,EAAEC,KAAK,IAAIA,KAAK,CAAClB,OAAO,KAAKkB,KAAK,CAACC,KAAK,IAAID,KAAK,CAACE,OAAO,CAAC,CAAC;EACrE;EAEA9F,aAAaA,CAAC2F,SAAiB;IAC7B,IAAI,CAAC,IAAI,CAAC7D,YAAY,EAAE;MACtB,OAAO,EAAE;;IAEX,MAAM8D,KAAK,GAAG,IAAI,CAAC9D,YAAY,CAAC6B,GAAG,CAACgC,SAAS,CAAC;IAC9C,IAAIC,KAAK,EAAEzB,MAAM,EAAE;MACjB,IAAIyB,KAAK,CAACzB,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,GAAG,IAAI,CAAC4B,mBAAmB,CAACJ,SAAS,CAAC,cAAc;;MAE7D,IAAIC,KAAK,CAACzB,MAAM,CAAC,OAAO,CAAC,EAAE;QACzB,OAAO,oCAAoC;;MAE7C,IAAIyB,KAAK,CAACzB,MAAM,CAAC,WAAW,CAAC,EAAE;QAC7B,OAAO,GAAG,IAAI,CAAC4B,mBAAmB,CAACJ,SAAS,CAAC,qBAAqBC,KAAK,CAACzB,MAAM,CAAC,WAAW,CAAC,CAAC6B,cAAc,aAAa;;MAEzH,IAAIJ,KAAK,CAACzB,MAAM,CAAC,WAAW,CAAC,EAAE;QAC7B,OAAO,GAAG,IAAI,CAAC4B,mBAAmB,CAACJ,SAAS,CAAC,oBAAoBC,KAAK,CAACzB,MAAM,CAAC,WAAW,CAAC,CAAC6B,cAAc,aAAa;;MAExH,IAAIJ,KAAK,CAACzB,MAAM,CAAC,kBAAkB,CAAC,EAAE;QACpC,OAAO,wBAAwB;;;IAGnC,OAAO,EAAE;EACX;EAEQ4B,mBAAmBA,CAACJ,SAAiB;IAC3C,MAAMM,YAAY,GAA8B;MAC9CtD,QAAQ,EAAE,WAAW;MACrBI,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,UAAU;MACpBC,eAAe,EAAE,kBAAkB;MACnCE,WAAW,EAAE,cAAc;MAC3BE,cAAc,EAAE,gBAAgB;MAChCC,aAAa,EAAE,gBAAgB;MAC/BC,WAAW,EAAE,aAAa;MAC1BC,iBAAiB,EAAE;KACpB;IACD,OAAOyC,YAAY,CAACN,SAAS,CAAC,IAAIA,SAAS;EAC7C;;;uBAlLW1D,iBAAiB,EAAA7C,EAAA,CAAA8G,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhH,EAAA,CAAA8G,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAlH,EAAA,CAAA8G,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAjBvE,iBAAiB;MAAAwE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX9B3H,EAAA,CAAAC,cAAA,aAA6C;UAOjCD,EAAA,CAAAE,SAAA,WAA6D;UAC7DF,EAAA,CAAAC,cAAA,YAA8B;UAAAD,EAAA,CAAAG,MAAA,yBAAkB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACrDJ,EAAA,CAAAC,cAAA,WAAsB;UAAAD,EAAA,CAAAG,MAAA,0CAAkC;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAI9DJ,EAAA,CAAAkB,UAAA,KAAA2G,iCAAA,iBAGM;UAGN7H,EAAA,CAAAkB,UAAA,KAAA4G,iCAAA,kBAGM;UAGN9H,EAAA,CAAAkB,UAAA,KAAA6G,iCAAA,kBAKM;UAGN/H,EAAA,CAAAkB,UAAA,KAAA8G,kCAAA,qBA8MO;UAGPhI,EAAA,CAAAC,cAAA,eAA8B;UAE1BD,EAAA,CAAAG,MAAA,kCACA;UAAAH,EAAA,CAAAC,cAAA,aAAkF;UAChFD,EAAA,CAAAG,MAAA,sBACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;;;UA1OFJ,EAAA,CAAAK,SAAA,IAAoB;UAApBL,EAAA,CAAAwB,UAAA,SAAAoG,GAAA,CAAApH,cAAA,CAAoB;UAMpBR,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAAwB,UAAA,SAAAoG,GAAA,CAAAlH,YAAA,CAAkB;UAMlBV,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAAwB,UAAA,UAAAoG,GAAA,CAAAlF,YAAA,CAAmB;UAQlB1C,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAAwB,UAAA,SAAAoG,GAAA,CAAAlF,YAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}