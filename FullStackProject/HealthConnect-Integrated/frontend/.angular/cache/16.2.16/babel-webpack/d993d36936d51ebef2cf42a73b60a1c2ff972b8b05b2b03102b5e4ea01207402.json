{"ast": null, "code": "let nextHandle = 1;\nlet resolved;\nconst activeHandles = {};\nfunction findAndClearHandle(handle) {\n  if (handle in activeHandles) {\n    delete activeHandles[handle];\n    return true;\n  }\n  return false;\n}\nexport const Immediate = {\n  setImmediate(cb) {\n    const handle = nextHandle++;\n    activeHandles[handle] = true;\n    if (!resolved) {\n      resolved = Promise.resolve();\n    }\n    resolved.then(() => findAndClearHandle(handle) && cb());\n    return handle;\n  },\n  clearImmediate(handle) {\n    findAndClearHandle(handle);\n  }\n};\nexport const TestTools = {\n  pending() {\n    return Object.keys(activeHandles).length;\n  }\n};", "map": {"version": 3, "names": ["nextH<PERSON>le", "resolved", "active<PERSON><PERSON><PERSON>", "findAndClearHandle", "handle", "Immediate", "setImmediate", "cb", "Promise", "resolve", "then", "clearImmediate", "TestTools", "pending", "Object", "keys", "length"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/rxjs/dist/esm/internal/util/Immediate.js"], "sourcesContent": ["let nextHandle = 1;\nlet resolved;\nconst activeHandles = {};\nfunction findAndClearHandle(handle) {\n    if (handle in activeHandles) {\n        delete activeHandles[handle];\n        return true;\n    }\n    return false;\n}\nexport const Immediate = {\n    setImmediate(cb) {\n        const handle = nextHandle++;\n        activeHandles[handle] = true;\n        if (!resolved) {\n            resolved = Promise.resolve();\n        }\n        resolved.then(() => findAndClearHandle(handle) && cb());\n        return handle;\n    },\n    clearImmediate(handle) {\n        findAndClearHandle(handle);\n    },\n};\nexport const TestTools = {\n    pending() {\n        return Object.keys(activeHandles).length;\n    }\n};\n"], "mappings": "AAAA,IAAIA,UAAU,GAAG,CAAC;AAClB,IAAIC,QAAQ;AACZ,MAAMC,aAAa,GAAG,CAAC,CAAC;AACxB,SAASC,kBAAkBA,CAACC,MAAM,EAAE;EAChC,IAAIA,MAAM,IAAIF,aAAa,EAAE;IACzB,OAAOA,aAAa,CAACE,MAAM,CAAC;IAC5B,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AACA,OAAO,MAAMC,SAAS,GAAG;EACrBC,YAAYA,CAACC,EAAE,EAAE;IACb,MAAMH,MAAM,GAAGJ,UAAU,EAAE;IAC3BE,aAAa,CAACE,MAAM,CAAC,GAAG,IAAI;IAC5B,IAAI,CAACH,QAAQ,EAAE;MACXA,QAAQ,GAAGO,OAAO,CAACC,OAAO,CAAC,CAAC;IAChC;IACAR,QAAQ,CAACS,IAAI,CAAC,MAAMP,kBAAkB,CAACC,MAAM,CAAC,IAAIG,EAAE,CAAC,CAAC,CAAC;IACvD,OAAOH,MAAM;EACjB,CAAC;EACDO,cAAcA,CAACP,MAAM,EAAE;IACnBD,kBAAkB,CAACC,MAAM,CAAC;EAC9B;AACJ,CAAC;AACD,OAAO,MAAMQ,SAAS,GAAG;EACrBC,OAAOA,CAAA,EAAG;IACN,OAAOC,MAAM,CAACC,IAAI,CAACb,aAAa,CAAC,CAACc,MAAM;EAC5C;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}