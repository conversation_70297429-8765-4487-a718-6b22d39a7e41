{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../core/services/auth.service\";\nimport * as i3 from \"../core/services/user.service\";\nimport * as i4 from \"@angular/common\";\nfunction ProfileComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.toggleEdit());\n    });\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵtext(2, \"Edit Profile \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.successMessage, \" \");\n  }\n}\nfunction ProfileComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"i\", 37);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.errorMessage, \" \");\n  }\n}\nfunction ProfileComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getFieldError(\"fullName\"), \" \");\n  }\n}\nfunction ProfileComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13)(2, \"h6\", 14);\n    i0.ɵɵelement(3, \"i\", 39);\n    i0.ɵɵtext(4, \"Professional Information \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 16)(6, \"label\", 40);\n    i0.ɵɵtext(7, \"Specialization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"input\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 16)(10, \"label\", 42);\n    i0.ɵɵtext(11, \"Hospital/Clinic\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 16)(14, \"label\", 44);\n    i0.ɵɵtext(15, \"Years of Experience\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"input\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 16)(18, \"label\", 29);\n    i0.ɵɵtext(19, \"License Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"input\", 30);\n    i0.ɵɵelementStart(21, \"small\", 22);\n    i0.ɵɵtext(22, \"License number cannot be changed\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"readonly\", !ctx_r4.isEditing);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"readonly\", !ctx_r4.isEditing);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"readonly\", !ctx_r4.isEditing);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", ctx_r4.currentUser == null ? null : ctx_r4.currentUser.licenseNumber);\n  }\n}\nfunction ProfileComponent_div_53_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 51);\n  }\n}\nfunction ProfileComponent_div_53_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Saving...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_53_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Save Changes\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"button\", 47);\n    i0.ɵɵtemplate(2, ProfileComponent_div_53_span_2_Template, 1, 0, \"span\", 48);\n    i0.ɵɵtemplate(3, ProfileComponent_div_53_span_3_Template, 2, 0, \"span\", 49);\n    i0.ɵɵtemplate(4, ProfileComponent_div_53_span_4_Template, 2, 0, \"span\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_53_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.toggleEdit());\n    });\n    i0.ɵɵtext(6, \" Cancel \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r5.isLoading || ctx_r5.profileForm.invalid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r5.isLoading);\n  }\n}\nexport let ProfileComponent = /*#__PURE__*/(() => {\n  class ProfileComponent {\n    constructor(formBuilder, authService, userService) {\n      this.formBuilder = formBuilder;\n      this.authService = authService;\n      this.userService = userService;\n      this.currentUser = null;\n      this.isLoading = false;\n      this.isEditing = false;\n      this.successMessage = '';\n      this.errorMessage = '';\n    }\n    ngOnInit() {\n      this.initializeForm();\n      this.loadUserData();\n    }\n    initializeForm() {\n      this.profileForm = this.formBuilder.group({\n        fullName: ['', [Validators.required, Validators.minLength(2)]],\n        email: [{\n          value: '',\n          disabled: true\n        }],\n        phoneNumber: [''],\n        address: [''],\n        specialization: [''],\n        affiliation: [''],\n        yearsOfExperience: ['']\n      });\n    }\n    loadUserData() {\n      this.authService.currentUser$.subscribe(user => {\n        if (user) {\n          this.currentUser = user;\n          this.populateForm(user);\n        }\n      });\n    }\n    populateForm(user) {\n      this.profileForm.patchValue({\n        fullName: user.fullName,\n        email: user.email,\n        phoneNumber: user.phoneNumber || '',\n        address: user.address || '',\n        specialization: user.specialization || '',\n        affiliation: user.affiliation || '',\n        yearsOfExperience: user.yearsOfExperience || ''\n      });\n    }\n    toggleEdit() {\n      this.isEditing = !this.isEditing;\n      this.successMessage = '';\n      this.errorMessage = '';\n      if (!this.isEditing) {\n        // Reset form when canceling edit\n        if (this.currentUser) {\n          this.populateForm(this.currentUser);\n        }\n      }\n    }\n    onSubmit() {\n      if (this.profileForm.invalid) {\n        this.markFormGroupTouched();\n        return;\n      }\n      this.isLoading = true;\n      this.successMessage = '';\n      this.errorMessage = '';\n      const updateRequest = {\n        fullName: this.profileForm.get('fullName')?.value,\n        phoneNumber: this.profileForm.get('phoneNumber')?.value,\n        address: this.profileForm.get('address')?.value\n      };\n      // Add doctor-specific fields if user is a doctor\n      if (this.currentUser?.role === 'DOCTOR') {\n        updateRequest.specialization = this.profileForm.get('specialization')?.value;\n        updateRequest.affiliation = this.profileForm.get('affiliation')?.value;\n        updateRequest.yearsOfExperience = this.profileForm.get('yearsOfExperience')?.value;\n      }\n      this.userService.updateProfile(updateRequest).subscribe({\n        next: updatedUser => {\n          this.isLoading = false;\n          this.isEditing = false;\n          this.successMessage = 'Profile updated successfully!';\n          this.currentUser = updatedUser;\n        },\n        error: error => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Failed to update profile. Please try again.';\n        }\n      });\n    }\n    markFormGroupTouched() {\n      Object.keys(this.profileForm.controls).forEach(key => {\n        const control = this.profileForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    isFieldInvalid(fieldName) {\n      const field = this.profileForm.get(fieldName);\n      return !!(field && field.invalid && (field.dirty || field.touched));\n    }\n    getFieldError(fieldName) {\n      const field = this.profileForm.get(fieldName);\n      if (field?.errors) {\n        if (field.errors['required']) {\n          return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;\n        }\n        if (field.errors['minlength']) {\n          return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${field.errors['minlength'].requiredLength} characters`;\n        }\n      }\n      return '';\n    }\n    static {\n      this.ɵfac = function ProfileComponent_Factory(t) {\n        return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.UserService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProfileComponent,\n        selectors: [[\"app-profile\"]],\n        decls: 54,\n        vars: 19,\n        consts: [[1, \"container\", \"py-4\"], [1, \"row\", \"justify-content-center\"], [1, \"col-md-8\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"bi\", \"bi-person-gear\", \"me-2\"], [\"class\", \"btn btn-outline-primary btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"card-body\"], [\"class\", \"alert alert-success\", \"role\", \"alert\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"row\", \"mb-4\"], [1, \"col-12\"], [1, \"text-primary\", \"mb-3\"], [1, \"bi\", \"bi-person\", \"me-2\"], [1, \"col-md-6\", \"mb-3\"], [\"for\", \"fullName\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"fullName\", \"formControlName\", \"fullName\", 1, \"form-control\", 3, \"readonly\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", \"readonly\", \"\", 1, \"form-control\"], [1, \"text-muted\"], [\"for\", \"phoneNumber\", 1, \"form-label\"], [\"type\", \"tel\", \"id\", \"phoneNumber\", \"formControlName\", \"phoneNumber\", \"placeholder\", \"Enter your phone number\", 1, \"form-control\", 3, \"readonly\"], [\"for\", \"address\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"address\", \"formControlName\", \"address\", \"placeholder\", \"Enter your address\", 1, \"form-control\", 3, \"readonly\"], [\"class\", \"row mb-4\", 4, \"ngIf\"], [1, \"bi\", \"bi-shield-check\", \"me-2\"], [1, \"form-label\"], [\"type\", \"text\", \"readonly\", \"\", 1, \"form-control\", 3, \"value\"], [\"class\", \"d-flex gap-2\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\"], [1, \"bi\", \"bi-pencil\", \"me-1\"], [\"role\", \"alert\", 1, \"alert\", \"alert-success\"], [1, \"bi\", \"bi-check-circle\", \"me-2\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"bi\", \"bi-exclamation-triangle\", \"me-2\"], [1, \"invalid-feedback\"], [1, \"bi\", \"bi-person-badge\", \"me-2\"], [\"for\", \"specialization\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"specialization\", \"formControlName\", \"specialization\", \"placeholder\", \"e.g., Cardiology\", 1, \"form-control\", 3, \"readonly\"], [\"for\", \"affiliation\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"affiliation\", \"formControlName\", \"affiliation\", \"placeholder\", \"Your workplace\", 1, \"form-control\", 3, \"readonly\"], [\"for\", \"yearsOfExperience\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"yearsOfExperience\", \"formControlName\", \"yearsOfExperience\", \"placeholder\", \"0\", \"min\", \"0\", \"max\", \"50\", 1, \"form-control\", 3, \"readonly\"], [1, \"d-flex\", \"gap-2\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", 4, \"ngIf\"], [4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"disabled\", \"click\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n        template: function ProfileComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h5\", 5);\n            i0.ɵɵelement(6, \"i\", 6);\n            i0.ɵɵtext(7, \"Profile Settings \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(8, ProfileComponent_button_8_Template, 3, 0, \"button\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"div\", 8);\n            i0.ɵɵtemplate(10, ProfileComponent_div_10_Template, 3, 1, \"div\", 9);\n            i0.ɵɵtemplate(11, ProfileComponent_div_11_Template, 3, 1, \"div\", 10);\n            i0.ɵɵelementStart(12, \"form\", 11);\n            i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_Template_form_ngSubmit_12_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(13, \"div\", 12)(14, \"div\", 13)(15, \"h6\", 14);\n            i0.ɵɵelement(16, \"i\", 15);\n            i0.ɵɵtext(17, \"Basic Information \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(18, \"div\", 16)(19, \"label\", 17);\n            i0.ɵɵtext(20, \"Full Name *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(21, \"input\", 18);\n            i0.ɵɵtemplate(22, ProfileComponent_div_22_Template, 2, 1, \"div\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"div\", 16)(24, \"label\", 20);\n            i0.ɵɵtext(25, \"Email Address\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(26, \"input\", 21);\n            i0.ɵɵelementStart(27, \"small\", 22);\n            i0.ɵɵtext(28, \"Email cannot be changed\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(29, \"div\", 16)(30, \"label\", 23);\n            i0.ɵɵtext(31, \"Phone Number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(32, \"input\", 24);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"div\", 16)(34, \"label\", 25);\n            i0.ɵɵtext(35, \"Address\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(36, \"input\", 26);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(37, ProfileComponent_div_37_Template, 23, 4, \"div\", 27);\n            i0.ɵɵelementStart(38, \"div\", 12)(39, \"div\", 13)(40, \"h6\", 14);\n            i0.ɵɵelement(41, \"i\", 28);\n            i0.ɵɵtext(42, \"Account Information \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(43, \"div\", 16)(44, \"label\", 29);\n            i0.ɵɵtext(45, \"Role\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(46, \"input\", 30);\n            i0.ɵɵpipe(47, \"titlecase\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"div\", 16)(49, \"label\", 29);\n            i0.ɵɵtext(50, \"Member Since\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(51, \"input\", 30);\n            i0.ɵɵpipe(52, \"date\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(53, ProfileComponent_div_53_Template, 7, 5, \"div\", 31);\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isEditing);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.successMessage);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formGroup\", ctx.profileForm);\n            i0.ɵɵadvance(9);\n            i0.ɵɵclassProp(\"is-invalid\", ctx.isFieldInvalid(\"fullName\"));\n            i0.ɵɵproperty(\"readonly\", !ctx.isEditing);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isFieldInvalid(\"fullName\"));\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"readonly\", !ctx.isEditing);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"readonly\", !ctx.isEditing);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", (ctx.currentUser == null ? null : ctx.currentUser.role) === \"DOCTOR\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"value\", i0.ɵɵpipeBind1(47, 14, ctx.currentUser == null ? null : ctx.currentUser.role));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"value\", i0.ɵɵpipeBind2(52, 16, ctx.currentUser == null ? null : ctx.currentUser.createdAt, \"mediumDate\"));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.isEditing);\n          }\n        },\n        dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i4.TitleCasePipe, i4.DatePipe],\n        styles: [\".card[_ngcontent-%COMP%]{border:none;border-radius:12px;box-shadow:0 4px 6px #0000001a}.card-header[_ngcontent-%COMP%]{background-color:transparent;border-bottom:1px solid #e9ecef;font-weight:600}.form-control[readonly][_ngcontent-%COMP%]{background-color:#f8f9fa;border-color:#e9ecef}.text-primary[_ngcontent-%COMP%]{color:#0d6efd!important}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#0d6efd 0%,#0b5ed7 100%);border:none;font-weight:500}.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#0b5ed7 0%,#0a58ca 100%);transform:translateY(-1px);box-shadow:0 4px 8px #0d6efd4d}.btn-outline-primary[_ngcontent-%COMP%]{border-color:#0d6efd;color:#0d6efd;font-weight:500}.btn-outline-secondary[_ngcontent-%COMP%]{font-weight:500}.alert[_ngcontent-%COMP%]{border:none;border-radius:8px}@media (max-width: 768px){.container[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}.card-body[_ngcontent-%COMP%]{padding:1.5rem}}\"]\n      });\n    }\n  }\n  return ProfileComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}