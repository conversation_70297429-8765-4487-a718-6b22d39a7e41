{"ast": null, "code": "import { isFunction } from './util/isFunction';\nimport { isSubscription, Subscription } from './Subscription';\nimport { config } from './config';\nimport { reportUnhandledError } from './util/reportUnhandledError';\nimport { noop } from './util/noop';\nimport { nextNotification, errorNotification, COMPLETE_NOTIFICATION } from './NotificationFactories';\nimport { timeoutProvider } from './scheduler/timeoutProvider';\nimport { captureError } from './util/errorContext';\nexport class Subscriber extends Subscription {\n  constructor(destination) {\n    super();\n    this.isStopped = false;\n    if (destination) {\n      this.destination = destination;\n      if (isSubscription(destination)) {\n        destination.add(this);\n      }\n    } else {\n      this.destination = EMPTY_OBSERVER;\n    }\n  }\n  static create(next, error, complete) {\n    return new SafeSubscriber(next, error, complete);\n  }\n  next(value) {\n    if (this.isStopped) {\n      handleStoppedNotification(nextNotification(value), this);\n    } else {\n      this._next(value);\n    }\n  }\n  error(err) {\n    if (this.isStopped) {\n      handleStoppedNotification(errorNotification(err), this);\n    } else {\n      this.isStopped = true;\n      this._error(err);\n    }\n  }\n  complete() {\n    if (this.isStopped) {\n      handleStoppedNotification(COMPLETE_NOTIFICATION, this);\n    } else {\n      this.isStopped = true;\n      this._complete();\n    }\n  }\n  unsubscribe() {\n    if (!this.closed) {\n      this.isStopped = true;\n      super.unsubscribe();\n      this.destination = null;\n    }\n  }\n  _next(value) {\n    this.destination.next(value);\n  }\n  _error(err) {\n    try {\n      this.destination.error(err);\n    } finally {\n      this.unsubscribe();\n    }\n  }\n  _complete() {\n    try {\n      this.destination.complete();\n    } finally {\n      this.unsubscribe();\n    }\n  }\n}\nconst _bind = Function.prototype.bind;\nfunction bind(fn, thisArg) {\n  return _bind.call(fn, thisArg);\n}\nclass ConsumerObserver {\n  constructor(partialObserver) {\n    this.partialObserver = partialObserver;\n  }\n  next(value) {\n    const {\n      partialObserver\n    } = this;\n    if (partialObserver.next) {\n      try {\n        partialObserver.next(value);\n      } catch (error) {\n        handleUnhandledError(error);\n      }\n    }\n  }\n  error(err) {\n    const {\n      partialObserver\n    } = this;\n    if (partialObserver.error) {\n      try {\n        partialObserver.error(err);\n      } catch (error) {\n        handleUnhandledError(error);\n      }\n    } else {\n      handleUnhandledError(err);\n    }\n  }\n  complete() {\n    const {\n      partialObserver\n    } = this;\n    if (partialObserver.complete) {\n      try {\n        partialObserver.complete();\n      } catch (error) {\n        handleUnhandledError(error);\n      }\n    }\n  }\n}\nexport class SafeSubscriber extends Subscriber {\n  constructor(observerOrNext, error, complete) {\n    super();\n    let partialObserver;\n    if (isFunction(observerOrNext) || !observerOrNext) {\n      partialObserver = {\n        next: observerOrNext !== null && observerOrNext !== void 0 ? observerOrNext : undefined,\n        error: error !== null && error !== void 0 ? error : undefined,\n        complete: complete !== null && complete !== void 0 ? complete : undefined\n      };\n    } else {\n      let context;\n      if (this && config.useDeprecatedNextContext) {\n        context = Object.create(observerOrNext);\n        context.unsubscribe = () => this.unsubscribe();\n        partialObserver = {\n          next: observerOrNext.next && bind(observerOrNext.next, context),\n          error: observerOrNext.error && bind(observerOrNext.error, context),\n          complete: observerOrNext.complete && bind(observerOrNext.complete, context)\n        };\n      } else {\n        partialObserver = observerOrNext;\n      }\n    }\n    this.destination = new ConsumerObserver(partialObserver);\n  }\n}\nfunction handleUnhandledError(error) {\n  if (config.useDeprecatedSynchronousErrorHandling) {\n    captureError(error);\n  } else {\n    reportUnhandledError(error);\n  }\n}\nfunction defaultErrorHandler(err) {\n  throw err;\n}\nfunction handleStoppedNotification(notification, subscriber) {\n  const {\n    onStoppedNotification\n  } = config;\n  onStoppedNotification && timeoutProvider.setTimeout(() => onStoppedNotification(notification, subscriber));\n}\nexport const EMPTY_OBSERVER = {\n  closed: true,\n  next: noop,\n  error: defaultErrorHandler,\n  complete: noop\n};", "map": {"version": 3, "names": ["isFunction", "isSubscription", "Subscription", "config", "reportUnhandledError", "noop", "nextNotification", "errorNotification", "COMPLETE_NOTIFICATION", "timeout<PERSON>rovider", "captureError", "Subscriber", "constructor", "destination", "isStopped", "add", "EMPTY_OBSERVER", "create", "next", "error", "complete", "SafeSubscriber", "value", "handleStoppedNotification", "_next", "err", "_error", "_complete", "unsubscribe", "closed", "_bind", "Function", "prototype", "bind", "fn", "thisArg", "call", "ConsumerObserver", "partialObserver", "handleUnhandledError", "observerOrNext", "undefined", "context", "useDeprecatedNextContext", "Object", "useDeprecatedSynchronousErrorHandling", "defaultErrorHandler", "notification", "subscriber", "onStoppedNotification", "setTimeout"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/node_modules/rxjs/dist/esm/internal/Subscriber.js"], "sourcesContent": ["import { isFunction } from './util/isFunction';\nimport { isSubscription, Subscription } from './Subscription';\nimport { config } from './config';\nimport { reportUnhandledError } from './util/reportUnhandledError';\nimport { noop } from './util/noop';\nimport { nextNotification, errorNotification, COMPLETE_NOTIFICATION } from './NotificationFactories';\nimport { timeoutProvider } from './scheduler/timeoutProvider';\nimport { captureError } from './util/errorContext';\nexport class Subscriber extends Subscription {\n    constructor(destination) {\n        super();\n        this.isStopped = false;\n        if (destination) {\n            this.destination = destination;\n            if (isSubscription(destination)) {\n                destination.add(this);\n            }\n        }\n        else {\n            this.destination = EMPTY_OBSERVER;\n        }\n    }\n    static create(next, error, complete) {\n        return new SafeSubscriber(next, error, complete);\n    }\n    next(value) {\n        if (this.isStopped) {\n            handleStoppedNotification(nextNotification(value), this);\n        }\n        else {\n            this._next(value);\n        }\n    }\n    error(err) {\n        if (this.isStopped) {\n            handleStoppedNotification(errorNotification(err), this);\n        }\n        else {\n            this.isStopped = true;\n            this._error(err);\n        }\n    }\n    complete() {\n        if (this.isStopped) {\n            handleStoppedNotification(COMPLETE_NOTIFICATION, this);\n        }\n        else {\n            this.isStopped = true;\n            this._complete();\n        }\n    }\n    unsubscribe() {\n        if (!this.closed) {\n            this.isStopped = true;\n            super.unsubscribe();\n            this.destination = null;\n        }\n    }\n    _next(value) {\n        this.destination.next(value);\n    }\n    _error(err) {\n        try {\n            this.destination.error(err);\n        }\n        finally {\n            this.unsubscribe();\n        }\n    }\n    _complete() {\n        try {\n            this.destination.complete();\n        }\n        finally {\n            this.unsubscribe();\n        }\n    }\n}\nconst _bind = Function.prototype.bind;\nfunction bind(fn, thisArg) {\n    return _bind.call(fn, thisArg);\n}\nclass ConsumerObserver {\n    constructor(partialObserver) {\n        this.partialObserver = partialObserver;\n    }\n    next(value) {\n        const { partialObserver } = this;\n        if (partialObserver.next) {\n            try {\n                partialObserver.next(value);\n            }\n            catch (error) {\n                handleUnhandledError(error);\n            }\n        }\n    }\n    error(err) {\n        const { partialObserver } = this;\n        if (partialObserver.error) {\n            try {\n                partialObserver.error(err);\n            }\n            catch (error) {\n                handleUnhandledError(error);\n            }\n        }\n        else {\n            handleUnhandledError(err);\n        }\n    }\n    complete() {\n        const { partialObserver } = this;\n        if (partialObserver.complete) {\n            try {\n                partialObserver.complete();\n            }\n            catch (error) {\n                handleUnhandledError(error);\n            }\n        }\n    }\n}\nexport class SafeSubscriber extends Subscriber {\n    constructor(observerOrNext, error, complete) {\n        super();\n        let partialObserver;\n        if (isFunction(observerOrNext) || !observerOrNext) {\n            partialObserver = {\n                next: (observerOrNext !== null && observerOrNext !== void 0 ? observerOrNext : undefined),\n                error: error !== null && error !== void 0 ? error : undefined,\n                complete: complete !== null && complete !== void 0 ? complete : undefined,\n            };\n        }\n        else {\n            let context;\n            if (this && config.useDeprecatedNextContext) {\n                context = Object.create(observerOrNext);\n                context.unsubscribe = () => this.unsubscribe();\n                partialObserver = {\n                    next: observerOrNext.next && bind(observerOrNext.next, context),\n                    error: observerOrNext.error && bind(observerOrNext.error, context),\n                    complete: observerOrNext.complete && bind(observerOrNext.complete, context),\n                };\n            }\n            else {\n                partialObserver = observerOrNext;\n            }\n        }\n        this.destination = new ConsumerObserver(partialObserver);\n    }\n}\nfunction handleUnhandledError(error) {\n    if (config.useDeprecatedSynchronousErrorHandling) {\n        captureError(error);\n    }\n    else {\n        reportUnhandledError(error);\n    }\n}\nfunction defaultErrorHandler(err) {\n    throw err;\n}\nfunction handleStoppedNotification(notification, subscriber) {\n    const { onStoppedNotification } = config;\n    onStoppedNotification && timeoutProvider.setTimeout(() => onStoppedNotification(notification, subscriber));\n}\nexport const EMPTY_OBSERVER = {\n    closed: true,\n    next: noop,\n    error: defaultErrorHandler,\n    complete: noop,\n};\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,cAAc,EAAEC,YAAY,QAAQ,gBAAgB;AAC7D,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,oBAAoB,QAAQ,6BAA6B;AAClE,SAASC,IAAI,QAAQ,aAAa;AAClC,SAASC,gBAAgB,EAAEC,iBAAiB,EAAEC,qBAAqB,QAAQ,yBAAyB;AACpG,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,YAAY,QAAQ,qBAAqB;AAClD,OAAO,MAAMC,UAAU,SAAST,YAAY,CAAC;EACzCU,WAAWA,CAACC,WAAW,EAAE;IACrB,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAID,WAAW,EAAE;MACb,IAAI,CAACA,WAAW,GAAGA,WAAW;MAC9B,IAAIZ,cAAc,CAACY,WAAW,CAAC,EAAE;QAC7BA,WAAW,CAACE,GAAG,CAAC,IAAI,CAAC;MACzB;IACJ,CAAC,MACI;MACD,IAAI,CAACF,WAAW,GAAGG,cAAc;IACrC;EACJ;EACA,OAAOC,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACjC,OAAO,IAAIC,cAAc,CAACH,IAAI,EAAEC,KAAK,EAAEC,QAAQ,CAAC;EACpD;EACAF,IAAIA,CAACI,KAAK,EAAE;IACR,IAAI,IAAI,CAACR,SAAS,EAAE;MAChBS,yBAAyB,CAACjB,gBAAgB,CAACgB,KAAK,CAAC,EAAE,IAAI,CAAC;IAC5D,CAAC,MACI;MACD,IAAI,CAACE,KAAK,CAACF,KAAK,CAAC;IACrB;EACJ;EACAH,KAAKA,CAACM,GAAG,EAAE;IACP,IAAI,IAAI,CAACX,SAAS,EAAE;MAChBS,yBAAyB,CAAChB,iBAAiB,CAACkB,GAAG,CAAC,EAAE,IAAI,CAAC;IAC3D,CAAC,MACI;MACD,IAAI,CAACX,SAAS,GAAG,IAAI;MACrB,IAAI,CAACY,MAAM,CAACD,GAAG,CAAC;IACpB;EACJ;EACAL,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACN,SAAS,EAAE;MAChBS,yBAAyB,CAACf,qBAAqB,EAAE,IAAI,CAAC;IAC1D,CAAC,MACI;MACD,IAAI,CAACM,SAAS,GAAG,IAAI;MACrB,IAAI,CAACa,SAAS,CAAC,CAAC;IACpB;EACJ;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MACd,IAAI,CAACf,SAAS,GAAG,IAAI;MACrB,KAAK,CAACc,WAAW,CAAC,CAAC;MACnB,IAAI,CAACf,WAAW,GAAG,IAAI;IAC3B;EACJ;EACAW,KAAKA,CAACF,KAAK,EAAE;IACT,IAAI,CAACT,WAAW,CAACK,IAAI,CAACI,KAAK,CAAC;EAChC;EACAI,MAAMA,CAACD,GAAG,EAAE;IACR,IAAI;MACA,IAAI,CAACZ,WAAW,CAACM,KAAK,CAACM,GAAG,CAAC;IAC/B,CAAC,SACO;MACJ,IAAI,CAACG,WAAW,CAAC,CAAC;IACtB;EACJ;EACAD,SAASA,CAAA,EAAG;IACR,IAAI;MACA,IAAI,CAACd,WAAW,CAACO,QAAQ,CAAC,CAAC;IAC/B,CAAC,SACO;MACJ,IAAI,CAACQ,WAAW,CAAC,CAAC;IACtB;EACJ;AACJ;AACA,MAAME,KAAK,GAAGC,QAAQ,CAACC,SAAS,CAACC,IAAI;AACrC,SAASA,IAAIA,CAACC,EAAE,EAAEC,OAAO,EAAE;EACvB,OAAOL,KAAK,CAACM,IAAI,CAACF,EAAE,EAAEC,OAAO,CAAC;AAClC;AACA,MAAME,gBAAgB,CAAC;EACnBzB,WAAWA,CAAC0B,eAAe,EAAE;IACzB,IAAI,CAACA,eAAe,GAAGA,eAAe;EAC1C;EACApB,IAAIA,CAACI,KAAK,EAAE;IACR,MAAM;MAAEgB;IAAgB,CAAC,GAAG,IAAI;IAChC,IAAIA,eAAe,CAACpB,IAAI,EAAE;MACtB,IAAI;QACAoB,eAAe,CAACpB,IAAI,CAACI,KAAK,CAAC;MAC/B,CAAC,CACD,OAAOH,KAAK,EAAE;QACVoB,oBAAoB,CAACpB,KAAK,CAAC;MAC/B;IACJ;EACJ;EACAA,KAAKA,CAACM,GAAG,EAAE;IACP,MAAM;MAAEa;IAAgB,CAAC,GAAG,IAAI;IAChC,IAAIA,eAAe,CAACnB,KAAK,EAAE;MACvB,IAAI;QACAmB,eAAe,CAACnB,KAAK,CAACM,GAAG,CAAC;MAC9B,CAAC,CACD,OAAON,KAAK,EAAE;QACVoB,oBAAoB,CAACpB,KAAK,CAAC;MAC/B;IACJ,CAAC,MACI;MACDoB,oBAAoB,CAACd,GAAG,CAAC;IAC7B;EACJ;EACAL,QAAQA,CAAA,EAAG;IACP,MAAM;MAAEkB;IAAgB,CAAC,GAAG,IAAI;IAChC,IAAIA,eAAe,CAAClB,QAAQ,EAAE;MAC1B,IAAI;QACAkB,eAAe,CAAClB,QAAQ,CAAC,CAAC;MAC9B,CAAC,CACD,OAAOD,KAAK,EAAE;QACVoB,oBAAoB,CAACpB,KAAK,CAAC;MAC/B;IACJ;EACJ;AACJ;AACA,OAAO,MAAME,cAAc,SAASV,UAAU,CAAC;EAC3CC,WAAWA,CAAC4B,cAAc,EAAErB,KAAK,EAAEC,QAAQ,EAAE;IACzC,KAAK,CAAC,CAAC;IACP,IAAIkB,eAAe;IACnB,IAAItC,UAAU,CAACwC,cAAc,CAAC,IAAI,CAACA,cAAc,EAAE;MAC/CF,eAAe,GAAG;QACdpB,IAAI,EAAGsB,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGC,SAAU;QACzFtB,KAAK,EAAEA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGsB,SAAS;QAC7DrB,QAAQ,EAAEA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGqB;MACpE,CAAC;IACL,CAAC,MACI;MACD,IAAIC,OAAO;MACX,IAAI,IAAI,IAAIvC,MAAM,CAACwC,wBAAwB,EAAE;QACzCD,OAAO,GAAGE,MAAM,CAAC3B,MAAM,CAACuB,cAAc,CAAC;QACvCE,OAAO,CAACd,WAAW,GAAG,MAAM,IAAI,CAACA,WAAW,CAAC,CAAC;QAC9CU,eAAe,GAAG;UACdpB,IAAI,EAAEsB,cAAc,CAACtB,IAAI,IAAIe,IAAI,CAACO,cAAc,CAACtB,IAAI,EAAEwB,OAAO,CAAC;UAC/DvB,KAAK,EAAEqB,cAAc,CAACrB,KAAK,IAAIc,IAAI,CAACO,cAAc,CAACrB,KAAK,EAAEuB,OAAO,CAAC;UAClEtB,QAAQ,EAAEoB,cAAc,CAACpB,QAAQ,IAAIa,IAAI,CAACO,cAAc,CAACpB,QAAQ,EAAEsB,OAAO;QAC9E,CAAC;MACL,CAAC,MACI;QACDJ,eAAe,GAAGE,cAAc;MACpC;IACJ;IACA,IAAI,CAAC3B,WAAW,GAAG,IAAIwB,gBAAgB,CAACC,eAAe,CAAC;EAC5D;AACJ;AACA,SAASC,oBAAoBA,CAACpB,KAAK,EAAE;EACjC,IAAIhB,MAAM,CAAC0C,qCAAqC,EAAE;IAC9CnC,YAAY,CAACS,KAAK,CAAC;EACvB,CAAC,MACI;IACDf,oBAAoB,CAACe,KAAK,CAAC;EAC/B;AACJ;AACA,SAAS2B,mBAAmBA,CAACrB,GAAG,EAAE;EAC9B,MAAMA,GAAG;AACb;AACA,SAASF,yBAAyBA,CAACwB,YAAY,EAAEC,UAAU,EAAE;EACzD,MAAM;IAAEC;EAAsB,CAAC,GAAG9C,MAAM;EACxC8C,qBAAqB,IAAIxC,eAAe,CAACyC,UAAU,CAAC,MAAMD,qBAAqB,CAACF,YAAY,EAAEC,UAAU,CAAC,CAAC;AAC9G;AACA,OAAO,MAAMhC,cAAc,GAAG;EAC1Ba,MAAM,EAAE,IAAI;EACZX,IAAI,EAAEb,IAAI;EACVc,KAAK,EAAE2B,mBAAmB;EAC1B1B,QAAQ,EAAEf;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}